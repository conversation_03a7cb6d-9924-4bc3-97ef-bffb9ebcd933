# tkp2sdbp

## !! Предупреждение !!
Перед начало запуска контейнером, убедитесь, что у вас не занят **80** порт.

## Начало использования
Требования:
    - Docker 20.x версии и выше
    - Docker-compose

## Сборка и запуск

### Сборка контейнеров
Данная команда скопирует .env, создаст external network "net" для работы с NSI, обнвоит файл hosts
```shell
make build
```

### Запуск контейнеров
Запускает быструю сборку со скачиванием базового php образа с нашего registry
```shell
make run
```
Однако, если нет доступа к registry тогда необходимо собрать base образ локально
```shell
make run_with_base
```

### Миграции

```shell
make migrate
```
### Работа с дампами
Есть два варианта накатки дампов:
1. В классическом варианте - выкачать с удаленной базы дамп и накатить [~30+min]
2. Скачать предварительно подготовленный дамп с s3 и накатить [~10-15min]

**Вариант 1**: Необходимо прописать в makefile для этой команды ip адрес сервера с mysql и пароль от юзера

```shell
make download_dump
```
После того как скачали дамп необходимо его накатить
```shell
make restore
```

**Вариант 2**:Выкачивается сжатый дамп с s3,
разархивируется и накатывается сразу же на локальную базу.
Необходимо предварительно установить [obsutil](https://cloud.ru/docs/obs/ug/topics/guides__tools-install__obsutil)
для работы с s3 и получить доступы
```shell
make run_dump_from_s3
```
### Вход в контейнер

Для входа в контейнер приложения

```shell
make app or docker compose exec php bash
```

### Остановка контейнеров

Чтобы остановить контейнеры, укажите следующую команду

```shell
make stop
```

### Уничтожить контейнеры

Чтобы уничтожить все контейнеры

```shell
make down
```
//Данный конфиг создается автоматически и уже сразу есть все доступы из-вне
Если вам нужен доступ к контейнерам из вне, например, доступ к базе данных, создайте рядом с `docker-compose.yml`
файл `docker-compose.override.yml`
Ниже пример как он выглядит:

```yml
version: '3.7'

services:
    mysql:
        ports:
            -   published: 3306
                target: 3306
                protocol: tcp

    # Database for work Keycloak
    postgresql:
        ports:
            -   published: 5432
                target: 5432
                protocol: tcp

    keycloak:
        ports:
            -   published: 8080
                target: 8080
                protocol: tcp
```

## Настройка и конфигурация проект(а/ов)

Для переопределений локальных конфигурации для нужного проекта создайте в папке проекта `config` папку `local` внутри неё создайте уже файл
по следующему шаблону `domain.php`, например, для домена `admin.sdbp.local` файл будет `admin.sdbp.local.php` внутри него вам будет доступ контекст параметров из родительского файла `config.php`
Ниже пример использования локального файла конфигурации:

```php
<?php

/**
 * @var object $config
 */

$config->database->mysql->host = 'mysql';
$config->database->mysql->user = 'root';
$config->database->mysql->password = 'password';
$config->database->mysql->database = 'sdbp.local';

$config->keycloak = [
    'client_id' => 'some_client_id',
    'client_secret' => 'secret',
    'realm' => 'some_realm',
    'base_url' => 'http://keycloak:8080',
    'is_enabled' => true,
];
```
Также из-за сложности проекта также нужно переопределить конфигурацию подключения к базе данных, для этого в папке `lib/Cfg` создайте файл
`local.cfg.php` и переопределите параметры подключения, ниже приведён пример:
```php
<?php

use lib\Cfg\Cfg;

Cfg::$db = [
    'server' => 'mysql',
    'user' => 'gds-user',
    'psw' => '>9}ta69Wx4$6K',
    'dbname' => 'nsi.local',
    'port' => '3306',
    'socket' => null
];
```

## Тестирование

Скопируйте файл `phpunit.xml.dist` и укажите ваш домен, для API СДБП.
Для локального окружения это `http://api.sdbp.local`

```shell
cp phpunit.xml.dist phpunit.xml
```
Для запуска тестов можно использовать команду

```shell
make test
```

Или для запуска внутри контейнера
```shell
vendor/bin/phpunit
```

minikube commands
```shell
minikube addons enable ingress
eval $(minikube docker-env)

docker build \
  -t sdbp-php:latest \
  -f ./docker/php/Dockerfile_dev_k8s . \
  --build-arg BASE_IMAGE_TAG=latest \
  --build-arg DOCKER_REPOSITORY_ADDR=swr.ru-moscow-1.hc.sbercloud.ru \
  --target php_base

docker build \
  -t sdbp-nginx:latest -f ./docker/nginx/Dockerfile .

helm install sdbp-php ./charts/php -f ./charts/php/values.yaml -f ./charts/php/values.minikube.yaml -f ./charts/php/secrets.yaml.local
helm install sdbp-nginx ./charts/nginx
minikube tunnel

#helm uninstall sdbp-php && helm install sdbp-php ./charts/php -f ./charts/php/values.yaml -f ./charts/php/values.minikube.yaml -f ./charts/php/secrets.yaml.local && kubectl delete pod -l app.kubernetes.io/instance=sdbp-nginx
#curl -H "Host: admin.sdbp.local" http://************/
#pv ./var/dump/backup.sql | kubectl cp -- mysql-sdbp-mysql-564df4db4d-82j7j:/tmp/dump.sql
#pv ./var/dump/backup.sql | kubectl exec -i mysql-sdbp-mysql-645b45cb54-ktlm2  -- mysql -ppassword sdbp.local
```


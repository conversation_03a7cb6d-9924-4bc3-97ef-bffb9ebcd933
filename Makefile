# Sometimes non-interactive mode should be enabled (e.g. pre-push hooks)
include .env
export $(shell sed 's/=.*//' .env)

ifeq (true, $(non-i))
  	PHP=docker compose exec -T php
else
	PHP=docker compose exec php
endif

app: prerequisites
	docker compose exec php bash

##############################################################
# Docker compose                                             #
##############################################################
build:
	cp -r env/dev/* ./
	cp env/dev/.env ./
	mkdir cache
	mkdir log
	docker network inspect net > /dev/null 2>&1 || docker network create net
	grep -qxF "127.0.0.1       admin.sdbp.local api.sdbp.local api-mobile.sdbp.local api-fin.sdbp.local api-geo.sdbp.local" /etc/hosts || echo "127.0.0.1       admin.sdbp.local api.sdbp.local api-mobile.sdbp.local api-fin.sdbp.local api-geo.sdbp.local" | sudo tee -a /etc/hosts

##############################################################
# Run with proxy, elk and keycloak   			 			 #
##############################################################
run:
	COMPOSE_PROFILES=elk,keycloak docker compose up -d

run_with_base:
	$(MAKE) build_base_image
	COMPOSE_PROFILES=elk,keycloak docker compose up -d

##############################################################
# Run with proxy but without elk and keycloak   			 #
##############################################################
run_light:
	docker compose up -d

stop:
	docker compose stop

down:
	docker compose down

deep-down:
	docker compose down -v --rmi=all --remove-orphans

migrate:
	docker compose exec php bash -c "/pdk/sdbp.local/vendor/bin/phinx migrate -e development -v"

rollback:
	docker compose exec php bash -c "/pdk/sdbp.local/vendor/bin/phinx rollback -e development -v"

# Example: make name=AddSomeColumnToSomeTable migrate_create
migrate_create:
	docker compose exec php bash -c "/pdk/sdbp.local/vendor/bin/phinx create $(name)"

dump:
	mkdir -p var/dump
	docker compose exec mysql /usr/bin/mysqldump -uroot -ppassword sdbp.local > var/dump/backup.sql
	mv var/dump/backup.sql var/dump/dump.`date +"%d-%m-%Y"`.sql

restore:
	pv var/dump/backup.sql | docker compose exec -T mysql /usr/bin/mysql -uroot -ppassword sdbp.local

download_dump:
	mysqldump --single-transaction -h <IP> -u gds-user -p<pass> sdbp54.prod  > ./var/dump/backup.sql -v

#https://cloud.ru/docs/obs/ug/topics/guides__tools-install__obsutil
#tar c ./var/dump/backup.sql | zstd -o ./var/dump/"$(date '+%Y%m%d-%H%M%S')-sdbp-54-dev.tar.zst"
run_dump_from_s3:
	echo "Fetching latest backup from S3..."
	@BUCKET="obs://tkp2dev/tkp2dev/dumps/"; \
	LATEST_BACKUP=$$(obsutil ls "$$BUCKET" -s | awk 'NR>8O' | head -n 4 | grep '54-dev' | sort -r | head -n 1) \
	DUMP_URL="$$LATEST_BACKUP"; \
	echo "Downloading and restoring: $$LATEST_BACKUP"; \
	obsutil cp "$$LATEST_BACKUP" ./dmp.tar.zst; \
	unzstd ./dmp.tar.zst; \
  	tar xOf ./dmp.tar > ./var/dump/backup.sql; \
    echo "Clean up.."; \
	rm ./dmp.tar ./dmp.tar.zst
#	$(MAKE) restore

place_dump_into_s3:
	obsutil cp ./var/d

composer_require:
	docker compose exec php sh -c "composer require $(name)"

composer_install:
	docker compose exec php sh -c "composer install"

composer_update:
	docker compose exec php sh -c "composer update"

test:
	docker compose exec php sh -c "vendor/bin/phpunit"

build_base_image:
	docker build -t swr.ru-moscow-1.hc.sbercloud.ru/tkp2/sdbp-php-base:latest -f ./docker/php/Dockerfile_base_alpine .
	#docker push swr.ru-moscow-1.hc.sbercloud.ru/tkp2/sdbp-php:local
##############################################################
# Prerequisites Setup                                        #
##############################################################

# We need both vendor/autoload.php and composer.lock being up to date
.PHONY: prerequisites
prerequisites: vendor/autoload.php composer.lock

ARG BASE_IMAGE_TAG=latest
ARG DOCKER_REPOSITORY_ADDR=swr.ru-moscow-1.hc.sbercloud.ru

FROM $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php-base:${BASE_IMAGE_TAG} AS php_base

# xdebug section
RUN apk add g++ make autoconf --virtual .build-deps && \
    pecl install xdebug-3.1.5  && \
    docker-php-ext-enable xdebug && \
    apk del .build-deps

# Install Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV PATH="${PATH}:/root/.composer/vendor/bin"

COPY ./ /pdk/sdbp.local/
#COPY ./docker/php/php.ini /usr/local/etc/php/php.ini

WORKDIR /pdk/sdbp.local

RUN composer install --prefer-dist --no-progress --no-interaction

# Create directories and set permissions
RUN mkdir -p cache log && \
    chown -R 1000:1000 ./ && \
    chmod -R 755 cache log

CMD ["php-fpm"]

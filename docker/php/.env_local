# SDBP Local Development Environment Configuration

# ========================================================================================================
# ENVIRONMENT SETTINGS
# ========================================================================================================
SDBP_ENVIRONMENT=development

# ========================================================================================================
# DATABASE CONFIGURATION
# ========================================================================================================
SDBP_DB_SERVER=mysql
SDBP_DB_USER=sdbp
SDBP_DB_PSW=password
SDBP_DB_NAME=sdbp.local
SDBP_DB_PORT=3306
SDBP_DB_DEBUG=true
SDBP_DB_SOCKET=

# ========================================================================================================
# TIME AND DATE SETTINGS
# ========================================================================================================
SDBP_DEFAULT_TZ=Europe/Moscow
SDBP_DATE_FORMAT=Y-m-d
SDBP_DATE_PICKER_FORMAT=yyyy-mm-dd

# ========================================================================================================
# SECURITY SETTINGS
# ========================================================================================================
SDBP_LOGIN_LEN_MIN=3
SDBP_LOGIN_LEN_MAX=255
SDBP_LOGIN_TRIES_MAX=3
SDBP_LOGIN_TRIES_LOCK=120
SDBP_PSW_LEN_MIN=6
SDBP_PSW_LEN_MAX=255
SDBP_REMEMBER_ME_TIME=2592000
SDBP_PSW_LEN_MIN_CASH_REGISTER=8
SDBP_PSW_LEN_MAX_CASH_REGISTER=12

# ========================================================================================================
# API SETTINGS
# ========================================================================================================
SDBP_GDS_API_HOST=api.sdbp.local
SDBP_API_TIME_LIMIT=300
SDBP_API_CONNECT_TIMEOUT=30
SDBP_API_TIMEOUT=90
SDBP_API_MOBILE_DEFAULT_PHONE=+79999999999
SDBP_API_MOBILE_DEFAULT_EMAIL=<EMAIL>
SDBP_MAX_ROWS=1048576

# ========================================================================================================
# CACHE SETTINGS
# ========================================================================================================
SDBP_CACHE_CLASS=File
SDBP_CACHE_DURATION=+1 hours
SDBP_CACHE_PREFIX=sdbp_development
SDBP_CACHE_PATH=../cache/
SDBP_CACHE_MASK=0777

# ========================================================================================================
# REDIS SETTINGS
# ========================================================================================================
SDBP_REDIS_HOST=redis
SDBP_REDIS_PORT=6379
SDBP_REDIS_DATABASE=0
SDBP_REDIS_PASSWORD=



# ========================================================================================================
# ADMIN MODULE SETTINGS
# ========================================================================================================

SDBP_ADMIN_PAYMENT_OWNER_SYSTEM_TITLE=Development
SDBP_ADMIN_GDS_API_URL=http://api.sdbp.local
SDBP_ADMIN_KEYCLOAK_BASE_URL=http://keycloak:8080
SDBP_ADMIN_KEYCLOAK_ENABLED=false
SDBP_ADMIN_KEYCLOAK_CLIENT_ID=
SDBP_ADMIN_KEYCLOAK_REALM=
SDBP_ADMIN_KEYCLOAK_CLIENT_SECRET=
SDBP_ADMIN_TELEGRAM_FAIL_SEND=false
SDBP_ADMIN_TELEGRAM_TOKEN=
SDBP_ADMIN_TELEGRAM_FAIL_CHAT_ID=
SDBP_ADMIN_API_MOBILE=http://am.sdbp.local
SDBP_ADMIN_LKP_ENDPOINT=localhost:5000
SDBP_ADMIN_LKP_ENABLED=false
SDBP_ADMIN_LKP_LOG_LEVEL=2
SDBP_ADMIN_LKP_SSL=true
SDBP_ADMIN_LKP_UPDATE_STATUS=60000
SDBP_ADMIN_REGION_ID=0
SDBP_ADMIN_MAP_PROVIDER=osm
SDBP_ADMIN_LANGUAGE=ru

# ========================================================================================================
# API MODULE SETTINGS
# ========================================================================================================
SDBP_API_OUTER_API_LOG=true

# ========================================================================================================
# LOGGING SETTINGS
# ========================================================================================================
SDBP_LOG_SUCCESS=true
SDBP_LOG_ERROR=true
SDBP_LOG_METHODS=POST,GET,PUT,DELETE
SDBP_LOG_ANSWER=true
SDBP_LOG_SERVER=true
SDBP_LOG_GET=true
SDBP_LOG_POST=true
SDBP_LOG_FILES=true
SDBP_LOG_SQL=true

# ========================================================================================================
# DIRECTORY SETTINGS
# ========================================================================================================
SDBP_DIR_CASH_REGISTER_CFG=../config/cash_register/

# ========================================================================================================
# CITY SETTINGS
# ========================================================================================================
SDBP_EXCLUDE_CITIES_ON_NAME_FROM_LIST=[]
SDBP_SHOW_ALL_CITIES_ON_YMAP=false

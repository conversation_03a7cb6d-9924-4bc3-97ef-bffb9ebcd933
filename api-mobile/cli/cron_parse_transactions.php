<?php

/**
 * @var $app core\app
 */

/*
 * парсилка транзакий терминалов
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$transactionLimit = (int)($app->args->limit ?? 0);
if ($transactionLimit <= 0) {
    $transactionLimit = 300;
}

$app->db->debug = false;

$app->log('Start parsing');

$exclude_partners = helper\console::arg('exclude_partners');
$exclude_partners_list = [];
if (!empty($exclude_partners)) {
    $exclude_partners_list = explode(",", $exclude_partners);
}
$app->terminal_transaction_model->parse_transactions($exclude_partners_list, $transactionLimit);
$app->log('Success parsing');
$app->log('*****************************');

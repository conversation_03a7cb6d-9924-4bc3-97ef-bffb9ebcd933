{{- if .Values.virtualService.enabled -}}
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: {{ include "nginx.fullname" . }}-gateway
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "nginx.labels" . | nindent 4 }}
spec:
  gateways:
{{- if .Values.gateway.enabled }}
  - {{ .Values.gateway.namespace }}/{{ include "nginx.fullname" . }}-gateway
{{- else }}
  - {{ .Values.virtualService.gateway }}
{{- end }}
  hosts:
{{- range $serverName, $server := .Values.servers }}
{{- if $server.serverName }}
{{- $hosts := splitList " " $server.serverName }}
{{- range $hosts }}
  - {{ . }}
{{- end }}
{{- end }}
{{- end }}
  http:
{{- range $serverName, $server := .Values.servers }}
{{- if $server.serverName }}
{{- $hosts := splitList " " $server.serverName }}
{{- range $hosts }}
  - match:
    - authority:
        exact: {{ . }}
      uri:
        prefix: /
    route:
    - destination:
        host: {{ $.Values.virtualService.destination.host }}
        port:
          number: {{ $.Values.virtualService.destination.port }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

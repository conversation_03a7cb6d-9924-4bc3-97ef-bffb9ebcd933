{{- if .Values.gateway.enabled -}}
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: {{ include "nginx.fullname" . }}-gateway
  namespace: {{ .Values.gateway.namespace }}
  labels:
    {{- include "nginx.labels" . | nindent 4 }}
spec:
  servers:
  - hosts:
{{- range $serverName, $server := .Values.servers }}
{{- if $server.serverName }}
{{- $hosts := splitList " " $server.serverName }}
{{- range $hosts }}
    - {{ . }}
{{- end }}
{{- end }}
{{- end }}
    port:
      name: {{ .Values.gateway.port.name }}
      number: {{ .Values.gateway.port.number }}
      protocol: {{ .Values.gateway.port.protocol }}
{{- if .Values.gateway.tls }}
    tls: {{ toYaml .Values.gateway.tls | nindent 6 }}
{{- else }}
    tls: {}
{{- end }}
{{- end }}

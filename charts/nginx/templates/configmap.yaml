apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "nginx.fullname" . }}-config
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "nginx.labels" . | nindent 4 }}
data:
  sdbp.conf: |
    # PHP-FPM upstream configuration
    upstream php-fpm {
      server {{ .Values.phpFpm.host }}:{{ .Values.phpFpm.port }};
    }

    server {
      listen       80;
      server_name  {{ .Values.servers.admin.serverName }};
      root         /pdk/sdbp.local/admin;

      index index.php;
      error_log   /proc/self/fd/2;
      access_log  /proc/self/fd/1;

      rewrite_log on;

      location / {
        try_files $uri $uri/ /index.php?$args;

        # Admin rewrites
        rewrite ^//?$ /controller/index.php last;
        rewrite ^/reference/list/?$ /controller/reference/list.php last;
        rewrite ^/reference/(new|edit)/?(\d+)?/?$ /controller/reference/add-edit.php?action=$1&reference=$2 last;
        rewrite ^/reference/(\d+)/?$ /controller/reference/item-list.php?reference=$1 last;
        rewrite ^/reference/(\d+)/(new|edit)/?([A-Za-z0-9_]+)?/?$ /controller/reference/item-add-edit.php?action=$2&reference=$1&item=$3 last;
        rewrite ^/user-admin/login/?$ /controller/user_admin/login.php last;
        rewrite ^/user-admin/logout/?$ /controller/user_admin/logout.php last;
        rewrite ^/user-admin/list/?$ /controller/user_admin/list.php last;
        rewrite ^/user-admin/add/?$ /controller/user_admin/add_edit.php last;
        rewrite ^/user-admin/edit/(\d+)/?$ /controller/user_admin/add_edit.php?user_id=$1 last;
        rewrite ^/user-admin/remove/(\d+)/?$ /controller/user_admin/remove.php?user_id=$1 last;
        rewrite ^/user-admin/check/?$ /controller/user_admin/check.php last;
        rewrite ^/card/list/?$ /controller/card/list.php last;
        rewrite ^/partner/list/?$ /controller/partner/list.php last;
        rewrite ^/partner/add/?$ /controller/partner/add_edit.php last;
        rewrite ^/partner/edit/(\d+)/?$ /controller/partner/add_edit.php?partner_id=$1 last;
        rewrite ^/partner/remove/(\d+)/?$ /controller/partner/remove.php?partner_id=$1 last;
        rewrite ^/agent/list/?$ /controller/agent/list.php last;
        rewrite ^/agent/add/?$ /controller/agent/add_edit.php last;
        rewrite ^/agent/edit/(\d+)/?$ /controller/agent/add_edit.php?agent_id=$1 last;
        rewrite ^/agent/check/key/?$ /controller/agent/check_key.php last;
        rewrite ^/organization/list/(\d+)/?$ /controller/organization/list.php?partner_id=$1 last;
        rewrite ^/organization/add/(\d+)/?$ /controller/organization/add_edit.php?partner_id=$1 last;
        rewrite ^/organization/edit/(\d+)/?$ /controller/organization/add_edit.php?organization_id=$1 last;
        rewrite ^/organization/remove/(\d+)/?$ /controller/organization/remove.php?organization_id=$1 last;
        rewrite ^/user/list/(\d+)/?$ /controller/user/list.php?partner_id=$1 last;
        rewrite ^/user/add/(\d+)/?$ /controller/user/add_edit.php?partner_id=$1 last;
        rewrite ^/user/edit/(\d+)/?$ /controller/user/add_edit.php?user_id=$1 last;
        rewrite ^/user/remove/(\d+)/?$ /controller/user/remove.php?user_id=$1 last;
        rewrite ^/user/check/?$ /controller/user/check.php last;
        rewrite ^/stat/city/?$ /controller/stat/city.php last;
        rewrite ^/stat/reference/?$ /controller/stat/reference.php?sum=total last;
        rewrite ^/stat/reference/profit/?$ /controller/stat/reference.php?sum=profit last;
        rewrite ^/stat/reference/ticket/?$ /controller/stat/reference.php?sum=ticket last;
        rewrite ^/stat/search/?$ /controller/stat/search.php last;
        rewrite ^/stat/rt_list/?$ /controller/stat/rt_list.php last;
        rewrite ^/stat/sale_depth/?$ /controller/stat/sale_depth.php last;
        rewrite ^/stat/start_date/?$ /controller/stat/start_date.php last;
        rewrite ^/stat/kzh_import/?$ /controller/stat/kzh_import.php last;
        rewrite ^/stat/kzh_import/(\d+)/download?$ /controller/stat/kzh_import_download.php?id=$1 last;
        rewrite ^/route/map/?$ /controller/route_map.php last;
        rewrite ^/payment/list/?$ /controller/payment/list.php last;
        rewrite ^/payment/edit/(\d+)/?$ /controller/payment/edit.php?payment_id=$1 last;
        rewrite ^/payment/plan/?$ /controller/payment/plan.php last;
        rewrite ^/payment/processing/(\d+)/?$ /controller/payment/processing.php?payment_id=$1 last;
        rewrite ^/payment/print/(\d+)/?$ /controller/payment/print.php?payment_id=$1 last;
        rewrite ^/operation/site/?$ /controller/operation/log.php?mode=site last;
        rewrite ^/operation/agents/?$ /controller/operation/log.php?mode=agents last;
        rewrite ^/operation/crm-armk/?$ /controller/operation/log.php?mode=crm-armk last;
        rewrite ^/station/merge/(\d+)/?$ /controller/station/merge.php?station_id=$1 last;
        rewrite ^/city-outer/list/?$ /controller/city_outer/list.php last;
        rewrite ^/city-outer/edit/(\d+)/?$ /controller/city_outer/edit.php?id=$1 last;
        rewrite ^/city-outer/hide/(\d+)/?$ /controller/city_outer/hide.php?id=$1 last;
        rewrite ^/city/list/?$ /controller/city/list.php last;
        rewrite ^/city/import/?$ /controller/city/import.php last;
        rewrite ^/city/import/upload?$ /controller/city/import/upload.php last;
        rewrite ^/city/import/getResults?$ /controller/city/import/getResults.php last;
        rewrite ^/city/import/setResultProcessed?$ /controller/city/import/setResultProcessed.php last;
        rewrite ^/city/import/setResultProblem?$ /controller/city/import/setResultProblem.php last;
        rewrite ^/city/import/setProblemProcessed?$ /controller/city/import/setProblemProcessed.php last;
        rewrite ^/city/add/?$ /controller/city/add_edit.php last;
        rewrite ^/city/edit/(\d+)/?$ /controller/city/add_edit.php?city_id=$1 last;
        rewrite ^/city/merge/(\d+)/?$ /controller/city/merge.php?city_id=$1 last;
        rewrite ^/city/check/unique/?$ /controller/city/check_unique.php last;
        rewrite ^/city/search/?$ /controller/city/search.php last;
        rewrite ^/station/list/?$ /controller/station/list.php last;
        rewrite ^/station/add/(\d+)/?$ /controller/station/add_edit.php?city_id=$1 last;
        rewrite ^/station/edit/(\d+)/?$ /controller/station/add_edit.php?station_id=$1 last;
        rewrite ^/station/ya_import/?$ /controller/station/ya_import.php last;
        rewrite ^/station/ya_import/get_route_list/?$ /controller/station/get_route_list_from_yandex.php last;
        rewrite ^/station/ya_import/get_route_stations/?$ /controller/station/get_route_station_list_from_yandex.php last;
        rewrite ^/station/ya_import/save_stations/?$ /controller/station/save_station_list_from_yandex.php last;
        rewrite ^/station-outer/list/?$ /controller/station_outer/list.php last;
        rewrite ^/station-outer/edit/(\d+)/?$ /controller/station_outer/edit.php?id=$1 last;
        rewrite ^/station/photo/(\d+)/?$ /controller/station/photo.php?station_id=$1 last;
        rewrite ^/station/get_station_photo/(\d+)/?$ /controller/station/get_station_photo.php?station_id=$1 last;
        rewrite ^/station/removephoto/(\d+)/?$ /controller/station/remove_photo.php?station_id=$1 last;
        rewrite ^/moder/city-outer/list/?$ /controller/moder_city_outer/list.php last;
        rewrite ^/moder/city-outer/edit/(\d+)/?$ /controller/moder_city_outer/edit.php?id=$1 last;
        rewrite ^/seo/direction/list/(\d+)?$ /controller/seo/direction_list.php?city_id=$1 last;
        rewrite ^/seo/direction/add/(\d+)?$ /controller/seo/direction_add_edit.php?city_id=$1 last;
        rewrite ^/seo/direction/edit/(\d+)?$ /controller/seo/direction_add_edit.php?direction_id=$1 last;
        rewrite ^/seo/direction/remove/(\d+)?$ /controller/seo/direction_remove.php?direction_id=$1 last;
        rewrite ^/seo/direction/item/list/(\d+)?$ /controller/seo/direction_item_list.php?direction_id=$1 last;
        rewrite ^/seo/direction/item/add/(\d+)?$ /controller/seo/direction_item_add.php?direction_id=$1 last;
        rewrite ^/seo/direction/item/remove/(\d+)/(\d+)/(\d+)?$ /controller/seo/direction_item_remove.php?direction_id=$1&item_id=$2&type=$3 last;
        rewrite ^/seo/direction/item/search/?$ /controller/seo/direction_item_search.php last;
        rewrite ^/telegram/message/list/?$ /controller/telegram_message_list.php last;
        rewrite ^/billing-rules/list/?$ /controller/billing_rules/list.php last;
        rewrite ^/billing-rules/add/?$ /controller/billing_rules/add_edit.php last;
        rewrite ^/billing-rules/edit/(\d+)/?$ /controller/billing_rules/add_edit.php?id=$1 last;
        rewrite ^/billing-rules/remove/(\d+)/?$ /controller/billing_rules/remove.php?id=$1 last;
        rewrite ^/vendor-conditions/list/?$ /controller/vendor_conditions/list.php?id=$1 last;
        rewrite ^/vendor-conditions/add/?$ /controller/vendor_conditions/add_edit.php last;
        rewrite ^/vendor-conditions/edit/(\d+)/?$ /controller/vendor_conditions/add_edit.php?vendor_condition_id=$1 last;
        rewrite ^/vendor-conditions/remove/(\d+)/?$ /controller/vendor_conditions/remove.php?vendor_condition_id=$1 last;
        rewrite ^/ticket/search/?$ /controller/ticket/search.php last;
        rewrite ^/ticket/detail/(\d+)/?$ /controller/ticket/detail.php?ticket_id=$1 last;
        rewrite ^/ticket/refund/manual/(\d+)/?$ /controller/ticket/refund_manual.php?ticket_id=$1 last;
        rewrite ^/ticket/refund_handle/(\d+)/(\d+)/?$ /controller/ticket/refund.php?ticket_id=$1&operation_id=$2&return_percent=$3 last;
        rewrite ^/city/search/nearest/?$ /controller/city_search_nearest.php last;
        rewrite ^/city/search/exist/?$ /controller/city_search_exist.php last;
        rewrite ^/region/search/?$ /controller/region_search.php last;
        rewrite ^/city/search/inregion/?$ /controller/city_region_search.php last;
        rewrite ^/vendor/check/key/?$ /controller/vendor/check_key.php last;
        rewrite ^/vendor/api/config/(\d+)/?$ /controller/vendor/api/config.php?vendor_id=$1 last;
        rewrite ^/vendor/api/config/(\d+)/remove/?$ /controller/vendor/api/config_remove.php?vendor_id=$1 last;
        rewrite ^/helpdesk/list/?$ /controller/helpdesk/list.php last;
        rewrite ^/helpdesk/request/(\d+)/edit/?$ /controller/helpdesk/request_addedit.php?helpdesk_request_id=$1 last;
        rewrite ^/helpdesk/request/add/?$ /controller/helpdesk/request_addedit.php?helpdesk_request_id=$1 last;
        rewrite ^/helpdesk/request/(\d+)/?$ /controller/helpdesk/request.php?helpdesk_request_id=$1 last;
        rewrite ^/helpdesk/request/(\d+)/comment/add/?$ /controller/helpdesk/request_comment_add.php?helpdesk_request_id=$1 last;
        rewrite ^/admin/?$ /controller/admin/index.php last;
        rewrite ^/supp/contragent/list/?$ /controller/supp/contragent_list.php last;
        rewrite ^/supp/uir/list/?$ /controller/supp/uir_list.php last;
        rewrite ^/supp/uir/list/(\d+)/?$ /controller/supp/uir_list.php?cid=$1 last;
        rewrite ^/supp/uipp/list/(\d+)/?$ /controller/supp/uipp_list.php?uirid=$1 last;
        rewrite ^/supp/uipp/list/?$ /controller/supp/uipp_list.php last;
        rewrite ^/terminal/group/list/?$ /controller/terminal/terminal_group_list.php last;
        rewrite ^/terminal/group/edit/(\d+)/?$ /controller/terminal/terminal_group_edit.php?terminal_group_id=$1 last;
        rewrite ^/terminal/group/add/?$ /controller/terminal/terminal_group_edit.php?terminal_group_id=0 last;
        rewrite ^/terminal/group/get_group_tablefields/?$ /controller/terminal/get_group_tablefields.php?tablename=$1 last;
        rewrite ^/terminal/group/get_reference_values/?$ /controller/terminal/get_reference_values.php?reference=$1 last;
        rewrite ^/terminal/list/?$ /controller/terminal/terminal_list.php last;
        rewrite ^/terminal/view/(\d+)/?$ /controller/terminal/terminal_view.php?terminal_id=$1 last;
      }

      location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        fastcgi_pass   php-fpm;
        fastcgi_index  index.php;
        include        fastcgi_params;
        fastcgi_ignore_client_abort on;
        fastcgi_read_timeout {{ .Values.phpFpm.readTimeout }};
        fastcgi_buffers {{ .Values.phpFpm.buffers }};
        fastcgi_buffer_size {{ .Values.phpFpm.bufferSize }};
        fastcgi_connect_timeout {{ .Values.phpFpm.connectTimeout }};
        fastcgi_send_timeout {{ .Values.phpFpm.sendTimeout }};
        client_max_body_size 100M;
      }

      location ~ \.(js|json|css|png|jpg|jpeg|gif|ico|otf|eot|ttf|woff)$ {
        expires 1d;
        log_not_found off;
      }

      location ~ /user\.passwd { deny  all; }
      location ~ /\.ht { deny  all; }
      location ~ /\.hg.* { deny  all; }
      location ~ /vendor/ { deny  all; }
      location ~ /cli/ { deny  all; }
    }

    server {
      listen       80;
      server_name  {{ .Values.servers.api.serverName }};
      root         /pdk/sdbp.local/api;
      keepalive_timeout 300;

      index index.php;
      error_log   /proc/self/fd/2;
      access_log  /proc/self/fd/1;

      location / {
        try_files $uri $uri/ /index.php?$args;

        # API rewrites
        rewrite ^//?$ /controller/index.php last;
        rewrite ^/currency/list/?$ /controller/currency_list.php last;
        rewrite ^/citizenship/list/?$ /controller/citizenship_list.php last;
        rewrite ^/loyalty/list/?$ /controller/loyalty_list.php last;
        rewrite ^/city/list/from/?$ /controller/city/list_from.php last;
        rewrite ^/city/list/to/?$ /controller/city/list_to.php last;
        rewrite ^/city/list/from/realtime/?$ /controller/city/realtime/list_from.php last;
        rewrite ^/city/list/to/realtime/?$ /controller/city/realtime/list_to.php last;
        rewrite ^/geo/list/from/?$ /controller/geo/list_from.php last;
        rewrite ^/geo/list/to/?$ /controller/geo/list_to.php last;
        rewrite ^/ride/list/?$ /controller/ride/list.php last;
        rewrite ^/ride/list/all?$ /controller/ride/list_all.php last;
        rewrite ^/ride/list/station/?$ /controller/ride/list_by_station.php last;
        rewrite ^/ride/?$ /controller/ride/ride.php last;
        rewrite ^/ride/position/free/?$ /controller/ride/position_free.php last;
        rewrite ^/ride/waypoints/?$ /controller/ride/waypoint_list.php last;
        rewrite ^/operation/booking/tmp/?$ /controller/operation/booking_tmp.php last;
        rewrite ^/operation/buy/?$ /controller/operation/buy.php last;
        rewrite ^/operation/cancel/?$ /controller/operation/cancel.php last;
        rewrite ^/operation/?$ /controller/operation/operation.php last;
        rewrite ^/operation/pdf/?$ /controller/operation/pdf.php last;
        rewrite ^/operation/renew/?$ /controller/operation/check.php last;
        rewrite ^/operation/check/?$ /controller/operation/check.php last;
        rewrite ^/ticket/update/?$ /controller/ticket/update.php last;
        rewrite ^/ticket/?$ /controller/ticket/ticket.php last;
        rewrite ^/ticket/by/uipp/?$ /controller/ticket/ticket_by_uipp.php last;
        rewrite ^/ticket/count/by/reference/?$ /controller/ticket/ticket_count_by_reference.php last;
        rewrite ^/ticket/sold/list/?$ /controller/ticket/ticket_sold_list.php last;
        rewrite ^/ticket/pdf/?$ /controller/ticket/pdf.php last;
        rewrite ^/ticket/refund/?$ /controller/ticket/refund.php last;
        rewrite ^/ticket/refund/calc/?$ /controller/ticket/refund_calc.php last;
        rewrite ^/ticket/data/is/required/?$ /controller/ticket/ticket_data_is_required.php last;
        rewrite ^/card_identity/list/?$ /controller/card_identity_list.php last;
        rewrite ^/station/list/from/?$ /controller/station/list_from.php last;
        rewrite ^/station/list/to/?$ /controller/station/list_to.php last;
        rewrite ^/1c/ticket_list/?$ /controller/1c/ticket_list.php last;
        rewrite ^/telegram/hook/([^\/]+)/?$ /controller/telegram/hook.php?token=$1 last;
        rewrite ^/sequence/([^\/]+)/?$ /controller/sequence.php?sequence_id=$1 last;
        rewrite ^/ticket/reference/list/?$ /controller/ticket/ticket_list_by_references.php last;
        rewrite ^/ride/search/request/create/?$ /controller/ride/search/request_create.php last;
        rewrite ^/ride/search/request/cancel/?$ /controller/ride/search/cancel.php last;
        rewrite ^/ride/search/result/?$ /controller/ride/search/result.php last;
        rewrite ^/ride/bus/scheme/place/?$ /controller/ride/bus_scheme_place.php last;
        rewrite ^/make/sitemap/?$ /controller/make_sitemap.php last;
        rewrite ^/terminal/test/?$ /controller/terminal/test.php last;
        rewrite ^/terminal/group/list/?$ /controller/terminal/get_terminal_group_list.php last;
        rewrite ^/terminal/transaction/list/?$ /controller/terminal/get_terminal_transactions.php last;
        rewrite ^/terminal/transaction/revise_by_period/?$ /controller/terminal/revise_terminal_transactions_by_period.php last;
        rewrite ^/terminal/transaction/count_by_period/?$ /controller/terminal/get_terminal_transaction_count_by_period.php last;
        rewrite ^/terminal/transaction/ids/?$ /controller/terminal/transactions_ids.php last;
        rewrite ^/reference/list/?$ /controller/reference/get_reference_list.php last;
        rewrite ^/reference/pan_hash_algorithms/?$ /controller/reference/pan_hash_algorithms_list.php last;
        rewrite ^/social_categories/list/?$ /controller/social_categories/get_social_categories_list.php last;
        rewrite ^/install/create_new_nsi/?$ /controller/install/create_agent_and_partner.php last;
        rewrite ^/emv/writeoffs/list/?$ /controller/emv/get_writeoffs_template_list.php last;
        rewrite ^/emv/writeoffs/nsi_list/?$ /controller/emv/get_writeoffs_template_list_nsi.php last;
        rewrite ^/emv/abonement/sell/?$ /controller/emv/post_abonement_sell.php last;
        rewrite ^/emv/abonement/list/?$ /controller/emv/get_abonement_list.php last;
        rewrite ^/emv/abonement/info/?$ /controller/emv/get_abonement_info.php last;
        rewrite ^/emv/abonement/history/?$ /controller/emv/get_abonement_history.php last;
        rewrite ^/emv/card/history/?$ /controller/emv/get_card_history.php last;
        rewrite ^/emv/trip/count/?$ /controller/emv/get_trip_count.php last;
        rewrite ^/emv/report/d40/?$ /controller/emv/report/get_emv_report_d40.php last;
        rewrite ^/emv/report/d41/?$ /controller/emv/report/get_emv_report_d41.php last;
        rewrite ^/ticket/check?$ /controller/ticket/check.php last;
        rewrite ^/ticket/check/onetime?$ /controller/ticket/check_onetime.php last;
        rewrite ^/emv/wallet/info/?$ /controller/emv/wallet/get_wallet_info.php last;
        rewrite ^/emv/wallet/upload/info/?$ /controller/emv/wallet/get_wallet_upload_info.php last;
        rewrite ^/emv/wallet/upload/callback/?$ /controller/emv/wallet/callback.php last;
        rewrite ^/emv/wallet/create/?$ /controller/emv/wallet/wallet_create.php last;
        rewrite ^/emv/wallet/transactions/?$ /controller/emv/wallet/get_transaction_list.php last;
        rewrite ^/emv/wallet/upload/?$ /controller/emv/wallet/wallet_upload.php last;
        rewrite ^/emv/wallet/bind/?$ /controller/emv/wallet/wallet_bind.php last;
        rewrite ^/emv/transfer/?$ /controller/emv/post_transfer.php last;
        rewrite ^/emv/delete/?$ /controller/emv/post_delete.php last;
        rewrite ^/emv/modify/?$ /controller/emv/post_modify.php last;
        rewrite ^/emv/stoplist/add/?$ /controller/emv/stoplist/post_add.php last;
        rewrite ^/emv/stoplist/load/?$ /controller/emv/stoplist/post_load.php last;
        rewrite ^/emission/modify/?$ /controller/emission/post_modify_card.php last;
        rewrite ^/emission/issue/?$ /controller/emission/issue_card.php last;
        rewrite ^/terminal/daystat/all/?$ /controller/terminal/daystat/get_all_terminal_day_stat.php last;
        rewrite ^/terminal/daystat/detail/?$ /controller/terminal/daystat/get_terminal_day_stat_detail.php last;
        rewrite ^/terminal/laststat/?$ /controller/terminal/daystat/get_terminal_last_stat_data.php last;
        rewrite ^/onetimeticket/get_ticket_list_by_issuer/?$ /controller/onetimeticket/get_ticket_list_by_issuer.php last;
        rewrite ^/onetimeticket/get_report_a1/?$ /controller/onetimeticket/get_report_a1.php last;
        rewrite ^/terminal/laststat/detail?$ /controller/terminal/daystat/get_terminal_last_stat_detail.php last;
        rewrite ^/terminal/transaction/confirm/?$ /controller/terminal/confirm_terminal_transactions.php last;
        rewrite ^/terminal/stats/aps/?$ /controller/terminal/get_terminal_app_stats.php last;
        rewrite ^/cards/transport/([^\/]+)/available-operations/?$ /controller/sbertroika/transport_available_operations.php?pan=$1 last;
        rewrite /cards/ips/([^\/]+)/([^\/]+)/available-operations/?$ /controller/sbertroika/ips_available_operations.php?ips_name=$1&pan_Hash=$2 last;
        rewrite ^/invoices/([^\/]+)/status/?$ /controller/sbertroika/invoices_status.php?invoice_id=$1 last;
        rewrite ^/invoices/one-click/?$ /controller/sbertroika/invoices_one_click.php last;
        rewrite ^/emv/debt_transaction/list?$ /controller/emv/get_debt_transaction.php last;
        rewrite ^/emv/debt_transaction/status?$ /controller/emv/check_debt_transacion_status.php last;
        rewrite ^/nsi/replication?$ /controller/nsi/index.php last;
        rewrite ^/golden_crown/get_trips_card_citizen_list?$ /controller/golden_crown/get_trips_card_citizen_list_action.php last;
        rewrite ^/emv/abonement/transfer-leftovers/?$ /controller/emv/transfer_of_leftovers.php last;
      }

      location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        fastcgi_pass   php-fpm;
        fastcgi_index  index.php;
        include        fastcgi_params;
        fastcgi_ignore_client_abort on;
        fastcgi_read_timeout {{ .Values.phpFpm.readTimeout }};
        fastcgi_buffers {{ .Values.phpFpm.buffers }};
        fastcgi_buffer_size {{ .Values.phpFpm.bufferSize }};
        fastcgi_connect_timeout {{ .Values.phpFpm.connectTimeout }};
        fastcgi_send_timeout {{ .Values.phpFpm.sendTimeout }};
        client_max_body_size 100M;
      }

      location ~ \.(js|json|css|png|jpg|jpeg|gif|ico|otf|eot|ttf|woff)$ {
        expires 1d;
        log_not_found off;
      }

      location ~ /user\.passwd { deny  all; }
      location ~ /\.ht { deny  all; }
      location ~ /\.hg.* { deny  all; }
      location ~ /vendor/ { deny  all; }
      location ~ /cli/ { deny  all; }
    }

    server {
      listen       80;
      server_name  {{ .Values.servers.apiMobile.serverName }};
      root         /pdk/sdbp.local/api-mobile;
      keepalive_timeout 300;

      index index.php;
      error_log   /proc/self/fd/2;
      access_log  /proc/self/fd/1;

      location / {
        try_files $uri $uri/ /index.php?$args;

        # API Mobile rewrites
        rewrite ^//?$ /controller/index.php last;
        rewrite ^/Reference/City/From/?$ /controller/city/list_from.php last;
        rewrite ^/Reference/City/To/?$ /controller/city/list_to.php last;
        rewrite ^/Reference/Station/From/?$ /controller/station/list_from.php last;
        rewrite ^/Reference/Station/To/?$ /controller/station/list_to.php last;
        rewrite ^/Reference/Station/List/?$ /controller/station/list.php last;
        rewrite ^/Reference/Currency/?$ /controller/currency_list.php last;
        rewrite ^/Reference/Version/?$ /controller/version.php last;
        rewrite ^/Trip/List/?$ /controller/ride/list.php last;
        rewrite ^/Trip/ListByRoute/?$ /controller/ride/list_by_route.php last;
        rewrite ^/Ride/?$ /controller/ride/ride.php last;
        rewrite ^/Trip/?$ /controller/ride/ride.php last;
        rewrite ^/Trip/Place/AvailabilityCheck/?$ /controller/ride/position_free.php last;
        rewrite ^/Trip/Point/?$ /controller/ride/waypoint_list.php last;
        rewrite ^/Reference/Exemption/?$ /controller/loyalty_list.php last;
        rewrite ^/Reference/Citizenship/?$ /controller/citizenship_list.php last;
        rewrite ^/Reference/HumanNameList/?$ /controller/human_name_list.php last;
        rewrite ^/Reference/IdCard/?$ /controller/card_identity_list.php last;
        rewrite ^/Trip/PersonalDataCheck/?$ /controller/ticket/ticket_data_is_required.php last;
        rewrite ^/Trip/Place/Scheme/?$ /controller/ride/bus_scheme_place.php last;
        rewrite ^/Ticket/Booking/?$ /controller/operation/booking_tmp.php last;
        rewrite ^/Ticket/Transaction/Status/?$ /controller/operation/check.php last;
        rewrite ^/Ticket/Transaction/Cancel/?$ /controller/operation/cancel.php last;
        rewrite ^/Ticket/Operation/?$ /controller/operation/operation.php last;
        rewrite ^/Ticket/Edit/?$ /controller/ticket/update.php last;
        rewrite ^/Ticket/Buy/?$ /controller/operation/buy.php last;
        rewrite ^/Ticket/?$ /controller/ticket/ticket.php last;
        rewrite ^/Ticket/BlankAll/?$ /controller/operation/pdf.php last;
        rewrite ^/Ticket/Blank/?$ /controller/ticket/pdf.php last;
        rewrite ^/Ticket/Freight/?$ /controller/ticket/freight_data.php last;
        rewrite ^/Ticket/Refund/AvailabilityCheck/?$ /controller/ticket/refund_calc.php last;
        rewrite ^/Ticket/Refund/Confirm/?$ /controller/ticket/refund.php last;
        rewrite ^/Ticket/Refund/Services/?$ /controller/ticket/refund_services.php last;
        rewrite ^/Heartbeat/?$ /controller/heartbeat.php last;
        rewrite ^/Terminal/Config/?$ /controller/terminal/terminal_get_config.php last;
        rewrite ^/Terminal/User/Config/?$ /controller/terminal/terminal_get_user_config.php last;
        rewrite ^/Terminal/Reference/List/?$ /controller/terminal/terminal_get_reference_list.php last;
        rewrite ^/Terminal/User/RoleFunction/List/?$ /controller/terminal/terminal_get_user_role_function_list.php last;
        rewrite ^/Terminal/Log/Status/?$ /controller/terminal/terminal_log_status.php last;
        rewrite ^/Terminal/Log/Send/?$ /controller/terminal/terminal_log_send.php last;
        rewrite ^/Terminal/Directive/Complete/?$ /controller/terminal/terminal_directive_complete.php last;
        rewrite ^/Terminal/Abonnement/List/?$ /controller/terminal/terminal_get_abonnement_template_list.php last;
        rewrite ^/Terminal/Abonnement/Sale/?$ /controller/terminal/terminal_post_abonnement_sale.php last;
        rewrite ^/Dispatch/Boarding/?$ /controller/dispatch/boarding.php last;
        rewrite ^/Dispatch/Ride/Departure/Change/?$ /controller/dispatch/ride_departure_change.php last;
        rewrite ^/Services/List/?$ /controller/services/services_list.php last;
        rewrite ^/Services/Buy/?$ /controller/services/services_buy.php last;
        rewrite ^/Services/Refund/AvailabilityCheck/?$ /controller/services/services_refund_check.php last;
        rewrite ^/Services/Refund/?$ /controller/Services/Services_refund.php last;
        rewrite ^/ProductImg/?$ /controller/get_image.php last;
        rewrite ^/Dispatch/Ticket/Status/?$ /controller/dispatch/ticket_status.php last;
        rewrite ^/Dispatch/Ticket/Status/Registration/Cancel/?$ /controller/dispatch/ticket_status_registration_cancel.php last;
        rewrite ^/Dispatch/Ticket/Status/Registration/OnLine/?$ /controller/dispatch/ticket_status_registration_online.php last;
        rewrite ^/Dispatch/Ticket/Status/Cancellation/?$ /controller/dispatch/ticket_status_cancellation.php last;
        rewrite ^/Dispatch/Ticket/Status/Registration/?$ /controller/dispatch/ticket_status_registration.php last;
        rewrite ^/Dispatch/Ticket/Status/Boarding/?$ /controller/dispatch/ticket_status_boarding.php last;
        rewrite ^/Dispatch/Ticket/Status/Arrival/?$ /controller/dispatch/ticket_status_arrival.php last;
        rewrite ^/Dispatch/Ticket/Status/SecurityCheck/?$ /controller/dispatch/ticket_status_security_check.php last;
        rewrite ^/Dispatch/Ticket/Status/Departure/?$ /controller/dispatch/ticket_status_departure.php last;
        rewrite ^/Dispatch/Ticket/Status/Departure/Cancel/?$ /controller/dispatch/ticket_status_departure_cancel.php last;
        rewrite ^/Dispatch/Ticket/UIPP/?$ /controller/dispatch/ticket_uipp.php last;
        rewrite ^/Off-Line/Tariff/List/?$ /controller/off-line/get_tariff.php last;
        rewrite ^/Off-Line/User/List/?$ /controller/off-line/get_user_list.php last;
        rewrite ^/Off-Line/User/Task/?$ /controller/off-line/get_user_task.php last;
        rewrite ^/Off-Line/User/Task/Version/?$ /controller/off-line/get_user_task_version.php last;
        rewrite ^/Off-Line/Transaction/Upload/?$ /controller/off-line/post_transaction_upload.php last;
        rewrite ^/Trip/List2/?$ /controller/ride/list2.php last;
        rewrite ^/Terminal/Unknown/Collect/?$ /controller/terminal/terminal_unknown_collect.php last;
        rewrite ^/Terminal/Unknown/ContactRequest/?$ /controller/terminal/terminal_unknown_contact_request.php last;
        rewrite ^/Terminal/Unknown/ServerList/?$ /controller/terminal/terminal_unknown_server_list.php last;
        rewrite ^/Terminal/EMV/StopList/?$ /controller/terminal/terminal_get_emv_stop_list.php last;
        rewrite ^/Off-Line/Outgo/Set/?$ /controller/off-line/post_offline_outgo_prm.php last;
        rewrite ^/Terminal/Statistic/Send?$ /controller/terminal/terminal_statistic_send.php last;
        rewrite ^/OneTimeTicket/Issue/?$ /controller/onetimeticket/issue.php last;
        rewrite ^/OneTimeTicket/Redeem/?$ /controller/onetimeticket/redeem.php last;
        rewrite ^/OneTimeTicket/Cancel/?$ /controller/onetimeticket/cancel.php last;
        rewrite ^/OneTimeTicket/Status/?$ /controller/onetimeticket/status.php last;
        rewrite ^/SPB/OneTimeTicket/Issue/?$ /controller/onetimeticket/spb_issue.php last;
        rewrite ^/SPB/OneTimeTicket/Redeem/?$ /controller/onetimeticket/spb_redeem.php last;
        rewrite ^/SPB/OneTimeTicket/Status/?$ /controller/onetimeticket/spb_status.php last;
        rewrite ^/SPB/OneTimeTicket/Price/?$ /controller/onetimeticket/spb_price.php last;
        rewrite ^/Route/List/?$ /controller/route/list.php last;
        rewrite ^/Route/Tag/List/?$ /controller/route/tag_list.php last;
        rewrite ^/Route/?$ /controller/route/route.php last;
        rewrite ^/Gallery/?$ /controller/gallery/gallery.php last;
        rewrite ^/Gallery/Photo?$ /controller/gallery/photo.php last;
        rewrite ^/RBS/callback?$ /controller/dump_to_file.php last;
        rewrite ^/Promocode/Check?$ /controller/promocode/check.php last;
      }

      location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        fastcgi_pass   php-fpm;
        fastcgi_index  index.php;
        include        fastcgi_params;
        fastcgi_ignore_client_abort on;
        fastcgi_read_timeout {{ .Values.phpFpm.readTimeout }};
        fastcgi_buffers {{ .Values.phpFpm.buffers }};
        fastcgi_buffer_size {{ .Values.phpFpm.bufferSize }};
        fastcgi_connect_timeout {{ .Values.phpFpm.connectTimeout }};
        fastcgi_send_timeout {{ .Values.phpFpm.sendTimeout }};
        client_max_body_size 100M;
      }

      location ~ \.(js|json|css|png|jpg|jpeg|gif|ico|otf|eot|ttf|woff)$ {
        expires 1d;
        log_not_found off;
      }

      location ~ /user\.passwd { deny  all; }
      location ~ /\.ht { deny  all; }
      location ~ /\.hg.* { deny  all; }
      location ~ /vendor/ { deny  all; }
      location ~ /cli/ { deny  all; }
    }

    server {
      listen       80;
      server_name  {{ .Values.servers.apiFin.serverName }};
      root         /pdk/sdbp.local/finstat;

      index index.html;
      error_log   /proc/self/fd/2;
      access_log  /proc/self/fd/1;

      location / {
        try_files $uri $uri/ /index.php?$args;

        # Finstat API rewrites
        rewrite ^/payment/list/?$ /controller/payment/list.php last;
        rewrite ^/payment/detail/(\d+)/?$ /controller/payment/detail.php?payment_id=$1 last;
        rewrite ^/payment/processing/(\d+)/?$ /controller/payment/processing.php?payment_id=$1 last;
        rewrite ^/payment/print/(\d+)/?$ /controller/payment/print.php?payment_id=$1 last;
        rewrite ^/ticket/list/?$ /controller/ticket/list.php last;
        rewrite ^/ticket/detail/(\d+)/?$ /controller/ticket/detail.php?ticket_id=$1 last;
        rewrite ^/ticket/print/(\d+)/?$ /controller/ticket/print.php?ticket_id=$1 last;
        rewrite ^/ticket/report/(\d+)/?$ /controller/ticket/report.php?ticket_id=$1 last;
        rewrite ^/ticket/report/list/?$ /controller/ticket/report_list.php last;
      }

      location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        fastcgi_pass   php-fpm;
        fastcgi_index  index.php;
        include        fastcgi_params;
        fastcgi_ignore_client_abort on;
        fastcgi_read_timeout {{ .Values.phpFpm.readTimeout }};
        fastcgi_buffers {{ .Values.phpFpm.buffers }};
        fastcgi_buffer_size {{ .Values.phpFpm.bufferSize }};
        fastcgi_connect_timeout {{ .Values.phpFpm.connectTimeout }};
        fastcgi_send_timeout {{ .Values.phpFpm.sendTimeout }};
        client_max_body_size 100M;
      }

      location ~ \.(js|json|css|png|jpg|jpeg|gif|ico|otf|eot|ttf|woff)$ {
        expires 1d;
        log_not_found off;
      }

      location ~ /user\.passwd { deny  all; }
      location ~ /\.ht { deny  all; }
      location ~ /\.hg.* { deny  all; }
      location ~ /vendor/ { deny  all; }
      location ~ /cli/ { deny  all; }
    }

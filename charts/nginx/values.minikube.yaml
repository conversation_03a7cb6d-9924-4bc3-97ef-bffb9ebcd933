# Default values for nginx.
replicaCount: 1

image:
  repository: sdbp-nginx
  tag: latest
  pullPolicy: Newer

podAnnotations: {}
podLabels: {}

service:
  type: ClusterIP
  port: 80

# Ingress configuration for virtual hosts
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
  hosts:
    - host: "admin.sdbp.local"
      paths:
        - path: /
          pathType: Prefix
    - host: "api.sdbp.local"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-mobile.sdbp.local"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-mobile.sdbp.an.local.xz-lab.ru"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-geo.sdbp.local"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-fin.sdbp.local"
      paths:
        - path: /
          pathType: Prefix

# Gateway configuration for Istio
gateway:
  enabled: false
  namespace: "istio-ingressgateway"
  port:
    name: "http"
    number: 80
    protocol: "HTTP"
  tls: {}

# VirtualService configuration for Istio
virtualService:
  enabled: false
  gateway: "istio-ingressgateway/minikube-gateway"
  destination:
    host: "sdbp-nginx.default.svc.cluster.local"
    port: 80

# Server names configuration for minikube environment
servers:
  admin:
    serverName: "admin.sdbp.local"
  api:
    serverName: "api.sdbp.local"
  apiMobile:
    serverName: "api-mobile.sdbp.local api-mobile.sdbp.an.local.xz-lab.ru"
  apiFin:
    serverName: "api-fin.sdbp.local"

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi

nodeSelector: {}
tolerations: []
affinity: {}

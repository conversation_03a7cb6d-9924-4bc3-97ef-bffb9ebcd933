{{- if .Values.useSecretTemplate }}
apiVersion: v1
kind: Secret
metadata:
  name: sdbp-php-secret
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "php.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.mysql.password }}
  mysql-password: {{ .Values.mysql.password | b64enc }}
  {{- end }}
  {{- if .Values.redis.password }}
  redis-password: {{ .Values.redis.password | b64enc }}
  {{- end }}
  {{- if .Values.admin.telegram.token }}
  telegram-token: {{ .Values.admin.telegram.token | b64enc }}
  {{- end }}
  {{- if .Values.admin.telegram.failChatId }}
  telegram-chat-id: {{ .Values.admin.telegram.failChatId | b64enc }}
  {{- end }}
  {{- if .Values.keycloak.clientSecret }}
  client-secret: {{ .Values.keycloak.clientSecret | b64enc }}
  {{- end }}
  {{- if .Values.api.telegram.token }}
  api-telegram-token: {{ .Values.api.telegram.token | b64enc }}
  {{- end }}
  {{- if .Values.api.telegram.failChatId }}
  api-telegram-chat-id: {{ .Values.api.telegram.failChatId | b64enc }}
  {{- end }}
  {{- if .Values.api.keycloak.clientSecret }}
  api-keycloak-client-secret: {{ .Values.api.keycloak.clientSecret | b64enc }}
  {{- end }}
  {{- if .Values.api.goldCrown.password }}
  api-goldcrown-password: {{ .Values.api.goldCrown.password | b64enc }}
  {{- end }}
  {{- if .Values.apiMobile.telegram.token }}
  api-mobile-telegram-token: {{ .Values.apiMobile.telegram.token | b64enc }}
  {{- end }}
  {{- if .Values.apiMobile.telegram.failChatId }}
  api-mobile-telegram-chat-id: {{ .Values.apiMobile.telegram.failChatId | b64enc }}
  {{- end }}

{{- end }}

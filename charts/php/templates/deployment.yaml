apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "php.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "php.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "php.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "php.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            - configMapRef:
                name: {{ include "php.fullname" . }}-config
          env:
            - name: SDBP_DB_PSW
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: mysql-password
            - name: SDBP_REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: redis-password
            - name: SDBP_ADMIN_TELEGRAM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: telegram-token
            - name: SDBP_ADMIN_TELEGRAM_FAIL_CHAT_ID
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: telegram-chat-id
            - name: SDBP_ADMIN_KEYCLOAK_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: client-secret
            - name: SDBP_API_TELEGRAM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-telegram-token
            - name: SDBP_API_TELEGRAM_FAIL_CHAT_ID
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-telegram-chat-id
            - name: SDBP_API_KEYCLOAK_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-keycloak-client-secret
            - name: SDBP_API_GOLDCROWN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-goldcrown-password
            - name: SDBP_API_MOBILE_TELEGRAM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-mobile-telegram-token
            - name: SDBP_API_MOBILE_TELEGRAM_FAIL_CHAT_ID
              valueFrom:
                secretKeyRef:
                  name: sdbp-php-secret
                  key: api-mobile-telegram-chat-id
          ports:
            - name: phpfpm
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          volumeMounts:
            - name: php-config
              mountPath: /usr/local/etc/php/php.ini
              subPath: php.ini
            - name: php-config
              mountPath: /usr/local/etc/php-fpm.conf
              subPath: php-fpm.conf
#          livenessProbe:
#            httpGet:
#              path: /
#              port: http
#          readinessProbe:
#            httpGet:
#              path: /
#              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: php-config
          configMap:
            name: {{ include "php.fullname" . }}-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "php.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "php.labels" . | nindent 4 }}
data:
  # Environment
  SDBP_ENVIRONMENT: {{ .Values.environment | default "development" | quote }}

  # Database Configuration
  SDBP_DB_SERVER: {{ .Values.mysql.host | quote }}
  SDBP_DB_USER: {{ .Values.mysql.username | quote }}
  SDBP_DB_NAME: {{ .Values.mysql.database | quote }}
  SDBP_DB_PORT: {{ .Values.mysql.port | quote }}
  SDBP_DB_DEBUG: {{ .Values.environment.dbdebug | default | not | quote }}
  SDBP_DB_SOCKET: {{ .Values.mysql.socket | default "" | quote }}

  # Time and Date Settings
  SDBP_DEFAULT_TZ: {{ .Values.timezone | default "Europe/Moscow" | quote }}
  SDBP_DATE_FORMAT: {{ .Values.date.format | default "Y-m-d" | quote }}
  SDBP_DATE_PICKER_FORMAT: {{ .Values.date.pickerFormat | default "yyyy-mm-dd" | quote }}

  # Security Settings
  SDBP_LOGIN_LEN_MIN: {{ .Values.security.loginLenMin | default "3" | quote }}
  SDBP_LOGIN_LEN_MAX: {{ .Values.security.loginLenMax | default "255" | quote }}
  SDBP_LOGIN_TRIES_MAX: {{ .Values.security.loginTriesMax | default "3" | quote }}
  SDBP_LOGIN_TRIES_LOCK: {{ .Values.security.loginTriesLock | default "120" | quote }}
  SDBP_PSW_LEN_MIN: {{ .Values.security.pswLenMin | default "6" | quote }}
  SDBP_PSW_LEN_MAX: {{ .Values.security.pswLenMax | default "255" | quote }}
  SDBP_REMEMBER_ME_TIME: {{ .Values.security.rememberMeTime | default "2592000" | quote }}
  SDBP_PSW_LEN_MIN_CASH_REGISTER: {{ .Values.security.pswLenMinCashRegister | default "8" | quote }}
  SDBP_PSW_LEN_MAX_CASH_REGISTER: {{ .Values.security.pswLenMaxCashRegister | default "12" | quote }}

  # API Settings
  SDBP_GDS_API_HOST: {{ .Values.api.gdsHost | default "api.sdbp.local" | quote }}
  SDBP_API_TIME_LIMIT: {{ .Values.fpm.phpAdminValues.max_execution_time | quote }}
  SDBP_API_CONNECT_TIMEOUT: {{ .Values.api.connectTimeout | default "30" | quote }}
  SDBP_API_TIMEOUT: {{ .Values.api.timeout | default "90" | quote }}
  SDBP_API_MOBILE_DEFAULT_PHONE: {{ .Values.api.mobileDefaultPhone | default "+79999999999" | quote }}
  SDBP_API_MOBILE_DEFAULT_EMAIL: {{ .Values.api.mobileDefaultEmail | default "<EMAIL>" | quote }}
  SDBP_MAX_ROWS: {{ .Values.api.maxRows | default "1048576" | quote }}

  # Cache Settings
  SDBP_CACHE_CLASS: {{ .Values.cache.class | default "File" | quote }}
  SDBP_CACHE_DURATION: {{ .Values.cache.duration | default "+1 hours" | quote }}
  SDBP_CACHE_PREFIX: {{ printf "sdbp_%s" .Values.environment | quote }}
  SDBP_CACHE_PATH: {{ .Values.cache.path | default "cache/" | quote }}
  SDBP_CACHE_MASK: {{ .Values.cache.mask | default "0777" | quote }}

  # Redis Settings
  SDBP_REDIS_HOST: {{ .Values.redis.host | default "redis" | quote }}
  SDBP_REDIS_PORT: {{ .Values.redis.port | default "6379" | quote }}
  SDBP_REDIS_DATABASE: {{ .Values.redis.database | default "0" | quote }}
  SDBP_REDIS_PASSWORD: {{ .Values.redis.password | default "" | quote }}

  # API Cache Settings
  SDBP_API_CACHE_CONNECTION: {{ .Values.api.cache.connection | default "redis" | quote }}
  SDBP_API_CACHE_DEFAULT_CLASS: {{ .Values.api.cache.default.className | default "\\Cake\\Cache\\Engine\\FileEngine" | quote }}
  SDBP_API_CACHE_DEFAULT_DURATION: {{ .Values.api.cache.default.duration | default "+1 hours" | quote }}
  SDBP_API_CACHE_DEFAULT_PREFIX: {{ .Values.api.cache.default.prefix | default "sdbp_api" | quote }}
  SDBP_API_CACHE_DEFAULT_PATH: {{ .Values.api.cache.default.path | default "../cache/" | quote }}
  SDBP_API_CACHE_DEFAULT_MASK: {{ .Values.api.cache.default.mask | default "0777" | quote }}
  SDBP_API_CACHE_REDIS_CLASS: {{ .Values.api.cache.redis.className | default "\\Cake\\Cache\\Engine\\RedisEngine" | quote }}
  SDBP_API_CACHE_REDIS_TIMEOUT: {{ .Values.api.cache.redis.timeout | default "0" | quote }}
  SDBP_API_CACHE_REDIS_PERSISTENT: {{ .Values.api.cache.redis.persistent | default false | quote }}
  SDBP_API_CACHE_REDIS_DURATION: {{ .Values.api.cache.redis.duration | default "+1 hours" | quote }}
  SDBP_API_CACHE_REDIS_PREFIX: {{ .Values.api.cache.redis.prefix | default "sdbp-kube_api_" | quote }}



  # Admin Module Settings
  SDBP_ADMIN_PAYMENT_OWNER_SYSTEM_TITLE: {{ .Values.environment.title | default "" | quote }}
  SDBP_ADMIN_GDS_API_URL: {{ printf "http://%s" .Values.api.gdsHost | default "http://api.sdbp.local" | quote }}
  SDBP_ADMIN_KEYCLOAK_BASE_URL: {{ .Values.keycloak.baseUrl | default "http://keycloak:8080" | quote }}
  SDBP_ADMIN_KEYCLOAK_ENABLED: {{ .Values.keycloak.enabled | default false | quote }}
  SDBP_ADMIN_KEYCLOAK_CLIENT_ID: {{ .Values.keycloak.clientId | default "" | quote }}
  SDBP_ADMIN_KEYCLOAK_REALM: {{ .Values.keycloak.realm | default "" | quote }}
  SDBP_ADMIN_TELEGRAM_FAIL_SEND: {{ .Values.admin.telegram.failSend | default false | quote }}
  SDBP_ADMIN_API_MOBILE: {{ .Values.api.mobileUrl | default "http://am.sdbp.local" | quote }}
  SDBP_ADMIN_LKP_ENDPOINT: {{ .Values.admin.lkp.endpoint | default "localhost:5000" | quote }}
  SDBP_ADMIN_LKP_ENABLED: {{ .Values.admin.lkp.enabled | default false | quote }}
  SDBP_ADMIN_LKP_LOG_LEVEL: {{ .Values.admin.lkp.logLevel | default "2" | quote }}
  SDBP_ADMIN_LKP_SSL: {{ .Values.admin.lkp.ssl | default true | quote }}
  SDBP_ADMIN_LKP_UPDATE_STATUS: {{ .Values.admin.lkp.updateStatus | default "60000" | quote }}
  SDBP_ADMIN_REGION_ID: {{ .Values.admin.regionId | default "0" | quote }}
  SDBP_ADMIN_MAP_PROVIDER: {{ .Values.admin.mapProvider | default "osm" | quote }}
  SDBP_ADMIN_LANGUAGE: {{ .Values.admin.language | default "ru" | quote }}

  # API Module Settings
  SDBP_API_OUTER_API_LOG: {{ .Values.api.outerApiLog | default true | quote }}
  SDBP_API_OUTER_API_LOG_SQL_CHUNK_SIZE: {{ .Values.api.outerApiLogSqlChunkSize | default "1" | quote }}
  SDBP_API_BUY_TICKET_CNT_MAX: {{ .Values.api.buyTicketCntMax | default "5" | quote }}
  SDBP_API_TELEGRAM_FAIL_SEND: {{ .Values.api.telegram.failSend | default true | quote }}
  SDBP_API_AUTO_REFUND: {{ .Values.api.autoRefund | default "0" | quote }}
  SDBP_API_DEFAULT_AGENT_ID: {{ .Values.api.defaultAgentId | default "1" | quote }}
  SDBP_API_NSI_HTTP_TIMEOUT: {{ .Values.api.nsiHttpTimeout | default "30" | quote }}
  SDBP_API_TARIFICATION_TIMEOUT: {{ .Values.api.tarification.timeout | default "0" | quote }}
  SDBP_API_TARIFICATION_DAYS_EXPIRED: {{ .Values.api.tarification.daysExpired | default "2" | quote }}
  SDBP_API_TARIFICATION_TRANSFER_AVAILABLE_COUNT: {{ .Values.api.tarification.transferAvailableCount | default "" | quote }}
  SDBP_API_EXPIRATION_DATE_SHIFT_TEMPLATE: {{ .Values.api.expirationDateShiftTemplate | default "0" | quote }}
  SDBP_API_TRANSFER_LEFTOVERS: {{ .Values.api.transferLeftovers | default false | quote }}
  SDBP_API_AUTO_SET_USER_ID: {{ .Values.api.autoSetUserID | default false | quote }}
  SDBP_API_CHECK_TIMEZONE_OFF: {{ .Values.api.checkTimezoneOff | default false | quote }}
  SDBP_API_MAX_TT_REQUEST_COUNT: {{ .Values.api.maxTtRequestCount | default "1000" | quote }}

  # API GoldCrown FTP Settings
  SDBP_API_GOLDCROWN_HOST: {{ .Values.api.goldCrown.host | default "localhost" | quote }}
  SDBP_API_GOLDCROWN_PORT: {{ .Values.api.goldCrown.port | default "21" | quote }}
  SDBP_API_GOLDCROWN_USERNAME: {{ .Values.api.goldCrown.username | default "foo" | quote }}
  SDBP_API_GOLDCROWN_PASSWORD: {{ .Values.api.goldCrown.password | default "pass" | quote }}
  SDBP_API_GOLDCROWN_ROOT_FOLDER: {{ .Values.api.goldCrown.rootFolder | default "/" | quote }}
  SDBP_API_GOLDCROWN_SOURCE_FOLDER: {{ .Values.api.goldCrown.sourceFolder | default "/Korona" | quote }}
  SDBP_API_GOLDCROWN_ANSWER_FOLDER: {{ .Values.api.goldCrown.answerFolder | default "/Troika" | quote }}
  SDBP_API_GOLDCROWN_OUTPUT_FOLDER: {{ .Values.api.goldCrown.outputFolder | default "/Troika" | quote }}

  # API Keycloak Settings
  SDBP_API_KEYCLOAK_CLIENT_ID: {{ .Values.api.keycloak.clientId | default "some_client_id" | quote }}
  SDBP_API_KEYCLOAK_CLIENT_SECRET: {{ .Values.api.keycloak.clientSecret | default "secret" | quote }}
  SDBP_API_KEYCLOAK_REALM: {{ .Values.api.keycloak.realm | default "some_realm" | quote }}
  SDBP_API_KEYCLOAK_BASE_URL: {{ .Values.api.keycloak.baseUrl | default "http://keycloak:8080" | quote }}
  SDBP_API_KEYCLOAK_ENABLED: {{ .Values.api.keycloak.enabled | default true | quote }}

  # Logging Settings
  SDBP_LOG_SUCCESS: {{ .Values.logging.success | default true | quote }}
  SDBP_LOG_ERROR: {{ .Values.logging.error | default true | quote }}
  SDBP_LOG_METHODS: {{ .Values.logging.methods | default "POST,GET,PUT,DELETE" | quote }}
  SDBP_LOG_ANSWER: {{ .Values.logging.answer | default true | quote }}
  SDBP_LOG_SERVER: {{ .Values.logging.server | default true | quote }}
  SDBP_LOG_GET: {{ .Values.logging.get | default true | quote }}
  SDBP_LOG_POST: {{ .Values.logging.post | default true | quote }}
  SDBP_LOG_FILES: {{ .Values.logging.files | default true | quote }}
  SDBP_LOG_SQL: {{ .Values.logging.sql | default true | quote }}

  # Directory Settings
  SDBP_DIR_CASH_REGISTER_CFG: {{ .Values.directories.cashRegisterCfg | default "../config/cash_register/" | quote }}

  # City Settings
  SDBP_EXCLUDE_CITIES_ON_NAME_FROM_LIST: {{ .Values.city.excludeCitiesOnNameFromList | default "[]" | quote }}
  SDBP_SHOW_ALL_CITIES_ON_YMAP: {{ .Values.city.showAllCitiesOnYMap | default "false" | quote }}

  # API-Mobile Module Settings
  SDBP_API_MOBILE_CACHE_CLASS: {{ .Values.apiMobile.cache.className | default "File" | quote }}
  SDBP_API_MOBILE_CACHE_DURATION: {{ .Values.apiMobile.cache.duration | default "+1 hours" | quote }}
  SDBP_API_MOBILE_CACHE_PREFIX: {{ .Values.apiMobile.cache.prefix | default "sdbp_api_mobile" | quote }}
  SDBP_API_MOBILE_CACHE_PATH: {{ .Values.apiMobile.cache.path | default "../cache/" | quote }}
  SDBP_API_MOBILE_CACHE_MASK: {{ .Values.apiMobile.cache.mask | default "0777" | quote }}
  SDBP_API_MOBILE_OUTER_API_LOG: {{ .Values.apiMobile.outerApiLog | default true | quote }}
  SDBP_API_MOBILE_OUTER_API_LOG_SQL_CHUNK_SIZE: {{ .Values.apiMobile.outerApiLogSqlChunkSize | default "1" | quote }}
  SDBP_API_MOBILE_BUY_TICKET_CNT_MAX: {{ .Values.apiMobile.buyTicketCntMax | default "5" | quote }}
  SDBP_API_MOBILE_MAX_TERMINAL_LOG_COUNT: {{ .Values.apiMobile.maxTerminalLogCount | default "5" | quote }}
  SDBP_API_MOBILE_TELEGRAM_FAIL_SEND: {{ .Values.apiMobile.telegram.failSend | default true | quote }}
  SDBP_API_MOBILE_AUTO_SET_USER_ID: {{ .Values.apiMobile.autoSetUserID | default false | quote }}
  SDBP_API_MOBILE_CHECK_TIMEZONE_OFF: {{ .Values.apiMobile.checkTimezoneOff | default false | quote }}
  SDBP_API_MOBILE_URL: {{ .Values.apiMobile.url | default "http://api-mobile.sdbp.local" | quote }}

  php-fpm.conf: |
    [global]
    error_log = /proc/self/fd/2
    log_level = debug

    [www]
    access.log = /proc/self/fd/2
    access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

    listen = 9000

    pm = dynamic
    pm.max_children = {{ .Values.fpm.maxChildren }}
    pm.start_servers = {{ .Values.fpm.startServers }}
    pm.min_spare_servers = {{ .Values.fpm.minSpareServers }}
    pm.max_spare_servers = {{ .Values.fpm.maxSpareServers }}
    pm.max_requests = {{ .Values.fpm.maxRequests }}

    request_terminate_timeout = {{ .Values.fpm.requestTerminateTimeout }}
    request_slowlog_timeout = {{ .Values.fpm.requestSlowlogTimeout }}
    slowlog = /proc/self/fd/2

    catch_workers_output = yes
    decorate_workers_output = no

    clear_env = no

    php_admin_value[error_log] = /proc/self/fd/2
    php_admin_flag[log_errors] = on

  php.ini: |
{{- $phpIni := .Files.Get "config/php.ini" }}
{{- $phpIni = $phpIni | replace "memory_limit = 2G" (printf "memory_limit = %s" .Values.fpm.phpAdminValues.memory_limit) }}
{{- $phpIni = $phpIni | replace "max_execution_time = 30" (printf "max_execution_time = %s" (.Values.fpm.phpAdminValues.max_execution_time | toString)) }}
{{- $phpIni = $phpIni | replace "upload_max_filesize = 32M" (printf "upload_max_filesize = %s" .Values.fpm.phpAdminValues.upload_max_filesize) }}
{{- $phpIni = $phpIni | replace "post_max_size = 32M" (printf "post_max_size = %s" .Values.fpm.phpAdminValues.post_max_size) }}
{{- $phpIni = $phpIni | replace "display_errors = On" (printf "display_errors = %s" .Values.php.settings.display_errors) }}
{{- $phpIni = $phpIni | replace "display_startup_errors = On" (printf "display_startup_errors = %s" .Values.php.settings.display_startup_errors) }}
{{- $phpIni = $phpIni | replace "error_reporting = E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED" (printf "error_reporting = %s" .Values.php.settings.error_reporting) }}
{{- if not .Values.php.debug.xdebug.enabled }}
{{- $phpIni = $phpIni | regexReplaceAll "\\[xdebug\\][\\s\\S]*" "" }}
{{- else }}
{{- $phpIni = $phpIni | replace "xdebug.mode=develop,debug,coverage" (printf "xdebug.mode=%s" .Values.php.debug.xdebug.mode) }}
{{- $phpIni = $phpIni | replace "xdebug.client_host=host.docker.internal" (printf "xdebug.client_host=%s" .Values.php.debug.xdebug.client_host) }}
{{- $phpIni = $phpIni | replace "xdebug.client_port=9003" (printf "xdebug.client_port=%s" (.Values.php.debug.xdebug.client_port | toString)) }}
{{- $phpIni = $phpIni | replace "xdebug.log=/pdk/sdbp.local/log/php-xdebug.log" (printf "xdebug.log=%s" .Values.php.debug.xdebug.log) }}
{{- $phpIni = $phpIni | replace "xdebug.start_with_request=true" (printf "xdebug.start_with_request=%s" (.Values.php.debug.xdebug.start_with_request | toString)) }}
{{- $phpIni = $phpIni | replace "xdebug.remote_enable=1" (printf "xdebug.remote_enable=%s" (.Values.php.debug.xdebug.remote_enable | toString)) }}
{{- $phpIni = $phpIni | replace "xdebug.remote_autostart=1" (printf "xdebug.remote_autostart=%s" (.Values.php.debug.xdebug.remote_autostart | toString)) }}
{{- $phpIni = $phpIni | replace "xdebug.remote_port=9009" (printf "xdebug.remote_port=%s" (.Values.php.debug.xdebug.remote_port | toString)) }}
{{- $phpIni = $phpIni | replace "xdebug.remote_host=localhost" (printf "xdebug.remote_host=%s" .Values.php.debug.xdebug.remote_host) }}
{{- $phpIni = $phpIni | replace "xdebug.remote_mode=req" (printf "xdebug.remote_mode=%s" .Values.php.debug.xdebug.remote_mode) }}
{{- $phpIni = $phpIni | replace "xdebug.idekey=PHPSTORM" (printf "xdebug.idekey=%s" .Values.php.debug.xdebug.idekey) }}
{{- end }}
{{ $phpIni | nindent 4 }}

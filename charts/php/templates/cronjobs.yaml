{{- range $job := .Values.cronJobs }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "php.fullname" $ }}-{{ $job.name }}
  labels:
    {{- include "php.labels" $ | nindent 4 }}
spec:
  schedule: {{ $job.schedule | quote }}
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          {{- with $.Values.imagePullSecrets }}
          imagePullSecrets:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          containers:
            - name: {{ $job.name }}
              image: "{{ $.Values.image.repository }}:{{ $.Values.image.tag | default $.Chart.AppVersion }}"
              imagePullPolicy: {{ $.Values.image.pullPolicy }}
              workingDir: /pdk/sdbp.local
              envFrom:
                - configMapRef:
                    name: {{ include "php.fullname" $ }}-config
              command:
                - php
              args:
                {{- toYaml $job.args | nindent 16 }}
              env:
                - name: SDBP_DB_PSW
                  valueFrom:
                    secretKeyRef:
                      name: sdbp-php-secret
                      key: mysql-password
                - name: SDBP_REDIS_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: sdbp-php-secret
                      key: redis-password
                - name: SDBP_ADMIN_TELEGRAM_TOKEN
                  valueFrom:
                    secretKeyRef:
                      name: sdbp-php-secret
                      key: telegram-token
                - name: SDBP_ADMIN_TELEGRAM_FAIL_CHAT_ID
                  valueFrom:
                    secretKeyRef:
                      name: sdbp-php-secret
                      key: telegram-chat-id
                - name: SDBP_ADMIN_KEYCLOAK_CLIENT_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: sdbp-php-secret
                      key: client-secret
                {{- with $.Values.env }}
                {{- toYaml . | nindent 16 }}
                {{- end }}
          restartPolicy: OnFailure
{{- end }}

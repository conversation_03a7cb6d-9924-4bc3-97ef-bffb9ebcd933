---
# Source: php/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: sdbp-php-secret
  namespace: 54dev
  labels:
    helm.sh/chart: php-0.1.2
    app.kubernetes.io/name: php
    app.kubernetes.io/instance: sdbp-php
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: Helm
type: Opaque
data:
  mysql-password: WkI0QWhPREAyUW1vWFJYNkdMcjAz
  redis-password: N3BpYnZXaGRxUEFT
  telegram-token: ************************************************************
  telegram-chat-id: LTEwMDEzOTMwNTUyMzQ=
  client-secret: bm9uZQ==
  api-telegram-token: ************************************************************
  api-telegram-chat-id: LTEwMDEzOTMwNTUyMzQ=
  api-keycloak-client-secret: bm9uZQ==
  api-goldcrown-password: bm9uZQ==
  api-mobile-telegram-token: ************************************************************
  api-mobile-telegram-chat-id: LTEwMDEzOTMwNTUyMzQ=

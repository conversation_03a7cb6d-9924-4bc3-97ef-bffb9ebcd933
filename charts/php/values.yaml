
replicaCount: 1
namespace: 54dev

image:
  repository: sdbp-php
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets:
  - name: "default-secret"

nameOverride: ""
fullnameOverride: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 9000

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80



# Admin module configuration
admin:
  paymentOwnerSystemTitle: ""
  telegram:
    failSend: false
    token: ""
    failChatId: ""
  lkp:
    endpoint: localhost:5000
    enabled: false
    logLevel: 2
    ssl: true
    updateStatus: 60000
  regionId: "0"
  mapProvider: osm
  language: ru

# Keycloak configuration
keycloak:
  baseUrl: http://keycloak:8080
  enabled: false
  clientId: ""
  clientSecret: ""
  realm: ""

# Logging configuration
logging:
  success: true
  error: true
  methods: POST,GET,PUT,DELETE
  answer: true
  server: true
  get: true
  post: true
  files: true
  sql: true

# MySQL configuration
mysql:
  host: mysql
  port: 3306
  database: sdbp
  username: sdbp
  password: ""
  socket: ""
  debug: false

# PHP-FPM configuration
fpm:
  maxChildren: 50
  startServers: 5
  minSpareServers: 5
  maxSpareServers: 35
  maxRequests: 500
  requestTerminateTimeout: 300
  requestSlowlogTimeout: 10
  phpAdminValues:
    memory_limit: 256M
    max_execution_time: 300
    upload_max_filesize: 64M
    post_max_size: 64M

# PHP configuration
php:
  # Environment-specific settings
  environment: development  # development, production, testing

  # Core PHP settings
  settings:
    display_errors: "On"
    display_startup_errors: "On"
    error_reporting: "E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED"
    log_errors: "On"

  # Debug and development tools
  debug:
    enabled: true
    xdebug:
      enabled: true
      mode: "develop,debug,coverage"
      client_host: "host.docker.internal"
      client_port: 9003
      log: "/pdk/sdbp.local/log/php-xdebug.log"
      start_with_request: true
      remote_enable: 1
      remote_autostart: 1
      remote_port: 9009
      remote_host: "localhost"
      remote_mode: "req"
      idekey: "PHPSTORM"

# Cache configuration
cache:
  class: File
  duration: +1 hours
  prefix: sdbp_${environment}
  path: cache/
  mask: 0777

environment:
  title: Development
  dbdebug: false

# Security configuration
security:
  loginLenMin: 3
  loginLenMax: 255
  loginTriesMax: 3
  loginTriesLock: 120
  pswLenMin: 6
  pswLenMax: 255
  rememberMeTime: 2592000
  pswLenMinCashRegister: 8
  pswLenMaxCashRegister: 12

# Directory configuration
directories:
  cashRegisterCfg: "../config/cash_register/"

# City configuration
city:
  excludeCitiesOnNameFromList: "[]"
  showAllCitiesOnYMap: false

# Date configuration
date:
  format: "Y-m-d"
  pickerFormat: "yyyy-mm-dd"

# Cron jobs configuration
cronJobs: {}

api:
  gdsHost: 'api.sdbp.local'
  outerApiLog: true
  outerApiLogSqlChunkSize: 1
  buyTicketCntMax: 5
  autoRefund: 0
  defaultAgentId: 1
  nsiHttpTimeout: 30
  connectTimeout: 30
  timeout: 90
  mobileDefaultPhone: "+79999999999"
  mobileDefaultEmail: "<EMAIL>"
  maxRows: 1048576
  transferLeftovers: false
  autoSetUserID: false
  checkTimezoneOff: false
  maxTtRequestCount: 1000
  expirationDateShiftTemplate: 0

  # API Cache configuration
  cache:
    connection: redis
    default:
      className: "\\Cake\\Cache\\Engine\\FileEngine"
      duration: "+1 hours"
      prefix: "sdbp_api"
      path: "../cache/"
      mask: "0777"
    redis:
      className: "\\Cake\\Cache\\Engine\\RedisEngine"
      timeout: 0
      persistent: false
      duration: "+1 hours"
      prefix: "sdbp-kube_api_"

  # API Telegram configuration
  telegram:
    failSend: true
    token: ""
    failChatId: ""

  # API Tarification configuration
  tarification:
    timeout: 0
    daysExpired: 2
    transferAvailableCount: ""

  # API GoldCrown FTP configuration
  goldCrown:
    host: "localhost"
    port: 21
    username: "foo"
    password: "pass"
    rootFolder: "/"
    sourceFolder: "/Korona"
    answerFolder: "/Troika"
    outputFolder: "/Troika"

  # API Keycloak configuration
  keycloak:
    clientId: "some_client_id"
    clientSecret: "secret"
    realm: "some_realm"
    baseUrl: "http://keycloak:8080"
    enabled: true

# API-Mobile module configuration
apiMobile:
  # API-Mobile Cache configuration
  cache:
    className: "File"
    duration: "+1 hours"
    prefix: "sdbp_api_mobile"
    path: "../cache/"
    mask: "0777"

  outerApiLog: true
  outerApiLogSqlChunkSize: 1
  buyTicketCntMax: 5
  maxTerminalLogCount: 5
  autoSetUserID: false
  checkTimezoneOff: false
  url: "http://api-mobile.sdbp.local"

  # API-Mobile Telegram configuration
  telegram:
    failSend: true
    token: ""
    failChatId: ""

useSecretTemplate: false

redis:
  host: 'redis-host'
  port: 6379
  database: 0
  password: ""

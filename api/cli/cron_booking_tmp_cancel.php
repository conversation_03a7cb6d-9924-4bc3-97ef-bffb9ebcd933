<?php

/**
 * Консольный инструмент для отены временных броней
 * Обязательно указать http_host
 * --ignore_date Игнорирование даты отмены операции. Применяется для отладки
 * @var $app core\app
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

// Игнорирование даты отмены операции
$ignore_date = isset($app->args->ignore_date) ? $app->args->ignore_date : false;

$app->log('Start cancel operations...');
$i = 0;
foreach ($app->operation_model->get_expired_operations($ignore_date) as $operation) {
    $app->log('Start cancel operation # ' . $operation->operation_id);
    $app->log('Operation status ' . $operation->status);
    // Получение массива билетов, если они врешние
    $ticket_list = $app->ticket_model->get_ticket_list_by_operation($operation->operation_id);
    if ($app->connector_manager->cancel($operation->operation_id, $operation->status, false)) {
        // Отмена внешних операций
        $vendor_api_config = $app->partner_model->get_vendor_api_config($operation->partner_id);
        if ($vendor_api_config && $vendor_api_config->booking_tmp_cancel_auto) {
            $app->connector_manager->cancel_booking_tmp($operation, $ticket_list);
        }
        $app->log('Success cancel operation # ' . $operation->operation_id);
    } else {
        $app->log('Fail cancel operation # ' . $operation->operation_id);
    }
    $i++;
}
$app->log('Total count processed operations: ' . $i);

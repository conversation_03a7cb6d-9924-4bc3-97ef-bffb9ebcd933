<?php

declare(strict_types=1);

use App\Core\Infrastructure\Logger\ConsoleLogger;
use core\app;
use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\PanHashAlgorithm;
use lib\Avn\PanHashAlgorithmDb;
use lib\Avn\PanHashDb;
use lib\AvnTypes\EMVAbonementList;
use lib\AvnTypes\TerminalTransaction;
use Psr\Log\AbstractLogger;



$_SERVER['DOCUMENT_ROOT'] = __DIR__.'/..';
require_once($_SERVER['DOCUMENT_ROOT'].'/lbaf/initconsole.php');

// Произведём сброс статус транзакции, а также заполним AbonementId
// Извлечём subscribeId из transportCardTransactionData
// Сбросим им статус и логи тарификатора и заполним TerminalTransaction.AbonementId = subscribeId

/** @var app $app */
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

if (!isset($app->args->count)) {
    $countHandleOldTransactions = 1000;
} else {
    $countHandleOldTransactions = (int)$app->args->count;
}

if (isset($app->args->vvv)) {
    $verbose = true;
} else {
    $verbose = false;
}

$logger = new ConsoleLogger();
if ($verbose) {
    $logger->setLevelVisibility('debug', true);
}

function updateTransaction(app $app, int $abonementId, int $transactionId, AbstractLogger $logger): void
{
    try {
        $app->db->query(
            sprintf(
                'UPDATE TerminalTransaction
                                SET AbonementId = %d,
                                    IsTariffed = 0,
                                    NSIReceivedDateTime = null,
                                    TarifficationErrors = null,
                                    TarifficationDateTime = null,
                                    TarifficationTime = null,
                                    TarifficationLog = null
                            WHERE TransactionId = %d',
                $abonementId,
                $transactionId
            )
        );
    } catch (Exception $exception) {
        $logger->error($exception->getMessage(), $exception->getTrace());
    }
}

function findAbonementIdForTransportCard(object $transaction, AbstractLogger $logger): ?int
{
    $transportCardTransactionData = json_decode($transaction->transportCardTransactionData, true);
    $transactionId = (int)$transaction->TransactionId;
    if (isset($transportCardTransactionData['subscribeId'])) {
        $subscribeId = (int)$transportCardTransactionData['subscribeId'];
        $logger->info(
            sprintf('Extract subscribe id: %s for transaction ID: %s', $subscribeId, $transactionId)
        );

        return $subscribeId;
    } else {
        $logger->warning('Could not extract or found subscribe id for transaction ID: '.$transactionId);
        return null;
    }
}

function getTransactions(app $app, int $paymentType, int $batchSize): array
{
    return $app->db->query_res(
        "SELECT
                    TransactionId,
                    transportCardTransactionData,
                    AbonementId,
                    IsTariffed,
                    TarifficationErrors,
                    TarifficationDateTime,
                    TarifficationTime,
                    TarifficationLog,
                    rideDateTime
                FROM TerminalTransaction
                WHERE IsTariffed = 2
                    AND NSIReceivedDateTime IS NULL
                    AND paymentType = $paymentType
                    AND transportCardTransactionData IS NOT NULL
                LIMIT $batchSize"
    );
}

function extractLeftCountTransactions(app $app, int $paymentType, AbstractLogger $logger): void
{
    $leftCountTransactions = $app->db->query_res(
        "SELECT COUNT(*) AS `totalCount`
                FROM TerminalTransaction
                WHERE paymentType = $paymentType
                  AND IsTariffed = 2
                  AND NSIReceivedDateTime IS NULL
                  AND transportCardTransactionData IS NOT NULL
            "
    );

    if ($leftCountTransactions > 0) {
        $logger->info('Left transactions count: '.$leftCountTransactions[0]->totalCount);
    }
}

function extractPanHashForBankCard(object $transaction, AbstractLogger $logger): ?string
{
    $emvTransactionData = json_decode($transaction->transportCardTransactionData, true);
    $emvOperationTags = $emvTransactionData['OperationTags'] ?? $emvTransactionData['OpTags'] ?? null;

    if (!empty($emvOperationTags) && is_string($emvOperationTags)) {
        $emvOperationTags = json_decode($emvOperationTags, true);
    }

    $emvTags = [];

    if (!empty($emvOperationTags)) {
        foreach ($emvOperationTags as $emvOperationTag) {
            $emvTags[$emvOperationTag['id']] = $emvOperationTag['value'] ?? null;
        }

        if (!isset($emvTags[2])) {
            $logger->error('Not found emv 2 for transaction ID: '.$transaction->TransactionId);

            return null;
        } else {
            $panHash = strtolower(hash('sha256', $emvTags[2]));
            $logger->info('Using hash($EMVTagList[2]) as PANHash: '.$panHash);

            return $panHash;
        }
    } else {
        return null;
    }
}

function findAbonement(
    EMVAbonementLib $emvAbonementLib,
    AbstractLogger $logger,
    string $panHash,
    string $rideDateTime
): ?object {
    $abonementList = $emvAbonementLib->GetAbonementList($panHash, $rideDateTime, false);

    if (!empty($abonementList) && count($abonementList) === 1) {
        $logger->info('Abonement found directly with AbonementId: '.$abonementList[0]['AbonementId']);

        return (object)$abonementList[0];
    }

    if (empty($abonementList)) {
        $logger->info('Try found abonement by custom pan hash: '. $panHash);
        $panHashDb = new PanHashDb();

        $customPanHashAlgorithms = getAllCustomPanHashAlgorithms($logger);

        foreach ($customPanHashAlgorithms as $algorithm) {
            try {
                $hash = $algorithm->hash($panHash);
                $logger->info('Calculated hash for algorithm: '.$algorithm->type.' hash: '.$hash);

                $panHashes = $panHashDb->getAllByAlgorithmIdAndPanHash($algorithm->id, $hash);

                if (empty($panHashes)) {
                    $logger->error('No entries found for hash: '.$hash);
                    break;
                }

                $logger->info('Found '.count($panHashes).' entries for hash: '.$hash);

                foreach ($panHashes as $panHash) {
                    if ($panHash->hashable_type === EMVAbonementList::class) {
                        $abonement = $emvAbonementLib->GetAbonement($panHash->hashable_id);

                        if ($abonement) {
                            $logger->info(
                                'Found abonement by custom pan hash with AbonementId: '.$panHash->hashable_id
                            );

                            return $abonement;
                        } else {
                            $logger->error('No abonement found for pan hash: '.$panHash->hashable_id);
                        }
                    }
                }
            } catch (\Exception $e) {
                $logger->warning('Calculated hash for algorithm: '.$e->getMessage());
            }
        }
    }

    return null;
}

/**
 * @return PanHashAlgorithm[]
 */
function getAllCustomPanHashAlgorithms(AbstractLogger $logger): ?array
{
    $customPanHashAlgorithms = (new PanHashAlgorithmDb())->all();
    if (empty($customPanHashAlgorithms)) {
        $logger->warning('Empty custom panHash algorithms');

        return null;
    }

    $logger->info('Found '.count($customPanHashAlgorithms).' custom algorithms');

    return $customPanHashAlgorithms;
}

function handleOldTransportCardTransactions(
    app $app,
    AbstractLogger $logger,
    int $countHandleOldTransactions
): void {
    $logger->info('Start handle old transactions with payment type = 1');

    do {
        extractLeftCountTransactions(
            $app,
            TerminalTransaction::PAYMENT_TYPE_MIFARE,
            $logger
        );

        // Получаем порцию неудачных транзакций
        $transactions = getTransactions($app, TerminalTransaction::PAYMENT_TYPE_MIFARE, $countHandleOldTransactions);

        if (empty($transactions)) {
            $logger->info('No found transactions for handling');
            break;
        } else {
            $logger->info("Found old transactions for handle: ".count($transactions));
            foreach ($transactions as $transaction) {
                $abonementId = findAbonementIdForTransportCard($transaction, $logger);
                if ($abonementId) {
                    updateTransaction($app, $abonementId, (int)$transaction->TransactionId, $logger);
                }
            }

            $logger->info("Remaining transactions to handle: ".count($transactions));
        }
    } while (true); // продолжаем, пока есть транзакции для обработки

    $logger->info('Finish handling old transactions with payment type = 1');
}

function handleOldBankCardTransactions(
    app $app,
    EMVAbonementLib $emvAbonementLib,
    AbstractLogger $logger,
    int $countHandleOldTransactions
): void {
    $logger->info('Start handle old transactions with payment type = 3');

    do {
        extractLeftCountTransactions(
            $app,
            TerminalTransaction::PAYMENT_TYPE_EMV,
            $logger
        );

        // Получаем порцию неудачных транзакций
        $transactions = getTransactions(
            $app,
            TerminalTransaction::PAYMENT_TYPE_EMV,
            $countHandleOldTransactions
        );

        if (empty($transactions)) {
            $logger->info('No found transactions for handling');
            break;
        } else {
            foreach ($transactions as $transaction) {
                $panHash = extractPanHashForBankCard($transaction, $logger);
                if ($panHash) {
                    $abonement = findAbonement($emvAbonementLib, $logger, $panHash, $transaction->rideDateTime);

                    if ($abonement) {
                        updateTransaction(
                            $app,
                            (int)$abonement->AbonementId,
                            (int)$transaction->TransactionId,
                            $logger
                        );
                    } else {
                        $logger->error('No abonement found for pan hash: '.$panHash);
                    }
                }

            }
        }

        $logger->info("Remaining transactions to handle: ".count($transactions));
    } while (true);

    $logger->info('Finish handling old transactions with payment type = 3');
}


handleOldTransportCardTransactions($app, $logger, $countHandleOldTransactions);
handleOldBankCardTransactions(
    $app,
    new EMVAbonementLib(),
    $logger,
    $countHandleOldTransactions
);

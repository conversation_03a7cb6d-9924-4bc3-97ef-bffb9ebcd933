<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

use lib\Avn\EMV\EMVAbonementLib;
use lib\Cfg\Cfg;

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$app->log('Start parse terminal stats');
try {
    $app->terminal_stat_model->parse_terminal_stat();
} catch (\Exception $ex) {
    $app->log('Error while parse terminal stats. ' . $ex->getMessage());
}
$app->log('End parse terminal stats');

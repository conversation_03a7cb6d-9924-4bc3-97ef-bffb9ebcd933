<?php

/*
 * Крон удаления записей старых ride_segment
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$app->db->debug = false;

$app->log('Start remove ride_segment');
$app->ride_segment_model->ride_segment_remove();
$app->log('Success remove ride_segment');
$app->log('*****************************');

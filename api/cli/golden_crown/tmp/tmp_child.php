<?php

use App\StopList\Console\Command\RecalculateCommandOptimize;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$csvFile = file('/tkp2/sdbp54prod/var/child.csv');
//$csvFile = file('/pdk/sdbp.local/var/child.csv');
$uids = [];
foreach ($csvFile as $line) {
    $uids[] = $line;
}

$abonementIds = $app->db->query("
SELECT AbonementId FROM EMVAbonementList
WHERE UID IN (" . implode(',', $uids) . ")
")->fetch_column('AbonementId');
if (!empty($abonementIds)) {
    $count = $app->db->query("
    DELETE FROM EMVAbonementCounters
    WHERE AbonementId IN (" . implode(',', $abonementIds) . ")
    ")->affected_rows();
    $app->log('DELETEd FROM EMVAbonementCounters: ' . $count);
}

$count = $app->db->query("
DELETE FROM EMVAbonementList
WHERE UID IN (" . implode(',', $uids) . ")
")->affected_rows();
$app->log('DELETEd FROM EMVAbonementList: ' . $count);

$count = $app->db->query("
UPDATE ABTCardList
SET SocialCategoriesAvailable='[115]'
WHERE UID IN (" . implode(',', $uids) . ")
")->affected_rows();
$app->log('UPDATEd ABTCardList: ' . $count);

$cardList = $app->db->query("
SELECT UID, UIDHash, PAN, PANHash FROM ABTCardList
WHERE UID IN (" . implode(',', $uids) . ")
")->assoc();
$app->log('Select ABTCardList: ' . count($cardList));

/*$gctsRaw = $app->db->query("
SELECT card_uid, activation_date, end_date FROM golden_crown_transactions
WHERE card_uid IN (" . implode(',', $uids) . ")
")->assoc();

$gtc = [];

foreach ($gctsRaw as $item) {
    $gtc[$item['card_uid']] = $item;
}*/

$data = [];
$now = date('Y-m-d H:i:s');
foreach ($cardList as $card) {
/*    $WriteOffsId = 120;
    $WriteOffsScheduleId = 1096;
    $SocialCategories = 115;
    $SocialCategoryForReports = toSqlString('['.$SocialCategories.']');
    $endDate = $gtc[$card['UID']]['end_date'];
    if ($gtc[$card['UID']]['activation_date'] < '2025-01-01 00:00:00') {
        $WriteOffsId = 19;
        $WriteOffsScheduleId = 1681;
        $SocialCategoryForReports = toSqlString('null');
        $SocialCategories = toSqlString('null');
    }*/
    $data[] = toSqlArray([
        toSqlString($card['UID']),
        toSqlString($card['UIDHash']),
        toSqlString($card['PAN']),
        toSqlString($card['PANHash']),

        120,
        1681,
        toSqlString('МПК Социальная карта. Безлимит Многодетные'),
        toSqlString('Льготный абонемент, Автобус, троллейбус, трамвай, метрополитен. Безлимит. На календарный месяц'),

        0,
        1,
        0,
        toSqlString('[115]'),
        115,
        0, //
        toSqlString('2025-01-01 00:00:00'),
        1,
        30,
        toSqlString('2025-02-01 00:00:00'),
        toSqlString('2025-02-01 00:00:00'),
        0,
        1,
        toSqlString($now),
        toSqlString($now),
        toSqlString('[{"d": "all", "et": "23:59:59", "st": "00:00:00"}]'),
        -1,
        toSqlString($now)
    ]);
    $data[] = toSqlArray([
        toSqlString($card['UID']),
        toSqlString($card['UIDHash']),
        toSqlString($card['PAN']),
        toSqlString($card['PANHash']),

        19,
        1096,
        toSqlString('МПК Социальная карта. Безлимит Многодетные'),
        toSqlString('Льготный абонемент, Автобус, троллейбус, трамвай, метрополитен. Безлимит. На календарный месяц'),

        0,
        1,
        0,
        toSqlString('null'),
        toSqlString('null'),
        0, //
        toSqlString('2024-12-01 00:00:00'),
        1,
        30,
        toSqlString('2025-01-01 00:00:00'),
        toSqlString('2025-01-01 00:00:00'),
        0,
        1,
        toSqlString($now),
        toSqlString($now),
        toSqlString('[{"d": "all", "et": "23:59:59", "st": "00:00:00"}]'),
        -1,
        toSqlString($now)
    ]);
}
$sqlTransactions = '
                INSERT INTO `EMVAbonementList` (
                    `UID`,
                    `UIDHash`,
                    `PAN`,
                    `PANHash`,

                    `WriteOffsId`,
                    `WriteOffsScheduleId`,
                    `Name`,
                    `Description`,

                    `AgentId`,
                    `IsSocial`,
                    `IsActive`,
                    `SocialCategories`,
                    `SocialCategoryForReports`,
                    `BlockStatus`,
                    `ValidTimeStart`,
                    `ValidTimeType`,
                    `ValidTimeDays`,
                    `ValidTimeEnd`,
                    `ValidTimeMaximum`,
                    `SellPrice`,
                    `CurrencyId`,
                    `SellDateTime`,
                    `LastCheckDateTime`,
                    `Schedule`,
                    `EditDateTime`,
                    `EditUserId`
                ) VALUES
            ';
$sqlTransactions .= implode(',', $data);
$insertedCount = $app->db->query("$sqlTransactions")->affected_rows();
$app->log('Inserted abonements: ' . $insertedCount);


$abonementIds = $app->db->query("
SELECT AbonementId FROM EMVAbonementList
WHERE UID IN (" . implode(',', $uids) . ")
")->fetch_column('AbonementId');
$app->log('Select AbonementIds: ' . count($abonementIds));

$data = [];
$now = date('Y-m-d H:i:s');
foreach ($abonementIds as $abonementId) {
    $data[] = toSqlArray([
        5, // CounterType
        30, // ConstCount
        0, // TripCount
        1, // IsSeaFerry
        1, // IsSeaHovercrafts
        1, // IsSeaHydrofoils
        1, // IsSeaEkranoplan
        1, // IsRiverFerry
        1, // IsRiverHovercrafts
        1, // IsRiverHydrofoils
        1, // IsRiverPlaningVessels
        1, // IsAircraft
        1, // IsHelicopter
        1, // IsBus
        1, // IsTrolleybus
        1, // IsTram
        1, // IsTaxi
        1, // IsLongDistanceTrains
        1, // IsSuburbanElectricTrain
        1, // IsFunicular
        1, // IsCableCar
        1, // IsMetro
        1, // IsPipeline
        $abonementId, // AbonementId
    ]);
}
$sqlTransactions = '
                INSERT INTO `EMVAbonementCounters` (
                    `CounterType`,
                    `ConstCount`,
                    `TripCount`,
                    `IsSeaFerry`,
                    `IsSeaHovercrafts`,
                    `IsSeaHydrofoils`,
                    `IsSeaEkranoplan`,
                    `IsRiverFerry`,
                    `IsRiverHovercrafts`,
                    `IsRiverHydrofoils`,
                    `IsRiverPlaningVessels`,
                    `IsAircraft`,
                    `IsHelicopter`,
                    `IsBus`,
                    `IsTrolleybus`,
                    `IsTram`,
                    `IsTaxi`,
                    `IsLongDistanceTrains`,
                    `IsSuburbanElectricTrain`,
                    `IsFunicular`,
                    `IsCableCar`,
                    `IsMetro`,
                    `IsPipeline`,
                    `AbonementId`
                ) VALUES
            ';
$sqlTransactions .= implode(',', $data);
$insertedCount = $app->db->query("$sqlTransactions")->affected_rows();
$app->log('Inserted counters: ' . $insertedCount);

function toSqlString($value)
{
    if (empty($value)) {
        return 'null';
    }
    return "'" . $value . "'";
}

function toSqlArray(array $data, bool $isString = false)
{
    if (empty($data)) {
        return '(0) AND 1=0';
    }
    if ($isString) {
        $data = array_map(function ($item) {
            return toSqlString($item);
        }, $data);
    }
    return '(' . implode(',', $data) . ')';
}

exit;

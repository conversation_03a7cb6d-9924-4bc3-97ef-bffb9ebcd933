<?php

declare(strict_types=1);

use App\EMV\Infrastructure\Repository\EMVAbonementCounterRepository;
use App\EMV\Infrastructure\Repository\EMVAbonementListRepository;
use App\EMV\Infrastructure\Repository\EMVWalletListRepository;
use App\GoldCrown\Infrastructure\Command\RestoreGoldenCurrentBalancesCommand;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownCurrentBalancesRepository;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownTransactionsRepository;
use App\GoldCrown\Infrastructure\Service\RestoreGoldenCrownCurrentBalancesService;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/** @var app $app */

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}


$files = scandir('/pdk/sdbp.local/var/Korona');
$fileN = 0;
foreach ($files as $file) {
    $app->log("Start file: " . $file);
    $fileName = geTableName($file);
    if ($fileName === false) {
        continue;
    }
    $fileN++;
    $app->log("Start row $fileN file: " . $fileName['name']);
    $csvFile = file('/pdk/sdbp.local/var/Korona/'.$fileName['name']);
    $data = [];
    foreach ($csvFile as $line) {
        $row = str_getcsv($line, ';');
        if ($fileName['type'] == 'trips_tk_dest_zk') {
            $data[] = [
                'uid' => $row[0],
                'pan' => 0,
                'source_name' => $fileName['name'],
            ];
        } else {
            $data[] = [
                'uid' => $row[0],
                'pan' => $row[1],
                'source_name' => $fileName['name'],
            ];
        }
    }
    if (count($data) == 0) {
        $app->log('Empty file: ' . $fileName['name']);
        continue;
    }
    insert_ignore($app, 'tmp_all_cards', ['uid', 'pan', 'source_name'], $data);
    $app->log("Success row $fileN file: " . $fileName['name']);
}

function insert_ignore(app $app, $table, $columns, $data)
{
    $columnsSql = '';
    foreach ($columns as $column) {
        $columnsSql .= '`' . mysqli_real_escape_string($app->db->mysqli, $column) . '`,';
    }

    $baseSql = '
                INSERT IGNORE INTO
                    `' . mysqli_real_escape_string($app->db->mysqli, $table) . '`
                    (' . substr($columnsSql, 0, -1) . ')
                VALUES

            ';
    $rows = [];
    foreach ($data as $row) {
        $rows[] = "('" . implode("','", $row) . "')";
    }
    $baseSql .= implode(',', $rows);

    $app->db->query($baseSql);
}

function geTableName($file)
{
    if (strpos($file, 'card_status_zk') !== false) {
        return [
            'name' => $file,
            'type' => 'card_status',
        ];
    }
    if (strpos($file, 'replenishment') !== false) {
        return [
            'name' => $file,
            'type' => 'replenishment',
        ];
    }
    if (strpos($file, 'trips_tk_dest_zk') !== false) {
        return [
            'name' => $file,
            'type' => 'trips_tk_dest_zk',
        ];
    }

    return false;
}




exit(0);

<?php

use App\StopList\Console\Command\RecalculateCommandOptimize;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$filesRaw = $app->db->query("
    SELECT g.card_uid, g.source_name FROM golden_crown_transactions g
    WHERE g.card_uid IN (
        SELECT a.UID FROM ABTCardList a
        WHERE (a.PAN IS NULL OR a.PAN LIKE 'tmp_%')
        AND a.UID IS NOT NULL
    )
    AND g.source_name LIKE 'card%'
")->assoc();

$app->log('Total count: ' . count($filesRaw));

$files = [];
foreach ($filesRaw as $item) {
    $files[$item['source_name']][] = $item['card_uid'];
}
$fileList = array_keys($files);

foreach ($fileList as $file) {
    $csvFile = file('/tkp2/sdbp54prod/var/csv/Korona/tmp/processed/card_status/' . $file);
    foreach ($csvFile as $line) {
        $row = str_getcsv($line, ';');
        if (in_array($row[0], $files[$file])) {
            try {
                $app->db->query("
                    UPDATE ABTCardList
                    SET PAN='".$row[1]."', sellPAN='".$row[1]."', PANHash=null
                    WHERE UID='".$row[0]."'
                ");
            } catch (Exception $e) {
                $app->log("Error {$e->getMessage()} uid - {$row[0]}");
            }
            //$app->log("UPDATEd ABTCardList: file - $file , uid - {$row[0]}, pan - {$row[1]}");
        }
    }
}

//$app->log('UPDATEd ABTCardList: ' . $count);


function toSqlString($value)
{
    if (empty($value)) {
        return 'null';
    }
    return "'" . $value . "'";
}

function toSqlArray(array $data, bool $isString = false)
{
    if (empty($data)) {
        return '(0) AND 1=0';
    }
    if ($isString) {
        $data = array_map(function ($item) {
            return toSqlString($item);
        }, $data);
    }
    return '(' . implode(',', $data) . ')';
}

exit;

<?php

declare(strict_types=1);

use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/** @var app $app */

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}

$goldenCrownTransactionUidsRaw = $app->db->query("SELECT card_uid FROM golden_crown_transactions WHERE source_name LIKE '/Korona/card_status_z%'
  GROUP BY card_uid")->result();
$goldenCrownTransactionUids = array_map(function ($item) {
    return $item->card_uid;
}, $goldenCrownTransactionUidsRaw);

$tmpUidsRaw = $app->db->query("SELECT DISTINCT uid FROM tmp_all_cards
	WHERE source_name LIKE 'card_status_zk%' ")->result();
$tmpUids = array_map(function ($item) {
    return $item->uid;
}, $tmpUidsRaw);

$lostUid = array_diff($tmpUids, $goldenCrownTransactionUids);
$i = 0;
foreach ($lostUid as $item) {
    $i++;
    $app->log($item);
    if ($i > 20) {
        break;
    }
}
echo '<pre>';
var_dump(count($lostUid));
echo '</pre>';
exit;

exit(0);

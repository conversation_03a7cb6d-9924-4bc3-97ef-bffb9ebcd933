<?php

use App\StopList\Console\Command\RecalculateCommandOptimize;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$uids = $app->db->query("
    SELECT card_pan, card_uid FROM golden_crown_transactions gt
    WHERE gt.source_name LIKE 'card_%'
    group by card_pan
")->fetch_column('card_uid', 'card_pan');

/*$uids = [];
foreach ($uidsRaw as $item) {
    $uids[$item] = 0;
}*/
$files = scandir('/tkp2/sdbp54prod/var/csv/Korona/tmp/processed/card_status');

$uidLosts = [];
$fileW = fopen('/tkp2/sdbp54prod/var/card_status_lost.csv', 'w');
foreach ($files as $file) {
    if (strpos($file, 'card_status_zk') !== false) {
        $csvFile = file('/tkp2/sdbp54prod/var/csv/Korona/tmp/processed/card_status/' . $file);
        foreach ($csvFile as $line) {
            $row = str_getcsv($line, ';');
            if (!isset($uids[$row[1]])) {
                $uidLosts[] = $row[1];
/*                $app->db->query("
                    UPDATE golden_crown_transactions
                    SET card_pan='{$row[1]}'
                    WHERE source_name LIKE 'card_%'
                    AND card_uid='{$row[0]}'
                ");*/
                $card = $app->db->query("
                    SELECT * FROM ABTCardList
                    WHERE UID='{$row[0]}'
                ")->assoc();
                if (empty($card)) {
                    $app->log("File - $file, uid - {$row[0]}, pan - {$row[1]}");
                    fputs($fileW, $line);
                }
            }
        }
    }
}
fclose($fileW);

echo '<pre>';
var_dump(count($uidLosts));
echo '</pre>';
exit;


function toSqlString($value)
{
    if (empty($value)) {
        return 'null';
    }
    return "'" . $value . "'";
}

function toSqlArray(array $data, bool $isString = false)
{
    if (empty($data)) {
        return '(0) AND 1=0';
    }
    if ($isString) {
        $data = array_map(function ($item) {
            return toSqlString($item);
        }, $data);
    }
    return '(' . implode(',', $data) . ')';
}

exit;

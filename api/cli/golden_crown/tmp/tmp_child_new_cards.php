<?php

use App\StopList\Console\Command\RecalculateCommandOptimize;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$csvFile = file('/tkp2/sdbp54prod/var/child.csv');
$uids = [];
foreach ($csvFile as $line) {
    $uids[] = str_replace('"', '', $line);
}

$gtUidRaw = $app->db->query("
    SELECT * FROM golden_crown_transactions gt
    WHERE gt.card_pan IS NULL AND gt.source_name LIKE 'card_%'
    LIMIT 100000
")->assoc();


$files = [];
foreach ($gtUidRaw as $item) {
    $files[$item['source_name']][] = $item['card_uid'];
}
$fileList = array_keys($files);

foreach ($fileList as $file) {
    $csvFile = file('/tkp2/sdbp54prod/var/csv/Korona/tmp/processed/card_status/' . $file);
    foreach ($csvFile as $line) {
        $row = str_getcsv($line, ';');
        if (in_array($row[0], $files[$file])) {
            $app->log("Find uid - {$row[0]} , in - {$file}");
            if (!empty($row[1])) {
                try {
                    $app->db->query("
                    UPDATE ABTCardList
                    SET PAN='".$row[1]."', sellPAN='".$row[1]."', PANHash=null
                    WHERE UID='".$row[0]."'");
                    $app->db->query("
                    UPDATE golden_crown_transactions
                    SET card_pan='".$row[1]."'
                    WHERE card_uid='".$row[0]."' AND source_name LIKE 'card_%' AND card_pan IS NULL
                    ");
                } catch (Exception $e) {
                    $app->log("Error {$e->getMessage()} uid - {$row[0]}");
                }
            } else {
                $app->log("Empty PAN uid - {$row[0]}");
            }
        }
    }
}




function toSqlString($value)
{
    if (empty($value)) {
        return 'null';
    }
    return "'" . $value . "'";
}

function toSqlArray(array $data, bool $isString = false)
{
    if (empty($data)) {
        return '(0) AND 1=0';
    }
    if ($isString) {
        $data = array_map(function ($item) {
            return toSqlString($item);
        }, $data);
    }
    return '(' . implode(',', $data) . ')';
}

exit;

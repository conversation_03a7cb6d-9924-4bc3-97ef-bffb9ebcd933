<?php

declare(strict_types=1);



use App\EMV\Infrastructure\Repository\EMVAbonementCounterRepository;
use App\EMV\Infrastructure\Repository\EMVAbonementListRepository;
use App\EMV\Infrastructure\Repository\EMVWalletListRepository;
use App\GoldCrown\Infrastructure\Command\CronUpdateGoldenCrownCurrentBalancesCommand;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownCurrentBalancesRepository;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownTransactionsRepository;
use core\app;

$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/** @var app $app */

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}

try {
    (new CronUpdateGoldenCrownCurrentBalancesCommand(
        $app,
        new GoldenCrownCurrentBalancesRepository($app),
        new GoldenCrownTransactionsRepository($app),
        new EMVWalletListRepository($app),
        new EMVAbonementCounterRepository($app),
        new EMVAbonementListRepository($app)
    ))->execute();
    $app->log('Update golden crown current balances is successes', 'SUCCESS');
    exit(0);
} catch (Exception $exception) {
    $app->logger->error($exception->getMessage(), $exception->getTrace());
    $app->log('Update golden crown current balances is failed', 'FAILURE');
    exit(1);
}

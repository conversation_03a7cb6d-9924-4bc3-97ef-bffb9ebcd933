<?php

declare(strict_types=1);

use App\EMV\Infrastructure\Repository\EMVAbonementCounterRepository;
use App\EMV\Infrastructure\Repository\EMVAbonementListRepository;
use App\EMV\Infrastructure\Repository\EMVWalletListRepository;
use App\GoldCrown\Infrastructure\Command\RestoreGoldenCurrentBalancesCommand;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownCurrentBalancesRepository;
use App\GoldCrown\Infrastructure\Repository\GoldenCrownTransactionsRepository;
use App\GoldCrown\Infrastructure\Service\RestoreGoldenCrownCurrentBalancesService;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/** @var app $app */

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}

try {
    $service = new RestoreGoldenCrownCurrentBalancesService(
        $app,
        new GoldenCrownCurrentBalancesRepository($app),
        new GoldenCrownTransactionsRepository($app),
        new EMVWalletListRepository($app),
        new EMVAbonementCounterRepository($app),
        new EMVAbonementListRepository($app)
    );
    (new RestoreGoldenCurrentBalancesCommand($app, $service))->execute();
} catch (Exception $exception) {
    $app->logger->error($exception->getMessage(), $exception->getTrace());
    $app->log('Error: ' . $exception->getMessage(), 'FAILURE');
    exit(1);
}
$app->log('Successfully complete command', 'SUCCESS');
exit(0);

<?php

declare(strict_types=1);

use App\GoldCrown\Infrastructure\Command\RemoveDuplicatesFromGoldenCrownTransactionsCommand;
use App\GoldCrown\Infrastructure\Service\RemoveDuplicatesFromGoldenCrownTransactionsService;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/** @var app $app */

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}

try {
    $service = new RemoveDuplicatesFromGoldenCrownTransactionsService($app);
    (new RemoveDuplicatesFromGoldenCrownTransactionsCommand($app, $service))->execute();
} catch (Exception $exception) {
    $app->logger->error($exception->getMessage(), $exception->getTrace());
    $app->log('Error: ' . $exception->getMessage(), 'FAILURE');
    exit(1);
}
$app->log('Successfully complete command', 'SUCCESS');
exit(0);

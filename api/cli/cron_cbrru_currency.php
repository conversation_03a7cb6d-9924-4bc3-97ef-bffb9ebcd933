<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$app->log('Start get currency rate of CBR');
// Получение курсов валют с ЦБР
$currency_rate = \helper\currency::currency_rate_cbr();
if (!$currency_rate) {
    $app->log('Error get currency rate of CBR!');
    die();
}

// Получение наших валют
$currency_list = $app->currency_model->get_list();

// Получаение валют, которые нужно обновить
$currency_update = [];
foreach ($currency_list as $item) {
    if (isset($currency_rate[$item->iso4217])) {
        $currency_update[$item->iso4217] = $currency_rate[$item->iso4217]->rate;

        //fix для авазара
        if ($item->iso4217 === 'AMD') {
            $currency_update[$item->iso4217] *= 1.02;
        }
    }
}

$app->log('Update currency rate');
// Обновление текущих курсов валют
if (count($currency_update)) {
    $app->currency_model->update_rate($currency_update);
}

<?php

/**
 * Created by PhpStorm.
 * User: 1
 * Date: 29.01.2021
 * Time: 10:36
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$datefrom = date("Y-m-d H:i:s", strtotime("now -1 day"));
if (!empty($app->args->datefrom)) {
    $datefrom = date("Y-m-d H:i:s", strtotime($app->args->datefrom));
}

$dateto = date("Y-m-d H:i:s", strtotime("now"));
if (!empty($app->args->dateto)) {
    $dateto = date("Y-m-d H:i:s", strtotime($app->args->dateto));
}

$partner_id = null;
if (!empty($app->args->partner_id)) {
    $partner_id = $app->args->partner_id;
}

$app->log('Start create tickets...');

$params = [
    "transactionType" => ["TT","T1"],
    "datefrom" => $datefrom,
    "dateto" => $dateto,
    "partner_id" => $partner_id
];
$transaction_list = $app->transaction_model->get_list_with_personal($params);

foreach ($transaction_list as $t) {
    $outer_ride_segment = $app->ride_model->get_outer_ride_segment($t->rideSegmentId, $t->VendorId);
    if (!empty($outer_ride_segment)) {
        $ride_segment = $app->ride_model->get_ride_segment($outer_ride_segment->ride_segment_id, true);
        if (!$ride_segment) {
            //пока считаем что всегда есть
            $app->log('no ridesegmentid=' . $outer_ride_segment->ride_segment_id);
        } else {
            $params = [];
            $personal_data = json_decode($t->personalPassengerData);

            $new_ticket = [
                'position'            => "auto",
                'series_number'       => trim($personal_data->CardIdentitySN),
                'name'                => trim($personal_data->LastName) . " " . trim($personal_data->FirstName) . " " . trim($personal_data->MiddleName),
                'birthday'            => $personal_data->Birthday,
                'gender'              => $personal_data->Gender,
                'phone'               => isset($personal_data->Phone) ? $personal_data->Phone : \lib\Cfg\Cfg::$apiMobileDefaultPassangerPhone,
                'email'               => isset($personal_data->Email) ? $personal_data->Email : \lib\Cfg\Cfg::$apiMobileDefaultPassangerEmail,
                'card_identity_id'    => $personal_data->CardIdentityId,
                'citizenship_id'      => $personal_data->CitizenshipId,
                'child_tariff'        => null,
            ];

            $params['ticket_data'] = [(object)$new_ticket];

            $app->agent = $app->agent_model->get_agent($t->AgentId);

            $result = $app->connector_manager->booking_tmp($ride_segment, $params['ticket_data'], null, $t->AgentId);

            if ($result->result == 'success') {
                //покупка
                $operation = $app->operation_model->get_operation_with_tickets($result->operation_id);

                $result = $app->connector_manager->buy($operation, 1, $t->AgentId);

                if ($result === true) {
                    //поставить у тикета ид транзакции
                    foreach ($operation->ticket_list as $ticket) {
                        $app->ticket_model->set_transaction_id($ticket->ticket_id, $t->TransactionId);
                    }
                } else {
                    $app->log("error buy ");
                    // Отправка уведомлений
                    throw new Exception('Error buying tickets', 3);
                }
            } else {
                if (isset($result->result) && $result->result == 'error') {
                    $app->log("error booking " . $result->message);
                    throw new Exception($result->message, $result->code);
                } else {
                    $app->log("error booking.");
                    throw new Exception('Error booking tickets', 3);
                }
            }
        }
    } else {
        $app->log("empty outer segment=" . $t->rideSegmentId);
    }
}

$app->log('Create tickets compeleted.');

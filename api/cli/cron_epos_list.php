<?php

use lib\Avn\TerminalNSILoadLib;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$app->log('Start load epos list');
try {
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_SCHEME'] = 'http';
    $tLib = new TerminalNSILoadLib();
    $eposList = $tLib->LoadEposList();
} catch (\Exception $e) {
}
$app->log('End load epos list');

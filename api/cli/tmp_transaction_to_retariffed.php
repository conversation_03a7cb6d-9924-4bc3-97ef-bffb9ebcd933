<?php

declare(strict_types=1);

use App\Core\Infrastructure\Logger\ConsoleLogger;
use core\app;
use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\PanHashAlgorithm;
use lib\Avn\PanHashAlgorithmDb;
use lib\Avn\PanHashDb;
use lib\AvnTypes\EMVAbonementList;
use lib\AvnTypes\TerminalTransaction;
use Psr\Log\AbstractLogger;



$_SERVER['DOCUMENT_ROOT'] = __DIR__.'/..';
require_once($_SERVER['DOCUMENT_ROOT'].'/lbaf/initconsole.php');

// Скрипт обновления транзакций, которые ранее прошли таррификацию с ошибкой
// --all все ошибки
// --empty_abonement_wallet нет сведений по абонементу и кошельку
// --empty_wallet  нет сведений по кошельку, но абонемент указан

/** @var app $app */
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.gds.int', 'FAILURE');
    die();
}

$logger = new ConsoleLogger();

$logger->info('Старт обновления');
if (isset($app->args->all) || isset($app->args->empty_abonement_wallet)){
    $logger->info('Обновление транзакций, где нет сведений по абонементу и кошельку');
    try {
        $count = $app->db->query("
        UPDATE TerminalTransaction
        SET
            IsTariffed = 1,
            NSIReceivedDateTime = NULL
        WHERE
            IsTariffed = 2
            AND TarifficationErrors like '%Tarifficate: Wallet not found%'
            AND AbonementId IS NULL
            AND WalletId IS NULL
        ")->affected_rows();
        $logger->info('Обновлено транзакций TerminalTransaction: ' . $count);
    } catch (Exception $exception) {
        $logger->warning($exception->getMessage());
    }
}

if (isset($app->args->all) || isset($app->args->empty_wallet)){
    $logger->info('Обновление транзакций, где нет сведений по кошельку, но абонемент указан');
    try {
        $count = $app->db->query("
        UPDATE TerminalTransaction
        SET
            IsTariffed = 1,
            NSIReceivedDateTime = NULL
        WHERE
            IsTariffed = 2
            AND `TarifficationErrors` like '%Tarifficate: Wallet not found%'
            AND WalletId IS NULL
            AND AbonementId IS NOT NULL
        ")->affected_rows();
        $logger->info('Обновлено транзакций TerminalTransaction: ' . $count);
    } catch (Exception $exception) {
        $logger->warning($exception->getMessage());
    }
}

$logger->info('Обновления завершены');

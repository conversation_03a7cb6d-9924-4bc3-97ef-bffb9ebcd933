<?php

/* @var $app \core\app */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!\helper\console::arg('http_host')) {
    $app->log('Не указан параметр http_host. Например --http_host=api.local.unitiki.int', 'FAILURE');
    die();
}


if (!\helper\console::arg('action')) {
    $app->log('Не указан параметр action. action может быть равен ride_segment, place, ride,   Например --action=ride_segment', 'FAILURE');
    die();
}

$debug = 1;
$progress = !(isset($app->args->progress) and $app->args->progress === 'off');
$actions = explode(',', $app->args->action);

foreach ($actions as $action) {
    if ($action == 'ride_segment') {
        // *** Очистка ride_segment ***
        $ride_segment_day_left = \helper\console::arg('day_left', 2);
        $ride_segment_chunk_size = \helper\console::arg('chunk_size', 500);
        $datetime_end_remove = new \DateTime('-' . (int) $ride_segment_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_ride_segment($datetime_end_remove, $ride_segment_chunk_size, $debug, $progress);
    } elseif ($action == 'ride_segment_outer_detail') {
        $ride_segment_day_left = \helper\console::arg('day_left', 2);
        $ride_segment_chunk_size = \helper\console::arg('chunk_size', 500);
        $datetime_end_remove = new \DateTime('-' . (int) $ride_segment_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_ride_segment_outer_detail($datetime_end_remove, $ride_segment_chunk_size, $debug, $progress);
    } elseif ($action == 'ride_segment_way_points') {
        $ride_segment_day_left = \helper\console::arg('day_left', 2);
        $ride_segment_chunk_size = \helper\console::arg('chunk_size', 500);
        $datetime_end_remove = new \DateTime('-' . (int) $ride_segment_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_ride_segment_way_points($datetime_end_remove, $ride_segment_chunk_size, $debug, $progress);
    } elseif ($action == 'search_stat') {
        $search_stat_day_left = \helper\console::arg('day_left', 180);
        $search_stat_chunk_size = \helper\console::arg('chunk_size', 500);
        $datetime_end_remove = new \DateTime('-' . (int) $search_stat_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_search_stat($datetime_end_remove, $search_stat_chunk_size, $debug, $progress);
    } elseif ($action == 'ticket_data') {
        // *** Очистка ticket_data ***
        $operation_day_left = \helper\console::arg('day_left', 7);
        $operation_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $operation_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_ticket_data($datetime_end_remove, $operation_chunk_size, $debug, $progress);
    } elseif ($action == 'operation_log') {
        // *** Очистка operation_log ***
        $operation_log_day_left = \helper\console::arg('day_left', 14);
        $operation_log_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $operation_log_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_operation_log($datetime_end_remove, $operation_log_chunk_size, $debug, $progress);
    } elseif ($action == 'api_log' or $action == '_api_log') {
        // *** Очистка _api_log ***
        $api_log_day_left = \helper\console::arg('day_left', 14);
        $api_log_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $api_log_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_api_log($datetime_end_remove, $api_log_chunk_size, $debug, $progress);
    } elseif ($action == 'api_outer_log' or $action == '_api_outer_log') {
        $api_outer_log_day_left = \helper\console::arg('day_left', 14);
        $api_outer_log_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $api_outer_log_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_api_outer_log($datetime_end_remove, $api_outer_log_chunk_size, $debug, $progress);
    } elseif ($action == 'log_vendorapi_buy' or $action == '_log_vendorapi_buy') {
        $log_vendorapi_buy_day_left = \helper\console::arg('day_left', 14);
        $log_vendorapi_buy_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $log_vendorapi_buy_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_log_vendorapi_buy($datetime_end_remove, $log_vendorapi_buy_chunk_size, $debug, $progress);
    } elseif ($action == 'log_vendorapi_checkride' or $action == '_log_vendorapi_checkride') {
        $log_vendorapi_checkride_day_left = \helper\console::arg('day_left', 14);
        $log_vendorapi_checkride_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int) $log_vendorapi_checkride_day_left . ' days') ;
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_log_vendorapi_checkride($datetime_end_remove, $log_vendorapi_checkride_chunk_size, $debug, $progress);
    } elseif ($action == 'log_vendorapi_tmpbook' or $action == '_log_vendorapi_tmpbook') {
        $log_vendorapi_tmpbook_day_left = \helper\console::arg('day_left', 14);
        $log_vendorapi_tmpbook_chunk_size = \helper\console::arg('chunk_size', 100);
        $datetime_end_remove = new \DateTime('-' . (int)$log_vendorapi_tmpbook_day_left . ' days');
        $datetime_end_remove->setTime(23, 59, 59);
        $app->garbage_collector_manager->clear_log_vendorapi_tmpbook($datetime_end_remove, $log_vendorapi_tmpbook_chunk_size, $debug, $progress);
    } elseif ($action == 'garbage') {
        $day_left = \helper\console::arg('day_left', 7);
        $chunk_size = \helper\console::arg('chunk_size', 500);
    //$datetime_end_remove = new \DateTime('-' . (int) $day_left . ' days') ;
        //$datetime_end_remove->setTime(23, 59, 59);
        //$app->garbage_collector_manager->clear_ride_segment_outer_detail($datetime_end_remove, $chunk_size, $debug, $progress);
    } else {
        $app->log('Неверный параметр action');
    }
}

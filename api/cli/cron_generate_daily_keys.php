<?php

/**
 * Created by PhpStorm.
 * User: 1
 * Date: 23.03.2019
 * Time: 20:23
 */



$_SERVER['DOCUMENT_ROOT'] = dirname(__DIR__);
require_once($_SERVER['DOCUMENT_ROOT'] . '/libc/initconsole.php');

$app->log('Start generate secret keys...');

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.nsi.test', 'FAILURE');
    die();
}

try {
    $days_ahead = (int)(($app->args->days_ahead) ?? 2);
    $life_time = (int)(($app->args->life_time) ?? 120);
    $dslib = new \lib\Avn\DailySecretLib();
    $dslib->GenerateDailySecretKeys($days_ahead, $life_time);
} catch (Exception $e) {
    $app->log("Error during generate secret keys. " . $e->getMessage(), 'FAILURE');
    die();
}

$app->log('Generate secret keys completed.');

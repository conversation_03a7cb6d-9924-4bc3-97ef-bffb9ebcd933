<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.unitiki.int', 'FAILURE');
    die();
}

if (!isset($app->args->type)) {
    $app->log('Не указан параметр type. type может быть равен month или week. Например --type=week');
    die();
}

if (!in_array($app->args->type, ['month', 'week'])) {
    $app->log('Ошибка! Неверное значение параметра type. type может быть равен month или week. Например --type=week');
    die();
}

$payment_type = $app->args->type;
switch ($payment_type) {
    case 'week':
        $amount_limit_rub = 5000;
        break;
    case 'month':
        $amount_limit_rub = null;
        break;
}

$date_start = isset($app->args->date_start) ? date_create($app->args->date_start) : null;
$date_end = isset($app->args->date_end) ? date_create($app->args->date_end) : null;

if (isset($app->args->date_start) && !$date_start) {
    $app->log('Параметр --date_start некорректный');
    die();
}

if (isset($app->args->date_end) && !$date_end) {
    $app->log('Параметр --date_end некорректный');
    die();
}

$partner_list = $app->partner_model->get_partners_vendor();
$currency_list = $app->currency_model->get_list();

$vendor_id = isset($app->args->vendor_id) ? $app->args->vendor_id : null;
foreach ($partner_list as $partner) {
    if ($vendor_id) {
        if ($vendor_id != $partner->partner_id) {
            continue;
        }
    }
    $payment = $app->payment_model->create_vendor_payment($partner, $amount_limit_rub, $date_start, $date_end);
    if ($payment) {
        $app->log('ID выплаты: ' . $payment->payment_id . '. Вендор: ' . $partner->title . '. Сумма: ' . (float) $payment->amount . ' ' . $currency_list[$payment->currency_id]->title_short);
    }
}

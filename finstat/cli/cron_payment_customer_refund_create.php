<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.unitiki.int', 'FAILURE');
    die();
}

$currency_list = $app->currency_model->get_list();
$billing_list = $app->payment_model->billing_list();

foreach ($billing_list as $billing) {
    foreach ($currency_list as $currency) {
        $payment_list = $app->payment_model->create_payments_customer_refund($currency, $billing->billing_id);
        foreach ($payment_list as $payment) {
            $app->log('ID: ' . $payment->payment_id . '. Сумма: ' . $payment->amount . ' ' . $currency->title_short . ' Биллинг: ' . $billing->title);
        }
    }
}

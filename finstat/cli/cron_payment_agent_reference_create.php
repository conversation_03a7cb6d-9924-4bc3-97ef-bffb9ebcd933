<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=api.unitiki.int', 'FAILURE');
    die();
}

$date_start = isset($app->args->date_start) ? $app->args->date_start : null;
if ($date_start) {
    $date_start = date_create($date_start);
    if (!$date_start) {
        $app->log('Invalid date_start');
    }
}

$date_end = isset($app->args->date_end) ? $app->args->date_end : null;
if ($date_end) {
    $date_end = date_create($date_end);
    if (!$date_end) {
        $app->log('Invalid date_end');
    }
}

$partner_list = $app->partner_model->get_partners_agent_reference();
$currency_list = $app->currency_model->get_list();

$agent_id = isset($app->args->agent_id) ? $app->args->agent_id : null;
foreach ($partner_list as $partner) {
    if ($partner->is_agent_inner) {
        continue;
    }
    if ($agent_id) {
        if ($agent_id != $partner->agent_id) {
            continue;
        }
    }
    $payment = $app->payment_model->create_agent_reference_payment($partner->partner_id, $date_start, $date_end);
    if ($payment) {
        $app->log('ID выплаты: ' . $payment->payment_id . '. Агент: ' . $partner->title . '. Сумма: ' . $payment->amount . ' ' . $currency_list[$payment->currency_id]->title_short);
    }
}

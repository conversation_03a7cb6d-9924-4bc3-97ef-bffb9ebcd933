# GitLab CI/CD Pipeline for SDBP Project

stages:
  - build_base
  - build_final
  - deploy_php_dev
  - deploy-nx-dev
#  - db_load

variables:
  CI_DEBUG_TRACE: "true"
  KUBEVALURL: "https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz"


# Build Stage Jobs
build_php_base:
  stage: build_base
  tags:
    - docker
  script:
    # Используем название ветки, номер билда и хэш для тега
    - SAFE_BRANCH_NAME=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9_.-]/-/g')
    - export TAG="${SAFE_BRANCH_NAME}-${CI_PIPELINE_IID}-${CI_COMMIT_SHORT_SHA}"
    - echo "BASE_IMAGE_TAG=${TAG}" > docker_tags.env
    - docker build
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php-base:$TAG
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php-base
      --pull -f ./docker/php/Dockerfile_base_alpine .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php-base:$TAG
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php-base:latest
  artifacts:
    reports:
      dotenv: docker_tags.env  # Expose the tag to other jobs
  rules:
    - if: '$CI_COMMIT_REF_NAME == "feature/no-config" || $CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME =~ /^release.*$/'
#      changes:
#        - docker/php/Dockerfile_alpine

build_nx_image:
  stage: build_base
  script:
    - SAFE_BRANCH_NAME=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9_.-]/-/g')
    - export TAG="${SAFE_BRANCH_NAME}-${CI_PIPELINE_IID}-${CI_COMMIT_SHORT_SHA}"
    - docker build
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-nginx:latest
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-nginx:$TAG
      -f ./docker/nginx/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-nginx:latest
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-nginx:$TAG
  rules:
    - if: '$CI_COMMIT_REF_NAME == "feature/no-config" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"'
  tags:
    - docker

build_php_final:
  stage: build_final
  needs:
    - build_php_base
  script:
    - SAFE_BRANCH_NAME=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9_.-]/-/g')
    - export TAG="${SAFE_BRANCH_NAME}-${CI_PIPELINE_IID}-${CI_COMMIT_SHORT_SHA}"
    - docker build
      --target php_base
      --build-arg BASE_IMAGE_TAG=$BASE_IMAGE_TAG
      --build-arg DOCKER_REPOSITORY_ADDR=$DOCKER_REPOSITORY_ADDR
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php:latest
      -t $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php:$TAG
      -f ./docker/php/Dockerfile_prod .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php:latest
    - docker push $DOCKER_REPOSITORY_ADDR/tkp2/sdbp-php:$TAG
  dependencies:
    - build_php_base
  rules:
    - if: '$CI_COMMIT_REF_NAME == "feature/no-config" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"'
  tags:
    - docker

deploy_php_dev:
  stage: deploy_php_dev
  image: alpine/helm:latest
  tags:
    - docker
  dependencies:
    - build_php_final
  variables:
    HELM_RELEASE_NAME: "sdbp-php"
    NAMESPACE: "54dev"
    CI_ENVIRONMENT_NAME: "${CI_ENVIRONMENT_SLUG:-dev}"
  script:
    - |
      SAFE_BRANCH_NAME=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9_.-]/-/g')
      export TAG="${SAFE_BRANCH_NAME}-${CI_PIPELINE_IID}-${CI_COMMIT_SHORT_SHA}"
      cat $KUBECONFIG_DEVELOP > kubeconfig.yaml
      export KUBECONFIG=kubeconfig.yaml
      helm upgrade --install ${HELM_RELEASE_NAME} ./charts/php \
        --namespace ${NAMESPACE} \
        --set image.repository=${DOCKER_REPOSITORY_ADDR}/tkp2/sdbp-php \
        --set image.tag=${TAG} \
        -f ./charts/php/values.yaml \
        --values ./charts/php/values.54dev.yaml \
        --atomic \
        --wait \
        --debug \
        --timeout 300s
  environment:
    name: ${CI_ENVIRONMENT_NAME}
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      variables:
        CI_ENVIRONMENT_NAME: "production"
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        CI_ENVIRONMENT_NAME: "dev"
    - if: '$CI_COMMIT_BRANCH == "feature/no-config"'
      variables:
        CI_ENVIRONMENT_NAME: "dev"
    - if: '$CI_COMMIT_REF_NAME =~ /^release.*$/'
      variables:
        CI_ENVIRONMENT_NAME: "release-candidate"
  needs:
    - job: build_php_final
      artifacts: true

#Установка HELM манифестов Деплой в кластер develop
deploy-nx-dev:
  stage: deploy-nx-dev
  image: alpine/helm:latest
  dependencies:
    - build_nx_image
    - deploy_php_dev
  when: on_success
  tags:
    - docker
  variables:
    HELM_RELEASE_NAME: "sdbp-nginx"
    NAMESPACE: "54dev"
    CI_ENVIRONMENT_NAME: "${CI_ENVIRONMENT_SLUG:-dev}"
  script:
    - |
      cat $KUBECONFIG_DEVELOP > kubeconfig.yaml
      export KUBECONFIG=kubeconfig.yaml
      helm upgrade --install ${HELM_RELEASE_NAME} ./charts/nginx \
        --namespace ${NAMESPACE} \
        --set image.repository=${DOCKER_REPOSITORY_ADDR}/tkp2/sdbp-nginx \
        --set image.tag=latest \
        -f ./charts/nginx/values.yaml \
        --values ./charts/nginx/values.54dev.yaml \
        --atomic \
        --wait \
        --debug \
        --timeout 300s
  environment:
    name: ${CI_ENVIRONMENT_NAME}
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      variables:
        CI_ENVIRONMENT_NAME: "production"
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        CI_ENVIRONMENT_NAME: "dev"
    - if: '$CI_COMMIT_BRANCH == "feature/no-config"'
      variables:
        CI_ENVIRONMENT_NAME: "dev"
    - if: '$CI_COMMIT_REF_NAME =~ /^release.*$/'
      when: manual
      variables:
        CI_ENVIRONMENT_NAME: "release-candidate"

#### Database Load Stage (Independent)
###db_load_from_s3:
###  stage: db_load
###  image:
###    name: amazon/aws-cli:latest
###    entrypoint: [""]
###  before_script: |
###    apk add --no-cache mysql-client kubectl
###    echo "$KUBE_CONFIG" | base64 -d > ~/.kube/config
###  script: |
###    echo "⏳ Loading database dump from S3 - Status: Pending"
###    aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID"
###    aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY"
###    aws configure set region "ru-moscow-1"
###    aws s3 cp "$S3_DUMP_PATH" /tmp/database_dump.sql
###    kubectl port-forward -n dev svc/mysql-sdbp-mysql 3306:3306 &
###    sleep 10
###    mysql -h 127.0.0.1 -P 3306 -u root -p"$DB_PASSWORD" sdbp < /tmp/database_dump.sql
###    echo "✅ Database load from S3 completed"
###  rules:
###    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"
###      when: manual
###  tags:
###    - kubernetes

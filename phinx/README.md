# Phinx Migration Checker

## Описание
Инструмент для проверки и применения миграций Phinx в нескольких окружениях (базах данных). Позволяет легко отслеживать статус миграций и применять их к разным окружениям через Docker.

## Требования
- Docker
- Make
- Git

## Структура проекта
```
phinx2/
├── Dockerfile          # Конфигурация Docker-образа
├── Makefile           # Команды для управления
├── phinx.php          # Конфигурация Phinx
├── check-migrations.sh # Скрипт проверки миграций
├── migrations/        # Директория с миграциями
│   └── pool/         # Файлы миграций
└── output/           # Результаты проверок
```

## Пошаговая инструкция по использованию

### 1. Начальная настройка
1.0 Положите миграции в папку migrations

1.1. Создайте файл `.env` с настройками баз данных:
```bash
make setup
```
Это создаст файл `.env` с примерами настроек для всех окружений.

1.2. Отредактируйте файл `.env` и укажите реальные параметры подключения к базам данных:
```ini
# Пример для продакшн окружения
PROD_DB_HOST=prod-db.example.com
PROD_DB_NAME=prod_database
PROD_DB_USER=prod_user
PROD_DB_PASS=prod_password
PROD_DB_PORT=3306

# Пример для тестового окружения
DEV_DB_HOST=dev-db.example.com
DEV_DB_NAME=dev_database
DEV_DB_USER=dev_user
DEV_DB_PASS=dev_password
DEV_DB_PORT=3306

# Добавьте другие окружения по аналогии:
# XXX_DB_HOST=hostname
# XXX_DB_NAME=dbname
# XXX_DB_USER=username
# XXX_DB_PASS=password
# XXX_DB_PORT=3306
```

1.3. Соберите Docker-образ:
```bash
make build
```

### 2. Проверка статуса миграций

2.1. Проверка статуса всех миграций во всех окружениях:
```bash
make run
# Если необходимо просканировать рядом стоящий СДБП то необходим запуск с общей сетью
make run-with-network
```

2.2. Результаты проверки будут доступны в директории `output/`:
- `migration-status.txt` - общий отчет по всем окружениям
- `{env}-status.txt` - детальный отчет по каждому окружению

### 3. Применение миграций

3.1. Применение миграций к определенному окружению:
```bash
# Для продакшн
make migrate-prod

# Для разработки
make migrate-dev

# Для других окружений
make migrate-XXX  # где XXX - префикс окружения из .env
```

### 4. Добавление нового окружения

4.1. Просто добавьте новые переменные в `.env` файл:
```ini
NEW_DB_HOST=new-db.example.com
NEW_DB_NAME=new_database
NEW_DB_USER=new_user
NEW_DB_PASS=new_password
NEW_DB_PORT=3306
```

4.2. Система автоматически обнаружит новое окружение и создаст для него команды.

### 5. Просмотр доступных команд

```bash
make help
```

## Особенности и возможности

1. **Динамическое определение окружений**
   - Автоматическое обнаружение окружений из `.env` файла
   - Не требует изменения кода при добавлении новых окружений

2. **Безопасность**
   - Подтверждение перед применением миграций
   - Особое предупреждение для продакшн-окружения
   - Конфиденциальные данные хранятся только в `.env` файле

3. **Удобство использования**
   - Единый интерфейс через команды `make`
   - Подробные отчеты о статусе миграций
   - Поддержка множества окружений

## Устранение неполадок

### Проблема: Миграции не применяются
1. Проверьте правильность данных в `.env` файле
2. Убедитесь, что базы данных доступны
3. Проверьте сетевые настройки Docker

### Проблема: Ошибка доступа к базе данных
1. Проверьте права пользователя базы данных
2. Убедитесь, что указан правильный порт
3. Проверьте сетевые правила и файрволы

### Проблема: Не видно нового окружения
1. Проверьте формат переменных в `.env` файле
2. Убедитесь, что все необходимые переменные заданы
3. Перезапустите команду `make help` для обновления списка

### Пример результата работы
```
Migration Status Check - Tue Jul  1 08:28:32 UTC 2025
=================================
Environments checked: dev prod qa stage uat

== PROD Environment Status ==
Checking migrations for prod environment...
----------------------------------------
Database Host: mysql-sdbp
----------------------------------------

Pending migrations:
   down  20241028151551                                            CreateGoldenCrownTripsKzhTable
   down  20241029095029                                            AddIsPendingColumnToGoldenCrownTransactions
   down  20241029100139                                            AddIndexOnCardUidGoldenCrownTransactions
   down  20241029100211                                            AddIndexOnCardUidSourceNameGoldenCrownTransactions
   down  20241029100234                                            AddIndexOnSourceNameGoldenCrownTransactions
   down  20250116124916                                            AddCountersTransferedToEmvAbonementList
   down  20250206084114                                            CreateLilmitAbonementsForPeriod
   down  20250212145018                                            AddTransferCountersForEmvWriteOffsTemplate
   down  20250403095354                                            AddTarifficationTimeoutFieldToTransferMatrixTable
   down  20250605222652                                            RemoveHelpdesk
   down  20250605224936                                            RemoveSuppSoppUipp
   down  20250607152700                                            RemoveBillingRules
   down  20250607161300                                            RemoveUserSkin

```

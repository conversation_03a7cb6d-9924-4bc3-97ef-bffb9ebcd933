<?php

use Phinx\Seed\AbstractSeed;

class CronTasksSeeder extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run()
    {
        $data = [
            [
                'name' => 'Загрузка данных по терминалам',
                'schedule' => '*/2 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=load_all_terminal_data --partner_id=1 --http_host=%API_HOST% --forks=1 >>%PROJECT_ROOT%/log/cron-load-all-terminal-data.log',
            ],
            [
                'name' => 'Парсинг большой ЖСОН на транзакции',
                'schedule' => '*/1 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api-mobile/cli/cron_parse_transactions.php --http_host=api-mobile.sdbp.XX.tkp2.prod >>%PROJECT_ROOT%/log/cron-parse-terminal-transaction.log',
            ],
            [
                'name' => 'Обработка EMV транзакций',
                'schedule' => '*/3 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=bill_emv_transaction --max_count=5000 --partner_id=1 --http_host=%API_HOST% --forks=1 >>%PROJECT_ROOT%/log/bill-emv-transaction.log',
            ],
            [
                'name' => 'Проверка EMV абонементов',
                'schedule' => '*/3 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=check_emv_abonements --max_count=5000 --partner_id=1 --http_host=%API_HOST% --forks=1 >>%PROJECT_ROOT%/log/check-emv-transaction.log',
            ],
            [
                'name' => 'Загрузка стоп-листов из НСИ в СДБП',
                'schedule' => '*/2 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=load_emv_stop_list --partner_id=1 --http_host=%API_HOST% --forks=1 >>%PROJECT_ROOT%/log/load-emv-stop-list.log',
            ],

            [
                'name' => 'Загрузка рейсов (date_end_shift = 0)',
                'schedule' => '*/5 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=ride_all --partner_id=1 --http_host=%API_HOST% --date_end_shift=0 --forks=1 >>%PROJECT_ROOT%/log/cron-ride-all.log',
            ],
            [
                'name' => 'Загрузка рейсов (date_end_shift = 2)',
                'schedule' => '*/30 * * * *',
                'is_active' => 1,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=ride_all --partner_id=1 --http_host=%API_HOST% --date_end_shift=2 --forks=1 >>%PROJECT_ROOT%/log/cron-ride-all.log',
            ],

            [
                'name' => 'Импорт карт внешней эмиссии',
                'schedule' => '*/1 * * * *',
                'command' => '%PHP_BIN% %PROJECT_ROOT%/admin/cli/cron_emission_import.php',
            ],
        ];

        $partners = $this->fetchAll('SELECT partner_id, title FROM partner WHERE !is_deleted');

        foreach ($partners as $partner) {
            $data[] = [
                'name' => 'Загрузка городов (' . $partner['title'] . ')',
                'schedule' => '*/10 * * * *',
                'is_active' => 0,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=reload_city --partner_id=' . $partner['partner_id'] . ' --http_host=%API_HOST% --forks=1 >>%PROJECT_ROOT%/log/cron-city-load-' . $partner['title'] . '.log',
            ];

            $data[] = [
                'name' => 'Точки маршрута (' . $partner['title'] . ')',
                'schedule' => '*/15 * * * *',
                'is_active' => 0,
                'command' => '%PHP_BIN% %PROJECT_ROOT%/api/cli/api_action.php --action=waypoints --partner_id=' . $partner['partner_id'] . ' --http_host=%API_HOST% --forks=5 >>%PROJECT_ROOT%/log/cron-waypoint-load-' . $partner['title'] . '.log',
            ];
        }

        $this->truncateWithConfirmation(['cron_tasks', 'cron_task_runs']);

        $this->table('cron_tasks')
            ->insert($data)
            ->saveData();
    }

    /**
     * @param string|array $tables
     */
    private function truncateWithConfirmation($tables): void
    {
        if (is_string($tables)) {
            $tables = [$tables];
        }

        $confirmation = readline('Удалить существующие данные в таблицах ' . implode(', ', $tables) . ' [y/n]? ');

        if (in_array($confirmation, ['y', 'Y'])) {
            foreach ($tables as $table) {
                $this->table($table)
                    ->truncate();
            }
        }
    }
}

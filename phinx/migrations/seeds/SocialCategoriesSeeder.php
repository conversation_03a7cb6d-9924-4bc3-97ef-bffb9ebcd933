<?php

use Phinx\Seed\AbstractSeed;

class SocialCategoriesSeeder extends AbstractSeed
{
    public function run()
    {
        $region = (int)readline('Номер региона: ');

        $this->query('DELETE FROM `social_categories` WHERE 1=1');

        $table = $this->table('social_categories');

        foreach ($this->getSocialCategories($region) as $category) {
            $table->insert($category);
        }

        $table->saveData();
    }

    private function getSocialCategories(int $region): Generator
    {
        $file = './migrations/data/social_categories/' . $region . '.csv';

        if (!file_exists($file)) {
            throw new \Exception('Файл ' . $file . ' не найден');
        }

        $input = new SplFileObject($file);

        $header = $input->fgetcsv();
        $headerCount = count($header);

        while (!$input->eof()) {
            $row = $input->fgetcsv();

            $data = [];

            for ($col = 0; $col < $headerCount; $col++) {
                $data[$header[$col]] = $row[$col];
            }

            yield $data;
        }
    }
}

<?php

use Phinx\Seed\AbstractSeed;

class AddSocialCategoryToExistingAbonements extends AbstractSeed
{
    protected array $socialCategoriesIdsSorted = [];

    public function run()
    {
        $this->socialCategoriesIdsSorted = $this->getSocialCategoriesIdsSorted();

        $page = 1;
        $abonements = $this->getAbonements($page);

        while (!empty($abonements)) {
            $this->getAdapter()->beginTransaction();
            foreach ($abonements as $abonement) {
                $this->log('debug', 'Updating abonement #' . $abonement['AbonementId']);

                $cardSocialCategories = $abonement['SocialCategoriesAvailable']
                    ? json_decode($abonement['SocialCategoriesAvailable'], true)
                    : null;
                $templateSocialCategories = $abonement['AllowedSocialCategories']
                    ? json_decode($abonement['AllowedSocialCategories'], true)
                    : null;

                $intersection = $this->getSortedIntersection($cardSocialCategories, $templateSocialCategories);
                $intersectionEncoded = $intersection ? json_encode($intersection) : null;
                $forReports = $intersection ? $intersection[0] : null;

                if ($abonement['SocialCategories'] !== $intersectionEncoded) {
                    $updateQuery = $this->getAdapter()->getQueryBuilder()
                        ->update('EMVAbonementList')
                        ->set('SocialCategories', $intersectionEncoded)
                        ->set('SocialCategoryForReports', $forReports)
                        ->where([
                            'AbonementId' => $abonement['AbonementId'],
                        ]);

                    $this->log('debug', $updateQuery->sql());
                    $updateQuery->execute();
                }
            }
            $this->getAdapter()->commitTransaction();
            $page++;
            $abonements = $this->getAbonements($page);
        }
    }

    protected function getSocialCategoriesIdsSorted(): array
    {
        $socialCategoriesSorted = $this->getAdapter()->getQueryBuilder()
            ->select(['id'])
            ->from('social_categories')
            ->orderDesc('priority')
            ->execute()
            ->fetchAll('assoc');

        return array_column($socialCategoriesSorted, 'id');
    }

    protected function getAbonements(int $page = 1): array
    {
        $this->log('debug', 'fetching abonements, page ' . $page);

        return $this->getAdapter()->getQueryBuilder()
            ->select([
                'EMVAbonementList.AbonementId',
                'EMVAbonementList.SocialCategories',
                'EMVAbonementList.SocialCategoryForReports',
                'EMVWriteOffsTemplate.IsRestrictedBySocialCategories',
                'EMVWriteOffsTemplate.AllowedSocialCategories',
                'ABTCardList.SocialCategoriesAvailable',
            ])
            ->from('EMVAbonementList')
            ->leftJoin(
                'ABTCardList',
                '(ABTCardList.PANHash IS NOT NULL AND ABTCardList.PANHash = EMVAbonementList.PANHash)
                     OR (ABTCardList.UIDHash IS NOT NULL AND ABTCardList.UIDHash = EMVAbonementList.UIDHash)
                     OR ABTCardList.Id = EMVAbonementList.CardId'
            )
            ->leftJoin('EMVWriteOffsTemplate', 'EMVAbonementList.WriteOffsId = EMVWriteOffsTemplate.Id')
            ->where([
                'EMVAbonementList.IsSocial' => true,
            ])
            ->page($page, 1000)
            ->execute()
            ->fetchAll('assoc');
    }

    protected function log(string $level, string $message)
    {
        $colorModifier = null;

        switch ($level) {
            case 'error':
                $colorModifier = '[41m'; //Red background
                break;
            case 'warning':
                $colorModifier = '[43m'; //Yellow background
                break;
            case 'info':
                $colorModifier = '[44m'; //Blue background
                break;
            default:
        }

        if ($colorModifier) {
            echo chr(27) . '[0m' . chr(27) . $colorModifier;
        }

        echo '[' . $level . '] ' . $message;

        if ($colorModifier) {
            echo chr(27) . '[0m';
        }

        echo PHP_EOL;
    }

    protected function getSortedIntersection(?array $cardSocialCategories, ?array $templateSocialCategories): ?array
    {
        if ($templateSocialCategories === null && $cardSocialCategories === null) {
            $intersection = [];
        } elseif ($templateSocialCategories === null) {
            // В шаблоне нет ограничений по соц. категориям
            $intersection = null;
        } elseif ($cardSocialCategories === null) {
            $intersection = $templateSocialCategories;
        } else {
            $intersection = array_intersect($templateSocialCategories, $cardSocialCategories);
        }

        if (is_array($intersection)) {
            $intersection = $this->sortCategoriesIds($intersection);
        }

        return $intersection;
    }

    protected function sortCategoriesIds(array $intersection): array
    {
        usort($intersection, function ($a, $b) {
            return array_search($a, $this->socialCategoriesIdsSorted)
                <=> array_search($b, $this->socialCategoriesIdsSorted);
        });

        return array_values($intersection);
    }
}

<?php

use Phinx\Seed\AbstractSeed;

class AltaiSocialTypesSeeder extends AbstractSeed
{
    public function run()
    {
        $this->query('DELETE FROM ReferenceList WHERE ReferenceName = "SocialType"');

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 0,
                'RecordName' => 'stUnknown',
                'RecordDescription' => 'Тип неизвестен',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 1,
                'RecordName' => 'stFederalAltai',
                'RecordDescription' => 'Федеральная льгота (Горный Алтай)',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 2,
                'RecordName' => 'stRegionalAltai',
                'RecordDescription' => 'Региональная льгота (Горный Алтай)',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 3,
                'RecordName' => 'stFederalMaima',
                'RecordDescription' => 'Федеральная льгота (Майма)',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 4,
                'RecordName' => 'stRegionalMaima',
                'RecordDescription' => 'Региональная льгота (Майма)',
            ],
        ])->saveData();
    }
}

<?php

use Phinx\Seed\AbstractSeed;

class PoehaliFix extends AbstractSeed
{
    protected const BIN = '31064700';

    protected bool $dryRun = true;

    public function run()
    {
        $confirmation = readline('Выполнять UPDATE и DELETE statements [y/n]? ');
        $this->dryRun = !in_array($confirmation, ['y', 'Y']);

        $this->log('info', 'Для карт с коротким паном ищем уже существующие карты с бинами');

        $importedShortPans = $this->fetchAll('SELECT PAN FROM ABTCardList WHERE LENGTH(PAN) = 11 AND PAN LIKE "004%";');
        $this->log('debug', 'Найдено ' . count($importedShortPans) . ' карт с коротким паном');
        $shortPans = array_column($importedShortPans, 'PAN');
        $longPans = array_map(function ($pan) {
            return self::BIN . $pan;
        }, $shortPans);

        $this->log('info', 'Удаляем уже существующие карты с бинами');
        $this->executeIfNotDry('DELETE FROM ABTCardList WHERE PAN IN ("' . implode('", "', $longPans) . '")');


        $this->log('info', 'Ищем абонементы на картах с коротким паном и проставляем им бины');
        $this->executeIfNotDry('
            UPDATE EMVAbonementList 
            SET PAN     = CONCAT("' . self::BIN . '", PAN),
                PANHash = SHA2(PAN, 256)
            WHERE PAN IN ("' . implode('", "', $shortPans) . '")
        ');


        $this->log('info', 'Картам «поехали», импортированным без бинов, проставляем бины и пересчитываем панхеш');
        $this->executeIfNotDry('
            UPDATE ABTCardList
            SET PAN     = CONCAT("' . self::BIN . '", PAN),
                PANHash = SHA2(PAN, 256)
            WHERE LENGTH(PAN) = 11
                AND PAN LIKE "004%";
        ');


        $this->log('info', 'Ищем карты «поехали» по бинам, проставляем им sellPAN и ставим тип карты «БК»');
        $this->executeIfNotDry('
            UPDATE ABTCardList
            SET sellPAN = SUBSTR(PAN, 9),
                sellPANHash = SHA2(sellPAN, 256),
                Type = 1
            WHERE PAN LIKE "' . self::BIN . '%";
        ');
    }

    protected function log(string $level, string $message)
    {
        $colorModifier = null;

        switch ($level) {
            case 'error':
                $colorModifier = '[41m'; //Red background
                break;
            case 'warning':
                $colorModifier = '[43m'; //Yellow background
                break;
            case 'info':
                $colorModifier = '[44m'; //Blue background
                break;
            default:
        }

        if ($colorModifier) {
            echo chr(27) . '[0m' . chr(27) . $colorModifier ;
        }

        echo '[' . $level . '] ' . $message . PHP_EOL;

        if ($colorModifier) {
            echo chr(27) . '[0m';
        }
    }

    protected function executeIfNotDry(string $query)
    {
        if (!$this->dryRun) {
            $this->log('debug', 'query: ' . $query);
            $result =  $this->query($query);
            $this->log('debug', 'affected rows: ' . $result->rowCount());
        } else {
            $this->log('debug', 'dry run: ' . $query);
        }
    }
}

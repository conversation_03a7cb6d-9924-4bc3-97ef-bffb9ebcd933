#!/bin/bash

function die {
  printf "Script failed: %s\n\n" "$1";
  exit 1;
}

programName=$0;
function usage {
  echo ""
  echo "Run prepared changes via pt-online-scheme-change tool."
  echo ""
  echo "usage: $programName --user string --dbname string --host string"
  echo ""
  echo " --user string  database user name"
  echo " --dbname string database database name"
  echo " --host string database host"
  echo ""
}

# Check if installed percona-toolkit on machine.
if ! type "pt-online-schema-change" > /dev/null; then
  echo "Please install percona-toolkit. Script is failed."
  echo "See: https://docs.percona.com/percona-toolkit/installation.html"
  exit 1;
fi

# Set up variables from command line arguments
while [ $# -gt 0 ]; do
    if [[ $1 == "--"* ]]; then
        v="${1/--/}"
        declare "$v"="$2"
        shift
    fi
    shift
done

# Check all required arguments
if [[ -z $user ]]; then
  usage
  die "Missing parameter --user"
elif [[ -z $host ]]; then
  usage
  die "Missing parameter --host"
elif [[ -z $dbname ]]; then
  usage
  die "Missing parameter --dbname"
fi

# Prepare query string for TerminalTransaction table.
query="ADD rawPacketId BIGINT(20) unsigned NULL, ADD KEY idx__tt__time_type_workDayNumber_terminalSerialNumber (transactionTime,transactionType,workDayNumber,terminalSerialNumber), ADD rpp_reason_code INT(11) NULL COMMENT 'Причина РПП', ADD KEY idx__transactionTime (transactionTime);";
table="TerminalTransaction";

# Execute the command
# --critical-load — значение, при котором прерывается выполнение запроса. Берется из SHOW GLOBAL STATUS;
# --alter — запрос, который необходимо выполнить без ключевых слов ALTER TABLE;
# D, или --database — база данных;
# t — таблица базы данных;
# u, или --user — пользователь базы данных;
# h - хост базы данных
# p, или --password — пароль пользователя базы данных;
# --execute — подтверждение выполнения операции.
# --ask-pass - спросить в интерактивном режиме пароль от MySQL
pt-online-schema-change --critical-load Threads_running=150 --alter "$query" D="$dbname",t="$table",u="$user",h="$host" --ask-pass --no-check-replication-filters --alter-foreign-keys-method=auto --recursion-method=none --execute

<?php

use Phinx\Migration\AbstractMigration;

class BusDeviceTypeQr extends AbstractMigration
{
    public function up()
    {
        $sql = "
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription)
                VALUES ('bus-device-type', 7, 'dtQr', 'QR - код');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'bus-device-type' and RecordId=7;
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIsPendingColumnToGoldenCrownTransactions extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_transactions')->hasColumn('is_pending')) {
            $this->table('golden_crown_transactions')
                ->addColumn('is_pending', 'boolean', [
                    'default' => false,
                    'null' => false,
                    'comment' => 'Флаг для отложенной обработки транзакции'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_transactions')->hasColumn('is_pending')) {
            $this->table('golden_crown_transactions')
                ->removeColumn('is_pending')
                ->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddWriteOffsSchedule extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('WriteOffsScheduleId', 'integer', [
                'default' => null,
                'after' => 'WriteOffsId',
                'null' => true,
                'comment' => 'Идентификатор связки шаблона и расписания (emv_xref_template_schedule)',
            ])
            ->update();

        $this->table('EMVAbonementList')
            ->addColumn('WriteOffsScheduleId', 'integer', [
                'default' => null,
                'after' => 'WriteOffsId',
                'null' => true,
                'comment' => 'Идентификатор связки шаблона и расписания (emv_xref_template_schedule)',
            ])
            ->update();

        $this->table('EMVWriteOffsTemplate')
            ->addColumn('IsAddSchedule', 'boolean', [
                'null' => false,
                'default' => false,
                'after' => 'IsSbolAvailable',
                'comment' => 'Добавлять расписание за день до активации продаж',
            ])
            ->update();
    }
}

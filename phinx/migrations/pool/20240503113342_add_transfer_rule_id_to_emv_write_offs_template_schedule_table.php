<?php

use Phinx\Migration\AbstractMigration;

class AddTransferRuleIdToEmvWriteOffsTemplateScheduleTable extends AbstractMigration
{
    protected const TABLE = 'emv_write_offs_template_schedule';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('transfer_rule_id', 'biginteger', [
                'comment' => 'Правило переноса остатков абонементов',
                'signed' => false,
                'null' => true,
                'default' => null
            ])
            ->addIndex(['transfer_rule_id'])
            ->update();
    }

    public function down()
    {
        $this->table(static::TABLE)
            ->removeIndex(['transfer_rule_id'])
            ->update();
        $this->table(static::TABLE)
            ->removeColumn('transfer_rule_id')
            ->update();
    }
}

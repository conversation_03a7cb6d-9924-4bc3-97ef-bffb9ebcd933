<?php

use Phinx\Migration\AbstractMigration;

class AddCardIdToEmvAbonementListDeletedTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementListDeleted')
            ->addColumn('CardId', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор карты в ABTCardList',
                'after' => 'WriteOffsId',
            ])
            ->update();
    }
}

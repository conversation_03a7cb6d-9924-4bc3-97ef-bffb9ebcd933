<?php

use Phinx\Migration\AbstractMigration;

class ResizeCardListColumns extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardList')
            ->changeColumn('UIDHash', 'string', [
                'length' => 64,
                'comment' => 'SHA256 от UID карты',
                'null' => true,
            ])
            ->changeColumn('PANHash', 'string', [
                'length' => 64,
                'comment' => 'SHA256 от PAN карты',
                'default' => null,
                'null' => true,
            ])
            ->changeColumn('Status', 'smallinteger', [
                'comment' => 'Статус жизненного цикла карты',
                'null' => false,
            ])
            ->changeColumn('Type', 'smallinteger', [
                'comment' => 'Тип карты',
                'null' => false,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardList')
            ->changeColumn('UIDHash', 'string', [
                'length' => 128,
                'comment' => 'SHA256 от UID карты',
                'null' => true,
            ])
            ->changeColumn('PANHash', 'string', [
                'length' => 128,
                'comment' => 'SHA256 от PAN карты',
                'default' => null,
                'null' => true,
            ])
            ->changeColumn('Status', 'integer', [
                'comment' => 'Статус жизненного цикла карты',
                'null' => false,
            ])
            ->changeColumn('Type', 'integer', [
                'comment' => 'Тип карты',
                'null' => false,
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CashierRoleNameUpdate extends AbstractMigration
{
    public function up()
    {
        $this->query("update ReferenceList set RecordDescription='Кассир билетный' where ReferenceName='user-role' and RecordName='urTiketSeller'");
    }

    public function down()
    {
        $this->query("update ReferenceList set RecordDescription='Кассир, продавец билетов' where ReferenceName='user-role' and RecordName='urTiketSeller'");
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AbonementsNumberSaleAvailable extends AbstractMigration
{
    public function change()
    {
        $table = $this->table('EMVWriteOffsTemplate');
        $table->addColumn('AbonementsNumberSaleAvailable', 'integer', [
            'comment' => 'Количество абонементов, доступных к покупке в указанный период',
            'signed' => false,
            'null' => true,
            'default' => null,
            'after' => 'RuleList',
        ])->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddEmvWalletUploadRole extends AbstractMigration
{
    public function up()
    {
        $this->table('admin_role')->insert([
            [
                'role_title' => 'emv_wallet_upload',
                'role_description' => 'Пополнение кошелька',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()->delete('admin_role')
            ->whereInList('role_title', [
                'emv_wallet_upload',
            ])
            ->execute();
    }
}

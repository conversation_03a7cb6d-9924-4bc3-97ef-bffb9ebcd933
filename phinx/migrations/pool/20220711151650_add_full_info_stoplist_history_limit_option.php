<?php

use Phinx\Migration\AbstractMigration;

class AddFullInfoStoplistHistoryLimitOption extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'full_info_stoplist_history_limit',
                'description' => 'Лимит количества записей в истории стоп-листа',
                'value' => 20,
                'type' => 'integer',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()
            ->delete('options')
            ->whereInList('key', [
                'full_info_stoplist_history_limit',
            ])
            ->execute();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class UpdateBlockReasonInBlockedAbonements extends AbstractMigration
{
    public function up()
    {
        $this->getQueryBuilder()
            ->update('EMVAbonementList')
            ->set('BlockedReason', 3)
            ->where([
                'IsActive' => '0',
                'BlockedReason' => '0',
            ])
            ->execute();
    }

    public function down()
    {
    }
}

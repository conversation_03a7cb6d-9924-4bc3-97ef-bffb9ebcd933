<?php

use Phinx\Migration\AbstractMigration;

class TerminalRawTransactionIndex extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalRawTransaction` ADD INDEX `TerminalRawTransaction_IsParsed_idx` (`IsParsed`);');
        $this->query('ALTER TABLE `TerminalRawTransaction` ADD INDEX `TerminalRawTransaction_TerminalId_idx` (`TerminalId`);');
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalRawTransaction` DROP INDEX `TerminalRawTransaction_IsParsed_idx`;");
        $this->query("ALTER TABLE `TerminalRawTransaction` DROP INDEX `TerminalRawTransaction_TerminalId_idx`;");
    }
}

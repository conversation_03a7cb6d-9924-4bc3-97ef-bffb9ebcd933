<?php

use Phinx\Migration\AbstractMigration;

class AddTidParamToAllTerminalGroups extends AbstractMigration
{
    public function up()
    {
        $terminalGroups = $this->getQueryBuilder()
            ->select('*')
            ->from('TerminalGroup')
            ->execute()
            ->fetchAll('assoc');

        foreach ($terminalGroups as $terminalGroup) {
            $config = $terminalGroup['TemplateConfig'] ? json_decode($terminalGroup['TemplateConfig']) : null;

            if ($config === null || json_last_error() !== JSON_ERROR_NONE) {
                $this->log(
                    'warning',
                    'TerminalGroup #' . $terminalGroup['Id'] . ' has invalid TemplateConfig, skipping'
                );
                continue;
            }

            $groupHasTidParam = false;
            foreach ($config as $param) {
                if ($param->Name === 'EMV.OfflineTkpTerminalId') {
                    $groupHasTidParam = true;
                    break;
                }
            }

            if (!$groupHasTidParam) {
                $config[] = (object)[
                    'Name' => 'EMV.OfflineTkpTerminalId',
                    'Type' => '2',
                    'Title' => 'Параметр для привязки терминала к TID',
                    'Params' => (object)[
                        'Max' => '99999999999',
                        'Min' => '1'
                    ],
                    'Default' => ' ',
                    'Required' => 0,
                    'Description' => 'Параметр для привязки терминала к TID',
                    'RegularMask' => '',
                ];

                $this->getQueryBuilder()
                    ->update('TerminalGroup')
                    ->set(['TemplateConfig' => json_encode($config)])
                    ->where(['Id' => $terminalGroup['Id']])
                    ->execute();

                $this->log(
                    'info',
                    'EMV.OfflineTkpTerminalId param was added to TerminalGroup #' . $terminalGroup['Id']
                );
            }
        }
    }

    public function down()
    {
    }

    protected function log(string $level, string $message)
    {
        $colorModifier = null;

        switch ($level) {
            case 'error':
                $colorModifier = '[41m'; //Red background
                break;
            case 'warning':
                $colorModifier = '[43m'; //Yellow background
                break;
            case 'info':
                $colorModifier = '[44m'; //Blue background
                break;
            default:
        }

        if ($colorModifier) {
            echo chr(27) . '[0m' . chr(27) . $colorModifier;
        }

        echo '[' . $level . '] ' . $message;

        if ($colorModifier) {
            echo chr(27) . '[0m';
        }

        echo PHP_EOL;
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class ModifyContentHashIndexOnEmissionImportTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardListFileImport')
            ->removeIndexByName('ContentHash')
            ->addIndex(['ContentHash', 'WriteOffsId'], [
                'unique' => true,
                'name' => 'ContentHash',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardListFileImport')
            ->removeIndexByName('ContentHash')
            ->addIndex(['ContentHash'], ['unique' => true])
            ->update();
    }
}

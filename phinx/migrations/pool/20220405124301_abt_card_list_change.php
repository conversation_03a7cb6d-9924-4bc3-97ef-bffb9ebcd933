<?php

use Phinx\Migration\AbstractMigration;
use Phinx\Db\Adapter\MysqlAdapter;

class AbtCardListChange extends AbstractMigration
{
    public function up()
    {
        $tab =  $this->table('ABTCardList');
        $tab->removeIndex('WalletId')->save();

        $tab
            ->renameColumn('WalletId', 'PayToolId')
            ->addColumn('PayToolType', 'integer', ['after' => "PayToolId",'limit' => MysqlAdapter::INT_TINY,'comment' => 'Тип абонемента. 1 - абонеме0нт, 2 - тариф, 3 - кошелек','null' => false])
            ->addIndex('PayToolId')
            ->addIndex('PayToolType')
            ->addIndex(['PayToolId','PayToolType'], ['unique' => true])
            ->save();
    }
    public function down()
    {
        $tab =  $this->table('ABTCardList');
        $tab->removeIndex('PayToolId')
            ->removeIndex('PayToolType')
            ->removeIndex(['PayToolId','PayToolType'])
            ->save();

        $tab
            ->renameColumn('PayToolId', 'WalletId')
            ->removeColumn('PayToolType')
            ->addIndex('WalletId')
            ->save();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class TerminalTransactionTicketIdx extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalTransaction` ADD INDEX (`ticketSeries`);');
        $this->query('ALTER TABLE `TerminalTransaction` ADD INDEX (`ticketNumber`);');
    }

    public function down()
    {
        $this->query('ALTER TABLE `TerminalTransaction` DROP INDEX `ticketSeries`;');
        $this->query('ALTER TABLE `TerminalTransaction` DROP INDEX `ticketNumber`;');
    }
}

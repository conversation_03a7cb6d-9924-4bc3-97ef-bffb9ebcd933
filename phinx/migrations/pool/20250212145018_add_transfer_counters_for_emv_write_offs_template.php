<?php

use Phinx\Migration\AbstractMigration;

class AddTransferCountersForEmvWriteOffsTemplate extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('EMVWriteOffsTemplate')->hasColumn('TransferCounters')) {
            $this->table('EMVWriteOffsTemplate')
                ->addColumn('TransferCounters', 'boolean', [
                    'default' => false,
                    'comment' => 'Переносить остатки счетчиков поездок на новый месяц'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('EMVWriteOffsTemplate')->hasColumn('TransferCounters')) {
            $this->table('EMVWriteOffsTemplate')
                ->removeColumn('TransferCounters')
                ->update();
        }
    }
}

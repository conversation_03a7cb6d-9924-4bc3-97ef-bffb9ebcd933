<?php

use Phinx\Migration\AbstractMigration;

class AddKzhImportTypeToOldRecords extends AbstractMigration
{
    public function change()
    {
        if ($this->isMigratingUp()) {
            $this->getQueryBuilder()
                ->update('__kzh_imports')
                ->set('type', 'citizens')
                ->where([
                    'file_name' => 'citizens.csv',
                ])
                ->execute();

            $this->getQueryBuilder()
                ->update('__kzh_imports')
                ->set('type', 'uids')
                ->where([
                    'file_name' => 'UIDs.csv',
                ])
                ->execute();

            $this->getQueryBuilder()
                ->update('__kzh_imports')
                ->set('type', 'privileges')
                ->where([
                    'file_name' => 'privileges.csv',
                ])
                ->execute();
        }
    }
}

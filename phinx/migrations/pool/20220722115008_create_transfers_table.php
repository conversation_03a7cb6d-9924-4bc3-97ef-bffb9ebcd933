<?php

use Phinx\Migration\AbstractMigration;

class CreateTransfersTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('transfers', [
                'comment' => 'Список разрешённых пересадок',
                'id' => 'id',
            ])
            ->addColumn('matrix_number', 'integer', [
                'null' => false,
                'comment' => 'Номер пересадочной матрицы',
            ])
            ->addColumn('type', 'integer', [
                'null' => false,
                'comment' => 'Тип пересадки',
            ])
            ->addColumn('timeout', 'integer', [
                'null' => true,
                'comment' => 'Время действия пересадки (в минутах с первой поездки)',
            ])
            ->addColumn('coeff_k', 'decimal', [
                'null' => false,
                'default' => 1,
                'comment' => 'Коеффициент «k» (y = kx + b)',
                'precision' => 10,
                'scale' => 4,
            ])
            ->addColumn('coeff_b', 'decimal', [
                'null' => false,
                'default' => 0,
                'comment' => 'Коеффициент «b» (y = kx + b)',
                'precision' => 10,
                'scale' => 4,
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();
    }
}

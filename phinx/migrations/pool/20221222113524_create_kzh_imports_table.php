<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class CreateKzhImportsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('__kzh_imports')
            ->addColumn('connection_id', 'integer', [
                'null' => false,
                'comment' => 'Id ftp-соединения (таблица __kzh_connections)',
            ])
            ->addColumn('file_name', 'string', [
                'null' => false,
                'limit' => 255,
                'comment' => 'Название файла',
            ])
            ->addColumn('content', 'blob', [
                'null' => true,
                'length' => MysqlAdapter::BLOB_LONG,
                'limit' => MysqlAdapter::BLOB_LONG,
                'comment' => 'Содержимое файла',
            ])
            ->addColumn('modification_time', 'datetime', [
                'null' => false,
                'comment' => 'Дата и время последнего изменения файла',
            ])
            ->addColumn('records_number', 'integer', [
                'null' => true,
                'comment' => 'Количество записей в файле',
            ])
            ->addColumn('imported_number', 'integer', [
                'null' => true,
                'comment' => 'Количество импортированных записей',
            ])
            ->addColumn('log', 'text', [
                'null' => true,
                'comment' => 'Лог импорта',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();
    }
}

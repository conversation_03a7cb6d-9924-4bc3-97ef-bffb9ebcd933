<?php

use Phinx\Migration\AbstractMigration;

class AddNewReferenceFnMode extends AbstractMigration
{
    public function up()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'FNMode';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('FNMode', 0, 'fnNone', 'Нет фискализации'),
                       ('FNMode', 1, 'fnInternal', 'Фискализация в кассе'),
                       ('FNMode', 2, 'fnCloud', 'Облачная фискализация '),
                       ('FNMode', 3, 'fnGibrid', 'Гибридная фискализация');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'FNMode';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

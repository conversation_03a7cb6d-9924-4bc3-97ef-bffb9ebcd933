<?php

use Phinx\Migration\AbstractMigration;

class AddClaimsRoles extends AbstractMigration
{
    public const TABLE = 'admin_role';
    public const FIELD = 'role_title';
    public const VALUE = 'claims_manage';

    public function up()
    {
        $this->down();

        $this->table(self::TABLE)
            ->insert([[self::FIELD => self::VALUE, 'role_description' => 'Доступ в претензионный центр']])
            ->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->delete(self::TABLE)
            ->where([self::FIELD => self::VALUE])
            ->execute();
    }
}

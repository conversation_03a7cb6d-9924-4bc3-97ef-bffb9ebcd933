<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class AddColumnsToCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('PAR', 'string', [
                'length' => 32,
                'comment' => 'PAR карты',
                'default' => null,
                'null' => true,
                'after' => 'PANHash',
            ])
            ->addColumn('PARHash', 'string', [
                'length' => 128,
                'comment' => 'SHA256 от PAR карты',
                'default' => null,
                'null' => true,
                'after' => 'PAR',
            ])
            ->addColumn('Status', 'integer', [
                'comment' => 'Статус жизненного цикла карты',
                'null' => false,
                'after' => 'PARHash',
            ])
            ->addColumn('IsBlocked', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Карта заблокирована',
                'default' => 0,
                'null' => false,
                'after' => 'Status',
            ])
            ->addColumn('IsPersonalised', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Карта персонализирована',
                'default' => 0,
                'null' => false,
                'after' => 'IsBlocked',
            ])
            ->addColumn('Type', 'integer', [
                'comment' => 'Тип карты',
                'null' => false,
                'after' => 'IsPersonalised',
            ])
            ->addColumn('EmissionApplicationNumber', 'string', [
                'length' => 32,
                'comment' => 'Номер заявки на эмиссию',
                'default' => null,
                'null' => true,
                'after' => 'Type',
            ])
            ->addColumn('CreateDateTime', 'timestamp', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
                'after' => 'EmissionApplicationNumber',
            ])
            ->update();
    }
}

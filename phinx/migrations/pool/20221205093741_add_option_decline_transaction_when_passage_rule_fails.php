<?php

use Phinx\Migration\AbstractMigration;

class AddOptionDeclineTransactionWhenPassageRuleFails extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'decline_transaction_when_passage_rule_fails',
                'description' => 'Отклонять транзакцию, если правило прохода не пройдено',
                'value' => 0,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'decline_transaction_when_passage_rule_fails',
            ])
            ->execute();
    }
}

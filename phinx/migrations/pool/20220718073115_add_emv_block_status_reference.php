<?php

use Phinx\Migration\AbstractMigration;

class AddEmvBlockStatusReference extends AbstractMigration
{
    public function up()
    {
        $this->deleteRecords();

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => 0,
                'RecordName' => 'bsActive',
                'RecordDescription' => 'Активен',
            ],
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => -1,
                'RecordName' => 'bsDelete',
                'RecordDescription' => 'Удалить из стоп-листа',
            ],
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => 1,
                'RecordName' => 'bsBlock',
                'RecordDescription' => 'Заблокировать',
            ],
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => 2,
                'RecordName' => 'bsUsed',
                'RecordDescription' => 'Кончились поездки',
            ],
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => 3,
                'RecordName' => 'bsExpired',
                'RecordDescription' => 'Истёк срок действия',
            ],
            [
                'ReferenceName' => 'EMVBlockStatus',
                'RecordId' => 4,
                'RecordName' => 'bsNotStarted',
                'RecordDescription' => 'Не наступил срок действия',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRecords();
    }

    private function deleteRecords()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where([
                'ReferenceName' => 'EMVBlockStatus',
            ])
            ->execute();
    }
}

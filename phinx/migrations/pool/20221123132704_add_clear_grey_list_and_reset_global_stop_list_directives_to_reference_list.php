<?php

use Phinx\Migration\AbstractMigration;

class AddClearGreyListAndResetGlobalStopListDirectivesToReferenceList extends AbstractMigration
{
    public function up()
    {
        $this->deleteRecords();

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'TerminalDirectiveType',
                'RecordId' => 5,
                'RecordName' => 'tdClearGreyList',
                'RecordDescription' => 'Очистить локальный стоп-лист',
            ],
            [
                'ReferenceName' => 'TerminalDirectiveType',
                'RecordId' => 6,
                'RecordName' => 'tdResetGlobalStopList',
                'RecordDescription' => 'Сбросить версию глобального стоп-листа',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRecords();
    }

    private function deleteRecords()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where([
                'ReferenceName' => 'TerminalDirectiveType',
            ])
            ->whereInList('RecordName', [
                'tdClearGreyList',
                'tdResetGlobalStopList',
            ])
            ->execute();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIndexOnCardUidGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('golden_crown_current_balances');
        if (!$table->hasIndex('card_uid')) {
            $table->addIndex(['card_uid'], ['name' => 'idx_card_uid_current_balances'])->save();
        }
    }

    public function down()
    {
        $table = $this->table('golden_crown_current_balances');
        if ($table->hasIndex('card_uid')) {
            $table->removeIndexByName('idx_card_uid_current_balances')->save();
        }
    }
}

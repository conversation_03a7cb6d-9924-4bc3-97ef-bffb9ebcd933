<?php

use Phinx\Migration\AbstractMigration;

class TerminalTransactionWalletId extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `TerminalTransaction` ADD WalletId bigint(20) unsigned default NULL comment 'Ид кошелька, по которому была оплата' after AbonementId;");
        $this->query("ALTER TABLE `EMVWalletUpload` ADD IsAuto tinyint(1) not null default 0 comment 'Признак автоматического пополнения кошелька' after Status;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalTransaction` DROP `WalletId`;");
        $this->query("ALTER TABLE `EMVWalletUpload` DROP `IsAuto`;");
    }
}

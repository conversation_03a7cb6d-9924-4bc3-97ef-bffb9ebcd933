<?php

use Phinx\Migration\AbstractMigration;

class AddAfterBalanceInGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_current_balances')->hasColumn('after_balance')) {
            $this->table('golden_crown_current_balances')
                ->addColumn('after_balance', 'integer', [
                    'default' => 0,
                    'comment' => 'Баланс кошелька после'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_current_balances')->hasColumn('after_balance')) {
            $this->table('golden_crown_current_balances')
                ->removeColumn('after_balance')
                ->update();
        }
    }
}

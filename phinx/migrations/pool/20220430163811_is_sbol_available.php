<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class IsSbolAvailable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWriteOffsTemplate')
            ->addColumn('IsSbolAvailable', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Доступен для продажи в СБОЛ',
                'default' => 0,
                'null' => false,
                'after' => 'IsSocial',
            ])
            ->update();
    }
}

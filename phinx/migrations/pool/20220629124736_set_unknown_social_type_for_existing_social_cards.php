<?php

use Phinx\Migration\AbstractMigration;

class SetUnknownSocialTypeForExistingSocialCards extends AbstractMigration
{
    public function up()
    {
        $this->getQueryBuilder()
            ->update('ABTCardList')
            ->set('SocialType', 0)
            ->where([
                'IsSocial' => '1',
            ])
            ->whereNull('SocialType')
            ->execute();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->update('ABTCardList')
            ->set('SocialType', null)
            ->where([
                'IsSocial' => '1',
                'SocialType' => '0',
            ])
            ->execute();
    }
}

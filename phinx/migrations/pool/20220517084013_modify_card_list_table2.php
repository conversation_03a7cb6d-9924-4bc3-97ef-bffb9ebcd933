<?php

use Phinx\Migration\AbstractMigration;

class ModifyCardListTable2 extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('PersoData', 'json', [
                'default' => null,
                'null' => true,
                'comment' => 'Данные чипа карты',
                'after' => 'Type',
            ])
            ->addColumn('IsSocial', 'boolean', [
                'default' => false,
                'null' => false,
                'comment' => 'Социальная карта',
                'after' => 'IsPersonalised',
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class TerminalStat extends AbstractMigration
{
    public function Up()
    {
        $sql = "
                CREATE TABLE IF NOT EXISTS TerminalStat (
                 
                    `Id`                    BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Уникальный идентификатор',
                    `TerminalId`            BIGINT UNSIGNED NOT NULL COMMENT 'Идентификатор терминала',
                    `Lat`                   DECIMAL(20,16) NULL COMMENT 'Широта',
                    `Lon`                   DECIMAL(20,16) NULL COMMENT 'Долгота',   
                    `EditDateTime`          TIMESTAMP DEFAULT NOW() COMMENT 'Дата и время создания записи',
                    `StatPack`              JSON COMMENT 'Статистический пакет'   
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Мониторинг терминалов';
                ALTER TABLE TerminalStat ADD INDEX TerminalStat_TerminalId_idx(TerminalId);
                ALTER TABLE TerminalStat ADD INDEX TerminalStat_EditDateTime_idx(EditDateTime);
                ALTER TABLE TerminalStat ADD INDEX TerminalStat_G1_idx(TerminalId,EditDateTime);
                ";
        $this->query($sql);
        $sql = "COMMIT";
        $this->query($sql);
    }

    public function Down()
    {
        $sql = "DROP TABLE IF EXISTS TerminalStat;";
        $this->query($sql);
        $sql = "COMMIT";
        $this->query($sql);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddStationsTerminal extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalList');

        if (!$table->hasColumn('Stations')) {
            $table->addColumn('Stations', 'json', [
                'null' => true,
                'default' => null,
                'comment' => 'Станции, которые содержат маршруты, которые показывать в маршрутном задании',
            ]);
        }
        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalList');

        if ($table->hasColumn('Stations')) {
            $table->removeColumn('Stations');
        }
        $table->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateTagAddWorkTimeInStation extends AbstractMigration
{
    public function up()
    {
        $this->query('CREATE TABLE `tag` (
            `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT \'ID тэга\',
            `name` varchar(32) DEFAULT NULL COMMENT \'Текст тэга\'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=\'Тэги\';');

        $this->query('CREATE TABLE `object_tag` (
            `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT \'ID тэга\',
            `object_type` varchar(32) DEFAULT NULL COMMENT \'Сущность - название таблицы\',
            `object_id` int(11) NOT NULL COMMENT \'Идентификатор сущности\',
            `tag_id` int(11) NOT NULL COMMENT \'Идентификатор тэга\'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=\'Тэги разных сущностей\';');

        $this->query('ALTER TABLE `object_tag`
            ADD KEY `ObjectTag_ObjectType_idx` (`object_type`),
            ADD KEY `ObjectTag_ObjectId_idx` (`object_id`),
            ADD KEY `ObjectTag_TagId_idx` (`tag_id`)
            ;');

        $this->query('ALTER TABLE `station` ADD `work_time` VARCHAR(255) NULL DEFAULT NULL COMMENT \'Время работы\';');
    }

    public function down()
    {
        $this->query("DROP TABLE `object_tag`;");
        $this->query("DROP TABLE `tag`;");
        $this->query("ALTER TABLE `station` DROP `work_time`;");
    }
}

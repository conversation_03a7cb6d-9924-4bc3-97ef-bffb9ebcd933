<?php

use Phinx\Migration\AbstractMigration;
use Phinx\Db\Adapter\MysqlAdapter;

class EmvPriceModificator extends AbstractMigration
{
    public function up()
    {
        $tab =  $this->table('EMVWriteOffsTemplate');
        $tab
            ->addColumn('IsPriceModificatorEnable', 'integer', ['after' => "IsSocial",'limit' => MysqlAdapter::INT_TINY,'comment' => 'Включена модификация цен','default' => 0])
            ->save();
    }
    public function down()
    {
        $tab =  $this->table('IsPriceModificatorEnable');
        $tab
            ->removeColumn('IsPriceModificatorEnable')
            ->save();
    }
}

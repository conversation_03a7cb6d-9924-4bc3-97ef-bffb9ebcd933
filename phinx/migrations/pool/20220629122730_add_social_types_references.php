<?php

use Phinx\Migration\AbstractMigration;

class AddSocialTypesReferences extends AbstractMigration
{
    public function up()
    {
        $this->deleteRecords();

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 0,
                'RecordName' => 'stUnknown',
                'RecordDescription' => 'Тип неизвестен',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 1,
                'RecordName' => 'stFederal',
                'RecordDescription' => 'Федеральная льгота',
            ],
            [
                'ReferenceName' => 'SocialType',
                'RecordId' => 2,
                'RecordName' => 'stRegional',
                'RecordDescription' => 'Региональная льгота',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRecords();
    }

    private function deleteRecords()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where(['ReferenceName' => 'SocialType'])
            ->execute();
    }
}

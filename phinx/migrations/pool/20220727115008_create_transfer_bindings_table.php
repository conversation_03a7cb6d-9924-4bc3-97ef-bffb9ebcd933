<?php

use Phinx\Migration\AbstractMigration;

class CreateTransferBindingsTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('transfer_bindings', [
                'comment' => 'Маршруты и типы транспорта пересадочных матриц',
                'id' => 'id',
            ])
            ->addColumn('transfer_id', 'integer', [
                'null' => false,
                'comment' => 'id пересадки',
            ])
            ->addColumn('from_partner_id', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор партнёра маршрута первой поездки',
            ])
            ->addColumn('from_route_id', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор маршрута первой поездки',
            ])
            ->addColumn('to_partner_id', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор партнёра маршрута второй поездки',
            ])
            ->addColumn('to_route_id', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор маршрута второй поездки',
            ])
            ->addColumn('from_transport_type', 'text', [
                'null' => true,
                'comment' => 'Тип транспорта для первой поездки (рефлист «TransportType»)',
            ])
            ->addColumn('to_transport_type', 'text', [
                'null' => true,
                'comment' => 'Тип транспорта для второй поездки (рефлист «TransportType»)',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();

        $this->table('transfer_bindings')
            ->addIndex([
                'from_partner_id',
                'to_partner_id',
            ], [
                'name' => 'partner_id',
            ])->update();
    }
}

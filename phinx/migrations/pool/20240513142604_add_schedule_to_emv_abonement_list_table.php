<?php

use Phinx\Migration\AbstractMigration;

class AddScheduleToEmvAbonementListTable extends AbstractMigration
{
    protected const TABLE = 'EMVAbonementList';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('Schedule', 'json', [
                'comment' => 'Временные интервалы',
                'null' => true,
                'default' => null,
                'after' => 'TransferredTo',
            ])
            ->update();
    }

    public function down()
    {
        if ($this->table(static::TABLE)->hasColumn('Schedule')) {
            $this->table(static::TABLE)
                ->removeColumn('Schedule')
                ->update();
        }
    }
}

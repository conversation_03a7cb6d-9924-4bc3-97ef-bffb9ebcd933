<?php

use Phinx\Migration\AbstractMigration;

class ModifyEmvManageRoles extends AbstractMigration
{
    public function up()
    {
        $existing = $this->fetchAll('SELECT `user_id` FROM `admin_xref_user_role` WHERE `role_title` = "emv_manage";');

        $this->query('UPDATE `admin_role` SET `role_description` = "Управление шаблонами", `role_title` = "emv_template_manage" WHERE `role_title` = "emv_manage";');
        $this->query('UPDATE `admin_xref_user_role` SET `role_title` = "emv_template_manage" WHERE `role_title` = "emv_manage";');

        $this->query('
            INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES 
                ("emv_template_view", "Просмотр шаблонов"),
                ("emv_abonement_wallet_view", "Просмотр абонементов"),
                ("emv_abonement_wallet_manage", "Управление абонементами");
        ');

        $this->query('UPDATE `admin_xref_user_role` SET `role_title` = "emv_template_manage" WHERE `role_title` = "emv_manage";');

        // Админам, у которых уже было право «Управление абонементами», добавляем все права
        $rowsToInsert = [];
        foreach ($existing as $xref) {
            $rowsToInsert[] = '(' . $xref['user_id'] . ', "emv_template_view")';
            $rowsToInsert[] = '(' . $xref['user_id'] . ', "emv_abonement_wallet_view")';
            $rowsToInsert[] = '(' . $xref['user_id'] . ', "emv_abonement_wallet_manage")';
        }

        if (!empty($rowsToInsert)) {
            $this->query('INSERT INTO `admin_xref_user_role` (`user_id`, `role_title`) VALUES ' . implode(', ', $rowsToInsert));
        }
    }

    public function down()
    {
        $this->query('UPDATE `admin_role` SET `role_description` = "Управление абонементами", `role_title` = "emv_manage" WHERE `role_title` = "emv_template_manage";');
        $this->query('UPDATE `admin_xref_user_role` SET `role_title` = "emv_manage" WHERE `role_title` = "emv_template_manage";');

        $this->query('DELETE FROM `admin_role` WHERE role_title IN ("emv_template_view", "emv_abonement_wallet_view", "emv_abonement_wallet_manage");');
        $this->query('DELETE FROM `admin_xref_user_role` WHERE role_title IN ("emv_template_view", "emv_abonement_wallet_view", "emv_abonement_wallet_manage");');
    }
}

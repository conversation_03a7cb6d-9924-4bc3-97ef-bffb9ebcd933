<?php

use Phinx\Migration\AbstractMigration;

class A150StopList extends AbstractMigration
{
    public function up()
    {
        $this->query("
            DROP TABLE  IF EXISTS EMVWalletStopList;
        ");

        $this->query("
                CREATE TABLE IF NOT EXISTS EMVWalletStopList (
                   `Id`                    BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Ключ записи',
                   `PANHash`               VARCHAR(128) NOT NULL COMMENT 'PAN hash номера карты',
                   `IsBlocked`             BOOLEAN NOT NULL DEFAULT false COMMENT 'Признак, что карта блокирована',
                   `IsActual`              BOOLEAN NOT NULL DEFAULT true  COMMENT 'Признак, что статус карты актуален',
                   `CreateDateTime`        TIMESTAMP NOT NULL DEFAULT  CURRENT_TIMESTAMP COMMENT 'Дата и время создания записи'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci comment 'Таблица для хранения стоп-листа по картам кошельки которых не удалось пополнить';
        ");
        $this->query("
            ALTER TABLE `EMVWalletStopList` 
                 ADD INDEX (IsActual),
                 ADD INDEX (IsBlocked),
                 ADD INDEX (PANHash),
                 ADD INDEX (PANHash,IsActual);");

        $this->query("
            DROP TABLE  IF EXISTS EMVWalletTransaction;
        ");

        $this->query("
             create table EMVWalletTransaction  (
                Id                      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Ключ записи',
                PANHash                 VARCHAR(64) NOT NULL comment 'PANHash карты',
                TransactionId           BIGINT UNSIGNED UNIQUE COMMENT 'Идентификатор тарифицированной транзакции',
                WalletId                BIGINT UNSIGNED NOT NULL comment 'Идентификатор кошелька примененного к транзакции',
                BalanceBefore           DECIMAL (10,3) NOT NULL comment 'Баланс кошелька до тарификации',
                BalanceAfter            DECIMAL (10,3) NOT NULL comment 'Баланс кошелька после тарификации',
                SourceTransaction       JSON default null comment 'Иходная транзакция',
                TerminalId              BIGINT UNSIGNED NOT NULL comment 'Идентификатор терминала на котором была проведена транзакция',
                VendorId                BIGINT UNSIGNED NOT NULL comment 'Идентификатор вендора к которому относится терминал',
                TerminalOrganizationId  BIGINT UNSIGNED NOT NULL comment 'Идентификатор организации к которой относится терминал',
                TransporterName         VARCHAR(255) comment 'Наименование перевозчика, осуществившего перевозку',
                RouteName               VARCHAR(255) comment 'Наименование маршрута',
                RouteNumber             VARCHAR(32) comment 'Номер маршрута',
                TransportType           INT UNSIGNED not null comment 'Тип транспорта',
                TripDateTime            TIMESTAMP NOT NULL comment 'Дата и время поездки',
                TarifficationDateTime   TIMESTAMP default now() comment 'Дата и время тарификации'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci comment 'Тарифицированные транзакции по кошелькам';
        ");

        $this->query("        
                ALTER TABLE EMVWalletTransaction 
                    ADD INDEX (PANHash),
                    ADD INDEX (TransactionId),
                    ADD INDEX (WalletId);");
    }


    public function down()
    {
        $this->query("
            DROP TABLE  IF EXISTS EMVWalletStopList;
        ");

        $this->query("
            DROP TABLE  IF EXISTS EMVWalletTransaction;
        ");
    }
}

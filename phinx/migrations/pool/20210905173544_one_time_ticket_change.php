<?php

use Phinx\Migration\AbstractMigration;

class OneTimeTicketChange extends AbstractMigration
{
    public function change()
    {
        $tabOTT =  $this->table('OneTimeTicketSPB', ['signed' => false,'id' => 'Id','comment' => 'Список выпущенных QR для оплаты проезда']);
        $tabOTT
            ->addColumn('Code', 'string', ['length' => 512,'comment' => 'тело сгенерированного QR'])
            ->addColumn('Price', 'integer', ['signed' => false, 'comment' => 'стоимость в копейках'])
            ->addColumn('StartDate', 'timestamp', ['default' => null , 'null' => true,'comment' => 'Начало срока действия талона'])
            ->addColumn('EndDate', 'timestamp', ['default' => null , 'null' => true,'comment' => 'Конец срока действия талона'])
            ->addColumn('Version', 'string', ['length' => 4,'default' => '0101','comment' => 'Версия сгенерированного QR'])
            ->addColumn('Type', 'string', ['length' => 2,'default' => '01','comment' => 'Способ использования талона'])
            ->addColumn('Status', 'smallinteger', ['signed' => false, 'default' => 0,'comment' => 'Статус талона'])
            ->addColumn('IssueVendorId', 'integer', ['signed' => false, 'comment' => 'Ид партнера, который выпустил талон'])
            ->addColumn('IssueTerminalId', 'integer', ['signed' => false, 'comment' => 'Ид терминала, с которого выпустили талон'])
            ->addColumn('IssueTerminalUserId', 'integer', ['signed' => false, 'comment' => 'Ид пользователя терминала, который выпустил талон'])
            ->addColumn('IssueDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP','comment' => 'Дата выпуска талона'])
            ->addColumn('RedeemVendorId', 'integer', ['default' => null , 'null' => true,'signed' => false, 'comment' => 'Ид партнера, который погасил талон'])
            ->addColumn('RedeemTerminalId', 'integer', ['default' => null , 'null' => true,'signed' => false, 'comment' => 'Ид терминала, с которого погасили талон'])
            ->addColumn('RedeemTerminalUserId', 'integer', ['default' => null , 'null' => true,'signed' => false, 'comment' => 'Ид пользователя терминала, который погасил талон'])
            ->addColumn('RedeemDateTime', 'timestamp', ['default' => null , 'null' => true,'comment' => 'Дата гашения талона'])
            ->addColumn('Transaction', 'json', ['default' => null,'null' => true,'comment' => 'Транзакция, для оплаты которой был применен талон'])

            ->addIndex('Code')
            ->addIndex('Status')
            ->addIndex('IssueVendorId')
            ->addIndex('IssueTerminalId')
            ->addIndex('IssueTerminalUserId')
            ->addIndex('IssueDateTime')
            ->addIndex('RedeemVendorId')
            ->addIndex('RedeemTerminalId')
            ->addIndex('RedeemTerminalUserId')
            ->addIndex('RedeemDateTime')
            ->create();

        $tabVector = $this->table('OneTimeTicketSPBVectorList', ['signed' => false,'id' => 'Id','comment' => 'Список векторов для генерации QR кодов']);
        $tabVector
            ->addColumn('QRVector', 'string', ['length' => 256,'comment' => 'Вектор'])
            ->addColumn('StartDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP','comment' => 'Дата и время начала действия вектора'])
            ->addColumn('EndDateTime', 'timestamp', ['default' => null, 'null' => true,'comment' => 'Дата и время окончания действия вектора. NULL - бессрочный'])
            ->addColumn('CreateDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP','comment' => 'Дата и время создания записи'])
            ->addIndex('StartDateTime')
            ->addIndex('EndDateTime')
            ->create();


        if ($this->isMigratingUp()) {
            $tabVector->insert([['Id' => 1, 'QRVector' => sha1('porebrik')]])
                ->save();
        } else {
            $this->execute('DELETE FROM OneTimeTicketSPBVectorList WHERE Id = 1');
        }
    }
}

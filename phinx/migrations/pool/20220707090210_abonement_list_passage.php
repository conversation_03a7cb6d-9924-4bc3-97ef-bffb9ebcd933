<?php

use Phinx\Migration\AbstractMigration;

class AbonementListPassage extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementList')
            ->addColumn('Passage', 'json', [
                'comment' => 'Правила прохода на момент создания абонемента',
                'null' => true,
            ])
            ->update();

        if ($this->isMigratingUp()) {
            $abonements = $this->fetchAll("select EMVAbonementList.*,EMVWriteOffsTemplate.RuleList from EMVAbonementList
    join EMVWriteOffsTemplate on EMVWriteOffsTemplate.Id=EMVAbonementList.WriteOffsId");
            foreach ($abonements as $abonement) {
                if (!empty($abonement["RuleList"])) {
                    $parsed = json_decode($abonement["RuleList"]);
                    if (!empty($parsed) && !empty($parsed->Passage)) {
                        $this->query("update EMVAbonementList
set Passage='" . json_encode($parsed->Passage) . "' where AbonementId=" . $abonement["AbonementId"] . ";");
                    }
                }
            }
        }
    }
}

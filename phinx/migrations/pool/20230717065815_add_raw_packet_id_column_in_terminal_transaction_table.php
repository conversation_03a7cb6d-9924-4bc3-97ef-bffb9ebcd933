<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddRawPacketIdColumnInTerminalTransactionTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'TerminalTransaction';
    private const COLUMN_NAME_VALUE = 'rawPacketId';

    public function up(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);

        if (!$table->hasColumn(self::COLUMN_NAME_VALUE)) {
            $table->addColumn(self::COLUMN_NAME_VALUE, 'biginteger', [
                'null' => true,
                'signed' => false,
            ]);
        }

        $table->update();
    }

    public function down(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);

        if ($table->hasColumn(self::COLUMN_NAME_VALUE)) {
            $table->removeColumn(self::COLUMN_NAME_VALUE);
        }

        $table->update();
    }
}

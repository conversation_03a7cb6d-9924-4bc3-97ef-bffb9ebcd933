<?php

use Phinx\Migration\AbstractMigration;

class AddBalanceColumnInGoldenGrownTransactionsTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'golden_crown_transactions';
    private const COLUMN_NAME_COLUMN_VALUE = 'balance';

    public function up(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);
        if (!$table->hasColumn(self::COLUMN_NAME_COLUMN_VALUE)) {
            $table->addColumn(self::COLUMN_NAME_COLUMN_VALUE, 'integer', [
                'null' => true,
                'signed' => false,
            ]);
            $table->save();
        }
    }

    public function down(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);
        if ($table->hasColumn(self::COLUMN_NAME_COLUMN_VALUE)) {
            $table->removeColumn(self::COLUMN_NAME_COLUMN_VALUE);
            $table->save();
        }
    }
}

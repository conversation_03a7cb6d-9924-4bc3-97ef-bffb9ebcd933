<?php

use Phinx\Migration\AbstractMigration;

class RemoveBillingRules extends AbstractMigration
{
    public function up()
    {
        // Удаляем таблицу billing_rules
        if ($this->hasTable('billing_rules')) {
            $this->table('billing_rules')->drop()->save();
        }

        // Удаляем роль billing_rules_manage
        $this->execute("DELETE FROM admin_role WHERE role_id = 'billing_rules_manage'");
        $this->execute("DELETE FROM admin_xref_user_role WHERE role_id = 'billing_rules_manage'");
    }

    public function down()
    {
        // Восстанавливаем роль billing_rules_manage
        $this->execute("INSERT INTO admin_role (role_id, title) VALUES ('billing_rules_manage', 'Управления наценками в сочетании Агент - Вендор')");

        // Восстанавливаем таблицу billing_rules
        $table = $this->table('billing_rules', ['id' => false, 'primary_key' => ['id']]);
        $table->addColumn('id', 'integer', ['limit' => 20, 'signed' => false, 'null' => false, 'identity' => true])
              ->addColumn('agent_id', 'integer', ['limit' => 10, 'signed' => false, 'null' => false])
              ->addColumn('vendor_id', 'integer', ['limit' => 10, 'signed' => false, 'null' => false])
              ->addColumn('enabled_service_markup', 'integer', ['limit' => 1, 'signed' => false, 'null' => true, 'comment' => 'Разрешен ли сервисный сбор у вендора'])
              ->addColumn('unitiki_price_markup', 'decimal', ['precision' => 10, 'scale' => 4, 'signed' => false, 'null' => true, 'comment' => 'Сервисный сбор Unitiki'])
              ->addColumn('unitiki_price_markup_fix', 'decimal', ['precision' => 10, 'scale' => 2, 'signed' => false, 'null' => true, 'comment' => 'Сервисный сбор Unitiki (фикс)'])
              ->addColumn('reseller_agent_commission', 'decimal', ['precision' => 10, 'scale' => 4, 'signed' => false, 'null' => true, 'comment' => 'Агентское вознаграждение реселлера'])
              ->addColumn('reseller_price_max', 'decimal', ['precision' => 10, 'scale' => 4, 'signed' => false, 'null' => true, 'comment' => 'Максимальный размер сервисного сбора реселлера'])
              ->addIndex(['agent_id', 'vendor_id'], ['unique' => true])
              ->create();
    }
}

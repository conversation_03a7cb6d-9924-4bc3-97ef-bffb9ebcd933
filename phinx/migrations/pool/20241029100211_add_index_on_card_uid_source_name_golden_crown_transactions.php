<?php

use Phinx\Migration\AbstractMigration;

class AddIndexOnCardUidSourceNameGoldenCrownTransactions extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('golden_crown_transactions');
        if (!$table->hasIndex(['card_uid', 'source_name'])) {
            $table->addIndex(['card_uid', 'source_name'], ['name' => 'idx_card_uid_source_name'])->save();
        }
    }

    public function down()
    {
        $table = $this->table('golden_crown_transactions');
        if ($table->hasIndex(['card_uid', 'source_name'])) {
            $table->removeIndexByName('idx_card_uid_source_name')->save();
        }
    }
}

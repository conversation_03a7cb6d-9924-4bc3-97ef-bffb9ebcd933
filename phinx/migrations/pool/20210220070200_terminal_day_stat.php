<?php

use Phinx\Migration\AbstractMigration;

class Terminal<PERSON><PERSON><PERSON><PERSON> extends AbstractMigration
{
    public function up()
    {
        $this->query('CREATE TABLE `TerminalDayStat` ( `TerminalId` BIGINT UNSIGNED NOT NULL COMMENT \'терминал\' , `Date` DATE NOT NULL COMMENT \'дата логирования\' , `ConfigFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первое обращение за конфигом\' , `ConfigLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последнее обращение за конфигом\' , `ConfigCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во обращений за конфигом\' , `HeartbeatFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первый пинг за сутки\' , `HeartbeatLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последний пинг за сутки\' , `HeartbeatCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во пингов\', `RawDataFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первая отправка сырых данных\' , `RawDataLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последняя отправка сырых данных\' , `RawDataCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во отправок сырых данных\' , PRIMARY KEY (`TerminalId`,`Date`)) ENGINE = InnoDB COMMENT = \'История обращений терминала за сутки\';');
        $this->query('CREATE TABLE `TerminalUserDayStat` ( `TerminalId` BIGINT UNSIGNED NOT NULL COMMENT \'терминал\' , `TerminalUserId` INT NOT NULL COMMENT \'пользователь\' , `Date` DATE NOT NULL COMMENT \'дата логирования\' , `UserTaskFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первое получение задания\' , `UserTaskLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последнее получение задания\' , `UserTaskCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во получений задания\' , `ShiftOpenFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первое открытие смены\' , `ShiftOpenLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последнее открытие смены\' , `ShiftOpenCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во открытий смены\' , `ShiftCloseFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первое закрытие смены\' , `ShiftCloseLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последнее закрытие смены\' , `ShiftCloseCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во закрытий смены\' , `TripFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первая поездка за день(TT,T1)\' , `TripLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последняя поездка за день\' , `TripCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во поездок\' , `TripSumm` DECIMAL(10,2) NOT NULL DEFAULT \'0\' COMMENT \'сумма поездок\' , `ReturnFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первый возврат за день (TR)\' , `ReturnLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последний возврат за день\' , `ReturnCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во возвратов\' , `ReturnSumm` DECIMAL(10,2) NOT NULL DEFAULT \'0\' COMMENT \'сумма возвратов\' , `AllTransactionFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первая любая транзакция\' , `AllTransactionLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последняя любая транзакция\' , `AllTransactionCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во транзакций за день\' , PRIMARY KEY (`TerminalId`,`TerminalUserId`,`Date`)) ENGINE = InnoDB;');
    }

    public function down()
    {
        $this->query('DROP TABLE `TerminalDayStat`;');
        $this->query('DROP TABLE `TerminalUserDayStat`;');
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddBlockStatusToAbonementListDeleted extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementListDeleted')
            ->addColumn('BlockStatus', 'integer', [
                'default' => 0,
                'limit' => 1,
                'after' => 'IsActive',
                'null' => false,
                'comment' => 'Тип блокировки карты (рефлист «EMVBlockStatus»)',
            ])
            ->save();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddInternalNameColumnInEmvWriteOffsTemplateScheduleTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('emv_write_offs_template_schedule');

        if (!$table->hasColumn('internal_name')) {
            $table->addColumn('internal_name', 'string', [
                'null' => true,
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->table('emv_write_offs_template_schedule');

        if ($table->hasColumn('internal_name')) {
            $table->removeColumn('internal_name');
        }

        $table->update();
    }
}

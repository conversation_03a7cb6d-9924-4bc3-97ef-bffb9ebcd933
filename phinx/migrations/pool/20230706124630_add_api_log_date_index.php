<?php

use Phinx\Migration\AbstractMigration;

class AddApiLogDateIndex extends AbstractMigration
{
    protected const TABLE_NAME = '_api_log';
    protected const INDEX_NAME = 'idx___api_log__date';
    protected const COLUMN_NAME = 'date';

    public function up()
    {
        $table = $this->table(self::TABLE_NAME);

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $this->table(self::TABLE_NAME)
                ->addIndex(
                    [
                    self::COLUMN_NAME,
                    ],
                    [
                        'name' => self::INDEX_NAME,
                    ]
                )
                ->update();
        }
    }

    public function down()
    {
        $table = $this->table(self::TABLE_NAME);

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME)->update();
        }
    }
}

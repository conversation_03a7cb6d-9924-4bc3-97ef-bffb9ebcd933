<?php

use Phinx\Migration\AbstractMigration;

class AddIgnoreWhenFormingStopListColumnToTerminalTransactionsTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalTransaction');

        if (!$table->hasColumn('IgnoreWhenFormingStopList')) {
            $table->addColumn('IgnoreWhenFormingStopList', 'boolean', [
                'null' => false,
                'default' => false,
                'comment' => 'Признак того, что транзакция не должна учитываться при формировании стоп-листа',
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalTransaction');

        if ($table->hasColumn('IgnoreWhenFormingStopList')) {
            $table->removeColumn('IgnoreWhenFormingStopList');
        }

        $table->update();
    }
}

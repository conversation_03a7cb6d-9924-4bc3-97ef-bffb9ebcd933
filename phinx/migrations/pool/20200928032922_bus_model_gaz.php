<?php

use Phinx\Migration\AbstractMigration;

class BusModelGaz extends AbstractMigration
{
    public function up()
    {
        $sql = "
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('bus-brand', 61, 'bbGA<PERSON>', 'ГАЗ');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'bus-brand' and RecordName='bbGAZ';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIndexOnCardUidGoldenCrownTransactions extends AbstractMigration
{
    public function up()
    {
        // Проверка на существование индекса перед его добавлением
        $table = $this->table('golden_crown_transactions');
        if (!$table->hasIndex('card_uid')) {
            $table->addIndex(['card_uid'], ['name' => 'idx_card_uid_transactions'])->save();
        }
    }

    public function down()
    {
        // Удаляем индекс, если он существует
        $table = $this->table('golden_crown_transactions');
        if ($table->hasIndex('card_uid')) {
            $table->removeIndexByName('idx_card_uid_transactions')->save();
        }
    }
}

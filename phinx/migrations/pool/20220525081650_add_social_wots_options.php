<?php

use Phinx\Migration\AbstractMigration;

class AddSocialWotsOptions extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'wot_list_only_non_social_wots_if_card_not_specified',
                'description' => 'В списке шаблонов списания, если не передан PANHash карты, скрыть социальные шаблоны',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
            [
                'key' => 'wot_list_social_wots_only_for_social_card_if_card_specified',
                'description' => 'В списке шаблонов списания, если передан PANHash карты, скрыть социальные шаблоны, если карта не является социальной',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
            [
                'key' => 'wallet_money_transfer_personalized_cards_only',
                'description' => 'Разрешить перенос средств только между персонализированными кошельками',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'wot_list_only_non_social_wots_if_card_not_specified',
                'wot_list_social_wots_only_for_social_card_if_card_specified',
                'wallet_money_transfer_personalized_cards_only',
            ])
            ->execute();
    }
}

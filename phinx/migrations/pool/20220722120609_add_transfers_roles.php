<?php

use Phinx\Migration\AbstractMigration;

class AddTransfersRoles extends AbstractMigration
{
    public function up()
    {
        $this->deleteRoles();

        $this->table('admin_role')->insert([
            [
                'role_title' => 'transfers_view',
                'role_description' => 'Просмотр пересадок',
            ],
            [
                'role_title' => 'transfers_manage',
                'role_description' => 'Редактирование пересадок',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRoles();
    }

    private function deleteRoles()
    {
        $this->getQueryBuilder()->delete('admin_role')
            ->whereInList('role_title', [
                'transfers_view',
                'transfers_manage',
            ])
            ->execute();
    }
}

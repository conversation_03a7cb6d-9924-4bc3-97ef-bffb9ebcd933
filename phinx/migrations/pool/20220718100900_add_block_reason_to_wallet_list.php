<?php

use Phinx\Migration\AbstractMigration;

class AddBlockReasonToWalletList extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('BlockedReason', 'integer', [
                'default' => null,
                'after' => 'BlockStatus',
                'null' => true,
                'comment' => 'Причина блокировки. Рефлист «StopListReason»',
            ])
            ->save();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class TerminalStatChange extends AbstractMigration
{
    public function up()
    {
        $this->query("
                ALTER TABLE `TerminalStat` 
                    ADD `Status` TINYINT UNSIGNED DEFAULT 0 COMMENT 'Статус обработки статистического пакета данных' AFTER `StatPack`,
                    ADD `Result` JSON NULL DEFAULT NULL COMMENT 'Статус обработки статистического пакета данных' AFTER `Status`
                    ;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalStat` DROP `Status`, DROP `Result`;");
    }
}

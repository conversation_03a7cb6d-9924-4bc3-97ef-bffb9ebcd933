<?php

use Phinx\Migration\AbstractMigration;

class AddPassageColumnToWalletsTable extends AbstractMigration
{
    protected const TABLE = 'EMVWalletList';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('Passage', 'json', [
                'comment' => 'Правила прохода на момент создания кошелька',
                'null' => true,
            ])
            ->update();

        $templates = $this->getQueryBuilder()
            ->select([
                'Id',
                'RuleList',
            ], true)
            ->from('EMVWriteOffsTemplate')
            ->execute()
            ->fetchAll('assoc');

        foreach ($templates as $template) {
            if (!empty($template['RuleList'])) {
                $parsed = json_decode($template['RuleList']);

                if (!empty($parsed) && !empty($parsed->Passage)) {
                    $passageString = json_encode($parsed->Passage);

                    $this->getQueryBuilder()
                        ->update(static::TABLE)
                        ->set('Passage', $passageString)
                        ->where([
                            'WriteOffsId' => $template['Id'],
                        ])
                        ->execute();
                }
            }
        }
    }

    public function down()
    {
        $this->table(static::TABLE)
            ->removeColumn('Passage')
            ->update();
    }
}

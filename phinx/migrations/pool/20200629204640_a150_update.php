<?php

use Phinx\Migration\AbstractMigration;

class A150Update extends AbstractMigration
{
    public function up()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVWriteOffsType' and RecordName = 'woWallet';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
        $this->query("
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
				        ('EMVWriteOffsType', '3', 'woWallet', 'EMV кошелек');
        ");

        $this->query("
            DROP TABLE  IF EXISTS EMVWalletList;
        ");

        $this->query("
                create table EMVWalletList
                    (
                        WalletId                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Ключ записи',
                        WriteOffsId             INT UNSIGNED NOT NULL comment 'Идентификатор правила списания',
                        Name                    VARCHAR(64) null comment 'Название',
                        Description             VARCHAR(256) null comment 'Расширенное описание',
                        AgentId                 INT UNSIGNED NOT NULL comment 'Идентификатор агента, активировавшего кошелек',
                        PANHash                 VARCHAR(64) NOT NULL comment 'PANHash карты',
                        IsActive                TINYINT NOT NULL comment 'Признак, что кошелек активен',
                        Balance                 DECIMAL (10,3) default NULL comment 'Баланс кошелька в установленной валюте',
                        IsAuto                  TINYINT NULL NULL comment 'Признак, что кошелек автоматически пополняется',
                        LowLimit                DECIMAL (10,3) default NULL comment 'Уровень баланса при котором срабатывает автоматическое пополнение кошелька',
                        HiLimit                 DECIMAL (10,3) default NULL comment 'Уровень баланса до которого автоматически пополняется кошелек',
                        LastTripDateTime 	    TIMESTAMP null default NULL COMMENT 'Дата и время последней зарегистрированной поездки',
                        ValidTimeType           tinyint default 1 comment 'Тип срока действия(EMVValidType)',
                        ValidTimeStart          timestamp default NULL null comment 'Начало действия',
                        ValidTimeDays           INT default 0 comment 'Период действия(дней)',
                        ValidTimeEnd            timestamp default NULL null comment 'Окончание действия(расчетный срок)',
                        SellPrice               decimal (10,3) default NULL comment 'Стоимость продажи в установленной валюте',
                        CurrencyId              INT UNSIGNED default 1 comment 'Валюта продажи',
                        SellDateTime            TIMESTAMP default NOW() comment 'Дата и время продажи'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci comment 'Список проданных абонементов';
        ");

        $this->query(
            "
            ALTER TABLE `EMVWalletList` 
                 ADD INDEX (PANHash),
                 ADD INDEX (PANHash,IsActive),
                 ADD INDEX (PANHash,ValidTimeStart,ValidTimeEnd),
                 ADD INDEX (IsActive,ValidTimeEnd);"
        );
    }



    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVWriteOffsType' and RecordName = 'woWallet';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
        $this->query("
            DROP TABLE  IF EXISTS EMVWalletList;
        ");
    }
}

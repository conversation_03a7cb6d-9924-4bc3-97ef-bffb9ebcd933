<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class EmvXrefTemplateAgent extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('emv_xref_template_agent', [
                'id' => 'id',
                'comment' => 'Агенты для шаблонов абонементов',
            ])
            ->addColumn('template_id', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор шаблона',
            ])
            ->addColumn('agent_id', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор агента',
            ])
            ->addColumn('is_deleted', 'boolean', [
                'null' => true,
                'default' => '0',
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Признак удаления',
            ])
            ->create();
    }
}

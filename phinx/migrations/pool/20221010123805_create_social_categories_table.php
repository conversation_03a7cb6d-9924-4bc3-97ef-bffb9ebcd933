<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class CreateSocialCategoriesTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('social_categories', [
                'comment' => 'Список социальных категорий',
                'id' => 'id',
            ])
            ->addColumn('social_type', 'integer', [
                'null' => false,
                'comment' => 'Тип льготы (рефлист «SocialType»)',
            ])
            ->addColumn('name', 'text', [
                'null' => false,
                'comment' => 'Название',
                'limit' => MysqlAdapter::TEXT_REGULAR,
            ])
            ->addColumn('short_name', 'string', [
                'null' => false,
                'comment' => 'Короткое название',
                'limit' => 50,
            ])
            ->addColumn('priority', 'integer', [
                'null' => false,
                'default' => 0,
                'comment' => 'Приоритет (чем больше, тем выше) для отчётности',
            ])
            ->addTimestamps()
            ->create();
    }
}

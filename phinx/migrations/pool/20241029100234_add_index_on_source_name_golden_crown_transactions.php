<?php

use Phinx\Migration\AbstractMigration;

class AddIndexOnSourceNameGoldenCrownTransactions extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('golden_crown_transactions');
        if (!$table->hasIndex('source_name')) {
            $table->addIndex(['source_name'], ['name' => 'idx_source_name'])->save();
        }
    }

    public function down()
    {
        $table = $this->table('golden_crown_transactions');
        if ($table->hasIndex('source_name')) {
            $table->removeIndexByName('idx_source_name')->save();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class IsSaleServiceForBankCard extends AbstractMigration
{
    public function change()
    {
        if ($this->isMigratingUp()) {
            $options = $this->table('options');
            $options->insert([
                [
                    "key" => "is_sale_service_for_bank_card",
                    "value" => "0"
                ]
            ])->saveData();
        } else {
            $this->execute("DELETE FROM options WHERE `key` in ('is_sale_service_for_bank_card') ");
        }
    }
}

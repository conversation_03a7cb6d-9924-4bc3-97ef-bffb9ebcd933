<?php

use Phinx\Migration\AbstractMigration;

class AddSourceNameAndPassDateColumnsIndexInGoldenCrownTransactionsTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'golden_crown_transactions';
    private const INDEX_NAME_VALUE = 'idx_source_pass_date';

    public function up()
    {
        if (!$this->table(self::TABLE_NAME_VALUE)->hasIndexByName(self::INDEX_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)
                ->addIndex(['source_name', 'pass_date'], ['name' => self::INDEX_NAME_VALUE])
                ->update()
            ;
        }
    }

    public function down()
    {
        if ($this->table(self::TABLE_NAME_VALUE)->hasIndexByName(self::INDEX_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)
                ->removeIndexByName(self::INDEX_NAME_VALUE)
                ->update()
            ;
        }
    }
}

<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class EmvWriteOffsTemplateSchedule extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('emv_write_offs_template_schedule', [
                'id' => 'id',
                'comment' => 'Расписания для шаблонов абонементов',
            ])
            ->addColumn('valid_time_start', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Начало действия',
            ])
            ->addColumn('valid_time_end', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Окончание действия',
            ])
            ->addColumn('valid_time_days', 'integer', [
                'default' => 0,
                'signed' => false,
                'comment' => 'Период действия(дней)',
            ])
            ->addColumn('sell_date_time_start', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время начала продаж',
            ])
            ->addColumn('sell_date_time_end', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Дата и время окончания продаж',
            ])
            ->addColumn('is_deleted', 'boolean', [
                'null' => true,
                'default' => '0',
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Признак удаления',
            ])
            ->create();

        $this
            ->table('emv_xref_template_schedule', [
                'id' => 'id',
                'comment' => 'Расписания для шаблонов абонементов',
            ])
            ->addColumn('template_id', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор шаблона',
            ])
            ->addColumn('schedule_id', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор расписания',
            ])
            ->addColumn('is_deleted', 'boolean', [
                'null' => true,
                'default' => '0',
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Признак удаления',
            ])
            ->create();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class SellTransportCardCategory extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `TerminalTransaction`
						CHANGE `sellTransportCardCategory` `sellTransportCardCategory` VARCHAR(32) NULL DEFAULT NULL
		;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalTransaction`
						CHANGE `sellTransportCardCategory` `sellTransportCardCategory` VARCHAR(12) NULL DEFAULT NULL
		;");
    }
}

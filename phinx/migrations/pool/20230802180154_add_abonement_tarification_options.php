<?php

use Phinx\Db\Table;
use Phinx\Migration\AbstractMigration;

class AddAbonementTarificationOptions extends AbstractMigration
{
    private const TABLE = 'options';

    public function up()
    {
        $table = $this->getTable();

        //$this->getQueryBuilder()->delete(self::TABLE)
        //    ->whereInList('key', [
        //        'check_bind_write_offs_template_ride_segment',
        //    ])
        //    ->execute();

        $table
            ->insert([
                [
                    'key' => 'check_writeoff_segment_binding',
                    'description' => 'Проверять привязку шаблона к организации по рейсу при тарификации',
                    'value' => 1,
                    'type' => 'boolean',
                    'is_editable' => 1,
                ],
                [
                    'key' => 'check_passage_rules',
                    'description' => 'Проверять правила прохода при тарификации',
                    'value' => 1,
                    'type' => 'boolean',
                    'is_editable' => 1,
                ],
                [
                    'key' => 'use_next_abonement_when_counter_exceeded',
                    'description' => 'Если при тарификации требуется списание с пустого абонемента, искать другой активный абонемент с поездками',
                    'value' => 1,
                    'type' => 'boolean',
                    'is_editable' => 1,
                ],
                [
                    'key' => 'charge_counters_with_no_trips_left',
                    'description' => 'Если при тарификации требуется списание с пустого абонемента, списывать поездки «в минус»',
                    'value' => 1,
                    'type' => 'boolean',
                    'is_editable' => 1,
                ],
            ])
            ->saveData();
    }

    public function down()
    {
        $table = $this->getTable();

        $this->getQueryBuilder()->delete(self::TABLE)
            ->whereInList('key', [
                'check_writeoff_segment_binding',
                'check_passage_rules',
                'use_next_abonement_when_counter_exceeded',
                'charge_counters_with_no_trips_left',
            ])
            ->execute();

        //$table
        //    ->insert([
        //        [
        //            'key' => 'check_bind_write_offs_template_ride_segment',
        //            'description' => 'Проверять привязку шаблона абонемента к организации по рейсу при тарификации',
        //            'value' => 1,
        //            'type' => 'boolean',
        //            'is_editable' => 1,
        //        ],
        //    ])
        //    ->saveData();
    }

    private function getTable(): Table
    {
        return $this->table(self::TABLE);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AgentSbol extends AbstractMigration
{
    public function up()
    {
        $lastId = 0;
        $sql = "SELECT MAX(agent_id) as agent_id_max FROM agent WHERE agent_id < 999999";
        $lastAgent = $this->query($sql)->fetchAll();
        if (is_array($lastAgent[0])) {
            $lastId = $lastAgent[0]['agent_id_max'];
        }

        $this->table('agent')->insert([
            [
                'agent_id'  => $lastId + 1,
                'title'     => 'СБОЛ',
                'slug'      => 'sbol',
            ]
        ])->saveData();
    }

    public function down()
    {
        $sql = "DELETE FROM agent WHERE slug = 'sbol'";
        $this->query($sql);
    }
}

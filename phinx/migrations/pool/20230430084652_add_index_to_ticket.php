<?php

use Phinx\Migration\AbstractMigration;

class AddIndexToTicket extends AbstractMigration
{
    private const INDEX_NAME_VALUE = 'idx__operation_id';

    public function up(): void
    {
        if (!$this->hasIndexReferenceNumber()) {
            $this->table('ticket')
                ->addIndex('operation_id', [
                    'name' => self::INDEX_NAME_VALUE
                ])
                ->update();
        }
    }

    public function down(): void
    {
        if ($this->hasIndexReferenceNumber()) {
            $this->table('ticket')
                ->removeIndex('operation_id')
                ->update();
        }
    }

    private function hasIndexReferenceNumber(): bool
    {
        $table = $this->table('ticket');

        return $table->hasIndex('operation_id');
    }
}

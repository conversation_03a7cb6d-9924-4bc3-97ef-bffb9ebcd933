<?php

use Phinx\Migration\AbstractMigration;

class AddPrimaryKeyToSearchResultTable extends AbstractMigration
{
    public function up()
    {
        //$this->table('search_result')->changePrimaryKey('search_id')->update();
        $this->query('ALTER TABLE `search_result` ADD PRIMARY KEY (`search_id`)');
    }

    public function down()
    {
        $this->query('ALTER TABLE search_result DROP PRIMARY KEY;');
    }
}

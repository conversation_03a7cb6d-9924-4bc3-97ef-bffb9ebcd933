<?php

use Phinx\Migration\AbstractMigration;

class AddBeforeCounterTripsInGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_current_balances')->hasColumn('before_counter_trips')) {
            $this->table('golden_crown_current_balances')
                ->addColumn('before_counter_trips', 'integer', [
                    'default' => 0,
                    'comment' => 'Количество поездок до'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_current_balances')->hasColumn('before_counter_trips')) {
            $this->table('golden_crown_current_balances')
                ->removeColumn('before_counter_trips')
                ->update();
        }
    }
}

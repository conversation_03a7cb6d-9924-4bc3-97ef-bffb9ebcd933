<?php

use Phinx\Migration\AbstractMigration;

class ChangeEmvStopList extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and Phi<PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $tab =  $this->table('EMVStopList');
        $tab
            //->changeColumn('PANHash','string',   [ 'null'=> true] )
            ->addColumn('WriteOffsId', 'biginteger', ['signed' => false,  'comment' => 'Идентификатор правила списания на основе которого создан элемент белого списка','null' => true])
            ->addColumn('Balance', 'string', ['length' => 256, 'comment' => 'Остаток поездок по абонементу, либо баланс кошелька','null' => true])
            ->addColumn('ExpirationDateTime', 'timestamp', ['null' => true, 'comment' => 'Дата и время окончания действия кошелька либо абонемента'])
            ->save();
    }
}

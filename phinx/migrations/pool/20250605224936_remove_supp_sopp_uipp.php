<?php

use Phinx\Migration\AbstractMigration;

class RemoveSuppSoppUipp extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {
        // Удаляем FEDERATED таблицы СУПП (если они существуют)
        $this->execute("DROP TABLE IF EXISTS `supp_contragent`;");
        $this->execute("DROP TABLE IF EXISTS `supp_uir`;");
        $this->execute("DROP TABLE IF EXISTS `supp_uipp`;");

        // Удаляем таблицу УИПП статусов
        $this->execute("DROP TABLE IF EXISTS `dispatch_ticket_status`;");

        // Удаляем роли СУПП
        $this->execute("DELETE FROM `admin_xref_user_role` WHERE `role_title` IN ('supp_manage', 'supp_show_efsmkpp_name');");
        $this->execute("DELETE FROM `admin_role` WHERE `role_title` IN ('supp_manage', 'supp_show_efsmkpp_name');");
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // Восстанавливаем роли СУПП
        $this->execute("
            INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES
                ('supp_manage', 'Просмотр данных по выдаче УИР и УИПП'),
                ('supp_show_efsmkpp_name', 'Показывать название системы, как ЕФС МКПП');
        ");

        // Примечание: FEDERATED таблицы не восстанавливаем, так как они создавались динамически в коде
        // и зависят от конфигурации внешней БД СУПП
    }
}

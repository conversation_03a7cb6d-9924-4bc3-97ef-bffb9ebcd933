<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringNotParsedTransactions extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_not_parsed_transactions';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                SELECT COUNT(1) AS cnt
                FROM TerminalRawTransaction tr
                WHERE tr.LoadDateTime >= DATE_ADD(DATE(NOW()), INTERVAL -7 DAY)
                  AND DATE_ADD(tr.LoadDateTime, INTERVAL 1 HOUR) < NOW()
                  AND tr.IsParsed = 0',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

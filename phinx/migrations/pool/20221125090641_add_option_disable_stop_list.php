<?php

use Phinx\Migration\AbstractMigration;

class AddOptionDisableStopList extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'disable_stop_list',
                'description' => 'Отправляется пустой стоп лист при запросе от терминала',
                'value' => 0,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'disable_stop_list',
            ])
            ->execute();
    }
}

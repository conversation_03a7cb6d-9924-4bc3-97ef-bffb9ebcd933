<?php

use Phinx\Migration\AbstractMigration;

class AddCardIdToEmvWalletListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('CardId', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор карты в ABTCardList',
                'after' => 'WriteOffsScheduleId',
            ])
            ->update();
    }
}

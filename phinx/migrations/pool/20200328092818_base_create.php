<?php

use Phinx\Migration\AbstractMigration;

class BaseCreate extends AbstractMigration
{
    public function up()
    {
        $sql = file_get_contents('migrations/data/create.sql', true);
        $this->query($sql);

        $triggers = file_get_contents('migrations/data/trigger.sql', true);
        $sqls = explode("$$$", $triggers);
        foreach ($sqls as $sql) {
            if (!empty(trim($sql))) {
                $this->query(trim($sql));
            }
        }

        $sql = file_get_contents('migrations/data/insert.sql', true);
        $this->query($sql);

        $sql = "COMMIT";
        $this->query($sql);
    }
}

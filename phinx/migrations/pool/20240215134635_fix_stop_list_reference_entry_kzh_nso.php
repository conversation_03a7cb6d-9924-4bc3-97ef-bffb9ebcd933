<?php

use Phinx\Migration\AbstractMigration;

class FixStopListReferenceEntryKzhNso extends AbstractMigration
{
    public const TABLE_NAME = 'ReferenceList';
    public const REFERENCE_NAME = 'StopListType';
    public const RECORD_ID = 15;

    public function up()
    {
        $this->getQueryBuilder()
            ->update(self::TABLE_NAME)
            ->set([
                'RecordName' => 'sltKzhNsoPanHash',
                'RecordDescription' => 'Белый список. Абонементы КЖ НСО.',
            ])
            ->where([
                'ReferenceName' => self::REFERENCE_NAME,
                'RecordId' => self::RECORD_ID,
            ])
            ->execute();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->update(self::TABLE_NAME)
            ->set([
                'RecordName' => 'sltKzhNkoPanHash',
                'RecordDescription' => 'Белый список. Абонементы КЖ НКО.',
            ])
            ->where([
                'ReferenceName' => self::REFERENCE_NAME,
                'RecordId' => self::RECORD_ID,
            ])
            ->execute();
    }
}

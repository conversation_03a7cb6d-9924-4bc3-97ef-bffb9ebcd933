<?php

use Phinx\Migration\AbstractMigration;

class AddIsForcedDeactivationEmvAbonementListTable extends AbstractMigration
{
    protected const TABLE = 'EMVAbonementList';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('IsForcedDeactivation', 'boolean', [
                'default' => false,
                'null' => false,
                'comment' => 'Принудительно деактивировать абонемент',
                'after' => 'IsActive',
            ])
            ->update();
    }

    public function down()
    {
        if ($this->table(static::TABLE)->hasColumn('IsForcedDeactivation')) {
            $this->table(static::TABLE)
                ->removeColumn('IsForcedDeactivation')
                ->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class FixTarifficationLogComment extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalTransaction');

        if ($table->hasColumn('TarifficationLog')) {
            $table->changeColumn('TarifficationLog', 'text', [
                'null' => true,
                'comment' => 'Лог тарификации',
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalTransaction');

        if ($table->hasColumn('TarifficationLog')) {
            $table->changeColumn('TarifficationLog', 'text', [
                'null' => true,
                'comment' => 'Лог тарификации (JSON)',
            ]);
        }

        $table->update();
    }
}

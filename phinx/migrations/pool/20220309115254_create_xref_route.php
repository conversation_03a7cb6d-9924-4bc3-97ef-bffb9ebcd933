<?php

use Phinx\Migration\AbstractMigration;

class CreateXrefRoute extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $tab =  $this->table('xref_route_partner', ['signed' => false,'id' => 'id','comment' => 'Временная таблица со списком маршрутов из НСИ']);
        $tab

            ->addColumn('partner_id', 'integer', ['signed' => false, 'comment' => 'Ид вендора, владельца маршрута','null' => false])
            ->addColumn('route_id', 'integer', ['signed' => false, 'comment' => 'ID маршрута','null' => false])
            ->addColumn('route_number', 'string', ['length' => 32,'comment' => 'Номер маршрута','null' => true])
            ->addColumn('route_name', 'string', ['length' => 128,'comment' => 'Название маршрута','null' => true])
            ->addColumn('route_body', 'json', ['comment' => 'Полные данные о маршруте','null' => true])
            ->addColumn('route_stations', 'json', ['comment' => 'Станции маршрута','null' => true])
            ->addColumn('route_schedule', 'json', ['comment' => 'Расписание движения на маршруте','null' => true])
            ->addColumn('route_transport', 'json', ['comment' => 'Список бортов обслуживающих маршрут','null' => true])
            ->addColumn('route_polyline', 'json', ['comment' => 'Полилиния маршрута','null' => true])
            ->addColumn('route_tags', 'string', ['length' => 512,'comment' => 'Список тэгов маршрута','null' => true])
            ->addColumn('version', 'string', ['length' => 128,'comment' => 'Версия данных маршрута','null' => true])
            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            ->addIndex(['partner_id'], ['unique' => false])
            ->addIndex(['route_id'], ['unique' => false])
            ->addIndex(['route_tags'], ['unique' => false])
            ->addIndex(['partner_id', 'route_id'], ['unique' => true])
            ->addIndex('edit_date_time')
            ->create();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIsCancelledSegment extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('ride_segment');

        if (!$table->hasColumn('is_cancelled_segment')) {
            $table->addColumn('is_cancelled_segment', 'boolean', [
                'null' => false,
                'default' => false,
                'comment' => 'Признак отмены сегменты',
            ]);
        }
        $table->update();
    }

    public function down()
    {
        $table = $this->table('ride_segment');

        if ($table->hasColumn('is_cancelled_segment')) {
            $table->removeColumn('is_cancelled_segment');
        }
        $table->update();
    }
}

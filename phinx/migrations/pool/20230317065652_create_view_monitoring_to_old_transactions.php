<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringToOldTransactions extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_to_old_transactions';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                 SELECT COUNT(1) AS cnt
                 FROM TerminalTransaction
                 WHERE ParseDateTime >= DATE_ADD(DATE(NOW()), INTERVAL -7 DAY)
                    AND transactionTime < DATE_ADD(DATE(NOW()), INTERVAL -5 YEAR)',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

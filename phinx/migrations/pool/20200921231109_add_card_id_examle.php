<?php

use Phinx\Migration\AbstractMigration;

class AddCardIdExamle extends AbstractMigration
{
    public function up()
    {
        $this->query("
                alter table card_identity 
                        modify title varchar(128) null,
                    	add example_number varchar(64) default null null comment 'Пример номера документа';     
            ");

        $this->query("
                    update card_identity set example_number = '' where 1 ;
                    update card_identity set example_number = '1234 123456' where card_identity_id = 1 ;
                    update card_identity set example_number = '123456789' where card_identity_id in (2,8) ;
                    update card_identity set example_number = 'XIV-АБ 123456' where card_identity_id in(4,14) ;
                    update card_identity set example_number = 'аБ 123456(7)' where card_identity_id in(5,6,9,10);
                    update card_identity set example_number = 'Ая-123 1234' where card_identity_id = 17 ;
            ");
    }

    public function down()
    {
        $this->query("
                alter table card_identity 
                        modify title varchar(50) null,
                    	drop column example_number;      
            ");
    }
}
/*
 *


 *
 * */

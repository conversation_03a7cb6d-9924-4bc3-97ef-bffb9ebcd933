<?php

use Phinx\Migration\AbstractMigration;

class AddBlockStatusToAbonementList extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementList')
            ->addColumn('BlockStatus', 'integer', [
                'default' => 0,
                'limit' => 1,
                'after' => 'IsActive',
                'null' => false,
                'comment' => 'Тип блокировки карты (рефлист «EMVBlockStatus»)',
            ])
            ->save();
    }
}

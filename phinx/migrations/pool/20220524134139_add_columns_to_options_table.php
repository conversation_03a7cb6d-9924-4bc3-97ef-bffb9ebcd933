<?php

use Phinx\Migration\AbstractMigration;

class AddColumnsToOptionsTable extends AbstractMigration
{
    public function up()
    {
        $this->table('options')
            ->addColumn('description', 'string', [
                'length' => 255,
                'null' => true,
                'after' => 'key',
                'comment' => 'Название',
            ])
            ->addColumn('type', 'string', [
                'length' => 10,
                'default' => 'string',
                'null' => false,
                'after' => 'value',
                'comment' => 'Тип значения (string, boolean, date, datetime, text)',
            ])
            ->addColumn('is_editable', 'boolean', [
                'default' => false,
                'null' => false,
                'after' => 'type',
                'comment' => 'Значение можно менять в интерфейсе',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('options')
            ->removeColumn('description')
            ->removeColumn('type')
            ->removeColumn('is_editable')
            ->update();
    }
}

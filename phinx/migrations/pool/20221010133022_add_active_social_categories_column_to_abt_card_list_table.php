<?php

use Phinx\Migration\AbstractMigration;

class AddActiveSocialCategoriesColumnToAbtCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('ActiveSocialCategories', 'json', [
                'after' => 'SocialCategoriesAvailable',
                'null' => true,
                'comment' => 'Список льгот, для которых доступен последний активный абонемент',
            ])
            ->update();
    }
}

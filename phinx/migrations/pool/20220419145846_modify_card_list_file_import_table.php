<?php

use Phinx\Migration\AbstractMigration;

class ModifyCardListFileImportTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardListFileImport')
            ->changeColumn('Journal', 'text', [
                'comment' => 'Результат загрузки',
                'null' => true,
            ])
            ->changeColumn('WriteOffsId', 'integer', [
                'signed' => false,
                'comment' => 'Id привязываемого к карте абонемента или кошелька',
                'null' => true,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardListFileImport')
            ->changeColumn('Journal', 'text', [
                'comment' => 'Результат загрузки',
                'null' => false,
            ])
            ->changeColumn('WriteOffsId', 'integer', [
                'signed' => false,
                'comment' => 'Id привязываемого к карте абонемента или кошелька',
                'null' => false,
            ])
            ->update();
    }
}

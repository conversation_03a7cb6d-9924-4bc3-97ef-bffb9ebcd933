<?php

use Phinx\Migration\AbstractMigration;

class TerminalTransactionType extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalTransaction` CHANGE `transactionType` `transactionType` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT \'Тип транзакции\';');
    }

    public function down()
    {
        $this->query('ALTER TABLE `TerminalTransaction` CHANGE `transactionType` `transactionType` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT \'Тип транзакции\'');
    }
}

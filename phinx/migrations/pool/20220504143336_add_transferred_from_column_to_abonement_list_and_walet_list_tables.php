<?php

use Phinx\Migration\AbstractMigration;

class AddTransferredFromColumnToAbonementListAndWaletListTables extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementList')
            ->addColumn('TransferredFrom', 'biginteger', [
                'signed' => false,
                'comment' => 'Id абонемента до смены носителя',
                'null' => true,
            ])
            ->addColumn('TransferredTo', 'biginteger', [
                'signed' => false,
                'comment' => 'Id абонемента после смены носителя',
                'null' => true,
            ])
            ->addIndex(['TransferredFrom', 'TransferredTo'])
            ->update();

        $this->table('EMVWalletList')
            ->addColumn('TransferredFrom', 'biginteger', [
                'signed' => false,
                'comment' => 'Id кошелька до смены носителя',
                'null' => true,
            ])
            ->addColumn('TransferredTo', 'biginteger', [
                'signed' => false,
                'comment' => 'Id кошелька после смены носителя',
                'null' => true,
            ])
            ->addIndex(['TransferredFrom', 'TransferredTo'])
            ->update();
    }
}

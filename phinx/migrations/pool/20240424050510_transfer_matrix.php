<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class TransferMatrix extends AbstractMigration
{
    public function change()
    {
        // >>> фикс ошибки миграции
        if ($this->hasTable("transfer_matrix")) { // таблица может существовать по неизвестным причинам
            $this->table(self::TABLE_NAME)->rename(self::TABLE_NAME . "_old")->save();
        }
        // <<< конец фикса

        $this->table('transfer_matrix', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'encoding' => 'utf8',
            'collation' => 'utf8_general_ci',
            'comment' => 'Список разрешённых пересадок',
            'row_format' => 'DYNAMIC',
        ])
            ->addColumn('id', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_REGULAR,
                'identity' => 'enable'
            ])
            ->addColumn('matrix_number', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment' => 'Номер пересадочной матрицы'
            ])
            ->addColumn('transfer_rule', 'json', [
                'null' => true,
                'default' => null,
                'comment' => 'Правила пересадок'
            ])
            ->addColumn('is_deleted', 'boolean', [
                'default' => false,
                'null' => false,
                'comment' => 'Признак удаления',
            ])
            ->addColumn('date_start', 'datetime', [
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время начала действия пересадок'
            ])
            ->addColumn('date_end', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Дата и время окончания действия пересадок'
            ])
            ->addColumn(
                'edit_date_time',
                'timestamp',
                [
                    'default' => 'CURRENT_TIMESTAMP',
                    'update' => 'CURRENT_TIMESTAMP',
                    'comment' => 'Дата и время изменения записи'
                ]
            )
            ->addColumn('edit_user_id', 'integer', [
                'null' => false,
                'comment' => 'Ид пользователя, который осуществил запись/изменение'
            ])->create()
        ;

        if ($this->isMigratingUp()) {
            $this->query('
			DROP TRIGGER IF EXISTS tg_transfer_matrix_BI;
            CREATE TRIGGER `tg_transfer_matrix_BI` BEFORE INSERT ON `transfer_matrix` FOR EACH ROW
            begin
                SET NEW.edit_user_id = @editUserId;
                IF @editUserId IS NULL
                    THEN
                        SET NEW.edit_user_id = -1;
                END IF;
            END
        ');

            $this->query('
			DROP TRIGGER IF EXISTS tg_transfer_matrix_BU;
           CREATE TRIGGER `tg_transfer_matrix_BU` BEFORE UPDATE ON `transfer_matrix` FOR EACH ROW
           begin
                IF
                    OLD.is_deleted!=NEW.is_deleted
                THEN
                SET NEW.edit_user_id = @editUserId;
                    IF @editUserId IS NULL
                        THEN
                            SET NEW.edit_user_id = -1;
                    END IF;
                END IF;
            END
        ');
        } else {
            $this->query('DROP TRIGGER if exists tg_transfer_matrix_BI;');
            $this->query('DROP TRIGGER if exists tg_transfer_matrix_BU;');
        }
    }
}

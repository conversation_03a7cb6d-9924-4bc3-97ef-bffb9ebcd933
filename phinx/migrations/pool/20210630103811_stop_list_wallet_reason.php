<?php

use Phinx\Migration\AbstractMigration;

class StopListWalletReason extends AbstractMigration
{
    public function up()
    {
        $sql = "
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription)
                VALUES ('StopListType', 4, 'sltWallet', 'Белый список по кошельку');
        ";
        $this->query($sql);

        $sql = "
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription)
                VALUES ('StopListReason', 5, 'slrWalletMinLimit', 'Достигнут минимальный лимит средств на счету кошелька');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'StopListType' and RecordId=4;
             Delete from ReferenceList Where ReferenceName = 'StopListReason' and RecordId=5;
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

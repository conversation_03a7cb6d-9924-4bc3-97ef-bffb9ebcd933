<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class StationPhoto extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('station_photo', ['comment' => 'Фото станций']);
        $table
            ->addColumn('station_id', 'integer', ['comment' => 'ИД станции'])
            ->addColumn('priority', 'integer', ['default' => 0, 'comment' => 'Порядоковый номер фото'])
            ->addColumn('photo', 'blob', ['limit' => MysqlAdapter::BLOB_LONG,'default' => null, 'null' => true])
            ->addColumn('photo_hash', 'string', ['limit' => 256, 'default' => null, 'null' => true])
            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время создания(изменения) записи'])
            ->addColumn('version', 'integer', ['default' => 0, 'comment' => 'Версия записи'])
            ->create();
    }
}

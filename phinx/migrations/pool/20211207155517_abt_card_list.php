<?php

use Phinx\Migration\AbstractMigration;

class AbtCardList extends AbstractMigration
{
    public function change()
    {
        $tab =  $this->table('ABTCardList', ['signed' => false,'id' => 'Id','comment' => 'Список Mifare карт с привязанными кошельками']);
        $tab
            ->addColumn('WalletId', 'integer', ['signed' => false, 'comment' => 'Ид кошелька, привязанного к карте','null' => true])
            ->addColumn('UID', 'string', ['length' => 14,'comment' => 'UID карты','null' => true])
            ->addColumn('UIDHash', 'string', ['length' => 128,'comment' => 'SHA256 от UID карты','null' => true])
            ->addColumn('PAN', 'string', ['length' => 32,'comment' => 'Номер карты', 'default' => null , 'null' => true])
            ->addColumn('PANHash', 'string', ['length' => 128,'comment' => 'SHA256 от PAN карты', 'default' => null , 'null' => true])
            ->addColumn('EditDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            ->addIndex(['WalletId'], ['unique' => true])
            ->addIndex(['UID'], ['unique' => true])
            ->addIndex(['UIDHash'], ['unique' => true])
            ->addIndex(['PAN'], ['unique' => true])
            ->addIndex(['PANHash'], ['unique' => true])
            ->addIndex('EditDateTime')
            ->create();

        $tab1 =  $this->table('EMVWalletList');
        $tab1
            -> addColumn('ChangeDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            -> addIndex('ChangeDateTime')
            -> update();
    }
}

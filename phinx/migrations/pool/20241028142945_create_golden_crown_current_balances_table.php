<?php

use Phinx\Migration\AbstractMigration;

class CreateGoldenCrownCurrentBalancesTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'golden_crown_current_balances';

    public function up()
    {
        // Проверка на существование таблицы
        if (!$this->hasTable(self::TABLE_NAME_VALUE)) {
            $table = $this->table(self::TABLE_NAME_VALUE, ['id' => 'id']);
            $table->addColumn('card_uid', 'string', ['limit' => 255, 'null' => false])
                ->addColumn('current_balance', 'integer', ['default' => 0])
                ->addColumn('current_counter_trips', 'integer', ['default' => 0])
                ->addColumn('updated_at', 'datetime', ['null' => true])
                ->addIndex(['card_uid'], ['unique' => true]) // уникальный индекс
                ->create();
        }
    }

    public function down()
    {
        // Проверка на существование таблицы перед удалением
        if ($this->hasTable(self::TABLE_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)->drop()->save();
        }
    }
}

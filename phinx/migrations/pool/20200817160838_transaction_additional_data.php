<?php

use Phinx\Migration\AbstractMigration;

class TransactionAdditionalData extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `TerminalTransaction` ADD transactionAdditionalData json default NULL comment 'дополнительные данные' after transportCardTransactionData;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalTransaction` DROP `transactionAdditionalData`;");
    }
}

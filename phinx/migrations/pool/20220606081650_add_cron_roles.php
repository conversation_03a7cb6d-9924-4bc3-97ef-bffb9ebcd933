<?php

use Phinx\Migration\AbstractMigration;

class AddCronRoles extends AbstractMigration
{
    public function up()
    {
        $this->deleteRoles();

        $this->table('admin_role')->insert([
            [
                'role_title' => 'cron_tasks_view',
                'role_description' => 'Просмотр крон-задач',
            ],
            [
                'role_title' => 'cron_tasks_manage',
                'role_description' => 'Редактирование крон-задач',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRoles();
    }

    private function deleteRoles()
    {
        $this->getQueryBuilder()->delete('admin_role')
            ->whereInList('role_title', [
                'cron_tasks_view',
                'cron_tasks_manage',
            ])
            ->execute();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class TransactionStatus extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalTransaction` ADD `transactionStatus` INT(11) NULL DEFAULT 0 AFTER `transactionVersion`, ADD `ticketsCount` INT(11) NULL DEFAULT NULL AFTER `ticketId`');
        $this->query('ALTER TABLE `TerminalTransaction` CHANGE `fiscalDocDate` `fiscalDocDate` BIGINT(20) UNSIGNED NULL DEFAULT \'0\' COMMENT \'Дата фискального документа\';');
    }

    public function down()
    {
        $this->query('ALTER TABLE `TerminalTransaction` DROP `transactionStatus`, DROP `ticketsCount`');
        $this->query('ALTER TABLE `TerminalTransaction` CHANGE `fiscalDocDate` `fiscalDocDate` BIGINT(20) UNSIGNED NOT NULL DEFAULT \'0\' COMMENT \'Дата фискального документа\';');
    }
}

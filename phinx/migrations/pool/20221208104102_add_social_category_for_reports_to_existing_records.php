<?php

use Phinx\Migration\AbstractMigration;

class AddSocialCategoryForReportsToExistingRecords extends AbstractMigration
{
    public function up()
    {
        $socialCategoriesIdsSorted = $this->getSocialCategoriesIdsSorted();
        $cardsWithAbonements = $this->getCardsWithAbonements();
        $now = date('Y-m-d H:i:s');

        foreach ($cardsWithAbonements as $card) {
            $this->log('debug', 'Updating card #' . $card['Id']);

            $activeSocialCategories = array_intersect(
                json_decode($card['AllowedSocialCategories']),
                json_decode($card['SocialCategoriesAvailable'])
            );

            usort($activeSocialCategories, function ($a, $b) use ($socialCategoriesIdsSorted) {
                return array_search($a, $socialCategoriesIdsSorted) <=> array_search($b, $socialCategoriesIdsSorted);
            });

            $journal = json_decode($card['Journal'], true) ?? [];
            $journal[] = [
                'date' => $now,
                'action' => 'modified',
                'source' => 'AddSocialCategoryForReportsToExistingRecords',
                'data' => [
                    'ActiveSocialCategories' => $activeSocialCategories,
                    'SocialCategoryForReports' => $activeSocialCategories[0] ?? null
                ],
            ];

            $updateQuery = $this->getQueryBuilder()
                ->update('ABTCardList')
                ->set('ActiveSocialCategories', json_encode($activeSocialCategories))
                ->set('SocialCategoryForReports', $activeSocialCategories[0] ?? null)
                ->set('Journal', json_encode($journal))
                ->where([
                    'Id' => $card['Id'],
                ]);

//            $this->log('debug', $updateQuery->sql());

            $updateQuery->execute();
        }
    }

    public function down()
    {
    }

    protected function getSocialCategoriesIdsSorted(): array
    {
        $socialCategoriesSorted = $this->getQueryBuilder()
            ->select(['id'])
            ->from('social_categories')
            ->orderDesc('priority')
            ->execute()
            ->fetchAll('assoc');

        return array_column($socialCategoriesSorted, 'id');
    }

    protected function getCardsWithAbonements(): array
    {
        return $this->getQueryBuilder()
            ->select([
                'ABTCardList.Id',
                'ActiveSocialCategories',
                'SocialCategoryForReports',
                'AllowedSocialCategories',
                'SocialCategoriesAvailable',
                'ABTCardList.Journal',
            ], true)
            ->from('ABTCardList')
            ->leftJoin(
                'EMVAbonementList',
                'ABTCardList.PANHash = EMVAbonementList.PANHash OR ABTCardList.UIDHash = EMVAbonementList.UIDHash'
            )
            ->leftJoin('EMVWriteOffsTemplate', 'EMVAbonementList.WriteOffsId = EMVWriteOffsTemplate.Id')
            ->where([
                'EMVAbonementList.AbonementId IS NOT' => null,
                'ABTCardList.SocialCategoriesAvailable IS NOT' => null,
                'EMVWriteOffsTemplate.IsRestrictedBySocialCategories' => true,
                'EMVWriteOffsTemplate.AllowedSocialCategories IS NOT' => null,
            ])
            ->execute()
            ->fetchAll('assoc');
    }

    protected function log(string $level, string $message)
    {
        $colorModifier = null;

        switch ($level) {
            case 'error':
                $colorModifier = '[41m'; //Red background
                break;
            case 'warning':
                $colorModifier = '[43m'; //Yellow background
                break;
            case 'info':
                $colorModifier = '[44m'; //Blue background
                break;
            default:
        }

        if ($colorModifier) {
            echo chr(27) . '[0m' . chr(27) . $colorModifier;
        }

        echo '[' . $level . '] ' . $message;

        if ($colorModifier) {
            echo chr(27) . '[0m';
        }

        echo PHP_EOL;
    }
}

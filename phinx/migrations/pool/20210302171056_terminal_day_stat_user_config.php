<?php

use Phinx\Migration\AbstractMigration;

class TerminalDayStatUserConfig extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalUserDayStat` ADD `UserConfigFirstDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'первое получение конфига юзера\' AFTER `Date`, ADD `UserConfigLastDateTime` TIMESTAMP NULL DEFAULT NULL COMMENT \'последнее получение конфига юзера\' AFTER `UserConfigFirstDateTime`, ADD `UserConfigCount` INT NOT NULL DEFAULT \'0\' COMMENT \'кол-во получений конфига юзера\' AFTER `UserConfigLastDateTime`;');
    }

    public function down()
    {
        $this->query('ALTER TABLE `TerminalUserDayStat`
  DROP `UserConfigFirstDateTime`,
  DROP `UserConfigLastDateTime`,
  DROP `UserConfigCount`;');
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddSocialAutoRenewalOptions extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'social_templates_auto_renewal',
                'description' => 'Автоматическая выдача бесплатных льгот',
                'value' => 0,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'social_templates_auto_renewal',
            ])
            ->execute();
    }
}

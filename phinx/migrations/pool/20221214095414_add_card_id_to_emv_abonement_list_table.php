<?php

use Phinx\Migration\AbstractMigration;

class AddCardIdToEmvAbonementListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementList')
            ->addColumn('CardId', 'integer', [
                'null' => true,
                'comment' => 'Идентификатор карты в ABTCardList',
                'after' => 'WriteOffsScheduleId',
            ])
            ->update();
    }
}

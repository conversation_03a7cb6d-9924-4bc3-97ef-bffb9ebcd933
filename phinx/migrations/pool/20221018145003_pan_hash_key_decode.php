<?php

use Phinx\Migration\AbstractMigration;

class PanHashKeyDecode extends AbstractMigration
{
    public function change()
    {
        $this->table('agent')
            ->addColumn('pan_hash_decode', 'boolean', [
                'after' => 'slug',
                'default' => false,
                'comment' => 'Дешифровка PAN банковской карты',
            ])
            ->addColumn('pan_hash_key', 'string', [
                'after' => 'pan_hash_decode',
                'null' => true,
                'limit' => 128,
                'comment' => 'Ключ для дешифровки PAN банковской карты',
            ])
            ->update();
    }
}

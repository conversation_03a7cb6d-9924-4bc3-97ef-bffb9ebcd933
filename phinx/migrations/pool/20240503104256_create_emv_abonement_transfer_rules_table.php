<?php

use Phinx\Migration\AbstractMigration;

class CreateEmvAbonementTransferRulesTable extends AbstractMigration
{
    public function up()
    {
        $sql = "
                CREATE TABLE IF NOT EXISTS EMVAbonementTransferRule (
                    `Id`                    BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Уникальный идентификатор',
                    `Name`                  VARCHAR(255) NOT NULL COMMENT 'Название',
                    `Type`                  VARCHAR(255) NOT NULL DEFAULT 'default' COMMENT 'Тип (default, ...)',
                    `MaxTransferCount`      INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Максимальное количество разрешённое для начисления остатков в период',
                    `IsActive`              TINYINT(4) DEFAULT '0' COMMENT 'Разрешено использовать(1)'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='Правила переноса остатков абонементов';
                ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "DROP TABLE IF EXISTS EMVAbonementTransferRule;";
        $this->query($sql);
    }
}

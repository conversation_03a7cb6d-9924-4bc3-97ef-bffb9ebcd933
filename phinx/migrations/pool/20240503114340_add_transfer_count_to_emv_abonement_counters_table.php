<?php

use Phinx\Migration\AbstractMigration;

class AddTransferCountToEmvAbonementCountersTable extends AbstractMigration
{
    protected const TABLE = 'EMVAbonementCounters';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('TransferInCount', 'integer', [
                'comment' => 'Количество перенесённое из другого счётчик',
                'signed' => false,
                'default' => '0'
            ])
            ->addColumn('TransferOutCount', 'integer', [
                'comment' => 'Количество перенесённое в другой счётчик',
                'signed' => false,
                'default' => '0'
            ])
            ->addColumn('History', 'json', [
                'comment' => 'История изменений',
                'null' => true,
                'default' => null
            ])
            ->update();
    }

    public function down()
    {
        $this->table(static::TABLE)
            ->removeColumn('TransferInCount')
            ->removeColumn('TransferOutCount')
            ->removeColumn('History')
            ->update();
    }
}

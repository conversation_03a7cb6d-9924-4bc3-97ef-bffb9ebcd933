<?php

use Phinx\Migration\AbstractMigration;

class StSbolUpload extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('st_sbol_invoice', ['comment' => 'Бэклог пополнения абонементов и кошельков через УСО Сбербанка']);
        $table
            ->addColumn('amount', 'decimal', ['precision' => 10, 'scale' => 3, 'comment' => 'Сумма платежа'])
            ->addColumn('status', 'string', ['limit' => 36, 'comment' => 'Текущий статус счёта'])
            ->addColumn('agent_transaction_id', 'string', ['limit' => 36, 'comment' => 'Идентификатор транзакции на стороне Банка'])
            ->addColumn('pan', 'string', ['comment' => 'Время операции'])
            ->addColumn('payment_system', 'string', ['default' => null, 'null' => true, 'comment' => 'Платежная система'])
            ->addColumn('write_offs_type', 'integer', ['default' => null, 'null' => true, 'comment' => 'Тип шаблона записи(услуги)'])
            ->addColumn('emv_wallet_upload_id', 'biginteger', ['default' => null, 'null' => true, 'comment' => 'Идентификатор пополнения кошелька', 'signed' => false])
            ->addColumn('emv_abonement_id', 'biginteger', ['default' => null, 'null' => true, 'comment' => 'Идентификатор проданного абнемента', 'signed' => false])
            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время создания(изменения) записи'])
            ->addColumn('version', 'integer', ['default' => 0, 'comment' => 'Версия записи'])
            ->addForeignKey('emv_wallet_upload_id', 'EMVWalletUpload', 'Id', ['delete' => 'NO_ACTION', 'update' => 'NO_ACTION'])
            ->addForeignKey('emv_abonement_id', 'EMVAbonementList', 'AbonementId', ['delete' => 'NO_ACTION', 'update' => 'NO_ACTION'])
            ->create();
    }
}

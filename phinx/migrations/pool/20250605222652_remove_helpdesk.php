<?php

use Phinx\Migration\AbstractMigration;

class RemoveHelpdesk extends AbstractMigration
{
    
    /**
     * Migrate Up.
     */
    public function up()
    {
        // Удаляем таблицы helpdesk
        $this->execute("DROP TABLE IF EXISTS `helpdesk_comment`;");
        $this->execute("DROP TABLE IF EXISTS `helpdesk_request`;");
        
        // Удаляем роль helpdesk_manage
        $this->execute("DELETE FROM `admin_xref_user_role` WHERE `role_title`='helpdesk_manage';");
        $this->execute("DELETE FROM `admin_role` WHERE `role_title`='helpdesk_manage';");
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // Восстанавливаем таблицы helpdesk (обратная операция)
        $this->execute("
            CREATE TABLE IF NOT EXISTS `helpdesk_request` (
              `helpdesk_request_id` int(11) NOT NULL AUTO_INCREMENT,
              `operation_id` int(11) DEFAULT NULL,
              `admin_user_id` int(11) DEFAULT NULL COMMENT 'ответственный исполнитель',
              `date_reminder` date DEFAULT NULL COMMENT 'дата когда нужно что то делать',
              `datetime_create` datetime NOT NULL COMMENT 'дата создания заявки',
              `datetime_update` datetime DEFAULT NULL COMMENT 'дата обновления заявки',
              `status` int(11) NOT NULL DEFAULT '0' COMMENT '0 new, 1 in progress, 2 done',
              PRIMARY KEY (`helpdesk_request_id`),
              KEY `operation_id` (`operation_id`),
              KEY `admin_user_id` (`admin_user_id`,`date_reminder`,`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
        ");
        
        $this->execute("
            CREATE TABLE IF NOT EXISTS `helpdesk_comment` (
              `helpdesk_comment_id` int(11) NOT NULL AUTO_INCREMENT,
              `helpdesk_request_id` int(11) NOT NULL,
              `user_id` int(11) NOT NULL,
              `datetime_create` datetime NOT NULL,
              `type` int(11) NOT NULL COMMENT '0 обычный, 1 системный',
              `content` text NOT NULL,
              PRIMARY KEY (`helpdesk_comment_id`),
              KEY `helpdesk_request_id` (`helpdesk_request_id`),
              KEY `user_id` (`user_id`,`datetime_create`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
        ");
        
        // Восстанавливаем роль
        $this->execute("
            INSERT INTO `admin_role` (
                `role_title` ,
                `role_description`
            )
            VALUES (
                'helpdesk_manage',  'Управление техподдержкой'
            );
        ");
    }
}

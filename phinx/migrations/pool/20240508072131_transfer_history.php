<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class TransferHistory extends AbstractMigration
{
    public function change()
    {
        $this->table('TransactionTransfer', [
            'id' => false,
            'primary_key' => ['TransactionId'],
            'engine' => 'InnoDB',
            'encoding' => 'utf8',
            'collation' => 'utf8_general_ci',
            'comment' => 'История поездок с пересадками',
            'row_format' => 'DYNAMIC',
        ])
            ->addColumn('TransactionId', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_BIG,
                'identity' => 'enable'
            ])
            ->addColumn('PANHash', 'string', [
                'length' => 64,
                'null' => false,
                'collation' => 'utf8_general_ci',
                'encoding' => 'utf8'])
            ->addColumn('TransactionTime', 'datetime', [
                'null' => false
            ])
            ->addColumn('RouteId', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_BIG
            ])
            ->addColumn('TransportTypeId', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_REGULAR
            ])
            ->addColumn('IsActive', 'boolean', [
                'default' => true,
                'null' => false,
                'comment' => 'Поездка участвует в расчете пересадок'
            ])
            ->addColumn('PreviousTransactionId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_BIG,
                'comment' => 'Пред. поездка'
            ])
            ->addColumn('TransferNumber', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment' => 'Порядковый номер в цепочке пересадок'
            ])
            ->addColumn('TransferId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment' => 'ИД пересадки'
            ])
            ->addColumn('TransferRule', 'json', [
                'null' => true,
                'default' => null,
                'comment' => 'Правило на момент обработки'
            ])
            ->addColumn('Duration', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment' => 'Факт. продолжительность пересадки'
            ])->create();
        ;
        $this->table('TransactionTransfer')
            ->addIndex('IsActive')->addIndex('PANHash')->addIndex(['IsActive','PANHash'])->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddServiceCardsReferences extends AbstractMigration
{
    public function up()
    {
        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'ServiceCardType',
                'RecordId' => 0,
                'RecordName' => 'sctMifare',
                'RecordDescription' => 'Mifare',
            ],
            [
                'ReferenceName' => 'ServiceCardType',
                'RecordId' => 1,
                'RecordName' => 'sctUltralight',
                'RecordDescription' => 'Ultralight',
            ],
            [
                'ReferenceName' => 'ServiceCardType',
                'RecordId' => 2,
                'RecordName' => 'sctEMV',
                'RecordDescription' => 'EMV',
            ],
            [
                'ReferenceName' => 'ServiceCardType',
                'RecordId' => 3,
                'RecordName' => 'sctEmMarine',
                'RecordDescription' => 'Em-Marine',
            ],

            [
                'ReferenceName' => 'ServiceCardStatus',
                'RecordId' => 0,
                'RecordName' => 'scsOrder',
                'RecordDescription' => 'Заказ',
            ],
            [
                'ReferenceName' => 'ServiceCardStatus',
                'RecordId' => 1,
                'RecordName' => 'scsWarehouse',
                'RecordDescription' => 'Склад',
            ],
            [
                'ReferenceName' => 'ServiceCardStatus',
                'RecordId' => 2,
                'RecordName' => 'scsActive',
                'RecordDescription' => 'Активна',
            ],
            [
                'ReferenceName' => 'ServiceCardStatus',
                'RecordId' => 3,
                'RecordName' => 'scsBlocked',
                'RecordDescription' => 'Заблокирована',
            ],
            [
                'ReferenceName' => 'ServiceCardStatus',
                'RecordId' => 4,
                'RecordName' => 'scsDestroyed',
                'RecordDescription' => 'Уничтожена',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->execute('DELETE FROM ReferenceList WHERE ReferenceName IN ("ServiceCardType", "ServiceCardStatus")');
    }
}

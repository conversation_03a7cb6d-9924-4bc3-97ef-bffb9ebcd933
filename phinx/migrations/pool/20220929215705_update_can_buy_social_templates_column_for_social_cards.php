<?php

use Phinx\Migration\AbstractMigration;

class UpdateCanBuySocialTemplatesColumnForSocialCards extends AbstractMigration
{
    public function up()
    {
        $this->getQueryBuilder()
            ->update('ABTCardList')
            ->set('CanBuySocialTemplates', 1)
            ->where([
                'IsSocial' => '1',
            ])
            ->execute();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->update('ABTCardList')
            ->set('CanBuySocialTemplates', 0)
            ->where([
                'IsSocial' => '1',
            ])
            ->execute();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddTarifficationTimeoutFieldToTransferMatrixTable extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('transfer_matrix')->hasColumn('tariffication_timeout')) {
            $this->table('transfer_matrix')
                ->addColumn('tariffication_timeout', 'integer', [
                    'default' => 30,
                    'comment' => 'Таймаут тарификации '
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('transfer_matrix')->hasColumn('tariffication_timeout')) {
            $this->table('transfer_matrix')
                ->removeColumn('tariffication_timeout')
                ->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddPanHashesAlgorithmIdIndex extends AbstractMigration
{
    protected const TABLE_NAME = 'pan_hashes';
    protected const INDEX_NAME = 'idx__pan_hashes__algorithm_id';
    protected const COLUMN_NAME = 'algorithm_id';

    public function up()
    {
        $table = $this->table(self::TABLE_NAME);

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $this->table(self::TABLE_NAME)
                ->addIndex(
                    [
                    self::COLUMN_NAME,
                    ],
                    [
                        'name' => self::INDEX_NAME,
                    ]
                )
                ->update();
        }
    }

    public function down()
    {
        $table = $this->table(self::TABLE_NAME);

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME)->update();
        }
    }
}

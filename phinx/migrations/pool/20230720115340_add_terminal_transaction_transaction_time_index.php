<?php

use Phinx\Migration\AbstractMigration;

class AddTerminalTransactionTransactionTimeIndex extends AbstractMigration
{
    protected const TABLE_NAME = 'TerminalTransaction';
    protected const INDEX_NAME = 'idx__transactionTime';
    protected const COLUMN_NAME = 'transactionTime';

    public function up()
    {
        $table = $this->table(self::TABLE_NAME);

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $this->table(self::TABLE_NAME)
                ->addIndex([self::COLUMN_NAME], ['name' => self::INDEX_NAME])
                ->update();
        }
    }

    public function down()
    {
        $table = $this->table(self::TABLE_NAME);

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME)->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddCreatedByColumnToTaskRunsTable extends AbstractMigration
{
    public function up()
    {
        $this->table('cron_task_runs')
            ->addColumn('created_by', 'json', [
                'null' => true,
                'after' => 'created_at',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('cron_task_runs')
            ->removeColumn('created_by')
            ->update();
    }
}

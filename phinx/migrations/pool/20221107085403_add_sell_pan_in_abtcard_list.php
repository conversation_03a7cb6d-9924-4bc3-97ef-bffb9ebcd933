<?php

use Phinx\Migration\AbstractMigration;

class AddSellPanInAbtcardList extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('sellPAN', 'string', [
                'length' => 32,
                'comment' => 'Если для продажи пан отличается от того, что на карте (если не отичается то PAN)',
                'default' => null,
                'null' => true,
                'after' => 'PersoData',
            ])
            ->addColumn('sellPANHash', 'string', [
                'length' => 64,
                'comment' => 'SHA256 от sellPAN',
                'default' => null,
                'null' => true,
                'after' => 'sellPAN',
            ])
            ->addIndex(['sellPAN'], [
                'name' => 'sellPAN',
                'unique' => false,
            ])
            ->addIndex(['sellPANHash'], [
                'name' => 'sellPANHash',
                'unique' => false,
            ])
            ->update();
    }
}

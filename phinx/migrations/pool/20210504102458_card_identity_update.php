<?php

use Phinx\Migration\AbstractMigration;

class CardIdentityUpdate extends AbstractMigration
{
    public function up()
    {
        $this->query("UPDATE `card_identity` SET `reg_mask` = '[IVXLCDM]{1,10}[А-Яа-я]{2}\\d{6}', `example_number` = 'XIVАБ123456' WHERE `card_identity`.`card_identity_id` = 4;");
    }

    public function down()
    {
        $this->query("UPDATE `card_identity` SET `reg_mask` = '[IVXLCDM]{1,10}-[А-Яа-я]{2} \\d{6}', `example_number` = 'XIV-АБ 123456' WHERE `card_identity`.`card_identity_id` = 4;");
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class TerminalDirective extends AbstractMigration
{
    public function up()
    {
        $this->query("CREATE TABLE `TerminalDirectiveList` ( `TerminalDirectiveId` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'пк' , `TerminalId` BIGINT UNSIGNED NOT NULL COMMENT 'терминал' , `Directive` JSON NOT NULL COMMENT 'директива' , `DateTimeCreate` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'дата создания' , `DateTimeComplete` TIMESTAMP NULL DEFAULT NULL COMMENT 'дата выполнения' , `UserId` INT UNSIGNED NOT NULL COMMENT 'автор-админ' , PRIMARY KEY (`TerminalDirectiveId`)) ENGINE = InnoDB CHARSET=utf8 COLLATE utf8_general_ci COMMENT = 'Директивы для выполнения на терминале';");
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'TerminalDirectiveType';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('TerminalDirectiveType', 1, 'tdLog', 'Запросить лог'),
                       ('TerminalDirectiveType', 2, 'tdBlock', 'Заблокировать терминал'),
                       ('TerminalDirectiveType', 3, 'tdUnblock', 'Разблокировать терминал');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $this->query("DROP TABLE `TerminalDirectiveList`;");
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'TerminalDirectiveType';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

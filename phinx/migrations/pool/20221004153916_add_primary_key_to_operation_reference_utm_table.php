<?php

use Phinx\Migration\AbstractMigration;

class AddPrimaryKeyToOperationReferenceUtmTable extends AbstractMigration
{
    public function up()
    {
        //$this->table('operation_reference_utm')->changePrimaryKey('operation_id')->update();
        $this->query('ALTER TABLE `operation_reference_utm` ADD PRIMARY KEY (`operation_id`)');
    }

    public function down()
    {
        $this->query('ALTER TABLE operation_reference_utm DROP PRIMARY KEY;');
    }
}

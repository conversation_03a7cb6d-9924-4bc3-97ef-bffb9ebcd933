<?php

use Phinx\Migration\AbstractMigration;

class AddJournalAndParColumnsToAbtCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('PAR', 'string', [
                'limit' => 32,
                'after' => 'PANHash',
                'comment' => 'PAR банковской карты',
                'null' => true,
            ])
            ->addColumn('Journal', 'json', [
                'comment' => 'История действий с картой',
                'null' => true,
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddKzhNsoAlgorithm extends AbstractMigration
{
    public const TABLE_NAME = 'pan_hash_algorithms';
    public const TYPE = 'KzhNso';
    public const ID = 3;

    public function up()
    {
        $this->table(self::TABLE_NAME)->insert([
            [
                'id' => self::ID,
                'type' => self::TYPE,
                'hmac_key' => 'GLeM9sQn6BB8CTiT',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->delete(self::TABLE_NAME)
            ->where(['type' => self::TYPE])
            ->where(['id' => self::ID])
            ->execute();
    }
}

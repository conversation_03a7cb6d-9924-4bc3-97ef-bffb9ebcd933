<?php

use Phinx\Migration\AbstractMigration;

class AddPassengerTypeUser extends AbstractMigration
{
    public function up()
    {
        $this->query('
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`, `Locales`) VALUES
				        ("user-role", "99", "ur<PERSON>assenger", "Пассажир"," {\"EN\": {\"Description\": \"Passenger\"}}" )
        ');
    }

    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'user-role' and RecordName = 'urPassenger';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
    }
}

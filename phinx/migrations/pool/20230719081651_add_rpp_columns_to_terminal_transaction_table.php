<?php

use Phinx\Migration\AbstractMigration;

class AddRppColumnsToTerminalTransactionTable extends AbstractMigration
{
    protected function getTable()
    {
        return $this->table('TerminalTransaction');
    }

    public function up()
    {
        $table = $this->getTable();

        if (!$table->hasColumn('rpp_reason_code')) {
            $table->addColumn('rpp_reason_code', 'integer', [
                'default' => null,
                'null' => true,
                'comment' => 'Причина РПП',
            ]);

            $table->update();
        }
    }

    public function down()
    {
        $table = $this->getTable();

        if ($table->hasColumn('rpp_reason_code')) {
            $table->removeColumn('rpp_reason_code');

            $table->update();
        }
    }
}

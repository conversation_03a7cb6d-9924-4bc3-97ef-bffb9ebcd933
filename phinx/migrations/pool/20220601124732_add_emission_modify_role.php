<?php

use Phinx\Migration\AbstractMigration;

class AddEmissionModifyRole extends AbstractMigration
{
    public function up()
    {
        $this->table('admin_role')->insert([
            [
                'role_title' => 'abt_emission_modify',
                'role_description' => 'Редактирование карт',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()->delete('admin_role')
            ->whereInList('role_title', [
                'abt_emission_modify',
            ])
            ->execute();
    }
}

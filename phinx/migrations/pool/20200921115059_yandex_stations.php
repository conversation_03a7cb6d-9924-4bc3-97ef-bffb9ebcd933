<?php

use Phinx\Migration\AbstractMigration;

class YandexStations extends AbstractMigration
{
    public function up()
    {
        $this->query(
            "CREATE TABLE YandexStation
				(
					Id bigint(20) unsigned PRIMARY KEY NOT NULL AUTO_INCREMENT,
				    CountryTitle varchar(128),
				    CountryCode varchar(128),
				    RegionTitle varchar(128),
				    RegionCode varchar(128),
				    CityTitle varchar(128),
				    CityCode varchar(128),
				    StationDirection varchar(128),
				    StationCode varchar(128),
				    StationEsrCode varchar(128),
				    StationType varchar(128),
				    StationTitle varchar(128),
				    StationLongitude varchar(128),
				    StationLatitude varchar(128),
				    StationTransportType varchar(128),
				    StationSummary varchar(640)
				) COMMENT = 'станции из Яндекса';"
        );
        $this->query("CREATE INDEX YandexStation_CountryTitle_index ON YandexStation (CountryTitle);");
        $this->query("CREATE INDEX YandexStation_CityTitle_index ON YandexStation (CityTitle);");
        $this->query("CREATE INDEX YandexStation_RegionTitle_index ON YandexStation (RegionTitle);");
        $this->query("CREATE INDEX YandexStation_StationDirection_index ON YandexStation (StationDirection);");
        $this->query("CREATE INDEX YandexStation_StationTitle_index ON YandexStation (StationTitle);");
        $this->query("CREATE INDEX YandexStation_StationTransportType_index ON YandexStation (StationTransportType);");
        $this->query("CREATE INDEX YandexStation_StationType_index ON YandexStation (StationType);");
        $this->query("CREATE UNIQUE INDEX YandexStation_StationSummary_uindex ON YandexStation (StationSummary);");
    }

    public function down()
    {
        $this->query("DROP TABLE `YandexStation`;");
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddValidityDatesToAbtCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('DateStart', 'date', [
                'null' => true,
                'after' => 'Type',
                'comment' => 'Дата начала действия карты',
            ])
            ->addColumn('DateEnd', 'date', [
                'null' => true,
                'after' => 'DateStart',
                'comment' => 'Дата окончания действия карты',
            ])
            ->update();
    }
}

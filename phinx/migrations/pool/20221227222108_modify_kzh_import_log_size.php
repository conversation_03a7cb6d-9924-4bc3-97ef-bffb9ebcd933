<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class ModifyKzhImportLogSize extends AbstractMigration
{
    public function up()
    {
        $this->table('__kzh_imports')
            ->changeColumn('log', 'text', [
                'null' => true,
                'limit' => MysqlAdapter::TEXT_MEDIUM,
                'comment' => 'Лог импорта',
            ])
            ->save();
    }

    public function down()
    {
        $this->table('__kzh_imports')
            ->changeColumn('log', 'text', [
                'null' => true,
                'limit' => MysqlAdapter::TEXT_SMALL,
                'comment' => 'Лог импорта',
            ])
            ->save();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreatePanHashAlorithmsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('pan_hash_alorithms')
            ->addColumn('type', 'string', [
                'limit' => 255,
            ])
            ->addColumn('hmac_key', 'string', [
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('left_salt', 'string', [
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('right_salt', 'string', [
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringS18 extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_s1_8h';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                SELECT COUNT(`TerminalTransaction`.`TransactionId`)
                    FROM TerminalTransaction
                    WHERE ((`TerminalTransaction`.`transactionType` = "S1")
                    AND (`TerminalTransaction`.`transactionTime` > (NOW() - INTERVAL 8 HOUR)))',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

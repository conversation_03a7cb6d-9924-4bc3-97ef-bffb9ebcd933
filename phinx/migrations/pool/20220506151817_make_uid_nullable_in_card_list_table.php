<?php

use Phinx\Migration\AbstractMigration;

class MakeUidNullableInCardListTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardList')
            ->changeColumn('UID', 'string', [
                'length' => 14,
                'comment' => 'UID карты',
                'null' => true,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardList')
            ->changeColumn('UID', 'string', [
                'length' => 14,
                'comment' => 'UID карты',
                'null' => false,
            ])
            ->update();
    }
}

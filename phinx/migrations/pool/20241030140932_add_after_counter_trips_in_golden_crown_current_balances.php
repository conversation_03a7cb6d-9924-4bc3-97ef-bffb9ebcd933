<?php

use Phinx\Migration\AbstractMigration;

class AddAfterCounterTripsInGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_current_balances')->hasColumn('after_counter_trips')) {
            $this->table('golden_crown_current_balances')
                ->addColumn('after_counter_trips', 'integer', [
                    'default' => 0,
                    'comment' => 'Количество поездок после'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_current_balances')->hasColumn('after_counter_trips')) {
            $this->table('golden_crown_current_balances')
                ->removeColumn('after_counter_trips')
                ->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateViewPartnerWithOrganizations extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v_partner_with_organizations';

    public function up()
    {
        $this->query(sprintf('CREATE VIEW %s AS
           SELECT p.*,
                  o.organization_id,
                  o.title_full
           FROM partner p
           LEFT JOIN organization o ON o.partner_id = p.partner_id
           WHERE p.is_deleted = 0;
        ', self::NAME_VIEW_VALUE));
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateIndexReferenceNumberInTerminalTransaction extends AbstractMigration
{
    private const INDEX_NAME_VALUE = 'idx__TerminalTransaction_referenceNumber';

    public function up(): void
    {
        if (!$this->hasIndexReferenceNumber()) {
            $this->table('TerminalTransaction')
                ->addIndex('referenceNumber', [
                    'name' => self::INDEX_NAME_VALUE
                ])
                ->update();
        }
    }

    public function down(): void
    {
        if ($this->hasIndexReferenceNumber()) {
            $this->table('TerminalTransaction')
                ->removeIndex('referenceNumber')
                ->update();
        }
    }

    private function hasIndexReferenceNumber(): bool
    {
        $table = $this->table('TerminalTransaction');

        return $table->hasIndex('referenceNumber');
    }
}

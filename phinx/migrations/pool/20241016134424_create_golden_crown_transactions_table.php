<?php

use Phinx\Migration\AbstractMigration;

class CreateGoldenCrownTransactionsTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'golden_crown_transactions';

    public function up()
    {
        if (!$this->hasTable(self::TABLE_NAME_VALUE)) {
            {
                $this->table(self::TABLE_NAME_VALUE)
                    ->addColumn('card_uid', 'string', [
                        'null' => true
                    ])
                    ->addColumn('card_pan', 'string', [
                        'length' => 19,
                        'null' => true
                    ])
                    ->addColumn('ticket_id', 'string', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('service_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('service_name', 'string', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('activation_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('end_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('interval_length', 'char', [
                        'null' => true,
                    ])
                    ->addColumn('counter_money', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('counter_trips', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('counter_type', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('relevance_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('pass_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('create_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('trc_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('amount', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('tariff_sum', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('route_num', 'string', [
                        'null' => true
                    ])
                    ->addColumn('ntran', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('tariff', 'string', [
                        'null' => true
                    ])
                    ->addColumn('st_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('transport_type', 'string', [
                        'null' => true
                    ])
                    ->addColumn('conductor_name', 'string', [
                        'null' => true
                    ])
                    ->addColumn('term_id', 'string', [
                        'null' => true
                    ])
                    ->addColumn('station_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('replenishment_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('replenishment_date', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('replenishment_sum', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('agent_id', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('service_cost', 'integer', [
                        'signed' => false,
                        'null' => true
                    ])
                    ->addColumn('blocked_at', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('unblocked_at', 'datetime', [
                        'null' => true
                    ])
                    ->addColumn('source_name', 'string', [
                        'null' => true
                    ])
                    ->create();
            }
        }
    }

    public function down()
    {
        if ($this->hasTable(self::TABLE_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)->drop();
        }
    }
}

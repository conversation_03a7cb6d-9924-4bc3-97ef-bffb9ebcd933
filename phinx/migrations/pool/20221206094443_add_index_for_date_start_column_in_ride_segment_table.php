<?php

use Phinx\Migration\AbstractMigration;

class AddIndexForDateStartColumnInRideSegmentTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('ride_segment');

        if (!$table->hasIndexByName('idx_date_start')) {
            $table->addIndex('date_start', [
                'name' => 'idx_date_start',
            ]);
        }
        $table->update();
    }

    public function down()
    {
        $table = $this->table('ride_segment');

        if ($table->hasIndexByName('idx_date_start')) {
            $table->removeIndexByName('idx_date_start');
        }

        $table->update();
    }
}

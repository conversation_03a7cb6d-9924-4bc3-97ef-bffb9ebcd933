<?php

use Phinx\Migration\AbstractMigration;

class AddScheduleToTransferMatrix extends AbstractMigration
{
    protected const TABLE = 'transfer_matrix';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('schedule', 'json', [
                'comment' => 'Временные интервалы',
                'null' => true,
                'default' => null,
            ])
            ->update();
    }

    public function down()
    {
        if ($this->table(static::TABLE)->hasColumn('schedule')) {
            $this->table(static::TABLE)
                ->removeColumn('schedule')
                ->update();
        }
    }
}

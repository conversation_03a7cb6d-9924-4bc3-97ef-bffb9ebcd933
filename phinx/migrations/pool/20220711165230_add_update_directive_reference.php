<?php

use Phinx\Migration\AbstractMigration;

class AddUpdateDirectiveReference extends AbstractMigration
{
    public function up()
    {
        $this->deleteRecords();

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'TerminalDirectiveType',
                'RecordId' => 4,
                'RecordName' => 'tdUpdate',
                'RecordDescription' => 'Обновить прошивку',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRecords();
    }

    private function deleteRecords()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where([
                'ReferenceName' => 'TerminalDirectiveType',
                'RecordName' => 'tdUpdate',
            ])
            ->execute();
    }
}

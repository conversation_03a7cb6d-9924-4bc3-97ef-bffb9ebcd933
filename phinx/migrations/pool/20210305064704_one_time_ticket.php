<?php

use Phinx\Migration\AbstractMigration;

class OneTimeTicket extends AbstractMigration
{
    public function up()
    {
        $this->query("CREATE TABLE `DailySecret` (
                `Id` BIGINT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'Primary key',
                `DateTimeStart` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Start of the secret\'s validity period',
                `DateTimeEnd` DATETIME DEFAULT NULL DEFAULT NULL COMMENT 'Expiration date time of the secret. NULL if unexpired',
                `Secret` VARCHAR(128) NULL COMMENT 'Secret key'
             ) ENGINE=InnoDB COMMENT='List of generated secrets';
        ");

        $this->query("ALTER TABLE `DailySecret` ADD INDEX (`DateTimeStart`);");
        $this->query("ALTER TABLE `DailySecret` ADD INDEX (`DateTimeEnd`);");
        $this->query("ALTER TABLE `DailySecret` ADD INDEX (`DateTimeStart`,`DateTimeEnd`);");

        $this->query(
            "CREATE TABLE `OneTimeTicket` (
			`Id` BIGINT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'ид разового билета' ,
			`Code` VARCHAR(512) NOT NULL COMMENT 'код билета',
			`Price` DECIMAL(6,2) NOT NULL COMMENT 'стоимость билета',
			`Status` INT NOT NULL DEFAULT 0 COMMENT 'статус билета. 0 - создан. 1 - погашен' ,
			`Data` JSON NULL DEFAULT NULL COMMENT 'дополнительные данные по билету в жсон' ,
			`IssueVendorId` INT(11) UNSIGNED NOT NULL COMMENT 'Ид партнера, для которого выпустили билет',
			`IssueTerminalId` bigint(20) UNSIGNED NOT NULL COMMENT 'Ид терминала, с которого выпустили билет',
			`IssueTerminalUserId` bigint(20) UNSIGNED NOT NULL COMMENT 'Ид пользователя терминала, который выпустил билет',
			`IssueDateTime` datetime NOT NULL default now() COMMENT 'Дата выпуска билета',
			`RedeemVendorId` INT(11) UNSIGNED NULL DEFAULT NULL COMMENT 'Ид партнера, для который погасил билет',
			`RedeemTerminalId` bigint(20) UNSIGNED NULL DEFAULT NULL  COMMENT 'Ид терминала, с которого погасили билет',
			`RedeemTerminalUserId` bigint(20) UNSIGNED NULL DEFAULT NULL  COMMENT 'Ид пользователя терминала, который погасил билет',
			`RedeemDateTime` datetime NULL DEFAULT NULL COMMENT 'Дата гашения билета',
			`RedeemTicketSeries` varchar(32) NULL default NULL COMMENT 'Серия билета на проезд (транзакции), по которому был погашен разовый билет',
			`RedeemTicketNumber` varchar(32) NULL default NULL COMMENT 'Номер билета на проезд (транзакции), по которому был погашен разовый билет',
			`TranscationId` bigint(20) UNSIGNED NULL default NULL COMMENT 'Ид транзакции, которая была оплачена разовым билетом',
			`EditDateTime` datetime NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT 'Дата редактирования билета'
			) ENGINE = InnoDB COMMENT = 'Разовый билет';"
        );

        $this->query("ALTER TABLE `OneTimeTicket` ADD UNIQUE INDEX (`Code`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`Status`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`IssueVendorId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`IssueTerminalId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`IssueTerminalUserId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`IssueDateTime`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`RedeemVendorId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`RedeemTerminalId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`RedeemTerminalUserId`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`RedeemDateTime`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD UNIQUE INDEX (`RedeemTicketSeries`,`RedeemTicketNumber`);");
        $this->query("ALTER TABLE `OneTimeTicket` ADD INDEX (`TranscationId`);");
    }

    public function down()
    {
        $this->query("DROP TABLE `DailySecret`;");
        $this->query("DROP TABLE `OneTimeTicket`;");
    }
}

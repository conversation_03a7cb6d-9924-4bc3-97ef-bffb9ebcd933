<?php

use Phinx\Migration\AbstractMigration;

class AddCountersTransferedToEmvAbonementList extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('EMVAbonementList')->hasColumn('CountersTransfered')) {
            $this->table('EMVAbonementList')
                ->addColumn('CountersTransfered', 'boolean', [
                    'default' => false,
                    'comment' => 'Счетчики абонемента были перенесены на новый'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('EMVAbonementList')->hasColumn('CountersTransfered')) {
            $this->table('EMVAbonementList')
                ->removeColumn('CountersTransfered')
                ->update();
        }
    }
}

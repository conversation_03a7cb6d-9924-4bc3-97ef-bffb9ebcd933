<?php

use Phinx\Migration\AbstractMigration;

class AddBankCardType extends AbstractMigration
{
    public function up()
    {
        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'ABTCardType',
                'RecordId' => 1,
                'RecordName' => 'ctEmv',
                'RecordDescription' => 'Банковская карта',
                'Locales' => '{"RU": {"Description": "Банковская карта"}, "EN": {"Description": "Bank card"}}',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where([
                'ReferenceName' => 'ABTCardType',
                'RecordId' => 1,
            ])
            ->execute();
    }
}

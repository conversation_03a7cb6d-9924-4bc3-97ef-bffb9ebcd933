<?php

use Phinx\Migration\AbstractMigration;

class AddOptionsGetCommonAbonement extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'sbol_get_common_abonement_social_card',
                'description' => 'Выдавать общегражданские абонементы льготным картам, по-умолчанию Да value = 1, если нет value = 0',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'sbol_get_common_abonement_social_card',
            ])
            ->execute();
    }
}

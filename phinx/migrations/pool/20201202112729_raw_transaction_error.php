<?php

use Phinx\Migration\AbstractMigration;

class RawTransactionError extends AbstractMigration
{
    public function up()
    {
        $this->query("CREATE TABLE `TerminalTransactionParseError` ( `Id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ид' , `TerminalId` BIGINT UNSIGNED NOT NULL COMMENT 'Идентификатор терминала от которого получены транзакции' , `AgentId` INT NOT NULL COMMENT 'Идентификатор пользователя терминала от которого получены транзакции' , `RawId` BIGINT UNSIGNED NOT NULL COMMENT 'ид сырой записи' , `RawIndex` INT NOT NULL COMMENT 'индекс транзакции в пакете' , `RawData` LONGTEXT NULL DEFAULT NULL COMMENT 'транзакция' , `ErrorText` VARCHAR(256) NULL DEFAULT NULL COMMENT 'текст ошибки' , `Status` INT NOT NULL DEFAULT '0' COMMENT 'статус обработки. 0 - ошибка. 1 - повторно успешная' , `ParsedDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'дата и время парсинга' , PRIMARY KEY (`Id`)) ENGINE = InnoDB COMMENT = 'Ошибки парсинга транзакций';");
    }

    public function down()
    {
        $this->query("DROP TABLE `TerminalTransactionParseError`;");
    }
}

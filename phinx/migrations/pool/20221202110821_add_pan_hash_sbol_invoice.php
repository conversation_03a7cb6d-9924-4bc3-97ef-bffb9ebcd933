<?php

use Phinx\Migration\AbstractMigration;

class AddPanHashSbolInvoice extends AbstractMigration
{
    public function up()
    {
        $this->table('st_sbol_invoice')
            ->addColumn('pan_hash', 'string', [
                'null' => true,
                'default' => null,
                'comment' => 'Пан-хэш карты',
                'after' => 'pan',
            ])
            ->addColumn('crypted_pan', 'string', [
                'null' => true,
                'default' => null,
                'comment' => 'Закриптованый пан',
                'after' => 'pan_hash',
            ])
            ->changeColumn('edit_date_time', 'timestamp', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(['pan_hash'], [
                'name' => 'pan_hash',
                'unique' => false,
            ])
            ->addIndex(['crypted_pan'], [
                'name' => 'crypted_pan',
                'unique' => false,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('st_sbol_invoice')
            ->removeColumn('pan_hash')
            ->removeColumn('crypted_pan')
            ->changeColumn(
                'edit_date_time',
                'timestamp',
                [
                    'default' => 'CURRENT_TIMESTAMP',
                    'comment' => 'Дата и время создания(изменения) записи',
                ]
            )
            ->removeIndexByName('pan_hash')
            ->removeIndexByName('crypted_pan')
            ->update();
    }
}

<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateCountTemplatesByScheduleIdView extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v_count_templates_by_schedule_id';

    public function up(): void
    {
        $this->dropView();

        $this->query(
            sprintf(
                "
            CREATE VIEW %s AS
            SELECT schedule_id, count(template_id) AS count_templates
            FROM emv_xref_template_schedule
            WHERE is_deleted = 0
            GROUP BY schedule_id;
        ",
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down(): void
    {
        $this->dropView();
    }

    private function dropView(): void
    {
        $this->query(sprintf("DROP VIEW IF EXISTS %s", self::NAME_VIEW_VALUE));
    }
}

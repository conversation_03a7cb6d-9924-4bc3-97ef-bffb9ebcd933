<?php

use Phinx\Migration\AbstractMigration;

class AddBalanceColumnsToWalletUploadTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletUpload')
            ->addColumn('BalanceBefore', 'decimal', [
                'default' => null,
                'null' => true,
                'precision' => 10,
                'scale' => 3,
                'comment' => 'Баланс кошелька до пополнения',
                'after' => 'Amount',
            ])
            ->addColumn('BalanceAfter', 'decimal', [
                'default' => null,
                'null' => true,
                'precision' => 10,
                'scale' => 3,
                'comment' => 'Баланс кошелька после успешного пополнения',
                'after' => 'BalanceBefore',
            ])
            ->update();
    }
}

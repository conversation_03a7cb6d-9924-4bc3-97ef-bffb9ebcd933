<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class ModifyJournalSizeOnCardListFileImportTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardListFileImport')
            ->changeColumn('Journal', 'text', [
                'comment' => 'Результат загрузки',
                'null' => true,
                'limit' => MysqlAdapter::TEXT_MEDIUM,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardListFileImport')
            ->changeColumn('Journal', 'text', [
                'comment' => 'Результат загрузки',
                'null' => false
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateTransferCardTypesTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('transfer_card_types', [
                'comment' => 'Типы карт для пересадок',
                'id' => 'id',
            ])
            ->addColumn('transfer_id', 'integer', [
                'null' => false,
                'comment' => 'id пересадки',
            ])
            ->addColumn('card_type', 'integer', [
                'null' => false,
                'comment' => 'Тип карты (рефлист «ABTCardType»)',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();

        $this->table('transfer_card_types')
            ->addIndex('card_type')->update();
    }
}

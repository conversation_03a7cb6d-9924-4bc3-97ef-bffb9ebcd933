<?php

use Phinx\Migration\AbstractMigration;

class AddAdditionalSettingsToEmvWriteOffsTemplateTable extends AbstractMigration
{
    protected const TABLE = 'EMVWriteOffsTemplate';

    public function up()
    {
        $this->table(static::TABLE)
            ->addColumn('AdditionalSettings', 'json', [
                'comment' => 'Дополнительные настройки',
                'null' => true,
                'default' => null,
                'after' => 'CurrencyId'
            ])
            ->update();
    }

    public function down()
    {
        $this->table(static::TABLE)
            ->removeColumn('AdditionalSettings')
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddNewUserR<PERSON> extends AbstractMigration
{
    public function up()
    {
//        $this->query("Delete from ReferenceList Where ReferenceName = 'EMVAcquiringMode';");
//        $this->query("ALTER TABLE ReferenceList AUTO_INCREMENT=1;");

        $this->query('
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`, `Locales`) VALUES
				        ("user-role", "7", "urMFCUser", "Сотрудник МФЦ"," {\"EN\": {\"Description\": \"MFC User\"}}" )
        ');
    }



    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'user-role' and RecordName = 'urMFCUser';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
    }
}

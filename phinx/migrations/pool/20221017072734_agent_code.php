<?php

use Phinx\Migration\AbstractMigration;

class AgentCode extends AbstractMigration
{
    public function change()
    {
        $this->table('agent')
            ->addColumn('slug', 'string', [
                'after' => 'pdf_template',
                'null' => true,
                'limit' => 64,
                'comment' => 'Идентификация агента в API покупки абонементов.',
            ])
            ->addIndex(['slug'], [
                'name' => 'slug',
                'unique' => false,
            ])
            ->update();

        $this->table('EMVAbonementList')
            ->addColumn('AgentSalePointCode', 'string', [
                'after' => 'Passage',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код точки агента',
            ])
            ->addColumn('AgentCode', 'string', [
                'after' => 'AgentSalePointCode',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код агента',
            ])
            ->update();

        $this->table('st_sbol_invoice')
            ->addColumn('agent_sale_point_code', 'string', [
                'after' => 'emv_abonement_id',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код точки агента',
            ])
            ->addColumn('agent_code', 'string', [
                'after' => 'agent_sale_point_code',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код агента',
            ])
            ->update();
    }
}

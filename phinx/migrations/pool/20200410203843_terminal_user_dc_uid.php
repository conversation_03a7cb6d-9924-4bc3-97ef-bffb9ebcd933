<?php

use Phinx\Migration\AbstractMigration;

class TerminalUserDcUid extends AbstractMigration
{
    public function up()
    {
        $this->query("alter table TerminalUser add UserUID VARCHAR(32) default null null 
                                         comment \"URL для проверки билета\" after UserPIN;");

        $sql = "
            DROP TRIGGER IF EXISTS tgTerminalUser_BD;        
            CREATE TRIGGER `tgTerminalUser_BD` BEFORE DELETE ON `TerminalUser`
             FOR EACH ROW BEGIN
              SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_AI;  
            CREATE TRIGGER `tgTerminalUser_AI` AFTER INSERT ON `TerminalUser`
             FOR EACH ROW begin
              declare j json default '{\"before\": {}, \"after\": {}}';
              SET j = json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
              SET j = json_set(j, '$.before.TerminalUserId', '', '$.after.TerminalUserId', NEW.TerminalUserId);
              SET j = json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
              SET j = json_set(j, '$.before.UserVendorId', '', '$.after.UserVendorId', NEW.UserVendorId);
              SET j = json_set(j, '$.before.UserRoleId', '', '$.after.UserRoleId', NEW.UserRoleId);
              SET j = json_set(j, '$.before.UserFIO', '', '$.after.UserFIO', NEW.UserFIO);
              SET j = json_set(j, '$.before.UserFIOShort', '', '$.after.UserFIOShort', NEW.UserFIOShort);
              SET j = json_set(j, '$.before.UserPersonnelNumber', '', '$.after.UserPersonnelNumber', NEW.UserPersonnelNumber);
              SET j = json_set(j, '$.before.UserPIN', '', '$.after.UserPIN', NEW.UserPIN);
              SET j = json_set(j, '$.before.UserUID', '', '$.after.UserUID', NEW.UserUID);
              SET j = json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
              SET j = json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
              SET j = json_set(j, '$.before.UserHash', '', '$.after.UserHash', NEW.UserHash);
              SET j = json_set(j, '$.before.UserPINHash', '', '$.after.UserPINHash', NEW.UserPINHash);
              SET j = json_set(j, '$.before.UserSign', '', '$.after.UserSign', NEW.UserSign);
              SET j = json_set(j, '$.before.APISecretKey', '', '$.after.APISecretKey', NEW.APISecretKey);
              SET j = json_set(j, '$.before.EditUserId', '', '$.after.EditUserId', NEW.EditUserId);
              SET j = json_set(j, '$.before.EditDateTime', '', '$.after.EditDateTime', NEW.EditDateTime);
              SET j = json_set(j, '$.before.Version', '', '$.after.Version', NEW.Version);
              call spSaveActionToLog('TerminalUser',  NEW.Id, 'insert', j,NEW.Version);
            END;
            
            DROP TRIGGER IF EXISTS tgTerminalUser_AU;                    
            CREATE TRIGGER `tgTerminalUser_AU` AFTER UPDATE ON `TerminalUser`
             FOR EACH ROW begin
              declare j json default '{\"before\": {}, \"after\": {}}';
              if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
              if not NEW.TerminalUserId <=> OLD.TerminalUserId then SET j = json_set(j, '$.before.TerminalUserId', OLD.TerminalUserId, '$.after.TerminalUserId', NEW.TerminalUserId); end if;
              if not NEW.VendorId <=> OLD.VendorId then SET j = json_set(j, '$.before.VendorId', OLD.VendorId, '$.after.VendorId', NEW.VendorId); end if;
              if not NEW.UserVendorId <=> OLD.UserVendorId then SET j = json_set(j, '$.before.UserVendorId', OLD.UserVendorId, '$.after.UserVendorId', NEW.UserVendorId); end if;
              if not NEW.UserRoleId <=> OLD.UserRoleId then SET j = json_set(j, '$.before.UserRoleId', OLD.UserRoleId, '$.after.UserRoleId', NEW.UserRoleId); end if;
              if not NEW.UserFIO <=> OLD.UserFIO then SET j = json_set(j, '$.before.UserFIO', OLD.UserFIO, '$.after.UserFIO', NEW.UserFIO); end if;
              if not NEW.UserFIOShort <=> OLD.UserFIOShort then SET j = json_set(j, '$.before.UserFIOShort', OLD.UserFIOShort, '$.after.UserFIOShort', NEW.UserFIOShort); end if;
              if not NEW.UserPersonnelNumber <=> OLD.UserPersonnelNumber then SET j = json_set(j, '$.before.UserPersonnelNumber', OLD.UserPersonnelNumber, '$.after.UserPersonnelNumber', NEW.UserPersonnelNumber); end if;
              if not NEW.UserPIN <=> OLD.UserPIN then SET j = json_set(j, '$.before.UserPIN', OLD.UserPIN, '$.after.UserPIN', NEW.UserPIN); end if;
              if not NEW.UserUID <=> OLD.UserUID then SET j = json_set(j, '$.before.UserUID', OLD.UserUID, '$.after.UserUID', NEW.UserUID); end if;
              if not NEW.VendorOrganizationId <=> OLD.VendorOrganizationId then SET j = json_set(j, '$.before.VendorOrganizationId', OLD.VendorOrganizationId, '$.after.VendorOrganizationId', NEW.VendorOrganizationId); end if;
              if not NEW.IsDeleted <=> OLD.IsDeleted then SET j = json_set(j, '$.before.IsDeleted', OLD.IsDeleted, '$.after.IsDeleted', NEW.IsDeleted); end if;
              if not NEW.UserHash <=> OLD.UserHash then SET j = json_set(j, '$.before.UserHash', OLD.UserHash, '$.after.UserHash', NEW.UserHash); end if;
              if not NEW.UserPINHash <=> OLD.UserPINHash then SET j = json_set(j, '$.before.UserPINHash', OLD.UserPINHash, '$.after.UserPINHash', NEW.UserPINHash); end if;
              if not NEW.UserSign <=> OLD.UserSign then SET j = json_set(j, '$.before.UserSign', OLD.UserSign, '$.after.UserSign', NEW.UserSign); end if;
              if not NEW.APISecretKey <=> OLD.APISecretKey then SET j = json_set(j, '$.before.APISecretKey', OLD.APISecretKey, '$.after.APISecretKey', NEW.APISecretKey); end if;
              if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
              if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
              if not NEW.Version <=> OLD.Version
              then
                SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
                call spSaveActionToLog('TerminalUser',  NEW.Id, 'update', j,NEW.Version);
              end if;
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_BU;                    
            CREATE TRIGGER `tgTerminalUser_BU` BEFORE UPDATE ON `TerminalUser`
             FOR EACH ROW begin
              SET @IncVersionFl = 0;
  
              IF OLD.VendorId!=NEW.VendorId OR OLD.UserVendorId!=NEW.UserVendorId THEN
                SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,\"0\"));
                SET @IncVersionFl = 1;
              END IF;
            
              IF OLD.UserRoleId!=NEW.UserRoleId THEN
                SET @IncVersionFl = 1;
              END IF;
              
              IF OLD.UserFIO!=NEW.UserFIO OR OLD.UserFIOShort!=NEW.UserFIOShort OR
                 OLD.UserPersonnelNumber!=NEW.UserPersonnelNumber OR OLD.UserPIN!=NEW.UserPIN OR
                 OLD.VendorOrganizationId!=NEW.VendorOrganizationId THEN
                SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
                SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
                SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
                SET @IncVersionFl = 1;
              END IF;
            
              IF OLD.IsDeleted!=NEW.IsDeleted THEN
                SET @IncVersionFl = 1;
              END IF;
            
              IF @IncVersionFl = 1 THEN
                SET NEW.Version = OLD.Version + 1;
                SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
                SET NEW.EditUserId = CURRENT_USER();
                SET NEW.EditDateTime= NOW();
              END IF;
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_BI;        
            CREATE TRIGGER `tgTerminalUser_BI` BEFORE INSERT ON `TerminalUser`
             FOR EACH ROW begin
              SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,\"0\"));
              SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
              SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
              SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
              SET NEW.Version = 1;
              SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
              SET NEW.EditUserId = CURRENT_USER();
            END;        
        ";
        $this->query($sql);
    }

    public function down()
    {
        $this->query("ALTER TABLE TerminalUser DROP UserUID; ");
        $sql = "
            DROP TRIGGER IF EXISTS tgTerminalUser_BD;        
            CREATE TRIGGER `tgTerminalUser_BD` BEFORE DELETE ON `TerminalUser`
             FOR EACH ROW BEGIN
              SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_AI;  
            CREATE TRIGGER `tgTerminalUser_AI` AFTER INSERT ON `TerminalUser`
             FOR EACH ROW begin
              declare j json default '{\"before\": {}, \"after\": {}}';
              SET j = json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
              SET j = json_set(j, '$.before.TerminalUserId', '', '$.after.TerminalUserId', NEW.TerminalUserId);
              SET j = json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
              SET j = json_set(j, '$.before.UserVendorId', '', '$.after.UserVendorId', NEW.UserVendorId);
              SET j = json_set(j, '$.before.UserRoleId', '', '$.after.UserRoleId', NEW.UserRoleId);
              SET j = json_set(j, '$.before.UserFIO', '', '$.after.UserFIO', NEW.UserFIO);
              SET j = json_set(j, '$.before.UserFIOShort', '', '$.after.UserFIOShort', NEW.UserFIOShort);
              SET j = json_set(j, '$.before.UserPersonnelNumber', '', '$.after.UserPersonnelNumber', NEW.UserPersonnelNumber);
              SET j = json_set(j, '$.before.UserPIN', '', '$.after.UserPIN', NEW.UserPIN);
              SET j = json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
              SET j = json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
              SET j = json_set(j, '$.before.UserHash', '', '$.after.UserHash', NEW.UserHash);
              SET j = json_set(j, '$.before.UserPINHash', '', '$.after.UserPINHash', NEW.UserPINHash);
              SET j = json_set(j, '$.before.UserSign', '', '$.after.UserSign', NEW.UserSign);
              SET j = json_set(j, '$.before.APISecretKey', '', '$.after.APISecretKey', NEW.APISecretKey);
              SET j = json_set(j, '$.before.EditUserId', '', '$.after.EditUserId', NEW.EditUserId);
              SET j = json_set(j, '$.before.EditDateTime', '', '$.after.EditDateTime', NEW.EditDateTime);
              SET j = json_set(j, '$.before.Version', '', '$.after.Version', NEW.Version);
              call spSaveActionToLog('TerminalUser',  NEW.Id, 'insert', j,NEW.Version);
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_AU;                    
            CREATE TRIGGER `tgTerminalUser_AU` AFTER UPDATE ON `TerminalUser`
             FOR EACH ROW begin
              declare j json default '{\"before\": {}, \"after\": {}}';
              if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
              if not NEW.TerminalUserId <=> OLD.TerminalUserId then SET j = json_set(j, '$.before.TerminalUserId', OLD.TerminalUserId, '$.after.TerminalUserId', NEW.TerminalUserId); end if;
              if not NEW.VendorId <=> OLD.VendorId then SET j = json_set(j, '$.before.VendorId', OLD.VendorId, '$.after.VendorId', NEW.VendorId); end if;
              if not NEW.UserVendorId <=> OLD.UserVendorId then SET j = json_set(j, '$.before.UserVendorId', OLD.UserVendorId, '$.after.UserVendorId', NEW.UserVendorId); end if;
              if not NEW.UserRoleId <=> OLD.UserRoleId then SET j = json_set(j, '$.before.UserRoleId', OLD.UserRoleId, '$.after.UserRoleId', NEW.UserRoleId); end if;
              if not NEW.UserFIO <=> OLD.UserFIO then SET j = json_set(j, '$.before.UserFIO', OLD.UserFIO, '$.after.UserFIO', NEW.UserFIO); end if;
              if not NEW.UserFIOShort <=> OLD.UserFIOShort then SET j = json_set(j, '$.before.UserFIOShort', OLD.UserFIOShort, '$.after.UserFIOShort', NEW.UserFIOShort); end if;
              if not NEW.UserPersonnelNumber <=> OLD.UserPersonnelNumber then SET j = json_set(j, '$.before.UserPersonnelNumber', OLD.UserPersonnelNumber, '$.after.UserPersonnelNumber', NEW.UserPersonnelNumber); end if;
              if not NEW.UserPIN <=> OLD.UserPIN then SET j = json_set(j, '$.before.UserPIN', OLD.UserPIN, '$.after.UserPIN', NEW.UserPIN); end if;
              if not NEW.VendorOrganizationId <=> OLD.VendorOrganizationId then SET j = json_set(j, '$.before.VendorOrganizationId', OLD.VendorOrganizationId, '$.after.VendorOrganizationId', NEW.VendorOrganizationId); end if;
              if not NEW.IsDeleted <=> OLD.IsDeleted then SET j = json_set(j, '$.before.IsDeleted', OLD.IsDeleted, '$.after.IsDeleted', NEW.IsDeleted); end if;
              if not NEW.UserHash <=> OLD.UserHash then SET j = json_set(j, '$.before.UserHash', OLD.UserHash, '$.after.UserHash', NEW.UserHash); end if;
              if not NEW.UserPINHash <=> OLD.UserPINHash then SET j = json_set(j, '$.before.UserPINHash', OLD.UserPINHash, '$.after.UserPINHash', NEW.UserPINHash); end if;
              if not NEW.UserSign <=> OLD.UserSign then SET j = json_set(j, '$.before.UserSign', OLD.UserSign, '$.after.UserSign', NEW.UserSign); end if;
              if not NEW.APISecretKey <=> OLD.APISecretKey then SET j = json_set(j, '$.before.APISecretKey', OLD.APISecretKey, '$.after.APISecretKey', NEW.APISecretKey); end if;
              if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
              if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
              if not NEW.Version <=> OLD.Version
              then
                SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
                call spSaveActionToLog('TerminalUser',  NEW.Id, 'update', j,NEW.Version);
              end if;
            END;

            DROP TRIGGER IF EXISTS tgTerminalUser_BU;                    
            CREATE TRIGGER `tgTerminalUser_BU` BEFORE UPDATE ON `TerminalUser`
             FOR EACH ROW begin
              SET @IncVersionFl = 0;
  
              IF OLD.VendorId!=NEW.VendorId OR OLD.UserVendorId!=NEW.UserVendorId THEN
                SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,\"0\"));
                SET @IncVersionFl = 1;
              END IF;
            
              IF OLD.UserRoleId!=NEW.UserRoleId THEN
                SET @IncVersionFl = 1;
              END IF;
              
              IF OLD.UserFIO!=NEW.UserFIO OR OLD.UserFIOShort!=NEW.UserFIOShort OR
                 OLD.UserPersonnelNumber!=NEW.UserPersonnelNumber OR OLD.UserPIN!=NEW.UserPIN OR
                 OLD.VendorOrganizationId!=NEW.VendorOrganizationId THEN
                SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
                SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
                SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
                SET @IncVersionFl = 1;
              END IF;
            
              IF OLD.IsDeleted!=NEW.IsDeleted THEN
                SET @IncVersionFl = 1;
              END IF;
            
              IF @IncVersionFl = 1 THEN
                SET NEW.Version = OLD.Version + 1;
                SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
                SET NEW.EditUserId = CURRENT_USER();
                SET NEW.EditDateTime= NOW();
              END IF;
            END;
            
            DROP TRIGGER IF EXISTS tgTerminalUser_BI;        
            CREATE TRIGGER `tgTerminalUser_BI` BEFORE INSERT ON `TerminalUser`
             FOR EACH ROW begin
              SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,\"0\"));
              SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
              SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
              SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
              SET NEW.Version = 1;
              SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
              SET NEW.EditUserId = CURRENT_USER();
            END;
        ";
        $this->query($sql);
    }
}

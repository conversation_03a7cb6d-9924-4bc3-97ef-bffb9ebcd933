<?php

use Phinx\Migration\AbstractMigration;

class WalletPanHash extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `EMVWalletList` ADD `PANHash` VARCHAR(64) NULL COMMENT 'PANHash карты' AFTER `AgentId`;");
        $this->query("DROP TABLE `EMVWalletCardBindind`;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVWalletList` DROP `PANHash`;");
        $this->query("CREATE TABLE `EMVWalletCardBindind` ( `PANHash` VARCHAR(64) NOT NULL COMMENT 'pan-хэш карты' , `WalletId` INT NOT NULL COMMENT 'ид кошелька' , PRIMARY KEY (`PANHash`)) ENGINE = InnoDB COMMENT = 'Связка кошельков с картами';");
    }
}

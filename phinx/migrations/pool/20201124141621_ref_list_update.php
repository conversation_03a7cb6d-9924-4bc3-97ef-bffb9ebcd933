<?php

use Phinx\Migration\AbstractMigration;

class RefListUpdate extends AbstractMigration
{
    /**
    0 - отключен

    11, 12 - он<PERSON><PERSON><PERSON><PERSON> / оффлайн Инпасс

    21, 22 - он<PERSON><PERSON>йн / оффлайн 2Can Ibox

    31 - онлайн Сбербанк

    41 - онлайн датафон

    51 - оффлайн ТКП транзакции выгружаются напрямую на сервера ТКП

    52 - оффлайн ТКП, транзакции проксируются на стороне СДБП

    61, 62, 63 - A<PERSON><PERSON> онлайн / оффлайн / JPay

    72 - оффлайн PayPro

    81 - онлайн SmartSkyPos
     */
    public function up()
    {
        $this->query("Delete from ReferenceList Where ReferenceName = 'EMVAcquiringMode';");
        $this->query("ALTER TABLE ReferenceList AUTO_INCREMENT=1;");

        $this->query("
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
				        ('EMVAcquiringMode', '0', 'eamOff', 'Отключен'),
				        ('EMVAcquiringMode', '11', 'eamInpassOnLine', 'Инпасс on-line'),
				        ('EMVAcquiringMode', '12', 'eamInpassOffLine', 'Инпасс off-line'),		                                                                                                            
				        ('EMVAcquiringMode', '21', 'eamIBoxOnLine', '2Can iBox on-line'),
				        ('EMVAcquiringMode', '22', 'eamIBoxOffLine', '2Can iBox off-line'),
				        ('EMVAcquiringMode', '31', 'eamSberbankOnLine', 'Сбербанк on-line'),
				        ('EMVAcquiringMode', '41', 'eamDataphoneOnLine', 'Датафон on-line'),
				        ('EMVAcquiringMode', '51', 'eamBPC', 'БПЦ выгрузка транзакций напрямую'),
				        ('EMVAcquiringMode', '51', 'eamSDBP2BPC', 'БПЦ выгрузка транзакций из СДБП'),
				        ('EMVAcquiringMode', '61', 'eamAqsiOnLine', 'aQsi on-line'),
				        ('EMVAcquiringMode', '62', 'eamAqsiOffLine', 'aQsi off-line'),
				        ('EMVAcquiringMode', '63', 'eamAqsiJPAY', 'aQsi JPAY'),
				        ('EMVAcquiringMode', '72', 'eamPayProOffLine', 'PayPro off-line'),
				        ('EMVAcquiringMode', '81', 'eamSmartSkyPosOnLine', 'SmartSkyPos on-line')
        ");
    }



    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVWriteOffsType' and RecordName = 'woWallet';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
    }
}

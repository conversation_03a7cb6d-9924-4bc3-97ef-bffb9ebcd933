<?php

use Phinx\Migration\AbstractMigration;

class AddValidTimeMaximumColumnToWalletsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('ValidTimeMaximum', 'timestamp', [
                'comment' => 'Максимальный срок действия абонемента. Только для типа vttIntervalAndDays',
                'after' => 'ValidTimeEnd',
                'null' => true,
            ])
            ->update();
    }
}

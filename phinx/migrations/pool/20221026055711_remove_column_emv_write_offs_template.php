<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class RemoveColumnEmvWriteOffsTemplate extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE emv_xref_template_schedule AUTO_INCREMENT=1001');

        $table = $this->table('EMVWriteOffsTemplate');

        $columns = [
            'ValidTimeStart',
            'ValidTimeEnd',
            'ValidTimeDays',
            'SellDateTimeStart',
            'SellDateTimeEnd',
        ];

        $changed = false;

        foreach ($columns as $column) {
            if ($table->hasColumn($column)) {
                $table->removeColumn($column);
                $changed = true;
            }
        }

        if ($changed) {
            $table->update();
        }
    }

    public function down()
    {
        $this->query('ALTER TABLE emv_xref_template_schedule AUTO_INCREMENT=1');

        $table = $this->table('EMVWriteOffsTemplate');

        $updated = false;

        if (!$table->hasColumn('ValidTimeStart')) {
            $table->addColumn('ValidTimeStart', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Начало действия',
            ]);
            $updated = true;
        }
        if (!$table->hasColumn('ValidTimeEnd')) {
            $table->addColumn('ValidTimeEnd', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Окончание действия',
            ]);
            $updated = true;
        }
        if (!$table->hasColumn('ValidTimeDays')) {
            $table->addColumn('ValidTimeDays', 'integer', [
                'default' => 0,
                'signed' => false,
                'comment' => 'Период действия(дней)',
            ]);
            $updated = true;
        }
        if (!$table->hasColumn('SellDateTimeStart')) {
            $table->addColumn('SellDateTimeStart', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время начала продаж',
            ]);
            $updated = true;
        }
        if (!$table->hasColumn('SellDateTimeEnd')) {
            $table->addColumn('SellDateTimeEnd', 'datetime', [
                'null' => true,
                'default' => null,
                'comment' => 'Дата и время окончания продаж',
            ]);
            $updated = true;
        }

        if ($updated) {
            $table->update();
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddSocialCategoriesAvailableColumnToAbtCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('CanBuySocialTemplates', 'boolean', [
                'after' => 'IsSocial',
                'default' => 0,
                'null' => false,
                'comment' => 'Возможна покупка социальных абонементов',
            ])
            ->addColumn('SocialCategoriesAvailable', 'json', [
                'after' => 'CanBuySocialTemplates',
                'null' => true,
                'comment' => 'Список доступных льгот',
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddDriverWithNoOutgoTerminalUserRole extends AbstractMigration
{
    private const ROLE_ID = 8;
    private const ROLE_NAME = 'urDriverNoOutgo';

    private const REFERENCE_TABLE_NAME = 'ReferenceList';
    private const REFERENCE_NAME = 'user-role';

    public function up()
    {
        $this->deleteOptions();

        $this->table(self::REFERENCE_TABLE_NAME)
            ->insert([
                [
                    'ReferenceName' => self::REFERENCE_NAME,
                    'RecordId' => self::ROLE_ID,
                    'RecordName' => self::ROLE_NAME,
                    'RecordDescription' => 'Водитель без занарядки',
                    'Locales' => '{"EN": {"Description": "Driver with no outgo"}}',
                ],
            ])
            ->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()
            ->delete(self::REFERENCE_TABLE_NAME)
            ->where(['ReferenceName' => self::REFERENCE_NAME])
            ->where(['RecordName' => self::ROLE_NAME])
            ->execute();
    }
}

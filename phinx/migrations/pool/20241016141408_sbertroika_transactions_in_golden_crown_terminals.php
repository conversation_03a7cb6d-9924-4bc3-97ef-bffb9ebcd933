<?php

use Phinx\Migration\AbstractMigration;

class SbertroikaTransactionsInGoldenCrownTerminals extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'sbertroika_transactions_in_golden_crown_terminals';

    public function up()
    {
        if (!$this->hasTable(self::TABLE_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)
                ->addColumn('card_uid', 'string', [
                    'null' => false,
                ])
                ->addColumn('pass_date', 'datetime', [
                    'null' => false,
                ])
                ->addColumn('create_date', 'datetime', [
                    'null' => false,
                ])
                ->addColumn('trc_id', 'integer', [
                    'null' => false,
                ])
                ->addColumn('service_id', 'integer', [
                    'null' => false,
                ])
                ->addColumn('amount', 'integer', [
                    'null' => false,
                ])
                ->addColumn('tariff_sum', 'integer', [
                    'null' => false,
                ])
                ->addColumn('route_num', 'string', [
                    'null' => false,
                ])
                ->addColumn('ntran', 'integer', [
                    'null' => false,
                ])
                ->addColumn('tariff', 'string', [
                    'null' => false,
                ])
                ->addColumn('st_id', 'integer', [
                    'null' => false,
                ])
                ->addColumn('transport_type', 'string', [
                    'null' => false,
                ])
                ->addColumn('conductor_name', 'string', [
                    'null' => false,
                ])
                ->addColumn('term_id', 'string', [
                    'null' => false,
                ])
                ->addColumn('station_id', 'integer', [
                    'null' => false,
                ])
                ->addColumn('source_name', 'string', [
                    'null' => false,
                ])
                ->create();
        }
    }

    public function down()
    {
        if ($this->hasTable(self::TABLE_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)->drop();
        }
    }
}

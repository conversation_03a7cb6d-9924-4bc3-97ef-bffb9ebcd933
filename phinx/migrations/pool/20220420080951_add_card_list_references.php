<?php

use Phinx\Migration\AbstractMigration;

class AddCardListReferences extends AbstractMigration
{
    public function up()
    {
        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'ABTCardType',
                'RecordId' => 0,
                'RecordName' => 'ctMifare',
                'RecordDescription' => 'Транспортная карта Mifare',
                'Locales' => '{"RU": {"Description": "Транспортная карта Mifare"}, "EN": {"Description": "Mifare transport card"}}',
            ],

            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 0,
                'RecordName' => 'ctApplication',
                'RecordDescription' => 'Заявка',
                'Locales' => '{"RU": {"Description": "Заявка"}, "EN": {"Description": "Application"}}',
            ],
            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 1,
                'RecordName' => 'ctProduction',
                'RecordDescription' => 'Производство',
                'Locales' => '{"RU": {"Description": "Производство"}, "EN": {"Description": "Production"}}',
            ],
            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 2,
                'RecordName' => 'ctWarehouse',
                'RecordDescription' => 'Склад',
                'Locales' => '{"RU": {"Description": "Склад"}, "EN": {"Description": "Warehouse"}}',
            ],
            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 3,
                'RecordName' => 'ctDistribution',
                'RecordDescription' => 'Дистрибуция',
                'Locales' => '{"RU": {"Description": "Дистрибуция"}, "EN": {"Description": "Distribution"}}',
            ],
            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 4,
                'RecordName' => 'ctActivated',
                'RecordDescription' => 'Активирована',
                'Locales' => '{"RU": {"Description": "Активирована"}, "EN": {"Description": "Activated"}}',
            ],
            [
                'ReferenceName' => 'ABTCardStatus',
                'RecordId' => 5,
                'RecordName' => 'ctDestroyed',
                'RecordDescription' => 'Уничтожена',
                'Locales' => '{"RU": {"Description": "Уничтожена"}, "EN": {"Description": "Destroyed"}}',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->execute('
            DELETE FROM ReferenceList 
            WHERE 
                (ReferenceName = "ABTCardType" AND RecordId = 0)
                OR (ReferenceName = "ABTCardStatus" AND RecordId IN (0, 1, 2, 3, 4, 5))
        ');
    }
}

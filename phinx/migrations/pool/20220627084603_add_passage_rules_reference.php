<?php

use Phinx\Migration\AbstractMigration;

class AddPassageRulesReference extends AbstractMigration
{
    public function up()
    {
        $this->deleteRecords();

        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'PassageRuleAction',
                'RecordId' => 0,
                'RecordName' => 'raPass',
                'RecordDescription' => 'Обслужить штатно',
            ],
            [
                'ReferenceName' => 'PassageRuleAction',
                'RecordId' => 1,
                'RecordName' => 'raPay',
                'RecordDescription' => 'Списать с БК или кошелька',
            ],
            [
                'ReferenceName' => 'PassageRuleAction',
                'RecordId' => 2,
                'RecordName' => 'raStop',
                'RecordDescription' => 'Отказать в обслуживании',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteRecords();
    }

    private function deleteRecords()
    {
        $this->getQueryBuilder()->delete('ReferenceList')
            ->where(['ReferenceName' => 'PassageRuleAction'])
            ->execute();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class EditJournalCommentInDeletedAbonementsTable extends AbstractMigration
{
    public function up()
    {
        $this->table('EMVAbonementListDeleted')
            ->changeColumn('Journal', 'json', [
                'comment' => 'История действий с абонементом',
                'null' => true,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('EMVAbonementListDeleted')
            ->changeColumn('Journal', 'json', [
                'comment' => 'Информация об удалении абонемента',
                'null' => true,
            ])
            ->update();
    }
}

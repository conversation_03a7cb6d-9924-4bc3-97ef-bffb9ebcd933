<?php

use Phinx\Migration\AbstractMigration;

class AddSbolOptions extends AbstractMigration
{
    public function change()
    {
        if ($this->isMigratingUp()) {
            $options = $this->table('options');
            $options->insert([
                [
                    "key" => "SBOL_PAN_HASH_DECODE_FLAG",
                    "value" => 0
                ],
                [
                    "key" => "SBOL_PAN_HASH_DECODE_KEY",
                    "value" => 0
                ]
            ])->saveData();
        } else {
            $this->execute("DELETE FROM options WHERE `key` in ('SBOL_PAN_HASH_DECODE_FLAG') ");
            $this->execute("DELETE FROM options WHERE `key` in ('SBOL_PAN_HASH_DECODE_KEY') ");
        }
    }
}

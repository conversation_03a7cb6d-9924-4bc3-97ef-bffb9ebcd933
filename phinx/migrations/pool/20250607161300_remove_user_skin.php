<?php

use Phinx\Migration\AbstractMigration;

class Remove<PERSON><PERSON><PERSON><PERSON> extends AbstractMigration
{
    public function up()
    {
        // Удаляем колонку skin из таблицы admin_user
        if ($this->hasTable('admin_user')) {
            $table = $this->table('admin_user');
            if ($table->hasColumn('skin')) {
                $table->removeColumn('skin')->save();
            }
        }
    }

    public function down()
    {
        // Восстанавливаем колонку skin в таблице admin_user
        if ($this->hasTable('admin_user')) {
            $table = $this->table('admin_user');
            if (!$table->hasColumn('skin')) {
                $table->addColumn('skin', 'string', [
                    'limit' => 64,
                    'null' => true,
                    'default' => null,
                    'comment' => 'цвет интерфейса',
                    'after' => 'name'
                ])->save();
            }
        }
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateLilmitAbonementsForPeriod extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'EMVWriteOffsTemplate';
    private const COLUMN_NAME_COLUMN_VALUE = 'AbonementsLimitForPeriod';

    public function up(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);
        if (!$table->hasColumn(self::COLUMN_NAME_COLUMN_VALUE)) {
            $table->addColumn(self::COLUMN_NAME_COLUMN_VALUE, 'integer', [
                'comment' => 'Количество абонементов, доступных к покупке в указанный период',
                'signed' => false,
                'null' => true,
                'default' => null,
                'after' => 'AbonementsNumberSaleAvailable',
            ]);
            $table->save();
        }
    }

    public function down(): void
    {
        $table = $this->table(self::TABLE_NAME_VALUE);
        if ($table->hasColumn(self::COLUMN_NAME_COLUMN_VALUE)) {
            $table->removeColumn(self::COLUMN_NAME_COLUMN_VALUE);
            $table->save();
        }
    }
}

<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class CreateDeletedAbonementsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementListDeleted', [
                'signed' => false,
                'id' => 'Id',
                'comment' => 'Список удалённых абонементов',
            ])
            ->addColumn('AbonementId', 'integer', [
                'signed' => false,
                'null' => false,
                'limit' => MysqlAdapter::INT_BIG,
                'comment' => 'Ключ абонемента из EMVAbonementList',
            ])
            ->addColumn('WriteOffsId', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор правила списания',
                'null' => false,
            ])
            ->addColumn('Name', 'string', [
                'limit' => 64,
                'comment' => 'Название',
                'null' => false,
            ])
            ->addColumn('Description', 'string', [
                'limit' => 256,
                'comment' => 'Расширенное описание',
                'null' => true,
            ])
            ->addColumn('AgentId', 'integer', [
                'signed' => false,
                'comment' => 'Идентификатор агента, продавшего абонемент',
                'null' => false,
            ])
            ->addColumn('PAN', 'string', [
                'limit' => 32,
                'comment' => 'PAN номер карты',
                'null' => true,
            ])
            ->addColumn('PANHash', 'string', [
                'limit' => 64,
                'comment' => 'Хэш от номера карты',
                'null' => true,
            ])
            ->addColumn('UID', 'string', [
                'limit' => 32,
                'comment' => 'UID номер карты',
                'null' => true,
            ])
            ->addColumn('UIDHash', 'string', [
                'limit' => 64,
                'comment' => 'Хэш от UID номера карты',
                'null' => true,
            ])
            ->addColumn('IsSocial', 'boolean', [
                'default' => false,
                'comment' => 'Является социальным',
                'null' => true,
            ])
            ->addColumn('IsActive', 'boolean', [
                'comment' => 'Признак, что абонемент активен',
                'null' => false,
            ])
            ->addColumn('BlockedReason', 'integer', [
                'signed' => false,
                'comment' => 'Причина блокировки. рефлист',
                'null' => true,
            ])
            ->addColumn('IsDayTripLimit', 'boolean', [
                'comment' => 'Ограничение количества поездок в день',
                'null' => true,
                'default' => 0,
            ])
            ->addColumn('MaxDayTrip', 'integer', [
                'comment' => 'Максимальное количество поездок в день',
                'null' => true,
            ])
            ->addColumn('CurrentDayTrip', 'integer', [
                'comment' => 'Совершено поездок за текущий день',
                'null' => true,
            ])
            ->addColumn('LastTripDateTime', 'timestamp', [
                'comment' => 'Дата и время последней зарегистрированной поездки',
                'null' => true,
            ])
            ->addColumn('ValidTimeType', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Тип срока действия(EMVValidType)',
                'null' => true,
                'default' => 1,
            ])
            ->addColumn('ValidTimeStart', 'timestamp', [
                'comment' => 'Начало действия',
                'null' => true,
            ])
            ->addColumn('ValidTimeDays', 'integer', [
                'comment' => 'Период действия(дней)',
                'null' => true,
                'default' => 0,
            ])
            ->addColumn('ValidTimeEnd', 'timestamp', [
                'comment' => 'Окончание действия',
                'null' => true,
            ])
            ->addColumn('ValidTimeMaximum', 'timestamp', [
                'comment' => 'Максимальный срок действия абонемента. Только для типа vttIntervalAndDays',
                'null' => true,
            ])
            ->addColumn('SellPrice', 'decimal', [
                'precision' => 10,
                'scale' => 3,
                'comment' => 'Стоимость продажи в установленной валюте',
                'null' => true,
            ])
            ->addColumn('CurrencyId', 'integer', [
                'signed' => false,
                'comment' => 'Валюта продажи',
                'null' => true,
                'default' => 1,
            ])
            ->addColumn('SellDateTime', 'timestamp', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время продажи',
                'null' => false,
            ])
            ->addColumn('LastCheckDateTime', 'timestamp', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время последней проверки абонемента на срок действия и счетчики',
                'null' => true,
            ])
            ->addColumn('PersonalData', 'json', [
                'comment' => 'Персональные данные пассажира, купившего абонемент',
                'null' => true,
            ])
            ->addColumn('PersonalDataStatus', 'integer', [
                'limit' => MysqlAdapter::INT_SMALL,
                'signed' => false,
                'comment' => 'Статус обработки персональных данных(передача в ЕЛК)',
                'null' => true,
                'default' => 0,
            ])
            ->addColumn('PersonalDataParseResult', 'json', [
                'comment' => 'Результат обработки ПД и передачи в ЕЛК',
                'null' => true,
            ])
            ->addColumn('TransferredFrom', 'integer', [
                'limit' => MysqlAdapter::INT_BIG,
                'signed' => false,
                'comment' => 'Id абонемента до смены носителя',
                'null' => true,
            ])
            ->addColumn('TransferredTo', 'integer', [
                'limit' => MysqlAdapter::INT_BIG,
                'signed' => false,
                'comment' => 'Id абонемента после смены носителя',
                'null' => true,
            ])
            ->addColumn('DeletedAt', 'timestamp', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время удаления абонемента',
                'null' => true,
            ])
            ->addColumn('Journal', 'json', [
                'comment' => 'Информация об удалении абонемента',
                'null' => true,
            ])
            ->create();
    }
}

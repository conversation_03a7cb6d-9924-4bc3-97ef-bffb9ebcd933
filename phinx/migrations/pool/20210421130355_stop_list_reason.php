<?php

use Phinx\Migration\AbstractMigration;

class StopListReason extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `EMVAbonementList` ADD `BlockedReason` INT UNSIGNED NULL DEFAULT NULL COMMENT 'Причина блокировки. рефлист' AFTER `IsActive`;");
        $sql = "
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('StopListType', 3, 'sltAbonementSocial', 'Белый список по социальному абонементу');
        ";
        $this->query($sql);

        $sql = "
             Delete from ReferenceList Where ReferenceName = 'StopListReason';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('StopListReason', 1, 'slrNoMoney', 'Нет денег на карте'),
                       ('StopListReason', 2, 'slrEmptyCounters', 'Кончились поездки по абонементу'),
                       ('StopListReason', 3, 'slrValidTimeExpired', 'Истек срок абонемента'),
                       ('StopListReason', 4, 'slrNotActive', 'Срок абонемента еще не наступил');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVAbonementList` DROP `BlockedReason`;");
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'StopListType' and RecordId=3;
             Delete from ReferenceList Where ReferenceName = 'StopListReason';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

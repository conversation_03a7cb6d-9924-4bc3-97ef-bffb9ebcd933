<?php

use Phinx\Migration\AbstractMigration;

class ListEmissionRole extends AbstractMigration
{
    public function up()
    {
        $this->query('DELETE FROM `admin_role` WHERE `role_title` = \'abt_emission_list\';');
        $this->query('INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES (\'abt_emission_list\', \'Просмотр списка карт\');');
    }

    public function down()
    {
        $this->query('DELETE FROM `admin_role` WHERE  `role_title` = \'abt_emission_list\';');
    }
}

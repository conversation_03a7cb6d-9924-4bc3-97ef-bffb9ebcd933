<?php

use Phinx\Migration\AbstractMigration;

class AddGoldenCrownBalancesUpdateTrigger extends AbstractMigration
{
    public function up()
    {
        // Define the SQL for the trigger creation
        $triggerSQL = <<<SQL
            CREATE TRIGGER after_update_golden_crown_balances
            AFTER UPDATE ON golden_crown_current_balances
            FOR EACH ROW
            BEGIN
                DECLARE tripIncrement INT DEFAULT 0;
                DECLARE balanceIncrement DECIMAL(10, 2) DEFAULT 0;

                -- Calculate the increments based on the new values
                SET tripIncrement = NEW.current_counter_trips;
                SET balanceIncrement = NEW.current_balance / 100;

                -- Update EMVAbonementCounters' TripCount for the corresponding UID
                IF tripIncrement > 0 THEN
                    UPDATE EMVAbonementCounters
                    SET TripCount = TripCount + tripIncrement
                    WHERE AbonementId = (
                        SELECT AbonementId FROM EMVAbonementList WHERE UID = NEW.card_uid AND IsActive = 1
                    );
                END IF;

                -- Update EMVWalletList's Balance for the corresponding UID
                IF balanceIncrement > 0 THEN
                    UPDATE EMVWalletList
                    SET Balance = Balance + balanceIncrement
                    WHERE UID = NEW.card_uid AND IsActive = 1;
                END IF;
            END;
        SQL;

        // Execute the SQL to create the trigger
        $this->execute($triggerSQL);
    }

    public function down()
    {
        // Define the SQL to drop the trigger
        $dropTriggerSQL = "DROP TRIGGER IF EXISTS after_update_golden_crown_balances;";

        // Execute the SQL to drop the trigger
        $this->execute($dropTriggerSQL);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddActiveSocialCategoriesColumnsToEmvAbonementListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVAbonementList')
            ->addColumn('SocialCategories', 'json', [
                'after' => 'IsSocial',
                'null' => true,
                'comment' => 'Пересечение списка льгот, для которых доступен шаблон и списка льгот, доступных карте',
            ])
            ->addColumn('SocialCategoryForReports', 'integer', [
                'after' => 'SocialCategories',
                'null' => true,
                'comment' => 'Первая по приоритету льгота из списка SocialCategories',
            ])
            ->update();
    }
}

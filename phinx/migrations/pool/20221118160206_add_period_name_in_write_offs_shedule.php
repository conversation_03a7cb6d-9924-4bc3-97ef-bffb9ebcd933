<?php

use Phinx\Migration\AbstractMigration;

class AddPeriodNameInWriteOffsShedule extends AbstractMigration
{
    public function change()
    {
        $this->table('emv_write_offs_template_schedule')
            ->addColumn('period_name', 'string', [
                'default' => null,
                'after' => 'sell_date_time_end',
                'null' => true,
                'comment' => 'Название периода',
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddUniqueIndexToGoldenCrownTransactions extends AbstractMigration
{
    public function up()
    {
        // Определяем таблицу
        $table = $this->table('golden_crown_transactions');

        // Проверяем, существует ли индекс перед созданием
        if (!$table->hasIndexByName('idx_unique_transaction')) {
            // Создаём уникальный индекс
            $table->addIndex(
                [
                    'card_uid',
                    'card_pan',
                    'source_name',
                ],
                [
                    'name' => 'idx_unique_transaction',
                    'unique' => true,
                ]
            )->save();
        }
    }

    public function down()
    {
        // Определяем таблицу
        $table = $this->table('golden_crown_transactions');

        // Удаляем индекс, если он существует
        if ($table->hasIndexByName('idx_unique_transaction')) {
            $table->removeIndexByName('idx_unique_transaction')->save();
        }
    }
}

<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class CreateTransactionTripsTable extends AbstractMigration
{
    protected const TABLE = 'TransactionTrips';

    public function up()
    {
        $this->table(static::TABLE, [
            'id' => false,
            'primary_key' => ['TransactionId'],
            'engine' => 'InnoDB',
            'encoding' => 'utf8',
            'collation' => 'utf8_general_ci',
            'comment' => 'История поездок с пересадками',
            'row_format' => 'DYNAMIC',
        ])
            ->addColumn('TransactionId', 'integer', [
                'null' => false,
                'limit' => MysqlAdapter::INT_BIG,
                'identity' => 'enable',
                'comment'=>'ИД из транзакции'
            ])
            ->addColumn('PANHash', 'string', [
                'length'=> 256,
                'null'=> false,
                'collation' => 'utf8_general_ci',
                'encoding' => 'utf8',
                'comment'=>'PAN hash из транзакции'
            ])
            ->addColumn('TransactionTime', 'datetime', [
                'null' => false,
                'comment'=>'Время транзакции'
            ])
            ->addColumn('RouteId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_BIG,
                'comment'=>'ИД маршрута партнёра из транзакции'
            ])
            ->addColumn('PartnerId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_BIG,
                'comment'=>'ИД партнёра из транзакции'
            ])
            ->addColumn('TransportTypeId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment'=>'Тип транспорта из транзакции'
            ])
            ->addColumn('PaymentType', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment'=>'Тип оплаты из транзакции'
            ])
            ->addColumn('EmvAbonementTemplateId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment'=>'ИД шаблона абонемента'
            ])
            ->addColumn('EmvWalletTemplateId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment'=>'ИД шаблона кошелька'
            ])
            ->addColumn('TransferChainHash', 'string', [
                'null' => true,
                'default' => null,
                'length'=> 256,
                'collation' => 'utf8_general_ci',
                'encoding' => 'utf8',
                'comment'=>'Уникальный hash цепочки поездок. Поездка участвует в расчете пересадок только если ChainHash=null.
                Не создавайте для него уникальный индекс, чтобы корректно работал "on duplicate key update" по идентификатору.'
            ])
            ->addColumn('TransferMatrixId', 'integer', [
                'null' => true,
                'default' => null,
                'limit' => MysqlAdapter::INT_REGULAR,
                'comment'=>'ИД матрицы пересадок'
            ])
            ->addColumn('TransferRule', 'json', [
                'null' => true,
                'default'=>null,
                'comment'=>'Правило на момент обработки'
            ])
            ->addColumn('OriginalData', 'json', [
                'comment' => 'Оригинальные данные транзакции',
                'null' => true,
                'default' => null,
            ])
            ->create();

        $this->table(static::TABLE)
            ->addIndex('TransferChainHash')->addIndex('PANHash')->addIndex(['TransferChainHash','PANHash'])->update();
    }

    public function down()
    {
        $this->table(static::TABLE)->drop()->update();
    }
}

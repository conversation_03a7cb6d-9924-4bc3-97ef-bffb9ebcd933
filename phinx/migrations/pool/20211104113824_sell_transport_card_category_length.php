<?php

use Phinx\Migration\AbstractMigration;

class SellTransportCardCategoryLength extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `sellTransportCardCategory` `sellTransportCardCategory` VARCHAR(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Категория транспортной карты';");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `sellTransportCardCategory` `sellTransportCardCategory` VARCHAR(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Категория транспортной карты';");
    }
}

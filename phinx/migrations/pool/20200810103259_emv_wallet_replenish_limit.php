<?php

use Phinx\Migration\AbstractMigration;

class EmvWalletReplenishLimit extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `EMVWalletList` ADD MinReplenishment float default NULL comment 'Минимальная сумма для пополнения кошелька' after HiLimit, ADD MaxReplenishment float default NULL comment 'Максимальная сумма для пополнения кошелька' after MinReplenishment;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVWalletList` DROP `MinReplenishment`,DROP `MaxReplenishment`;");
    }
}

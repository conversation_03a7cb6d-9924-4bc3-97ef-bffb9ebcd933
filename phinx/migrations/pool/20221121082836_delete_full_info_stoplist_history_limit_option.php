<?php

use Phinx\Migration\AbstractMigration;

class DeleteFullInfoStoplistHistoryLimitOption extends AbstractMigration
{
    public function up()
    {
        $this->getQueryBuilder()
            ->delete('options')
            ->whereInList('key', [
                'full_info_stoplist_history_limit',
            ])
            ->execute();
    }

    public function down()
    {
        $this->table('options')->insert([
            [
                'key' => 'full_info_stoplist_history_limit',
                'description' => 'Лимит количества записей в истории стоп-листа',
                'value' => 20,
                'type' => 'integer',
                'is_editable' => 1,
            ],
        ])->saveData();
    }
}

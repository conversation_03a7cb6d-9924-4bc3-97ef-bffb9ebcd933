<?php

use Phinx\Migration\AbstractMigration;

class GalleryDescriptionLength extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `xref_gallery_partner` CHANGE `description` `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Описание';");

        $station_list = $this->fetchAll("select * from `xref_gallery_partner` where partner_id=0 and object_type='station';");
        foreach ($station_list as $station) {
            $station_description = $this->fetchAll("select description from station where station_id=" . $station["object_id"] . ";");
            $this->query("update xref_gallery_partner set description='" . $station_description[0]["description"] . "' where id=" . $station["id"]);
        }
    }

    public function down()
    {
        $this->query("ALTER TABLE `xref_gallery_partner` CHANGE `description` `description` VARCHAR(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Описание';");
    }
}

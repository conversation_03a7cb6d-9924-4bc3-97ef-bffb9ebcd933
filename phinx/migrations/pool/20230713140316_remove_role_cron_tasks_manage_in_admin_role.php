<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class RemoveRoleCronTasksManageInAdminRole extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'admin_role';

    private const ROLE_NAME_VALUE = 'cron_tasks_manage';
    private const ROLE_DESCRIPTION_VALUE = 'Редактирование крон-задач';

    public function up(): void
    {
        $role = $this->getRole();

        if (!empty($role)) {
            $this->getQueryBuilder()
                ->delete(self::TABLE_NAME_VALUE)
                ->where(['role_title' => self::ROLE_NAME_VALUE])
                ->execute();
        }
    }

    public function down(): void
    {
        $role = $this->getRole();

        if (empty($role)) {
            $this->getQueryBuilder()
                ->insert(['role_title', 'role_description'])
                ->into(self::TABLE_NAME_VALUE)
                ->values([
                    'role_title' => self::ROLE_NAME_VALUE,
                    'role_description' => self::ROLE_DESCRIPTION_VALUE
                ])
                ->execute();
        }
    }

    /**
     * @return array|false
     */
    private function getRole()
    {
        return $this->getQueryBuilder()
            ->select(['role_title', 'role_description'])
            ->from(self::TABLE_NAME_VALUE)
            ->where(['role_title' => self::ROLE_NAME_VALUE])
            ->execute()
            ->fetchAssoc();
    }
}

<?php

use Phinx\Migration\AbstractMigration;
use Phinx\Db\Adapter\MysqlAdapter;

class ImportEmissionReestCreate extends AbstractMigration
{
    public function change()
    {
        $tab =  $this->table('ABTCardListFileImport', ['signed' => false,'id' => 'Id','comment' => 'Реестр загрузки списков карт карт ']);
        $tab
            ->addColumn('Name', 'string', ['length' => 256,'comment' => 'Название загруженного файла','null' => true])
            ->addColumn('Content', 'blob', ['limit' => MysqlAdapter::BLOB_LONG,'comment' => 'Содержимое загруженного файла','null' => false])
            ->addColumn('ContentHash', 'string', ['length' => 128,'comment' => 'Хэш загруженного файла', 'default' => null , 'null' => false])
            ->addColumn('WriteOffsId', 'integer', ['signed' => false, 'comment' => 'Id привязываемого к карте абонемента или кошелька','null' => false])
            ->addColumn('Status', 'integer', ['comment' => 'Статус файла. 0 - загружен, 1 - файл распарсен, -1 - ошибка парсинга', 'default' => 0 , 'null' => false])
            ->addColumn('Journal', 'text', ['comment' => 'Результат загрузки','null' => false])
            ->addColumn('LoadDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP',  'comment' => 'Дата и время загрузки '])
            ->addColumn('ParseDateTime', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время обработки'])
            ->addIndex(['ContentHash'], ['unique' => true])
            ->addIndex('Status')
            ->addIndex('LoadDateTime')
            ->addIndex('ParseDateTime')
            ->create();
    }
}

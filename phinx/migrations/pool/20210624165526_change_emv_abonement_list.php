<?php

use Phinx\Migration\AbstractMigration;

class ChangeEmvAbonementList extends AbstractMigration
{
    public function change()
    {
        $tab = $this->table('EMVAbonementList');
        $tab
            ->addColumn('PersonalData', 'json', ['default' => null,'null' => true,'comment' => 'Персональные данные пассажира, купившего абонемент'])
            ->addColumn('PersonalDataStatus', 'smallinteger', ['signed' => false,'default' => 0,'null' => true,'comment' => 'Статус обработки персональных данных(передача в ЕЛК)'])
            ->addColumn('PersonalDataParseResult', 'json', ['default' => null,'null' => true,'comment' => 'Результат обработки ПД и передачи в ЕЛК'])
            ->save();
    }
}

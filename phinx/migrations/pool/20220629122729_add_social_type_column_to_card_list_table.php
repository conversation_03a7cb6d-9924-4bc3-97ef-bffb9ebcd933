<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class AddSocialTypeColumnToCardListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('ABTCardList')
            ->addColumn('SocialType', 'integer', [
                'null' => true,
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Тип льготы (рефлист «SocialType»)',
                'after' => 'IsSocial',
            ])
            ->update();
    }
}

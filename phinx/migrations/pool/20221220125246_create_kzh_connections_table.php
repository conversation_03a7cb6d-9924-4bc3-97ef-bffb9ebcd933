<?php

use Phinx\Migration\AbstractMigration;

class CreateKzhConnectionsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('__kzh_connections')
            ->addColumn('host', 'string', ['limit' => 255])
            ->addColumn('port', 'integer', ['limit' => 11])
            ->addColumn('login', 'string', ['limit' => 255])
            ->addColumn('password', 'string', ['limit' => 255])
            ->addColumn('ssl', 'boolean')
            ->addColumn('path', 'string', [
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('is_active', 'boolean', ['default' => true])
            ->addColumn('agent_id', 'integer', [
                'null' => false,
                'comment' => 'Id агента, от имени которого будет производиться импорт',
            ])
            ->addColumn('pan_hash_algorithm_id', 'integer', [
                'null' => true,
                'comment' => 'Id алгоритма хеширования PAN (если отличается от sha-256; таблица pan_hash_algorithms)',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();
    }
}

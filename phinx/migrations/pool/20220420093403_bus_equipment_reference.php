<?php

use Phinx\Migration\AbstractMigration;

class BusEquipmentReference extends AbstractMigration
{
    public function up()
    {
        $sql = "
             DELETE FROM ReferenceList where ReferenceName='bus-equipment';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription)
                VALUES
                 ('bus-equipment', 1, 'has_air_conditioning', 'кондиционер'),
                 ('bus-equipment', 2, 'has_toilet', 'туалет'),
                 ('bus-equipment', 3, 'has_wi_fi', 'бесплатный wi-fi'),
                 ('bus-equipment', 4, 'has_usb_charging', 'usb-зарядка'),
                 ('bus-equipment', 5, 'has_tv', 'телевизор'),
                 ('bus-equipment', 6, 'has_stewardess', 'сопровождение стюардессы'),
                 ('bus-equipment', 7, 'has_food', 'питание'),
                 ('bus-equipment', 8, 'has_cold_drinks', 'прохладительные напитки'),
                 ('bus-equipment', 9, 'has_cafe', 'ресторан/кафе/бар на борту (за доп. плату)'),
                 ('bus-equipment', 10, 'has_bike_zone', 'зона для велосипедов'),
                 ('bus-equipment', 11, 'has_baby_carriage_zone', 'зона для детский колясок'),
                 ('bus-equipment', 12, 'pet_allowed', 'можно ли с домашними животными'),
                 ('bus-equipment', 13, 'own_food_allowed', 'можно ли со своей едой'),
                 ('bus-equipment', 14, 'own_alcohol_allowed', 'можно ли со своим алкоголем'),
                 ('bus-equipment', 15, 'has_mmkg_seats', 'доступ и места для ММКГ'),
                 ('bus-equipment', 16, 'has_excursion', 'экскурсия'),
                 ('bus-equipment', 17, 'has_plaid', 'теплые пледы'),
                 ('bus-equipment', 18, 'has_showprogram', 'шоу-программа'),
                 ('bus-equipment', 19, 'has_disco', 'дискотека'),
                 ('bus-equipment', 20, 'has_child_animation', 'детская анимация'),
                 ('bus-equipment', 21, 'has_live_music', 'живая музыка')
                 ;
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'bus-equipment';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddKzhNsoSaratovAlgorithm extends AbstractMigration
{
    public const TABLE_NAME = 'pan_hash_algorithms';
    public const TYPE = 'KzhNsoSaratov';
    public const ID = 4;

    public function up()
    {
        $this->table(self::TABLE_NAME)->insert([
            [
                'id' => self::ID,
                'type' => self::TYPE,
                'hmac_key' => '',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->delete(self::TABLE_NAME)
            ->where(['type' => self::TYPE])
            ->where(['id' => self::ID])
            ->execute();
    }
}

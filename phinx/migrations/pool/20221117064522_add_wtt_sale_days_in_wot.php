<?php

use Phinx\Migration\AbstractMigration;

class AddWttSaleDaysInWot extends AbstractMigration
{
    public function up()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVValidType' and RecordName = 'vttSaleDays';
        				   ALTER TABLE ReferenceList AUTO_INCREMENT=1;");

        $this->query("
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
				        ('EMVValidType', '4', 'vttSaleDays', 'Действует со дня покупки');
        ");
    }

    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVValidType' and RecordName = 'vttSaleDays';
        				   ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
    }
}

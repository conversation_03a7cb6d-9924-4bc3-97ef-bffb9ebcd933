<?php

use Phinx\Migration\AbstractMigration;

class ChangePanInvoice extends AbstractMigration
{
    private const PAN_COLUMN = 'pan';

    private const TABLE_NAME = 'st_sbol_invoice';

    public function up()
    {
        $this->table(self::TABLE_NAME)
            ->changeColumn(self::PAN_COLUMN, 'string', [
                'null' => true,
                'comment' => 'Пан карты',
            ])
            ->update();
    }

    public function down()
    {
        $this->query("UPDATE `st_sbol_invoice` SET pan='' WHERE pan IS NULL");
        $this->table(self::TABLE_NAME)
            ->changeColumn(self::PAN_COLUMN, 'string', [
                'null' => false,
                'comment' => 'Время операции',
            ])
            ->update();
    }
}

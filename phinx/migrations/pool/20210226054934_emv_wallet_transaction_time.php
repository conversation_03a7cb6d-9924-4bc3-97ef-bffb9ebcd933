<?php

use Phinx\Migration\AbstractMigration;

class EmvWalletTransactionTime extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE EMVWalletTransaction modify  TripDateTime  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP comment \'Дата и время поездки\';');
    }

    public function down()
    {
        $this->query('ALTER TABLE EMVWalletTransaction modify  TripDateTime  TIMESTAMP NOT NULL comment \'Дата и время поездки\';');
    }
}

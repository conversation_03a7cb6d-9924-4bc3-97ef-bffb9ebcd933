<?php

use Phinx\Migration\AbstractMigration;

class AddDayOfMounthForAutopayTemplate extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWriteOffsTemplate')
            ->addColumn('DayForAutopay', 'integer', [
                'default' => null,
                'after' => 'IsAddSchedule',
                'null' => true,
                'comment' => 'Число, начиная с которого в автоплатеж выбирается следующий месяц',
            ])
            ->save();
    }
}

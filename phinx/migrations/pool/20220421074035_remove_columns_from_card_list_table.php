<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class RemoveColumnsFromCardListTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('ABTCardList');

        $table->removeIndex('PayToolId')
            ->removeIndex('PayToolType')
            ->removeIndex(['PayToolId', 'PayToolType'])
            ->save();

        $table->removeColumn('PAR')
            ->removeColumn('PARHash')
            ->removeColumn('PayToolId')
            ->removeColumn('PayToolType')
            ->save();
    }

    public function down()
    {
        $this->table('ABTCardList')
            ->addColumn('PAR', 'string', [
                'length' => 32,
                'comment' => 'PAR карты',
                'default' => null,
                'null' => true,
                'after' => 'PANHash',
            ])
            ->addColumn('PARHash', 'string', [
                'length' => 128,
                'comment' => 'SHA256 от PAR карты',
                'default' => null,
                'null' => true,
                'after' => 'PAR',
            ])
            ->addColumn('PayToolId', 'integer', [
                'signed' => false,
                'comment' => 'Ид кошелька, привязанного к карте',
                'null' => true
            ])
            ->addColumn('PayToolType', 'integer', [
                'after' => 'PayToolId',
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Тип абонемента. 1 - абонеме0нт, 2 - тариф, 3 - кошелек',
                'null' => false
            ])
            ->addIndex('PayToolId')
            ->addIndex('PayToolType')
            ->addIndex(['PayToolId', 'PayToolType'], ['unique' => true])
            ->save();
    }
}

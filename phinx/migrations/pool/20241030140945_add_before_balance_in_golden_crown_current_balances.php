<?php

use Phinx\Migration\AbstractMigration;

class AddBeforeBalanceInGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_current_balances')->hasColumn('before_balance')) {
            $this->table('golden_crown_current_balances')
                ->addColumn('before_balance', 'integer', [
                    'default' => 0,
                    'comment' => 'Баланс кошелька до'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_current_balances')->hasColumn('before_balance')) {
            $this->table('golden_crown_current_balances')
                ->removeColumn('before_balance')
                ->update();
        }
    }
}

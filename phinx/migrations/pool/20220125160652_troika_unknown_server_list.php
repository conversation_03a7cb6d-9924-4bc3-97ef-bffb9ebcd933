<?php

use Phinx\Migration\AbstractMigration;

class TroikaUnknownServerList extends AbstractMigration
{
    public function up()
    {
        $sql = "
             DELETE FROM ReferenceList where ReferenceName='sdbp-server-list';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription)
                VALUES
                 ('sdbp-server-list', 1, 'https://api-mobile.tkp2.test.sbertroika.tech', 'sdbp.tkp2.test'),
                 ('sdbp-server-list', 2, 'https://am.06.tkp2.sbertroika.tech', 'sdbp.06.tkp2.prod'),
                 ('sdbp-server-list', 3, 'https://am.33.tkp2.sbertroika.tech', 'sdbp.33.tkp2.prod'),
                 ('sdbp-server-list', 4, 'https://am.36.tkp2.sbertroika.tech', 'sdbp.36.tkp2.prod'),
                 ('sdbp-server-list', 5, 'https://am.40.tkp2.sbertroika.tech', 'sdbp.40.tkp2.prod'),
                 ('sdbp-server-list', 6, 'https://am.47.tkp2.sbertroika.tech', 'sdbp.47.tkp2.prod'),
                 ('sdbp-server-list', 7, 'https://am.61.tkp2.sbertroika.tech', 'sdbp.61.tkp2.prod');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'sdbp-server-list';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

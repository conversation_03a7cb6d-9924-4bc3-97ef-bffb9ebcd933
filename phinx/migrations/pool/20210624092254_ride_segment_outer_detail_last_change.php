<?php

use Phinx\Migration\AbstractMigration;

class RideSegmentOuterDetailLastChange extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `ride_segment_outer_detail` ADD `date_time_change` DATETIME NULL DEFAULT NULL COMMENT 'время последнего изменения даннных сегмента' AFTER `jData`, ADD INDEX (date_time_change);");
    }

    public function down()
    {
        $this->query("ALTER TABLE `ride_segment_outer_detail` DROP `date_time_change`;");
    }
}

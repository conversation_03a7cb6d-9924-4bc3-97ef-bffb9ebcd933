<?php

use Phinx\Migration\AbstractMigration;

class AddSocialCategoryRestrictionsToEmvWriteOffsTemplateTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWriteOffsTemplate')
            ->addColumn('IsRestrictedBySocialCategories', 'boolean', [
                'null' => false,
                'default' => false,
                'comment' => 'Действует ограничение по льготным категориям',
                'after' => 'IsSocial',
            ])
            ->addColumn('AllowedSocialCategories', 'json', [
                'null' => true,
                'default' => null,
                'comment' => 'Разрешённые типы льгот (рефлист «SocialCategory», только при IsRestrictedBySocialCategories = 1)',
                'after' => 'IsRestrictedBySocialCategories',
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringTarifficationQueue extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_tariffication_queue';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                            SELECT DATE(transactionTime), COUNT(TransactionId)
                            FROM TerminalTransaction
                                WHERE NSIReceivedDateTime IS NULL
                                    AND NOT IsTariffed
                                    AND (paymentType = 3 OR paymentType = 1)
                                    AND transportCardTransactionData IS NOT NULL
                                GROUP BY DATE(transactionTime);',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

<?php

use Phinx\Migration\AbstractMigration;
use Phinx\Util\Literal;

class AddReferenceNumberColumnInTerminalTransactionTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalTransaction');

        if (!$table->hasColumn('referenceNumber')) {
            $table->addColumn('referenceNumber', 'uuid', [
                'null' => true,
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalTransaction');

        if ($table->hasColumn('referenceNumber')) {
            $table->removeColumn('referenceNumber');
        }

        $table->update();
    }
}

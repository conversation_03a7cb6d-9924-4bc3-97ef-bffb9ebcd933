<?php

use Phinx\Migration\AbstractMigration;

class AddEditDateTimeEditUserId extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `emv_xref_template_agent` ADD edit_date_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'Дата и время изменений\', add edit_user_id INT not null COMMENT \'Ид пользователя, который осуществил запись/изменение\'');
        $this->query('ALTER TABLE `emv_xref_template_schedule` ADD edit_date_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'Дата и время изменений\', add edit_user_id INT not null COMMENT \'Ид пользователя, который осуществил запись/изменение\'');
        $this->query('ALTER TABLE `EMVAbonementList` ADD EditDateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'Дата и время изменений\', add EditUserId INT not null COMMENT \'Ид пользователя, который осуществил запись/изменение\'');
        $this->query('ALTER TABLE `EMVWalletList` ADD EditDateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'Дата и время изменений\', add EditUserId INT not null COMMENT \'Ид пользователя, который осуществил запись/изменение\'');
        $this->query('
			DROP TRIGGER IF EXISTS tg_emv_xref_template_agent_BI;
            CREATE TRIGGER `tg_emv_xref_template_agent_BI` BEFORE INSERT ON `emv_xref_template_agent` FOR EACH ROW
            begin
                SET NEW.edit_user_id = @editUserId;
                IF @editUserId IS NULL
                    THEN
                        SET NEW.edit_user_id = -1;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tg_emv_xref_template_agent_BU;
           CREATE TRIGGER `tg_emv_xref_template_agent_BU` BEFORE UPDATE ON `emv_xref_template_agent` FOR EACH ROW
           begin
                IF
                    OLD.is_deleted!=NEW.is_deleted
                THEN
                SET NEW.edit_user_id = @editUserId;
                    IF @editUserId IS NULL
                        THEN
                            SET NEW.edit_user_id = -1;
                    END IF;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tg_emv_xref_template_schedule_BI;
            CREATE TRIGGER `tg_emv_xref_template_schedule_BI` BEFORE INSERT ON `emv_xref_template_schedule` FOR EACH ROW
            begin
                SET NEW.edit_user_id = @editUserId;
                IF @editUserId IS NULL
                    THEN
                        SET NEW.edit_user_id = -1;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tg_emv_xref_template_schedule_BU;
           CREATE TRIGGER `tg_emv_xref_template_schedule_BU` BEFORE UPDATE ON `emv_xref_template_schedule` FOR EACH ROW
           begin
                IF
                    OLD.is_deleted!=NEW.is_deleted
                THEN
                SET NEW.edit_user_id = @editUserId;
                    IF @editUserId IS NULL
                        THEN
                            SET NEW.edit_user_id = -1;
                    END IF;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tgEMVAbonementList_BI;
            CREATE TRIGGER `tgEMVAbonementList_BI` BEFORE INSERT ON `EMVAbonementList` FOR EACH ROW
            begin
                SET NEW.EditUserId = @editUserId;
                IF @editUserId IS NULL
                    THEN
                        SET NEW.EditUserId = -1;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tgEMVAbonementList_BU;
           CREATE TRIGGER `tgEMVAbonementList_BU` BEFORE UPDATE ON `EMVAbonementList` FOR EACH ROW
           begin
                IF
                    OLD.BlockStatus!=NEW.BlockStatus
                THEN
                SET NEW.EditUserId = @editUserId;
                    IF @editUserId IS NULL
                        THEN
                            SET NEW.EditUserId = -1;
                    END IF;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tgEMVWalletList_BI;
            CREATE TRIGGER `tgEMVWalletList_BI` BEFORE INSERT ON `EMVWalletList` FOR EACH ROW
            begin
                SET NEW.EditUserId = @editUserId;
                IF @editUserId IS NULL
                    THEN
                        SET NEW.EditUserId = -1;
                END IF;
            END
        ');

        $this->query('
			DROP TRIGGER IF EXISTS tgEMVWalletList_BU;
           CREATE TRIGGER `tgEMVWalletList_BU` BEFORE UPDATE ON `EMVWalletList` FOR EACH ROW
           begin
                IF
                    OLD.BlockStatus!=NEW.BlockStatus
                THEN
                SET NEW.EditUserId = @editUserId;
                    IF @editUserId IS NULL
                        THEN
                            SET NEW.EditUserId = -1;
                    END IF;
                END IF;
            END
        ');
    }

    public function down()
    {
        $this->query('DROP TRIGGER if exists tg_emv_xref_template_agent_BI;');
        $this->query('DROP TRIGGER if exists tg_emv_xref_template_agent_BU;');
        $this->query('DROP TRIGGER if exists tg_emv_xref_template_schedule_BI;');
        $this->query('DROP TRIGGER if exists tg_emv_xref_template_schedule_BU;');
        $this->query('DROP TRIGGER if exists tgEMVAbonementList_BI;');
        $this->query('DROP TRIGGER if exists tgEMVAbonementList_BU;');
        $this->query('DROP TRIGGER if exists tgEMVWalletList_BI;');
        $this->query('DROP TRIGGER if exists tgEMVWalletList_BU;');

        $this->query('ALTER TABLE `emv_xref_template_agent` DROP `edit_date_time`, DROP edit_user_id');
        $this->query('ALTER TABLE `EMVAbonementList` DROP `EditDateTime`, DROP EditUserId');
        $this->query('ALTER TABLE `emv_xref_template_schedule` DROP `edit_date_time`, DROP edit_user_id');
        $this->query('ALTER TABLE `EMVWalletList` DROP `EditDateTime`, DROP EditUserId');
    }
}

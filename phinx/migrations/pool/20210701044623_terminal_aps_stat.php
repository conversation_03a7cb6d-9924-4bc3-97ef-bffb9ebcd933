<?php

use Phinx\Migration\AbstractMigration;

class <PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractMigration
{
    public function up()
    {
        $this->query(
            "CREATE TABLE `TerminalApsStat` (
				`Id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Первичный ключ' ,
				`TerminalId` BIGINT NOT NULL COMMENT 'Ид терминала в СДБП' ,
				`VendorId` INT NOT NULL COMMENT 'Ид вендора' ,
				`TerminalVendorId` INT NOT NULL COMMENT 'Ид терминала у вендора' ,
				`Dt` BIGINT UNSIGNED NOT NULL COMMENT 'Unix time в мс, когда были сделаны данные' ,
				`Nm` VARCHAR(64) NOT NULL COMMENT 'идентификатор устройства подсчета пассажиров' ,
				`Tp` INT NOT NULL COMMENT 'Тип устройства' ,
				`TotalIn` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Счетчик вошедших пассажиров, накопительный' ,
				`TotalOut` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Счетчик вышедших пассажиров, накопительный' ,
				`CountIn` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Кол-во вошедших пассажиров относительно предыдущей записи' ,
				`CountOut` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Счетчик вышедших пассажиров относительно предыдущей записи' ,
				`Lat` DECIMAL(20,16) NULL COMMENT 'Широта' ,
				`Lon` DECIMAL(20,16) NULL COMMENT 'Долгота' ,
				PRIMARY KEY (`Id`), INDEX `TerminalApsStat_TerminalId_idx` (`TerminalId`),
				INDEX `TerminalApsStat_VendorId_idx` (`VendorId`),
				INDEX `TerminalApsStat_TerminalVendorId_idx` (`TerminalVendorId`)
			)
			ENGINE = InnoDB COMMENT = 'Распарсенные данные по счетчика подсчета пассажиров';"
        );
        $this->query("ALTER TABLE `TerminalApsStat` ADD UNIQUE `TerminalApsStat_Group1_uniq_idx` (`TerminalId`, `Dt`, `Nm`, `Tp`);");
    }

    public function down()
    {
        $this->query("DROP TABLE `TerminalApsStat`;");
    }
}

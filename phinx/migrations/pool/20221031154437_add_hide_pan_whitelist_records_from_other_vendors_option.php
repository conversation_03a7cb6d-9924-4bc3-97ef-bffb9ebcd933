<?php

use Phinx\Migration\AbstractMigration;

class AddHidePanWhitelistRecordsFromOtherVendorsOption extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'hide_pan_whitelist_records_from_other_vendors',
                'description' => 'Показывать записи стоплиста с типом 1 и 3 только привязанным вендорам',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'hide_pan_whitelist_records_from_other_vendors',
            ])
            ->execute();
    }
}

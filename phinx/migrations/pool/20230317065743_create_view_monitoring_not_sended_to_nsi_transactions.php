<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringNotSendedToNsiTransactions extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_not_sended_to_nsi_transactions';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                SELECT COUNT(1) AS cnt
                FROM TerminalTransaction
                WHERE ParseDateTime >= DATE_ADD(DATE(NOW()), INTERVAL -7 DAY)
                  AND DATE_ADD(ParseDateTime, INTERVAL 1 HOUR) < NOW()
                  AND NSIReceivedDateTime IS NULL',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

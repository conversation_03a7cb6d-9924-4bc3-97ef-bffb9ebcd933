<?php

use Phinx\Migration\AbstractMigration;

class AddIndexOnUpdatedAtGoldenCrownCurrentBalances extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('golden_crown_current_balances');
        if (!$table->hasIndex('updated_at')) {
            $table->addIndex(['updated_at'], ['name' => 'idx_updated_at'])->save();
        }
    }

    public function down()
    {
        $table = $this->table('golden_crown_current_balances');
        if ($table->hasIndex('updated_at')) {
            $table->removeIndexByName('idx_updated_at')->save();
        }
    }
}

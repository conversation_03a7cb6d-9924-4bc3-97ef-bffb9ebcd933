<?php

use Phinx\Migration\AbstractMigration;

class AddFreeRefundAgent extends AbstractMigration
{
    public function up()
    {
        $this->table('agent')
            ->addColumn('free_refund', 'boolean', [
                'default' => 0,
                'comment' => 'Возврат билета в любое время',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('agent')
            ->removeColumn('free_refund')
            ->update();
    }
}

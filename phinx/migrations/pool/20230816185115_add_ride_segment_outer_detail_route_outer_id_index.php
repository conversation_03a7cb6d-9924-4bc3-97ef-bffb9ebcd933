<?php

use Phinx\Migration\AbstractMigration;

class AddRideSegmentOuterDetailRouteOuterIdIndex extends AbstractMigration
{
    protected const TABLE_NAME = 'ride_segment_outer_detail';
    protected const INDEX_NAME = 'idx__route_outer_id';
    protected const COLUMN_NAME = 'route_outer_id';

    public function up()
    {
        $table = $this->table(self::TABLE_NAME);

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $this->table(self::TABLE_NAME)
                ->addIndex([self::COLUMN_NAME], ['name' => self::INDEX_NAME])
                ->update();
        }
    }

    public function down()
    {
        $table = $this->table(self::TABLE_NAME);

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME)->update();
        }
    }
}

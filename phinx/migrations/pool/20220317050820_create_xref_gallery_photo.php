<?php

use Phinx\Migration\AbstractMigration;

class CreateXrefGalleryPhoto extends AbstractMigration
{
    public function change()
    {
        $tab =  $this->table('xref_gallery_partner', ['signed' => false,'id' => 'id','comment' => 'Временная таблица со списком галерей из НСИ']);
        $tab
            ->addColumn('partner_id', 'integer', ['signed' => false, 'comment' => 'Ид вендора, владельца галереи','null' => false])
            ->addColumn('gallery_id', 'integer', ['signed' => false, 'comment' => 'ID галереи','null' => false])
            ->addColumn('title', 'string', ['length' => 128,'comment' => 'Название','null' => true])
            ->addColumn('description', 'string', ['length' => 512,'comment' => 'Описание','null' => true])
            ->addColumn('object_type', 'string', ['length' => 32,'comment' => 'Сущность - название таблицы','null' => false])
            ->addColumn('object_id', 'integer', ['signed' => false, 'comment' => 'Идентификатор сущности', 'null' => false])
            ->addColumn('version', 'string', ['length' => 128,'comment' => 'Версия данных галереи','null' => true])

            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            ->addIndex(['partner_id'], ['unique' => false])
            ->addIndex(['object_type','object_id'], ['unique' => false])
            ->addIndex(['partner_id', 'gallery_id'], ['unique' => true])
            ->addIndex('edit_date_time')
            ->create();

        $tab =  $this->table('xref_photo_partner', ['signed' => false,'id' => 'id','comment' => 'Временная таблица со списком фото из НСИ']);
        $tab
            ->addColumn('partner_id', 'integer', ['signed' => false, 'comment' => 'Ид вендора, владельца фото','null' => false])
            ->addColumn('photo_id', 'integer', ['signed' => false, 'comment' => 'ID фото','null' => false])
            ->addColumn('gallery_id', 'integer', ['signed' => false, 'comment' => 'ID галереи','null' => false])
            ->addColumn('priority', 'integer', ['signed' => false, 'comment' => 'Приоритет', 'null' => false])
            ->addColumn('photo', 'blob', ['length' => \Phinx\Db\Adapter\MysqlAdapter::BLOB_LONG,'comment' => 'фото'])

            ->addColumn('version', 'string', ['length' => 128,'comment' => 'Версия данных фото','null' => true])
            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            ->addIndex(['partner_id'], ['unique' => false])
            ->addIndex(['partner_id', 'gallery_id', 'photo_id'], ['unique' => true])
            ->addIndex('edit_date_time')
            ->create();
    }
}

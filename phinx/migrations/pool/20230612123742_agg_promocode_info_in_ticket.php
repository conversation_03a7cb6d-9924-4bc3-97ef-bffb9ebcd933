<?php

use Phinx\Migration\AbstractMigration;

class AggPromocodeInfoInTicket extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('ticket');

        if (!$table->hasColumn('promocode_percent')) {
            $table->addColumn('promocode_percent', 'float', [
                'null' => true,
                'default' => null,
                'comment' => '% скидки',
            ]);
        }
        if (!$table->hasColumn('promocode_amount')) {
            $table->addColumn('promocode_amount', 'float', [
                'null' => true,
                'default' => null,
                'comment' => 'Сумма скидки',
            ]);
        }
        $table->update();
    }

    public function down()
    {
        $table = $this->table('ticket');

        if ($table->hasColumn('promocode_percent')) {
            $table->removeColumn('promocode_percent');
        }
        if ($table->hasColumn('promocode_amount')) {
            $table->removeColumn('promocode_amount');
        }
        $table->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddOptionCheckBindWriteOffsTemplateOrganization extends AbstractMigration
{
    public function up()
    {
        $this->deleteOptions();

        $this->table('options')->insert([
            [
                'key' => 'check_bind_write_offs_template_ride_segment',
                'description' => 'Проверять привязку шаблона абонемента к организации по рейсу при тарификации',
                'value' => 1,
                'type' => 'boolean',
                'is_editable' => 1,
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->deleteOptions();
    }

    private function deleteOptions()
    {
        $this->getQueryBuilder()->delete('options')
            ->whereInList('key', [
                'check_bind_write_offs_template_ride_segment',
            ])
            ->execute();
    }
}

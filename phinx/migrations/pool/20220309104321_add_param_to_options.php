<?php

use Phinx\Migration\AbstractMigration;

class AddParamToOptions extends AbstractMigration
{
    public function change()
    {
        if ($this->isMigratingUp()) {
            $options = $this->table('options');
            $options->insert([
                [
                    "key" => "route_last_time_update",
                    "value" => date("Y-m-d H:i:s")
                ],
                [
                    "key" => "route_update_interval",
                    "value" => '300'
                ]

            ])->saveData();
        } else {
            $this->execute("DELETE FROM options WHERE `key` in ('route_last_time_update','route_update_interval') ");
        }
    }
}

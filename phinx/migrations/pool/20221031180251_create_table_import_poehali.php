<?php

use Phinx\Migration\AbstractMigration;

class CreateTableImportPoehali extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('__import_poehali', [
            'signed' => false,
            'id' => 'id',
            'comment' => 'Импортированные записи эмиссии "Поехали"',
        ]);

        $table->addColumn('pan_card', 'string', [
            'comment' => 'PAN-карты',
            'null' => false
        ]);

        $table->addColumn('type', 'string', [
            'comment' => 'Тип проездного',
            "null" => false
        ]);

        $table->addColumn('status', 'string', [
            'comment' => 'Статус проездного',
            'null' => false
        ]);

        $table->addColumn('code_social', 'integer', [
            'comment' => 'Код соц. льготы',
            "null" => true
        ]);

        $table->addColumn('date_end_benefit', 'datetime', [
            'comment' => 'Срок действия льготы',
            'null' => true
        ]);

        $table->addIndex('pan_card');
        $table->addIndex('type');
        $table->addIndex('status');
        $table->addIndex('code_social');
        $table->addIndex('date_end_benefit');

        $table->create();
    }
}

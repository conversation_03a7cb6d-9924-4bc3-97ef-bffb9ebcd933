<?php

use Phinx\Migration\AbstractMigration;

class ImportEmissionRole extends AbstractMigration
{
    public function up()
    {
        $this->query('DELETE FROM `admin_role` WHERE `role_title` = \'abt_emission_import\';');
        $this->query('INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES (\'abt_emission_import\', \'Импорт карт внешней эмиссии\');');
    }

    public function down()
    {
        $this->query('DELETE FROM `admin_role` WHERE  `role_title` = \'abt_emission_import\';');
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreatePanHashesTable extends AbstractMigration
{
    public function change()
    {
        $this->table('pan_hashes')
            ->addColumn('hashable_type', 'string')
            ->addColumn('hashable_id', 'integer')
            ->addColumn('algorithm_id', 'integer')
            ->addColumn('hash', 'string')
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])

            ->addIndex(['hashable_type', 'hashable_id'])
            ->addIndex(['hashable_type', 'hashable_id', 'algorithm_id'], ['unique' => true])
            ->addIndex(['hash'])

            ->create();
    }
}

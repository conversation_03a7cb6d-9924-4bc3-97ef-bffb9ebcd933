<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringTarifficationSpeed extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_tariffication_speed';

    public function up()
    {
        $this->query(
            'CREATE OR REPLACE VIEW ' . self::NAME_VIEW_VALUE . ' AS
                 SELECT
                     DATE_FORMAT(tt.TarifficationDateTime, \'%Y-%m-%d %H\') AS hour,
                     COUNT(TransactionId) as transactions_tariffied,
                     AVG(tt.TarifficationTime) AS average_time,
                     (1/ AVG(tt.TarifficationTime)) as average_per_second
                 FROM (SELECT TransactionId, TarifficationDateTime, TarifficationTime
                       FROM TerminalTransaction
                       WHERE IsTariffed > 0
                         AND (paymentType = 3 OR paymentType = 1)
                         AND transactionTime > (NOW() - INTERVAL 24 HOUR)) tt
                 GROUP BY DATE_FORMAT(TarifficationDateTime, \'%Y-%m-%d %H\')
                 ORDER BY TarifficationDateTime;'
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

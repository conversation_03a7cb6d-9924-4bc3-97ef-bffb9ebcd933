\<?php

use Phinx\Migration\AbstractMigration;

class AddWoTroikaType extends AbstractMigration
{
    public function up()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVWriteOffsType' and RecordName = 'woTroika';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
        $this->query("
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
				        ('EMVWriteOffsType', '4', 'woTroika', 'Тройка');
        ");

        $this->query("ALTER TABLE `EMVWriteOffsTemplate` ADD `app_code` INT NULL DEFAULT 0 COMMENT 'app_code' AFTER `CurrencyId`, ADD `crd_code` INT NULL DEFAULT 0 COMMENT 'crd_code' AFTER `app_code`;");
        $this->query("UPDATE `EMVWriteOffsTemplate` SET `app_code` = 0, `crd_code` = 0 WHERE Id>0;");
    }

    public function down()
    {
        $this->query(" Delete from ReferenceList Where ReferenceName = 'EMVWriteOffsType' and RecordName = 'woTroika';
        				ALTER TABLE ReferenceList AUTO_INCREMENT=1;");


        $this->query("ALTER TABLE `EMVWriteOffsTemplate` DROP `app_code`;");
        $this->query("ALTER TABLE `EMVWriteOffsTemplate` DROP `crd_code`;");
    }
}

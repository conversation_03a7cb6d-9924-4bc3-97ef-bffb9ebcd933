<?php

use Phinx\Migration\AbstractMigration;

class EmvWalletUpdate extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `EMVWalletList` DROP `PANHash`;");
        $this->query("ALTER TABLE `EMVWalletList` ADD `Number` VARCHAR(12) NOT NULL COMMENT 'номер кошелька' AFTER `SellDateTime`;");
        $this->query("CREATE TABLE `EMVWalletCardBindind` ( `PANHash` VARCHAR(64) NOT NULL COMMENT 'pan-хэш карты' , `WalletId` INT NOT NULL COMMENT 'ид кошелька' , PRIMARY KEY (`PANHash`)) ENGINE = InnoDB COMMENT = 'Связка кошельков с картами';");
        $this->query("ALTER TABLE `EMVWalletList` ADD `ExternalClientId` INT NOT NULL COMMENT 'ид аккаунта в ТОР-ЕЛК' AFTER `Number`;");
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVWalletList` ADD `PANHash` VARCHAR(64) NOT NULL COMMENT 'PANHash карты' AFTER `AgentId`;");
        $this->query("ALTER TABLE `EMVWalletList` DROP `Number`;");
        $this->query("DROP TABLE `EMVWalletCardBindind`;");
        $this->query("ALTER TABLE `EMVWalletList` DROP `ExternalClientId`;");
    }
}

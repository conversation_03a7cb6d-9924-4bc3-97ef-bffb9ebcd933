<?php

use Phinx\Migration\AbstractMigration;

class FixUpdateDateTime extends AbstractMigration
{
    public function up()
    {
        $this->query("ALTER TABLE `TerminalOrganizationList` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalUser` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalGroup` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalList` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalRoleFunction` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalGroup` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `EMVWriteOffsBinding` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `EMVWriteOffsTemplate` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalOrganizationList` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalUser` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalGroup` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalList` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalRoleFunction` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `TerminalGroup` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `EMVWriteOffsBinding` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT 'Дата и время создания(изменения) записи'");
        $this->query("ALTER TABLE `EMVWriteOffsTemplate` CHANGE `EditDateTime` `EditDateTime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи'");
    }
}

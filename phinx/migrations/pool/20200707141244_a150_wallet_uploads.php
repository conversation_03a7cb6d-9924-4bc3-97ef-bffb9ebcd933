<?php

use Phinx\Migration\AbstractMigration;

class A150WalletUploads extends AbstractMigration
{
    public function up()
    {
        $this->query("Delete from ReferenceList Where ReferenceName = 'EMVWalletUploadStatus';");
        $this->query("ALTER TABLE ReferenceList AUTO_INCREMENT=1;");
        $this->query("
		        INSERT INTO `ReferenceList` (`ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
				        ('EMVWalletUploadStatus', '0', 'wusCreate', 'Списание пополнения создано'),
				        ('EMVWalletUploadStatus', '1', 'wusRequested', 'Запрос на списание отпрален'),
				        ('EMVWalletUploadStatus', '2', 'wusSuccessfully', 'Списание успешно'),
				        ('EMVWalletUploadStatus', '3', 'wusUnsuccessful', 'Списание не успешно'),
				        ('EMVWalletUploadStatus', '4', 'wusDisabled', 'Списание не успешно карта добавлена в стоп-лист')		                                                                                                             
				        ;
        ");


        $this->query("
            DROP TABLE  IF EXISTS EMVWalletUpload;
        ");

        $this->query("
                CREATE TABLE IF NOT EXISTS EMVWalletUpload (
                   `Id`                     BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Ключ записи',
                   `WalletId`               BIGINT UNSIGNED NOT NULL comment 'Идентификатор пополняемого кошелька',
                   `Amount`                 DECIMAL (10,3) NOT NULL comment 'Сумма пополнения',
                   `Status`                 TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Статус пополнения - EMVWalletUploadStatus',
                   `History`                JSON NULL DEFAULT NULL COMMENT 'История изменения статуса',
                   `CreateDateTime`         TIMESTAMP NOT NULL DEFAULT  CURRENT_TIMESTAMP COMMENT 'Дата и время создания записи', 
                   `EditDateTime`           TIMESTAMP NOT NULL DEFAULT  CURRENT_TIMESTAMP ON UPDATE  CURRENT_TIMESTAMP COMMENT 'Дата и время изменения записи'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci comment 'Таблица для хранения стоп-листа по картам кошельки которых не удалось пополнить';
        ");

        $this->query("
            ALTER TABLE `EMVWalletUpload` 
                 ADD INDEX (WalletId),
                 ADD INDEX (WalletId, Status),
                 ADD INDEX (Status),
                 ADD INDEX (WalletId, CreateDateTime);");
    }


    public function down()
    {
        $this->query("Delete from ReferenceList Where ReferenceName = 'EMVWalletUploadStatus';");
        $this->query("ALTER TABLE ReferenceList AUTO_INCREMENT=1;");

        $this->query("
            DROP TABLE  IF EXISTS EMVWalletUpload;
        ");
    }
}

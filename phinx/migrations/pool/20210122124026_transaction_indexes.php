<?php

use Phinx\Migration\AbstractMigration;

class TransactionIndexes extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `TerminalTransaction` ADD INDEX (`terminalSerialNumber`);');
        $this->query('ALTER TABLE `TerminalTransaction` ADD INDEX (`transactionType`);');
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `operationId` `operationId` BIGINT(20) NULL DEFAULT NULL COMMENT 'Идентификатор операции';");
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `ticketId` `ticketId` BIGINT(20) NULL DEFAULT NULL COMMENT 'Идентификатор билета';");
    }

    public function down()
    {
        $this->query('ALTER TABLE `TerminalTransaction` DROP INDEX `terminalSerialNumber`;');
        $this->query('ALTER TABLE `TerminalTransaction` DROP INDEX `transactionType`;');
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `operationId` `operationId` INT(11) NULL DEFAULT NULL COMMENT 'Идентификатор операции';");
        $this->query("ALTER TABLE `TerminalTransaction` CHANGE `ticketId` `ticketId` INT(11) NULL DEFAULT NULL COMMENT 'Идентификатор билета';");
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIndexDateTicket extends AbstractMigration
{
    private const INDEX_NAME_VALUE = 'idx__datetime_start';

    public function up(): void
    {
        if (!$this->hasIndexReferenceNumber()) {
            $this->table('ticket')
                ->addIndex('datetime_start', [
                    'name' => self::INDEX_NAME_VALUE
                ])
                ->update();
        }
    }

    public function down(): void
    {
        if ($this->hasIndexReferenceNumber()) {
            $this->table('ticket')
                ->removeIndex('datetime_start')
                ->update();
        }
    }

    private function hasIndexReferenceNumber(): bool
    {
        $table = $this->table('ticket');

        return $table->hasIndex('datetime_start');
    }
}

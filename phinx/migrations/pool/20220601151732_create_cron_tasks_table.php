<?php

use Phinx\Migration\AbstractMigration;

class CreateCronTasksTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('cron_tasks', [
                'comment' => 'Список заданий для крона',
                'id' => 'id',
            ])
            ->addColumn('name', 'string', [
                'limit' => 255,
                'comment' => 'Название задания',
            ])
            ->addColumn('schedule', 'string', [
                'limit' => 20,
                'comment' => 'Расписание задания',
            ])
            ->addColumn('command', 'string', [
                'limit' => 255,
                'comment' => 'Команда для запуска',
            ])
            ->addColumn('is_active', 'boolean', [
                'default' => true,
                'comment' => 'Флаг активности задания',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->addColumn('last_ran_at', 'datetime', [
                'null' => true,
                'comment' => 'Дата и время последнего запуска задания',
            ])
            ->create();
    }
}

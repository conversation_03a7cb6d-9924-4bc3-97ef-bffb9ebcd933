<?php

use Phinx\Migration\AbstractMigration;

class ChangeXrefGalleryUniqueIndex extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('xref_gallery_partner');
        $table->removeIndex(['partner_id', 'gallery_id']);
        $table->addIndex(['partner_id', 'gallery_id', 'object_type'], ['unique' => true])->save();
    }

    public function down()
    {
        $table = $this->table('xref_gallery_partner');
        $table->removeIndex(['partner_id', 'gallery_id', 'object_type']);
        $table->addIndex(['partner_id', 'gallery_id'], ['unique' => true])->save();
    }
}

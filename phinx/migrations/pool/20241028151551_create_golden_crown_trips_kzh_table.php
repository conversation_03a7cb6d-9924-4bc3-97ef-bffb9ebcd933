<?php

use Phinx\Migration\AbstractMigration;

class CreateGoldenCrownTripsKzhTable extends AbstractMigration
{
    public function up()
    {
        // Check if the table already exists before creating it
        if (!$this->hasTable('golden_сrown_trips_kzh')) {
            $table = $this->table('golden_сrown_trips_kzh', ['id' => 'id']);
            $table->addColumn('hash_pan', 'string', ['limit' => 255, 'null' => false])
                ->addColumn('pass_date', 'datetime', ['null' => false])
                ->addColumn('create_date', 'datetime', ['null' => false])
                ->addColumn(
                    'trc_id',
                    'integer',
                    ['null' => true]
                )
                ->addColumn(
                    'amount',
                    'integer',
                    ['null' => true]
                )
                ->addColumn('route_num', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('perk_code', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('eterm_id', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('ern', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('payment_system', 'string', ['limit' => 255, 'null' => true])
                ->addColumn(
                    'ntran',
                    'integer',
                    ['null' => true]
                )
                ->addColumn('ticket_number', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('tariff', 'string', ['limit' => 255, 'null' => true])
                ->addColumn(
                    'st_id',
                    'integer',
                    ['null' => true]
                )
                ->addColumn('st_name', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('transport_type', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('conductor_name', 'string', ['limit' => 255, 'null' => true])
                ->addColumn(
                    'service_id',
                    'integer',
                    ['null' => true]
                )
                ->addColumn('service_name', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('source_name', 'string', ['limit' => 255, 'null' => true])
                ->addColumn('created_at', 'datetime', ['null' => false])
                ->create();
        }
    }

    public function down()
    {
        // Check if the table exists before dropping it
        if ($this->hasTable('golden_сrown_trips_kzh')) {
            $this->table('golden_сrown_trips_kzh')->drop()->save();
        }
    }
}

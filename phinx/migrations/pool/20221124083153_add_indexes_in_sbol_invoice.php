<?php

use Phinx\Migration\AbstractMigration;

class AddIndexesInSbolInvoice extends AbstractMigration
{
    public function change()
    {
        $this->table('st_sbol_invoice')
            ->addColumn('create_date_time', 'timestamp', [
                'null' => false,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
                'after' => 'request_headers',
            ])
            ->addIndex(['pan'], [
                'name' => 'pan',
                'unique' => false,
            ])
            ->addIndex(['agent_transaction_id'], [
                'name' => 'agent_transaction_id',
                'unique' => false,
            ])->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class CreateViewMonitoringS1248 extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v__monitoring_s1_s2_48';

    public function up()
    {
        $this->query(
            sprintf(
                'CREATE OR REPLACE VIEW %s AS
                    WITH all_tr AS (SELECT DATE(transactionTime) AS tdate,
                       transactionType       AS t_type,
                       workDayNumber         AS t_wdn,
                       terminalSerialNumber  AS t_sn
                    FROM TerminalTransaction
                    WHERE transactionType IN (\'S1\', \'S2\')),

                    s1_table_48 AS (SELECT t_type,
                                        t_wdn,
                                        t_sn
                                 FROM all_tr
                                 WHERE t_type = \'S1\'
                                   AND tdate = DATE(NOW() - INTERVAL 2 DAY)
                                 GROUP BY t_sn, t_wdn),

                    s2_table_48 AS (SELECT t_type,
                                        t_wdn,
                                        t_sn
                                 FROM all_tr
                                 WHERE t_type = \'S2\'
                                   AND tdate >= DATE(NOW() - INTERVAL 2 DAY)
                                 GROUP BY t_sn, t_wdn)
                    SELECT COUNT(t_type)                AS \'s1_48h\',
                           (SELECT COUNT(s2.t_wdn)
                            FROM s1_table_48 s1
                                     LEFT JOIN s2_table_48 s2
                                               ON s1.t_wdn = s2.t_wdn
                                                   AND s1.t_sn = s2.t_sn
                            WHERE s2.t_wdn IS NOT NULL) AS \'s2_48h\'
                    FROM s1_table_48',
                self::NAME_VIEW_VALUE
            )
        );
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

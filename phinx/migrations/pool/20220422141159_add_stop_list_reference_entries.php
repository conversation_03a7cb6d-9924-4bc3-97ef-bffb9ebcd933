<?php

use Phinx\Migration\AbstractMigration;

class AddStopListReferenceEntries extends AbstractMigration
{
    public function up()
    {
        $this->table('ReferenceList')->insert([
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 5,
                'RecordName' => 'sltMifareWalletUID',
                'RecordDescription' => 'Белый список. ABT mifare карты с кошельком. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 6,
                'RecordName' => 'sltBlackMifareUID',
                'RecordDescription' => 'Черный список. ABT mifare карт. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 7,
                'RecordName' => 'sltBlackEmvBIN',
                'RecordDescription' => 'Черный список. BIN Emv.',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 8,
                'RecordName' => 'sltBlackTroikaUID',
                'RecordDescription' => 'Черный список. Тройка. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 9,
                'RecordName' => 'sltBlackProstorUID',
                'RecordDescription' => 'Черный список. Простор. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 10,
                'RecordName' => 'sltBlackLenoblUID',
                'RecordDescription' => 'Черный список. ЛенОбласть. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 11,
                'RecordName' => 'sltGrayLenoblUID',
                'RecordDescription' => 'Серый список. ЛенОбласть. UID',
            ],
            [
                'ReferenceName' => 'StopListType',
                'RecordId' => 12,
                'RecordName' => 'sltMifareUID',
                'RecordDescription' => 'Белый список. ABT mifare карты с абонементом. UID',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->execute('
            DELETE FROM ReferenceList 
            WHERE ReferenceName = "StopListType" AND RecordId IN (5, 6, 7, 8, 9, 10, 11, 12)
        ');
    }
}

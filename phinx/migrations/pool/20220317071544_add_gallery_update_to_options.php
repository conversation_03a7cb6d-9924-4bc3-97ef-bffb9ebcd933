<?php

use Phinx\Migration\AbstractMigration;

class AddGalleryUpdateToOptions extends AbstractMigration
{
    public function change()
    {
        if ($this->isMigratingUp()) {
            $options = $this->table('options');
            $options->insert([
                [
                    "key" => "gallery_last_time_update",
                    "value" => date("Y-m-d H:i:s")
                ],
                [
                    "key" => "gallery_update_interval",
                    "value" => '300'
                ]

            ])->saveData();
        } else {
            $this->execute("DELETE FROM options WHERE `key` in ('gallery_last_time_update','gallery_update_interval') ");
        }
    }
}

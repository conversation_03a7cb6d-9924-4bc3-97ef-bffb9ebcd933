<?php

use Phinx\Migration\AbstractMigration;

class AddIndexMonitoringS12 extends AbstractMigration
{
    private const TABLE = 'TerminalTransaction';
    private const INDEX_NAME = 'idx__tt__time_type_workDayNumber_terminalSerialNumber';

    public function up(): void
    {
        $table = $this->table(self::TABLE);

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $table->addIndex([
                    'transactionTime',
                    'transactionType',
                    'workDayNumber',
                    'terminalSerialNumber',
                ], [
                    'name' => self::INDEX_NAME,
                ])
                ->update();
        }
    }

    public function down(): void
    {
        $table = $this->table(self::TABLE);

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME)
                ->update();
        }
    }
}

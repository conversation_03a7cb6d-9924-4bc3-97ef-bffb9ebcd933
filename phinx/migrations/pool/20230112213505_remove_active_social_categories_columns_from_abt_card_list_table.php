<?php

use Phinx\Migration\AbstractMigration;

class RemoveActiveSocialCategoriesColumnsFromAbtCardListTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardList')
            ->removeColumn('ActiveSocialCategories')
            ->removeColumn('SocialCategoryForReports')
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardList')
            ->addColumn('ActiveSocialCategories', 'json', [
                'after' => 'SocialCategoriesAvailable',
                'null' => true,
                'comment' => 'Список льгот, для которых доступен последний активный абонемент',
            ])
            ->addColumn('SocialCategoryForReports', 'integer', [
                'null' => true,
                'after' => 'ActiveSocialCategories',
            ])
            ->update();
    }
}

<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class ModifyCardListTable extends AbstractMigration
{
    public function up()
    {
        $this->table('ABTCardList')
            ->changeColumn('PayToolType', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Тип абонемента. 0 — без привязки, 1 - абонемент, 2 - тариф, 3 - кошелек',
                'null' => false,
            ])
            ->changeColumn('UID', 'string', [
                'length' => 14,
                'comment' => 'UID карты',
                'null' => false,
            ])
            ->update();
    }

    public function down()
    {
        $this->table('ABTCardList')
            ->changeColumn('PayToolType', 'integer', [
                'limit' => MysqlAdapter::INT_TINY,
                'comment' => 'Тип абонемента. 1 - абонеме0нт, 2 - тариф, 3 - кошелек',
                'null' => false,
            ])
            ->changeColumn('UID', 'string', [
                'length' => 14,
                'comment' => 'UID карты',
                'null' => true,
            ])
            ->update();
    }
}

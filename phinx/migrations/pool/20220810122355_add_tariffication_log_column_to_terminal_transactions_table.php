<?php

use Phinx\Migration\AbstractMigration;

class AddTarifficationLogColumnToTerminalTransactionsTable extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalTransaction');

        if (!$table->hasColumn('TarifficationLog')) {
            $table->addColumn('TarifficationLog', 'text', [
                'null' => true,
                'comment' => 'Лог тарификации (JSON)',
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalTransaction');

        if ($table->hasColumn('TarifficationLog')) {
            $table->removeColumn('TarifficationLog');
        }

        $table->update();
    }
}

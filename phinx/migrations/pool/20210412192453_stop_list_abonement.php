<?php

use Phinx\Migration\AbstractMigration;

class StopListAbonement extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `EMVStopList` ADD `IsAbonement` BOOLEAN NOT NULL DEFAULT FALSE COMMENT \'Есть ли активный абонемент\' AFTER `IsBlocked`, ADD `AbonementId` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT \'ид абонемента\' AFTER `IsAbonement`;');
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVStopList`
  DROP `IsAbonement`,
  DROP `AbonementId`;");
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddUniqueIndexToGoldenCrownTripsKzhTable extends AbstractMigration
{
    public function up()
    {
        // Определяем таблицу
        $table = $this->table('golden_сrown_trips_kzh');

        // Проверяем, существует ли индекс перед созданием
        if (!$table->hasIndexByName('idx_unique_transaction')) {
            // Создаём уникальный индекс
            $table->addIndex(
                [
                    'hash_pan',
                    'pass_date',
                    'create_date',
                ],
                [
                    'name' => 'idx_unique_transaction',
                    'unique' => true,
                ]
            )->save();
        }
    }

    public function down()
    {
        // Определяем таблицу
        $table = $this->table('golden_сrown_trips_kzh');

        // Удаляем индекс, если он существует
        if ($table->hasIndexByName('idx_unique_transaction')) {
            $table->removeIndexByName('idx_unique_transaction')->save();
        }
    }
}

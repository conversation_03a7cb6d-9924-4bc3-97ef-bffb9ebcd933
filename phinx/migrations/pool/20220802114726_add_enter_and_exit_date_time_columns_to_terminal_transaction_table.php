<?php

use Phinx\Migration\AbstractMigration;

class AddEnterAndExitDateTimeColumnsToTerminalTransactionTable extends AbstractMigration
{
    public function change()
    {
        $this->table('TerminalTransaction')
            ->addColumn('enterDateTime', 'datetime', [
                'comment' => 'Дата и время входа',
                'after' => 'exitStationName',
                'null' => true,
            ])
            ->addColumn('exitDateTime', 'datetime', [
                'comment' => 'Дата и время выхода',
                'after' => 'enterDateTime',
                'null' => true,
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIsProcessedColumnInGoldenCrownTransactionsTable extends AbstractMigration
{
    public function up()
    {
        // Проверка наличия столбца перед добавлением
        if (!$this->table('golden_crown_transactions')->hasColumn('is_processed')) {
            $this->table('golden_crown_transactions')
                ->addColumn('is_processed', 'boolean', [
                    'default' => false,
                    'null' => false,
                    'comment' => 'Флаг для фильтрации транзакцией, которые обработаны, т.е посчитан баланс'
                ])
                ->update();
        }
    }

    public function down()
    {
        // Удаление столбца, если он существует
        if ($this->table('golden_crown_transactions')->hasColumn('is_processed')) {
            $this->table('golden_crown_transactions')
                ->removeColumn('is_processed')
                ->update();
        }
    }
}

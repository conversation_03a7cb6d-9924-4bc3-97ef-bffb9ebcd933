<?php

use Phinx\Migration\AbstractMigration;

class CreateKzhWriteoffBindingsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('__kzh_writeoff_bindings')
            ->addColumn('connection_id', 'integer', [
                'null' => false,
                'comment' => 'Id ftp-соединения (таблица __kzh_connections)',
            ])
            ->addColumn('kzh_tariff_id', 'integer', [
                'null' => false,
                'comment' => 'Id тарифа в КЖ КО',
            ])
            ->addColumn('writeoff_id', 'integer', [
                'null' => false,
                'comment' => 'Id правила списаний (таблица EMVWriteOffsTemplate)',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'default' => null,
                'update' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время изменения записи',
            ])
            ->create();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddStopListReferenceEntryKzhNso extends AbstractMigration
{
    public const TABLE_NAME = 'ReferenceList';
    public const REFERENCE_NAME = 'StopListType';
    public const RECORD_ID = 15;

    public function up()
    {
        $this->table(self::TABLE_NAME)->insert([
            [
                'ReferenceName' => self::REFERENCE_NAME,
                'RecordId' => self::RECORD_ID,
                'RecordName' => 'sltKzhNkoPanHash',
                'RecordDescription' => 'Белый список. Абонементы КЖ НКО.',
            ],
        ])->saveData();
    }

    public function down()
    {
        $this->getQueryBuilder()
            ->delete(self::TABLE_NAME)
            ->where(['ReferenceName' => self::REFERENCE_NAME])
            ->where(['RecordId' => self::RECORD_ID])
            ->execute();
    }
}

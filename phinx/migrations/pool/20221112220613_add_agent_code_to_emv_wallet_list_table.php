<?php

use Phinx\Migration\AbstractMigration;

class AddAgentCodeToEmvWalletListTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('AgentSalePointCode', 'string', [
                'after' => 'Journal',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код точки агента',
            ])
            ->addColumn('AgentCode', 'string', [
                'after' => 'AgentSalePointCode',
                'null' => true,
                'limit' => 64,
                'comment' => 'Код агента',
            ])
            ->update();
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddKzhImportRoles extends AbstractMigration
{
    public function up()
    {
        $this->query('DELETE FROM `admin_role` WHERE `role_title` = \'stat_kzh_import_view\';');
        $this->query('INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES (\'stat_kzh_import_view\', \'Просмотр статистики импорта КЖ КО\');');
        $this->query('DELETE FROM `admin_xref_user_role` WHERE `role_title` = \'stat_kzh_import_view\' AND user_id = 1;');
        $this->query('INSERT INTO `admin_xref_user_role` (`user_id`, `role_title`) VALUES (1, \'stat_kzh_import_view\');');
    }

    public function down()
    {
        $this->query('DELETE FROM `admin_role` WHERE  `role_title` = \'stat_kzh_import_view\';');
    }
}

<?php

use Phinx\Migration\AbstractMigration;

class AddIsCheckInCheckOutColumnToEmvWriteOffsTemplateTable extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWriteOffsTemplate')
            ->addColumn('IsCheckInCheckOut', 'boolean', [
                'default' => false,
                'after' => 'IsSocial',
                'comment' => 'Тарификация check-in, check-out',
            ])
            ->update();
    }
}

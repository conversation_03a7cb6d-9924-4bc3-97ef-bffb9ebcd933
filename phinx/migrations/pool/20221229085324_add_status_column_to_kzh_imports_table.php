<?php

use Phinx\Migration\AbstractMigration;

class AddStatusColumnToKzhImportsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('__kzh_imports')
            ->addColumn('status', 'integer', [
                'comment' => 'Статус обработки (0 — не обработан, 1 — обрабатывается, 2 — обработан, 3 — ошибка)',
                'after' => 'modification_time',
            ])
            ->addIndex(['connection_id', 'file_name', 'status'], [
                'name' => 'idx__connection_id__file_name__status',
                'unique' => false,
            ])
            ->update();
    }
}

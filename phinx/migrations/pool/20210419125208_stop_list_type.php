<?php

use Phinx\Migration\AbstractMigration;

class StopListType extends AbstractMigration
{
    public function up()
    {
        $this->query('ALTER TABLE `EMVStopList` CHANGE `IsAbonement` `Type` TINYINT(1) NOT NULL DEFAULT \'0\' COMMENT \'рефлист. тип списка\';');
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'StopListType';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
             INSERT INTO ReferenceList (ReferenceName, RecordId, RecordName, RecordDescription) 
                VALUES ('StopListType', 0, 'sltBlackPAN', 'Черный список по PANHash'),
                       ('StopListType', 1, 'sltAbonementPAN', 'Белый список по абонементу'),
                       ('StopListType', 2, 'sltBlackPAR', 'Черный список по PARHash');
        ";
        $this->query($sql);
    }

    public function down()
    {
        $this->query("ALTER TABLE `EMVStopList` CHANGE `Type` `IsAbonement` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'Есть ли активный абонемент';");
        $sql = "
             Delete from ReferenceList Where ReferenceName = 'StopListType';
             ALTER TABLE ReferenceList AUTO_INCREMENT=1;
        ";
        $this->query($sql);
    }
}

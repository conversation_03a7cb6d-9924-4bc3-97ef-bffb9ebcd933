<?php

use Phinx\Migration\AbstractMigration;

class CreateViewRideSegmentStat extends AbstractMigration
{
    private const NAME_VIEW_VALUE = 'v_ride_segement_stat';

    public function up()
    {
        $this->query(sprintf('
            CREATE VIEW %s AS
            SELECT partner_id,
                   MAX(date_start)                                                     AS date_start_max,
                   count(case when date_start = CURDATE() then 1 end)                  AS today_rides_count,
                   count(case when date_start = CURDATE() + INTERVAL 1 DAY then 1 end) AS tomorrow_rides_count,
                   count(ride_segment_id)                                              AS ride_future_cnt
            FROM ride_segment
            WHERE date_start >= CURDATE()
            GROUP BY partner_id;
        ', self::NAME_VIEW_VALUE));
    }

    public function down()
    {
        $this->query(sprintf('DROP VIEW %s', self::NAME_VIEW_VALUE));
    }
}

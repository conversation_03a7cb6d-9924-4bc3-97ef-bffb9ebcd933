<?php

use Phinx\Migration\AbstractMigration;

class CreateGoldenCrownWhiteListVtkTable extends AbstractMigration
{
    private const TABLE_NAME_VALUE = 'golden_crown_whitelist_vtk';

    public function up(): void
    {
        if (!$this->hasTable(self::TABLE_NAME_VALUE)) {
            $table = $this->table(self::TABLE_NAME_VALUE);
            $table->addColumn('device_id', 'integer', ['null' => true]);
            $table->addColumn('card_uid', 'string', ['null' => false]);
            $table->addColumn('is_active', 'boolean', ['default' => true]);
            $table->addColumn('blocked_at', 'datetime', ['null' => true]);
            $table->addColumn('unblocked_at', 'datetime', ['null' => true]);
            $table->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP']);
            $table->addIndex('card_uid', ['unique' => true]);
            $table->create();
        }
    }

    public function down(): void
    {
        if ($this->hasTable(self::TABLE_NAME_VALUE)) {
            $this->table(self::TABLE_NAME_VALUE)->drop()->save();
        }
    }
}

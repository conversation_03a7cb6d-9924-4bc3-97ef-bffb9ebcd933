<?php

use Phinx\Migration\AbstractMigration;

class SplitKzhImportedNumber extends AbstractMigration
{
    public function up()
    {
        $this->table('__kzh_imports')
            ->renameColumn('imported_number', 'cards_imported_number')
            ->changeColumn('cards_imported_number', 'integer', [
                'null' => true,
                'comment' => 'Количество импортированных карт',
            ])
            ->addColumn('abonements_imported_number', 'integer', [
                'null' => true,
                'comment' => 'Количество импортированных абонементов',
                'after' => 'cards_imported_number',
            ])
            ->addColumn('social_categories_imported_number', 'integer', [
                'null' => true,
                'comment' => 'Количество импортированных социальных категорий',
                'after' => 'abonements_imported_number',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('__kzh_imports')
            ->renameColumn('cards_imported_number', 'imported_number')
            ->changeColumn('imported_number', 'integer', [
                'null' => true,
                'comment' => 'Количество импортированных записей',
            ])
            ->removeColumn('abonements_imported_number')
            ->removeColumn('social_categories_imported_number')
            ->update();
    }
}

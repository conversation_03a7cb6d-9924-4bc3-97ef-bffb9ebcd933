<?php

use Phinx\Migration\AbstractMigration;

class OptionsTable extends AbstractMigration
{
    public function change()
    {
        $tab =  $this->table('options', ['signed' => false,'id' => 'id','comment' => 'Динамически меняющиеся параметры СДБП']);
        $tab
            ->addColumn('key', 'string', ['length' => 255,'comment' => 'ИД опции','null' => false])
            ->addColumn('value', 'text', ['comment' => 'значение','null' => false])
            ->addColumn('edit_date_time', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => 'Дата и время изменения записи'])
            ->addIndex(['key'], ['unique' => true])
            ->addIndex('edit_date_time')
            ->create();

        if ($this->isMigratingUp()) {
            $options = $this->table('options');

            $options->insert([
                [
                    "key" => "sl_last_time_update",
                    "value" => date("Y-m-d H:i:s")
                ],
                [
                    "key" => "sl_update_interval",
                    "value" => '5'
                ]

            ])->saveData();
        }
    }
}

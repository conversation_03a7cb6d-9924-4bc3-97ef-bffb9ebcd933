<?php

use Phinx\Migration\AbstractMigration;

class TerminalOrganizationTicketCheckUrl extends AbstractMigration
{
    public function up()
    {
        $this->query("alter table TerminalOrganizationList add CheckTicketURL VARCHAR(128) default null null 
                                         comment \"URL для проверки билета\" after Email;");

        $sql = "
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BD;
                CREATE TRIGGER `tgTerminalOrganizationList_BD` BEFORE DELETE ON `TerminalOrganizationList`
                 FOR EACH ROW BEGIN
                  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_AU;
                CREATE TRIGGER `tgTerminalOrganizationList_AU` AFTER UPDATE ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  declare j json default '{\"before\": {}, \"after\": {}}';
                
                  IF OLD.Id <=> NEW.Id THEN SET j = json_set(j,'$.before.Id', OLD.Id,'$.after.Id', NEW.Id); END IF;
                  IF OLD.VendorId <=> NEW.VendorId THEN SET j = json_set(j,'$.before.VendorId', OLD.VendorId,'$.after.VendorId', NEW.VendorId); END IF;
                  IF OLD.VendorOrganizationId<=> NEW.VendorOrganizationId THEN SET j = json_set(j,'$.before.VendorOrganizationId', OLD.VendorOrganizationId,'$.after.VendorOrganizationId', NEW.VendorOrganizationId); END IF;
                  IF OLD.Name <=> NEW.Name THEN SET j = json_set(j,'$.before.Name', OLD.Name,'$.after.Name', NEW.Name); END IF;
                  IF OLD.Address <=> NEW.Address THEN SET j = json_set(j,'$.before.Address', OLD.Address,'$.after.Address', NEW.Address); END IF;
                  IF OLD.Email <=> NEW.Email THEN SET j = json_set(j,'$.before.Email', OLD.Email,'$.after.Email', NEW.Email); END IF;
                  IF OLD.CheckTicketURL <=> NEW.CheckTicketURL THEN SET j = json_set(j,'$.before.CheckTicketURL', OLD.CheckTicketURL,'$.after.CheckTicketURL', NEW.CheckTicketURL); END IF;
                  IF OLD.INN <=> NEW.INN THEN SET j = json_set(j,'$.before.INN', OLD.INN,'$.after.INN', NEW.INN); END IF;
                  IF OLD.FNTaxCode <=> NEW.FNTaxCode THEN SET j = json_set(j,'$.before.FNTaxCode', OLD.FNTaxCode,'$.after.FNTaxCode', NEW.FNTaxCode); END IF;
                  IF OLD.FNTaxCodeDefault <=> NEW.FNTaxCodeDefault THEN SET j = json_set(j,'$.before.FNTaxCodeDefault', OLD.FNTaxCodeDefault,'$.after.FNTaxCodeDefault', NEW.FNTaxCodeDefault); END IF;
                  IF OLD.FNOperatingMode <=> NEW.FNOperatingMode THEN SET j = json_set(j,'$.before.FNOperatingMode', OLD.FNOperatingMode,'$.after.FNOperatingMode', NEW.FNOperatingMode); END IF;
                  IF OLD.FNIsGambling <=> NEW.FNIsGambling THEN SET j = json_set(j,'$.before.FNIsGambling', OLD.FNIsGambling,'$.after.FNIsGambling', NEW.FNIsGambling); END IF;
                  IF OLD.FNIsLottery <=> NEW.FNIsLottery THEN SET j = json_set(j,'$.before.FNIsLottery', OLD.FNIsLottery,'$.after.FNIsLottery', NEW.FNIsLottery); END IF;
                  IF OLD.FNPaymentAgent <=> NEW.FNPaymentAgent THEN SET j = json_set(j,'$.before.FNPaymentAgent', OLD.FNPaymentAgent,'$.after.FNPaymentAgent', NEW.FNPaymentAgent); END IF;
                  IF OLD.OFDServer <=> NEW.OFDServer THEN SET j = json_set(j,'$.before.OFDServer', OLD.OFDServer,'$.after.OFDServer', NEW.OFDServer); END IF;
                  IF OLD.OFDServerPort <=> NEW.OFDServerPort THEN SET j = json_set(j,'$.before.OFDServerPort', OLD.OFDServerPort,'$.after.OFDServerPort', NEW.OFDServerPort); END IF;
                  IF OLD.OFDName <=> NEW.OFDName THEN SET j = json_set(j,'$.before.OFDName', OLD.OFDName,'$.after.OFDName', NEW.OFDName); END IF;
                  IF OLD.OFDINN <=> NEW.OFDINN THEN SET j = json_set(j,'$.before.OFDINN', OLD.OFDINN,'$.after.OFDINN', NEW.OFDINN); END IF;
                  IF OLD.OFDReceiptCheckURI <=> NEW.OFDReceiptCheckURI THEN SET j = json_set(j,'$.before.OFDReceiptCheckURI', OLD.OFDReceiptCheckURI,'$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI); END IF;
                  IF OLD.FnsServerAddress <=> NEW.FnsServerAddress THEN SET j = json_set(j,'$.before.FnsServerAddress', OLD.FnsServerAddress,'$.after.FnsServerAddress', NEW.FnsServerAddress); END IF;
                  IF OLD.SupportName <=> NEW.SupportName THEN SET j = json_set(j,'$.before.SupportName', OLD.SupportName,'$.after.SupportName', NEW.SupportName); END IF;
                  IF OLD.SupportPhone <=> NEW.SupportPhone THEN SET j = json_set(j,'$.before.SupportPhone', OLD.SupportPhone,'$.after.SupportPhone', NEW.SupportPhone); END IF;
                  IF OLD.SupportEmail <=> NEW.SupportEmail THEN SET j = json_set(j,'$.before.SupportEmail', OLD.SupportEmail,'$.after.SupportEmail', NEW.SupportEmail); END IF;
                  IF OLD.IsDeleted <=> NEW.IsDeleted  THEN SET j = json_set(j,'$.before.IsDeleted', OLD.IsDeleted,'$.after.IsDeleted', NEW.IsDeleted); END IF;
                
                  IF not NEW.Version <=> OLD.Version
                  THEN
                    SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
                    call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'update', j,NEW.Version);
                  END IF;
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BU;
                CREATE TRIGGER `tgTerminalOrganizationList_BU` BEFORE UPDATE ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  
                
                  IF  OLD.Id                   !=   NEW.Id                    OR
                      OLD.VendorId             !=   NEW.VendorId              OR
                      OLD.VendorOrganizationId !=   NEW.VendorOrganizationId  OR
                      OLD.Name                 !=   NEW.Name                  OR
                      OLD.Address              !=   NEW.Address               OR
                      OLD.Email                !=   NEW.Email                 OR
                      OLD.CheckTicketURL       !=   NEW.CheckTicketURL                   OR
                      OLD.INN                  !=   NEW.INN                   OR
                      OLD.FNTaxCode            !=   NEW.FNTaxCode             OR
                      OLD.FNTaxCodeDefault     !=   NEW.FNTaxCodeDefault      OR
                      OLD.FNOperatingMode      !=   NEW.FNOperatingMode       OR
                      OLD.FNIsGambling         !=   NEW.FNIsGambling          OR
                      OLD.FNIsLottery          !=   NEW.FNIsLottery           OR
                      OLD.FNPaymentAgent       !=   NEW.FNPaymentAgent        OR
                      OLD.OFDServer            !=   NEW.OFDServer             OR
                      OLD.OFDServerPort        !=   NEW.OFDServerPort         OR
                      OLD.OFDName              !=   NEW.OFDName               OR
                      OLD.OFDINN               !=   NEW.OFDINN                OR
                      OLD.OFDReceiptCheckURI   !=   NEW.OFDReceiptCheckURI    OR
                      OLD.FnsServerAddress     !=   NEW.FnsServerAddress      OR
                      OLD.SupportName          !=   NEW.SupportName           OR
                      OLD.SupportPhone         !=   NEW.SupportPhone          OR
                      OLD.SupportEmail         !=   NEW.SupportEmail          OR
                      OLD.IsDeleted            !=   NEW.IsDeleted
                  THEN
                    
                    SET NEW.Version = OLD.Version + 1;
                    SET NEW.EditUserId = CURRENT_USER();
                    SET NEW.EditDateTime= NOW();
                  END IF;
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_AI;
                CREATE TRIGGER `tgTerminalOrganizationList_AI` AFTER INSERT ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  declare j json default '{\"before\": {}, \"after\": {}}';
                  SET j=json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
                  SET j=json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
                  SET j=json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
                  SET j=json_set(j, '$.before.Name', '', '$.after.Name', NEW.Name);
                  SET j=json_set(j, '$.before.Address', '', '$.after.Address', NEW.Address);
                  SET j=json_set(j, '$.before.Email', '', '$.after.Email', NEW.Email);
                  SET j=json_set(j, '$.before.CheckTicketURL', '', '$.after.CheckTicketURL', NEW.CheckTicketURL);
                  SET j=json_set(j, '$.before.INN', '', '$.after.INN', NEW.INN);
                  SET j=json_set(j, '$.before.FNTaxCode', '', '$.after.FNTaxCode', NEW.FNTaxCode);
                  SET j=json_set(j, '$.before.FNTaxCodeDefault', '', '$.after.FNTaxCodeDefault', NEW.FNTaxCode);
                  SET j=json_set(j, '$.before.FNOperatingMode', '', '$.after.FNOperatingMode', NEW.FNOperatingMode);
                  SET j=json_set(j, '$.before.FNIsGambling', '', '$.after.FNIsGambling', NEW.FNIsGambling);
                  SET j=json_set(j, '$.before.FNIsLottery', '', '$.after.FNIsLottery', NEW.FNIsLottery);
                  SET j=json_set(j, '$.before.FNPaymentAgent', '', '$.after.FNPaymentAgent', NEW.FNPaymentAgent);
                  SET j=json_set(j, '$.before.OFDServer', '', '$.after.OFDServer', NEW.OFDServer);
                  SET j=json_set(j, '$.before.OFDServerPort', '', '$.after.OFDServerPort', NEW.OFDServerPort);
                  SET j=json_set(j, '$.before.OFDName', '', '$.after.OFDName', NEW.OFDName);
                  SET j=json_set(j, '$.before.OFDINN', '', '$.after.OFDINN', NEW.OFDINN);
                  SET j=json_set(j, '$.before.OFDReceiptCheckURI', '', '$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI);
                  SET j=json_set(j, '$.before.FnsServerAddress', '', '$.after.FnsServerAddress', NEW.FnsServerAddress);
                  SET j=json_set(j, '$.before.SupportName', '', '$.after.SupportName', NEW.SupportName);
                  SET j=json_set(j, '$.before.SupportPhone', '', '$.after.SupportPhone', NEW.SupportPhone);
                  SET j=json_set(j, '$.before.SupportEmail', '', '$.after.SupportEmail', NEW.SupportEmail);
                  SET j=json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
                  call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'insert', j,NEW.Version);
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BI;
                CREATE TRIGGER `tgTerminalOrganizationList_BI` BEFORE INSERT ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  SET NEW.Version = 1;
                  SET NEW.EditUserId = CURRENT_USER();
                END;
        ";

        $this->query($sql);
    }

    public function down()
    {
        $this->query("ALTER TABLE `TerminalOrganizationList` DROP `CheckTicketURL`; ");
        $sql = "
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BD;
                CREATE TRIGGER `tgTerminalOrganizationList_BD` BEFORE DELETE ON `TerminalOrganizationList`
                 FOR EACH ROW BEGIN
                  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_AU;
                CREATE TRIGGER `tgTerminalOrganizationList_AU` AFTER UPDATE ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  declare j json default '{\"before\": {}, \"after\": {}}';
                
                  IF OLD.Id <=> NEW.Id THEN SET j = json_set(j,'$.before.Id', OLD.Id,'$.after.Id', NEW.Id); END IF;
                  IF OLD.VendorId <=> NEW.VendorId THEN SET j = json_set(j,'$.before.VendorId', OLD.VendorId,'$.after.VendorId', NEW.VendorId); END IF;
                  IF OLD.VendorOrganizationId<=> NEW.VendorOrganizationId THEN SET j = json_set(j,'$.before.VendorOrganizationId', OLD.VendorOrganizationId,'$.after.VendorOrganizationId', NEW.VendorOrganizationId); END IF;
                  IF OLD.Name <=> NEW.Name THEN SET j = json_set(j,'$.before.Name', OLD.Name,'$.after.Name', NEW.Name); END IF;
                  IF OLD.Address <=> NEW.Address THEN SET j = json_set(j,'$.before.Address', OLD.Address,'$.after.Address', NEW.Address); END IF;
                  IF OLD.Email <=> NEW.Email THEN SET j = json_set(j,'$.before.Email', OLD.Email,'$.after.Email', NEW.Email); END IF;
                  IF OLD.INN <=> NEW.INN THEN SET j = json_set(j,'$.before.INN', OLD.INN,'$.after.INN', NEW.INN); END IF;
                  IF OLD.FNTaxCode <=> NEW.FNTaxCode THEN SET j = json_set(j,'$.before.FNTaxCode', OLD.FNTaxCode,'$.after.FNTaxCode', NEW.FNTaxCode); END IF;
                  IF OLD.FNTaxCodeDefault <=> NEW.FNTaxCodeDefault THEN SET j = json_set(j,'$.before.FNTaxCodeDefault', OLD.FNTaxCodeDefault,'$.after.FNTaxCodeDefault', NEW.FNTaxCodeDefault); END IF;
                  IF OLD.FNOperatingMode <=> NEW.FNOperatingMode THEN SET j = json_set(j,'$.before.FNOperatingMode', OLD.FNOperatingMode,'$.after.FNOperatingMode', NEW.FNOperatingMode); END IF;
                  IF OLD.FNIsGambling <=> NEW.FNIsGambling THEN SET j = json_set(j,'$.before.FNIsGambling', OLD.FNIsGambling,'$.after.FNIsGambling', NEW.FNIsGambling); END IF;
                  IF OLD.FNIsLottery <=> NEW.FNIsLottery THEN SET j = json_set(j,'$.before.FNIsLottery', OLD.FNIsLottery,'$.after.FNIsLottery', NEW.FNIsLottery); END IF;
                  IF OLD.FNPaymentAgent <=> NEW.FNPaymentAgent THEN SET j = json_set(j,'$.before.FNPaymentAgent', OLD.FNPaymentAgent,'$.after.FNPaymentAgent', NEW.FNPaymentAgent); END IF;
                  IF OLD.OFDServer <=> NEW.OFDServer THEN SET j = json_set(j,'$.before.OFDServer', OLD.OFDServer,'$.after.OFDServer', NEW.OFDServer); END IF;
                  IF OLD.OFDServerPort <=> NEW.OFDServerPort THEN SET j = json_set(j,'$.before.OFDServerPort', OLD.OFDServerPort,'$.after.OFDServerPort', NEW.OFDServerPort); END IF;
                  IF OLD.OFDName <=> NEW.OFDName THEN SET j = json_set(j,'$.before.OFDName', OLD.OFDName,'$.after.OFDName', NEW.OFDName); END IF;
                  IF OLD.OFDINN <=> NEW.OFDINN THEN SET j = json_set(j,'$.before.OFDINN', OLD.OFDINN,'$.after.OFDINN', NEW.OFDINN); END IF;
                  IF OLD.OFDReceiptCheckURI <=> NEW.OFDReceiptCheckURI THEN SET j = json_set(j,'$.before.OFDReceiptCheckURI', OLD.OFDReceiptCheckURI,'$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI); END IF;
                  IF OLD.FnsServerAddress <=> NEW.FnsServerAddress THEN SET j = json_set(j,'$.before.FnsServerAddress', OLD.FnsServerAddress,'$.after.FnsServerAddress', NEW.FnsServerAddress); END IF;
                  IF OLD.SupportName <=> NEW.SupportName THEN SET j = json_set(j,'$.before.SupportName', OLD.SupportName,'$.after.SupportName', NEW.SupportName); END IF;
                  IF OLD.SupportPhone <=> NEW.SupportPhone THEN SET j = json_set(j,'$.before.SupportPhone', OLD.SupportPhone,'$.after.SupportPhone', NEW.SupportPhone); END IF;
                  IF OLD.SupportEmail <=> NEW.SupportEmail THEN SET j = json_set(j,'$.before.SupportEmail', OLD.SupportEmail,'$.after.SupportEmail', NEW.SupportEmail); END IF;
                  IF OLD.IsDeleted <=> NEW.IsDeleted  THEN SET j = json_set(j,'$.before.IsDeleted', OLD.IsDeleted,'$.after.IsDeleted', NEW.IsDeleted); END IF;
                
                  IF not NEW.Version <=> OLD.Version
                  THEN
                    SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
                    call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'update', j,NEW.Version);
                  END IF;
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BU;
                CREATE TRIGGER `tgTerminalOrganizationList_BU` BEFORE UPDATE ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  
                
                  IF  OLD.Id                   !=   NEW.Id                    OR
                      OLD.VendorId             !=   NEW.VendorId              OR
                      OLD.VendorOrganizationId !=   NEW.VendorOrganizationId  OR
                      OLD.Name                 !=   NEW.Name                  OR
                      OLD.Address              !=   NEW.Address               OR
                      OLD.Email                !=   NEW.Email                 OR
                      OLD.INN                  !=   NEW.INN                   OR
                      OLD.FNTaxCode            !=   NEW.FNTaxCode             OR
                      OLD.FNTaxCodeDefault     !=   NEW.FNTaxCodeDefault      OR
                      OLD.FNOperatingMode      !=   NEW.FNOperatingMode       OR
                      OLD.FNIsGambling         !=   NEW.FNIsGambling          OR
                      OLD.FNIsLottery          !=   NEW.FNIsLottery           OR
                      OLD.FNPaymentAgent       !=   NEW.FNPaymentAgent        OR
                      OLD.OFDServer            !=   NEW.OFDServer             OR
                      OLD.OFDServerPort        !=   NEW.OFDServerPort         OR
                      OLD.OFDName              !=   NEW.OFDName               OR
                      OLD.OFDINN               !=   NEW.OFDINN                OR
                      OLD.OFDReceiptCheckURI   !=   NEW.OFDReceiptCheckURI    OR
                      OLD.FnsServerAddress     !=   NEW.FnsServerAddress      OR
                      OLD.SupportName          !=   NEW.SupportName           OR
                      OLD.SupportPhone         !=   NEW.SupportPhone          OR
                      OLD.SupportEmail         !=   NEW.SupportEmail          OR
                      OLD.IsDeleted            !=   NEW.IsDeleted
                  THEN
                    
                    SET NEW.Version = OLD.Version + 1;
                    SET NEW.EditUserId = CURRENT_USER();
                    SET NEW.EditDateTime= NOW();
                  END IF;
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_AI;
                CREATE TRIGGER `tgTerminalOrganizationList_AI` AFTER INSERT ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  declare j json default '{\"before\": {}, \"after\": {}}';
                  SET j=json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
                  SET j=json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
                  SET j=json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
                  SET j=json_set(j, '$.before.Name', '', '$.after.Name', NEW.Name);
                  SET j=json_set(j, '$.before.Address', '', '$.after.Address', NEW.Address);
                  SET j=json_set(j, '$.before.Email', '', '$.after.Email', NEW.Email);
                  SET j=json_set(j, '$.before.INN', '', '$.after.INN', NEW.INN);
                  SET j=json_set(j, '$.before.FNTaxCode', '', '$.after.FNTaxCode', NEW.FNTaxCode);
                  SET j=json_set(j, '$.before.FNTaxCodeDefault', '', '$.after.FNTaxCodeDefault', NEW.FNTaxCode);
                  SET j=json_set(j, '$.before.FNOperatingMode', '', '$.after.FNOperatingMode', NEW.FNOperatingMode);
                  SET j=json_set(j, '$.before.FNIsGambling', '', '$.after.FNIsGambling', NEW.FNIsGambling);
                  SET j=json_set(j, '$.before.FNIsLottery', '', '$.after.FNIsLottery', NEW.FNIsLottery);
                  SET j=json_set(j, '$.before.FNPaymentAgent', '', '$.after.FNPaymentAgent', NEW.FNPaymentAgent);
                  SET j=json_set(j, '$.before.OFDServer', '', '$.after.OFDServer', NEW.OFDServer);
                  SET j=json_set(j, '$.before.OFDServerPort', '', '$.after.OFDServerPort', NEW.OFDServerPort);
                  SET j=json_set(j, '$.before.OFDName', '', '$.after.OFDName', NEW.OFDName);
                  SET j=json_set(j, '$.before.OFDINN', '', '$.after.OFDINN', NEW.OFDINN);
                  SET j=json_set(j, '$.before.OFDReceiptCheckURI', '', '$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI);
                  SET j=json_set(j, '$.before.FnsServerAddress', '', '$.after.FnsServerAddress', NEW.FnsServerAddress);
                  SET j=json_set(j, '$.before.SupportName', '', '$.after.SupportName', NEW.SupportName);
                  SET j=json_set(j, '$.before.SupportPhone', '', '$.after.SupportPhone', NEW.SupportPhone);
                  SET j=json_set(j, '$.before.SupportEmail', '', '$.after.SupportEmail', NEW.SupportEmail);
                  SET j=json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
                  call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'insert', j,NEW.Version);
                END;
                
                DROP TRIGGER IF EXISTS tgTerminalOrganizationList_BI;
                CREATE TRIGGER `tgTerminalOrganizationList_BI` BEFORE INSERT ON `TerminalOrganizationList`
                 FOR EACH ROW begin
                  SET NEW.Version = 1;
                  SET NEW.EditUserId = CURRENT_USER();
                END;
        ";
        $this->query($sql);
    }
}

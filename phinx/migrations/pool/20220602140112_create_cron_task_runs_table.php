<?php

use Phinx\Migration\AbstractMigration;

class CreateCronTaskRunsTable extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('cron_task_runs', [
                'comment' => 'Список выполненных заданий для крона',
                'id' => 'id',
            ])
            ->addColumn('task_id', 'biginteger', [
                'null' => true,
                'comment' => 'Id задания',
            ])
            ->addColumn('pid', 'biginteger', [
                'null' => true,
                'comment' => 'Pid процесса',
            ])
            ->addColumn('result_code', 'integer', [
                'null' => true,
                'comment' => 'Код результата выполнения задания',
            ])
            ->addColumn('output', 'text', [
                'null' => true,
                'comment' => 'Результат выполнения задания',
            ])
            ->addColumn('memory_usage', 'integer', [
                'null' => true,
                'comment' => 'Количество потреблённой памяти',
            ])
            ->addColumn('peak_memory_usage', 'integer', [
                'null' => true,
                'comment' => 'Пиковое количество потреблённой памяти',
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => 'Дата и время создания записи',
            ])
            ->addColumn('started_at', 'datetime', [
                'null' => true,
                'comment' => 'Дата и время начала выполнения задания',
            ])
            ->addColumn('finished_at', 'datetime', [
                'null' => true,
                'comment' => 'Дата и время окончания выполнения задания',
            ])
            ->addIndex('task_id')
            ->create();
    }
}

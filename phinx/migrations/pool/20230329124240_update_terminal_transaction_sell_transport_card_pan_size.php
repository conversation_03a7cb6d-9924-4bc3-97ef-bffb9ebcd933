<?php

use Phinx\Migration\AbstractMigration;

class UpdateTerminalTransactionSellTransportCardPanSize extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('TerminalTransaction');
        if ($table->hasColumn('sellTransportCardPAN')) {
            $table->changeColumn('sellTransportCardPAN', 'string', [
                'length' => 20,
                'null' => true,
                'comment' => 'PAN транспортной карты',
            ]);
        }
        $table->update();
    }

    public function down()
    {
        $table = $this->table('TerminalTransaction');
        if ($table->hasColumn('sellTransportCardPAN')) {
            $table->changeColumn('sellTransportCardPAN', 'string', [
                'length' => 16,
                'null' => true,
                'comment' => 'PAN транспортной карты',
            ]);
        }
        $table->update();
    }
}

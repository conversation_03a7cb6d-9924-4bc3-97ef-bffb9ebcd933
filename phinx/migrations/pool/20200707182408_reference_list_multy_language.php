<?php

use Phinx\Migration\AbstractMigration;

class ReferenceListMultyLanguage extends AbstractMigration
{
    public function up()
    {
        $this->query("
            ALTER TABLE ReferenceList 
                    ADD `Locales` JSON DEFAULT NULL NULL COMMENT 'Localization';
        ");

        $this->query("
            UPDATE ReferenceList  SET Locales = JSON_OBJECT(IF(Id<197,'RU','EN'),JSON_OBJECT('Description',RecordDescription)) WHERE 1;
        ");
    }

    public function down()
    {
        $this->query("
                ALTER TABLE ReferenceList
                    DROP `Locales`;
        ");
    }
}

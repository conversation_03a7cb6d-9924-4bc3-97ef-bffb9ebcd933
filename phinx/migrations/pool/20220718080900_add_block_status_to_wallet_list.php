<?php

use Phinx\Migration\AbstractMigration;

class AddBlockStatusToWalletList extends AbstractMigration
{
    public function change()
    {
        $this->table('EMVWalletList')
            ->addColumn('BlockStatus', 'integer', [
                'default' => 0,
                'limit' => 1,
                'after' => 'IsActive',
                'null' => false,
                'comment' => 'Тип блокировки карты (рефлист «EMVBlockStatus»)',
            ])
            ->save();
    }
}

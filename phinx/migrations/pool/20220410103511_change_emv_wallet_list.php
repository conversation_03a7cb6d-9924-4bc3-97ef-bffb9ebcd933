<?php

use Phinx\Migration\AbstractMigration;

class ChangeEmvWalletList extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    addCustomColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Any other destructive changes will result in an error when trying to
     * rollback the migration.
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $tab =  $this->table('EMVWalletList');

        if ($this->isMigratingUp()) {
            $tab->changeColumn('PANHash', 'string', [ 'length' => 64, 'null' => true, 'comment' => 'Хэш от номера карты']);
        }
//        else
//            $tab->changeColumn('PANHash','string',   [ 'length'=>64, 'null'=> false, 'comment'=>'Хэш от номера карты'] );


        $tab
            //->changeColumn('PANHash','string',   [ 'null'=> true] )
            ->addColumn('PAN', 'string', ['length' => 32, 'after' => "AgentId", 'comment' => 'PAN номер карты','null' => true])
            ->addColumn('UID', 'string', ['length' => 32, 'after' => "PANHash", 'comment' => 'UID номер карты','null' => true])
            ->addColumn('UIDHash', 'string', ['length' => 64, 'after' => "UID", 'comment' => 'Хэш от UID карты','null' => true])
            ->addIndex('PAN')
            ->addIndex('PANHash')
            ->addIndex('UID')
            ->addIndex('UIDHash')
            ->addIndex(['UIDHash','IsActive'])
            ->addIndex(['PANHash','IsActive'])
            ->addIndex(['UID','IsActive'])
            ->addIndex(['PAN','IsActive'])
            ->addIndex(['ValidTimeStart','ValidTimeEnd'])
            ->addIndex(['PAN','ValidTimeStart','ValidTimeEnd'])
            ->addIndex(['UID','ValidTimeStart','ValidTimeEnd'])
            ->addIndex(['UIDHash','ValidTimeStart','ValidTimeEnd'])
            ->save();
    }
}

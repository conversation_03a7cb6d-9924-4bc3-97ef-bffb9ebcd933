<?php

use Phinx\Db\Table;
use Phinx\Migration\AbstractMigration;

class AddTypeColumnToKzhImportsTable extends AbstractMigration
{
    protected const TABLE_NAME = '__kzh_imports';
    protected const COLUMN_NAME = 'type';
    protected const INDEX_NAME = 'idx__connection_id__type';

    public function up()
    {
        $table = $this->getTable();

        if (!$table->hasColumn(self::COLUMN_NAME)) {
            $table->addColumn(self::COLUMN_NAME, 'string', [
                'comment' => 'Тип файла импорта',
                'after' => 'connection_id',
                'length' => 10,
            ]);
        }

        if (!$table->hasIndexByName(self::INDEX_NAME)) {
            $table->addIndex(['connection_id', self::COLUMN_NAME], [
                'name' => self::INDEX_NAME,
                'unique' => false,
            ]);
        }

        $table->update();
    }

    public function down()
    {
        $table = $this->getTable();

        if ($table->hasColumn(self::COLUMN_NAME)) {
            $table->removeColumn(self::COLUMN_NAME);
        }

        if ($table->hasIndexByName(self::INDEX_NAME)) {
            $table->removeIndexByName(self::INDEX_NAME);
        }

        $table->update();
    }

    protected function getTable(): Table
    {
        return $this->table(self::TABLE_NAME);
    }
}

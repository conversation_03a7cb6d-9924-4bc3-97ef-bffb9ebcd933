<?php

use Phinx\Migration\AbstractMigration;

class AddShortDescriptionToCounterTypesReferences extends AbstractMigration
{
    public function up()
    {
        $this->updateShortDescription(1, 'На все виды транспорта');
        $this->updateShortDescription(2, 'На группу видов транспорта');
        $this->updateShortDescription(3, 'На каждый вид транспорта');
        $this->updateShortDescription(4, 'Безлимит');
        $this->updateShortDescription(5, 'Безлимит на группу видов транспорта');
    }

    protected function updateShortDescription(int $recordId, string $shortDescription)
    {
        $this->getQueryBuilder()
            ->update('ReferenceList')
            ->set('RecordShortDescription', $shortDescription)
            ->where([
                'ReferenceName' => 'EMVWriteOffsCounterType',
                'RecordId' => $recordId,
            ])
            ->execute();
    }

    public function down()
    {
    }
}

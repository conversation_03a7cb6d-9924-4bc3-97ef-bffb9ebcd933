$$$
CREATE TRIGGER `tgTerminalGroup_AI` AFTER INSERT ON `TerminalGroup` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';

  SET j = json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
  SET j = json_set(j, '$.before.Name', '', '$.after.Name', NEW.Name);
  SET j = json_set(j, '$.before.Description', '', '$.after.Description', NEW.Description);
  SET j = json_set(j, '$.before.VendorName', '', '$.after.VendorName', NEW.VendorName);
  SET j = json_set(j, '$.before.ModelName', '', '$.after.ModelName', NEW.ModelName);
  SET j = json_set(j, '$.before.BaseOSName', '', '$.after.BaseOSName', NEW.BaseOSName);
  SET j = json_set(j, '$.before.Type', '', '$.after.Type', NEW.Type);
  SET j = json_set(j, '$.before.TemplateConfig', '', '$.after.TemplateConfig', NEW.TemplateConfig);
  SET j = json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
  SET j = json_set(j, '$.before.EditUserId', '', '$.after.EditUserId', NEW.EditUserId);
  SET j = json_set(j, '$.before.EditDateTime', '', '$.after.EditDateTime', NEW.EditDateTime);
  SET j = json_set(j, '$.before.Version', '', '$.after.Version', NEW.Version);
  call spSaveActionToLog('TerminalGroup',  NEW.Id, 'insert', j,NEW.Version);
END
$$$
CREATE TRIGGER `tgTerminalGroup_AU` AFTER UPDATE ON `TerminalGroup` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';

  if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
  if not NEW.Name <=> OLD.Name then SET j = json_set(j, '$.before.Name', OLD.Name, '$.after.Name', NEW.Name); end if;
  if not NEW.Description <=> OLD.Description then SET j = json_set(j, '$.before.Description', OLD.Description, '$.after.Description', NEW.Description); end if;
  if not NEW.VendorName <=> OLD.VendorName then SET j = json_set(j, '$.before.VendorName', OLD.VendorName, '$.after.VendorName', NEW.VendorName); end if;
  if not NEW.ModelName <=> OLD.ModelName then SET j = json_set(j, '$.before.ModelName', OLD.ModelName, '$.after.ModelName', NEW.ModelName); end if;
  if not NEW.BaseOSName <=> OLD.BaseOSName then SET j = json_set(j, '$.before.BaseOSName', OLD.BaseOSName, '$.after.BaseOSName', NEW.BaseOSName); end if;
  if not NEW.Type <=> OLD.Type then SET j = json_set(j, '$.before.Type', OLD.Type, '$.after.Type', NEW.Type); end if;
  if not NEW.TemplateConfig <=> OLD.TemplateConfig then SET j = json_set(j, '$.before.TemplateConfig', OLD.TemplateConfig, '$.after.TemplateConfig', NEW.TemplateConfig); end if;
  if not NEW.IsDeleted <=> OLD.IsDeleted then SET j = json_set(j, '$.before.IsDeleted', OLD.IsDeleted, '$.after.IsDeleted', NEW.IsDeleted); end if;
  if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
  if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
  if not NEW.Version <=> OLD.Version then SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version); end if;
  call spSaveActionToLog('TerminalGroup',  NEW.Id, 'update', j,NEW.Version);

END
$$$
CREATE TRIGGER `tgTerminalGroup_BD` BEFORE DELETE ON `TerminalGroup` FOR EACH ROW BEGIN
  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
END
$$$
CREATE TRIGGER `tgTerminalGroup_BU` BEFORE UPDATE ON `TerminalGroup` FOR EACH ROW begin

  IF not NEW.Id <=> OLD.Id OR not NEW.Name <=> OLD.Name OR not NEW.Description <=> OLD.Description OR
     not NEW.VendorName <=> OLD.VendorName OR not NEW.ModelName <=> OLD.ModelName OR not NEW.BaseOSName <=> OLD.BaseOSName OR
     not NEW.Type <=> OLD.Type OR not NEW.TemplateConfig <=> OLD.TemplateConfig OR not NEW.IsDeleted <=> OLD.IsDeleted OR
     not NEW.EditUserId <=> OLD.EditUserId OR not NEW.EditDateTime <=> OLD.EditDateTime
  THEN
    SET NEW.Version = OLD.Version + 1;
  END IF;

END
$$$
CREATE TRIGGER `tgTerminalList_AI` AFTER INSERT ON `TerminalList` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';

  SET j = json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
  SET j = json_set(j, '$.before.TerminalId', '', '$.after.TerminalId', NEW.TerminalId);
  SET j = json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
  SET j = json_set(j, '$.before.TerminalVendorId', '', '$.after.TerminalVendorId', NEW.TerminalVendorId);
  SET j = json_set(j, '$.before.TerminalSerialNumber', '', '$.after.TerminalSerialNumber', NEW.TerminalSerialNumber);
  SET j = json_set(j, '$.before.FNSerialNumber', '', '$.after.FNSerialNumber', NEW.FNSerialNumber);
  SET j = json_set(j, '$.before.TerminalDescription', '', '$.after.TerminalDescription', NEW.TerminalDescription);
  SET j = json_set(j, '$.before.TerminalGroupId', '', '$.after.TerminalGroupId', NEW.TerminalGroupId);
  SET j = json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
  SET j = json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
  SET j = json_set(j, '$.before.TerminalHash', '', '$.after.TerminalHash', NEW.TerminalHash);
  SET j = json_set(j, '$.before.TerminalSign', '', '$.after.TerminalSign', NEW.TerminalSign);
  SET j = json_set(j, '$.before.TerminalConfig', '', '$.after.TerminalConfig', NEW.TerminalConfig);
  SET j = json_set(j, '$.before.EditUserId', '', '$.after.EditUserId', NEW.EditUserId);
  SET j = json_set(j, '$.before.EditDateTime', '', '$.after.EditDateTime', NEW.EditDateTime);
  SET j = json_set(j, '$.before.Version', '', '$.after.Version', NEW.Version);
  call spSaveActionToLog('TerminalList',  NEW.Id, 'insert', j,NEW.Version);
END
$$$
CREATE TRIGGER `tgTerminalList_AU` AFTER UPDATE ON `TerminalList` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';
  if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
  if not NEW.TerminalId <=> OLD.TerminalId then SET j = json_set(j, '$.before.TerminalId', OLD.TerminalId, '$.after.TerminalId', NEW.TerminalId); end if;
  if not NEW.VendorId <=> OLD.VendorId then SET j = json_set(j, '$.before.VendorId', OLD.VendorId, '$.after.VendorId', NEW.VendorId); end if;
  if not NEW.TerminalVendorId <=> OLD.TerminalVendorId then SET j = json_set(j, '$.before.TerminalVendorId', OLD.TerminalVendorId, '$.after.TerminalVendorId', NEW.TerminalVendorId); end if;
  if not NEW.TerminalSerialNumber <=> OLD.TerminalSerialNumber then SET j = json_set(j, '$.before.TerminalSerialNumber ', OLD.TerminalSerialNumber, '$.after.TerminalSerialNumber', NEW.TerminalSerialNumber); end if;
  if not NEW.FNSerialNumber <=> OLD.FNSerialNumber then SET j = json_set(j, '$.before.FNSerialNumber', OLD.FNSerialNumber, '$.after.FNSerialNumber', NEW.FNSerialNumber); end if;
  if not NEW.TerminalDescription <=> OLD.TerminalDescription then SET j = json_set(j, '$.before.TerminalDescription', OLD.TerminalDescription, '$.after.TerminalDescription', NEW.TerminalDescription); end if;
  if not NEW.TerminalGroupId <=> OLD.TerminalGroupId then SET j = json_set(j, '$.before.TerminalGroupId', OLD.TerminalGroupId, '$.after.TerminalGroupId', NEW.TerminalGroupId); end if;
  if not NEW.VendorOrganizationId <=> OLD.VendorOrganizationId then SET j = json_set(j, '$.before.VendorOrganizationId', OLD.VendorOrganizationId, '$.after.VendorOrganizationId', NEW.VendorOrganizationId); end if;
  if not NEW.IsDeleted <=> OLD.IsDeleted then SET j = json_set(j, '$.before.IsDeleted', OLD.IsDeleted, '$.after.IsDeleted', NEW.IsDeleted); end if;
  if not NEW.TerminalHash <=> OLD.TerminalHash then SET j = json_set(j, '$.before.TerminalHash', OLD.TerminalHash, '$.after.TerminalHash', NEW.TerminalHash); end if;
  if not NEW.TerminalSign <=> OLD.TerminalSign then SET j = json_set(j, '$.before.TerminalSign', OLD.TerminalSign, '$.after.TerminalSign', NEW.TerminalSign); end if;
  if not NEW.TerminalConfig <=> OLD.TerminalConfig then SET j = json_set(j, '$.before.TerminalConfig', OLD.TerminalConfig, '$.after.TerminalConfig', NEW.TerminalConfig); end if;
  if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
  if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
  if not NEW.Version <=> OLD.Version
  then
      SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
      call spSaveActionToLog('TerminalList',  NEW.Id, 'update', j,NEW.Version);
  end if;
END
$$$
CREATE TRIGGER `tgTerminalList_BD` BEFORE DELETE ON `TerminalList` FOR EACH ROW BEGIN
  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
END
$$$
CREATE TRIGGER `tgTerminalList_BI` BEFORE INSERT ON `TerminalList` FOR EACH ROW begin


			SET NEW.TerminalId = CONVERT(CONCAT(NEW.VendorId, LPAD(NEW.TerminalVendorId,6,"0")), UNSIGNED);

			SET NEW.TerminalHash = SHA1(CONCAT(SHA1(NEW.TerminalSerialNumber),SHA1(NEW.FNSerialNumber)));

			SET NEW.TerminalSign = SHA1(CONCAT(SHA1(NEW.FNSerialNumber),SHA1(NEW.TerminalSerialNumber)));

			SET NEW.Version = 1;
			SET NEW.EditUserId = CURRENT_USER();
END
$$$
CREATE TRIGGER `tgTerminalList_BU` BEFORE UPDATE ON `TerminalList` FOR EACH ROW begin

		SET @IncVersionFl = 0;


		IF OLD.VendorId!=NEW.VendorId OR OLD.TerminalVendorId!=NEW.TerminalVendorId THEN
			SET NEW.TerminalId = CONVERT(CONCAT(NEW.VendorId, LPAD(NEW.TerminalVendorId,6,"0")), UNSIGNED);
			SET @IncVersionFl = 1;
		END IF;

		IF OLD.FNSerialNumber!=NEW.FNSerialNumber OR OLD.TerminalSerialNumber!=NEW.TerminalSerialNumber THEN

			SET NEW.TerminalHash = SHA1(CONCAT(SHA1(NEW.TerminalSerialNumber),SHA1(NEW.FNSerialNumber)));

			SET NEW.TerminalSign = SHA1(CONCAT(SHA1(NEW.FNSerialNumber),SHA1(NEW.TerminalSerialNumber)));
			SET @IncVersionFl = 1;
		END IF;

		IF
			OLD.IsDeleted!=NEW.IsDeleted OR
			OLD.TerminalDescription!=NEW.TerminalDescription OR
			OLD.TerminalGroupId!=NEW.TerminalGroupId OR
			OLD.TerminalConfig!=NEW.TerminalConfig OR
			OLD.VendorOrganizationId!=NEW.VendorOrganizationId
		THEN
			SET @IncVersionFl = 1;
		END IF;

		IF @IncVersionFl = 1 THEN

			SET NEW.Version = OLD.Version + 1;
			SET NEW.EditUserId = CURRENT_USER();
			SET NEW.EditDateTime= NOW();
		END IF;
END
$$$
CREATE TRIGGER `tgTerminalOrganizationList_AI` AFTER INSERT ON `TerminalOrganizationList` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';
  SET j=json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
  SET j=json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
  SET j=json_set(j, '$.before.VendorOrganizationId', '', '$.after.VendorOrganizationId', NEW.VendorOrganizationId);
  SET j=json_set(j, '$.before.Name', '', '$.after.Name', NEW.Name);
  SET j=json_set(j, '$.before.Address', '', '$.after.Address', NEW.Address);
  SET j=json_set(j, '$.before.Email', '', '$.after.Email', NEW.Email);
  SET j=json_set(j, '$.before.INN', '', '$.after.INN', NEW.INN);
  SET j=json_set(j, '$.before.FNTaxCode', '', '$.after.FNTaxCode', NEW.FNTaxCode);
  SET j=json_set(j, '$.before.FNTaxCodeDefault', '', '$.after.FNTaxCodeDefault', NEW.FNTaxCode);
  SET j=json_set(j, '$.before.FNOperatingMode', '', '$.after.FNOperatingMode', NEW.FNOperatingMode);
  SET j=json_set(j, '$.before.FNIsGambling', '', '$.after.FNIsGambling', NEW.FNIsGambling);
  SET j=json_set(j, '$.before.FNIsLottery', '', '$.after.FNIsLottery', NEW.FNIsLottery);
  SET j=json_set(j, '$.before.FNPaymentAgent', '', '$.after.FNPaymentAgent', NEW.FNPaymentAgent);
  SET j=json_set(j, '$.before.OFDServer', '', '$.after.OFDServer', NEW.OFDServer);
  SET j=json_set(j, '$.before.OFDServerPort', '', '$.after.OFDServerPort', NEW.OFDServerPort);
  SET j=json_set(j, '$.before.OFDName', '', '$.after.OFDName', NEW.OFDName);
  SET j=json_set(j, '$.before.OFDINN', '', '$.after.OFDINN', NEW.OFDINN);
  SET j=json_set(j, '$.before.OFDReceiptCheckURI', '', '$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI);
  SET j=json_set(j, '$.before.FnsServerAddress', '', '$.after.FnsServerAddress', NEW.FnsServerAddress);
  SET j=json_set(j, '$.before.SupportName', '', '$.after.SupportName', NEW.SupportName);
  SET j=json_set(j, '$.before.SupportPhone', '', '$.after.SupportPhone', NEW.SupportPhone);
  SET j=json_set(j, '$.before.SupportEmail', '', '$.after.SupportEmail', NEW.SupportEmail);
  SET j=json_set(j, '$.before.IsDeleted', '', '$.after.IsDeleted', NEW.IsDeleted);
  call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'insert', j,NEW.Version);
END
$$$
CREATE TRIGGER `tgTerminalOrganizationList_AU` AFTER UPDATE ON `TerminalOrganizationList` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';

  IF OLD.Id <=> NEW.Id THEN SET j = json_set(j,'$.before.Id', OLD.Id,'$.after.Id', NEW.Id); END IF;
  IF OLD.VendorId <=> NEW.VendorId THEN SET j = json_set(j,'$.before.VendorId', OLD.VendorId,'$.after.VendorId', NEW.VendorId); END IF;
  IF OLD.VendorOrganizationId<=> NEW.VendorOrganizationId THEN SET j = json_set(j,'$.before.VendorOrganizationId', OLD.VendorOrganizationId,'$.after.VendorOrganizationId', NEW.VendorOrganizationId); END IF;
  IF OLD.Name <=> NEW.Name THEN SET j = json_set(j,'$.before.Name', OLD.Name,'$.after.Name', NEW.Name); END IF;
  IF OLD.Address <=> NEW.Address THEN SET j = json_set(j,'$.before.Address', OLD.Address,'$.after.Address', NEW.Address); END IF;
  IF OLD.Email <=> NEW.Email THEN SET j = json_set(j,'$.before.Email', OLD.Email,'$.after.Email', NEW.Email); END IF;
  IF OLD.INN <=> NEW.INN THEN SET j = json_set(j,'$.before.INN', OLD.INN,'$.after.INN', NEW.INN); END IF;
  IF OLD.FNTaxCode <=> NEW.FNTaxCode THEN SET j = json_set(j,'$.before.FNTaxCode', OLD.FNTaxCode,'$.after.FNTaxCode', NEW.FNTaxCode); END IF;
  IF OLD.FNTaxCodeDefault <=> NEW.FNTaxCodeDefault THEN SET j = json_set(j,'$.before.FNTaxCodeDefault', OLD.FNTaxCodeDefault,'$.after.FNTaxCodeDefault', NEW.FNTaxCodeDefault); END IF;
  IF OLD.FNOperatingMode <=> NEW.FNOperatingMode THEN SET j = json_set(j,'$.before.FNOperatingMode', OLD.FNOperatingMode,'$.after.FNOperatingMode', NEW.FNOperatingMode); END IF;
  IF OLD.FNIsGambling <=> NEW.FNIsGambling THEN SET j = json_set(j,'$.before.FNIsGambling', OLD.FNIsGambling,'$.after.FNIsGambling', NEW.FNIsGambling); END IF;
  IF OLD.FNIsLottery <=> NEW.FNIsLottery THEN SET j = json_set(j,'$.before.FNIsLottery', OLD.FNIsLottery,'$.after.FNIsLottery', NEW.FNIsLottery); END IF;
  IF OLD.FNPaymentAgent <=> NEW.FNPaymentAgent THEN SET j = json_set(j,'$.before.FNPaymentAgent', OLD.FNPaymentAgent,'$.after.FNPaymentAgent', NEW.FNPaymentAgent); END IF;
  IF OLD.OFDServer <=> NEW.OFDServer THEN SET j = json_set(j,'$.before.OFDServer', OLD.OFDServer,'$.after.OFDServer', NEW.OFDServer); END IF;
  IF OLD.OFDServerPort <=> NEW.OFDServerPort THEN SET j = json_set(j,'$.before.OFDServerPort', OLD.OFDServerPort,'$.after.OFDServerPort', NEW.OFDServerPort); END IF;
  IF OLD.OFDName <=> NEW.OFDName THEN SET j = json_set(j,'$.before.OFDName', OLD.OFDName,'$.after.OFDName', NEW.OFDName); END IF;
  IF OLD.OFDINN <=> NEW.OFDINN THEN SET j = json_set(j,'$.before.OFDINN', OLD.OFDINN,'$.after.OFDINN', NEW.OFDINN); END IF;
  IF OLD.OFDReceiptCheckURI <=> NEW.OFDReceiptCheckURI THEN SET j = json_set(j,'$.before.OFDReceiptCheckURI', OLD.OFDReceiptCheckURI,'$.after.OFDReceiptCheckURI', NEW.OFDReceiptCheckURI); END IF;
  IF OLD.FnsServerAddress <=> NEW.FnsServerAddress THEN SET j = json_set(j,'$.before.FnsServerAddress', OLD.FnsServerAddress,'$.after.FnsServerAddress', NEW.FnsServerAddress); END IF;
  IF OLD.SupportName <=> NEW.SupportName THEN SET j = json_set(j,'$.before.SupportName', OLD.SupportName,'$.after.SupportName', NEW.SupportName); END IF;
  IF OLD.SupportPhone <=> NEW.SupportPhone THEN SET j = json_set(j,'$.before.SupportPhone', OLD.SupportPhone,'$.after.SupportPhone', NEW.SupportPhone); END IF;
  IF OLD.SupportEmail <=> NEW.SupportEmail THEN SET j = json_set(j,'$.before.SupportEmail', OLD.SupportEmail,'$.after.SupportEmail', NEW.SupportEmail); END IF;
  IF OLD.IsDeleted <=> NEW.IsDeleted  THEN SET j = json_set(j,'$.before.IsDeleted', OLD.IsDeleted,'$.after.IsDeleted', NEW.IsDeleted); END IF;

  IF not NEW.Version <=> OLD.Version
  THEN
    SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
    call spSaveActionToLog('TerminalOrganizationList',  NEW.Id, 'update', j,NEW.Version);
  END IF;
END
$$$
CREATE TRIGGER `tgTerminalOrganizationList_BD` BEFORE DELETE ON `TerminalOrganizationList` FOR EACH ROW BEGIN
  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
END
$$$
CREATE TRIGGER `tgTerminalOrganizationList_BI` BEFORE INSERT ON `TerminalOrganizationList` FOR EACH ROW begin

  SET NEW.Version = 1;
  SET NEW.EditUserId = CURRENT_USER();
END
$$$
CREATE TRIGGER `tgTerminalOrganizationList_BU` BEFORE UPDATE ON `TerminalOrganizationList` FOR EACH ROW begin


  IF  OLD.Id                   !=   NEW.Id                    OR
      OLD.VendorId             !=   NEW.VendorId              OR
      OLD.VendorOrganizationId !=   NEW.VendorOrganizationId  OR
      OLD.Name                 !=   NEW.Name                  OR
      OLD.Address              !=   NEW.Address               OR
      OLD.Email                !=   NEW.Email                 OR
      OLD.INN                  !=   NEW.INN                   OR
      OLD.FNTaxCode            !=   NEW.FNTaxCode             OR
      OLD.FNTaxCodeDefault     !=   NEW.FNTaxCodeDefault      OR
      OLD.FNOperatingMode      !=   NEW.FNOperatingMode       OR
      OLD.FNIsGambling         !=   NEW.FNIsGambling          OR
      OLD.FNIsLottery          !=   NEW.FNIsLottery           OR
      OLD.FNPaymentAgent       !=   NEW.FNPaymentAgent        OR
      OLD.OFDServer            !=   NEW.OFDServer             OR
      OLD.OFDServerPort        !=   NEW.OFDServerPort         OR
      OLD.OFDName              !=   NEW.OFDName               OR
      OLD.OFDINN               !=   NEW.OFDINN                OR
      OLD.OFDReceiptCheckURI   !=   NEW.OFDReceiptCheckURI    OR
      OLD.FnsServerAddress     !=   NEW.FnsServerAddress      OR
      OLD.SupportName          !=   NEW.SupportName           OR
      OLD.SupportPhone         !=   NEW.SupportPhone          OR
      OLD.SupportEmail         !=   NEW.SupportEmail          OR
      OLD.IsDeleted            !=   NEW.IsDeleted
  THEN

    SET NEW.Version = OLD.Version + 1;
    SET NEW.EditUserId = CURRENT_USER();
    SET NEW.EditDateTime= NOW();
  END IF;
END
$$$
CREATE TRIGGER `tgTerminalRoleFunction_AU` AFTER UPDATE ON `TerminalRoleFunction` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';
  if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
  if not NEW.VendorId <=> OLD.VendorId then SET j = json_set(j, '$.before.VendorId', OLD.VendorId, '$.after.VendorId', NEW.VendorId); end if;
  if not NEW.RoleId <=> OLD.RoleId then SET j = json_set(j, '$.before.RoleId', OLD.RoleId, '$.after.RoleId', NEW.RoleId); end if;
  if not NEW.FunctionId <=> OLD.FunctionId then SET j = json_set(j, '$.before.FunctionId', OLD.FunctionId, '$.after.FunctionId', NEW.FunctionId); end if;
  if not NEW.IsEnable <=> OLD.IsEnable then SET j = json_set(j, '$.before.IsEnable', OLD.IsEnable, '$.after.IsEnable', NEW.IsEnable); end if;
  if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
  if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
  if not NEW.Version <=> OLD.Version
  then
    SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
    call spSaveActionToLog('TerminalRoleFunction',  NEW.Id, 'update', j,NEW.Version);
  end if;
END
$$$
CREATE TRIGGER `tgTerminalRoleFunction_BI` BEFORE INSERT ON `TerminalRoleFunction` FOR EACH ROW begin

  SET NEW.Version = 1;
  SET NEW.EditUserId = CURRENT_USER();
END
$$$
CREATE TRIGGER `tgTerminalRoleFunction_BU` BEFORE UPDATE ON `TerminalRoleFunction` FOR EACH ROW begin
  IF
  OLD.Id!=NEW.Id OR OLD.VendorId!=NEW.VendorId OR OLD.RoleId!=NEW.RoleId OR
  OLD.FunctionId!=NEW.FunctionId OR OLD.IsEnable!=NEW.IsEnable
  THEN

    SET NEW.Version = OLD.Version + 1;
    SET NEW.EditUserId = CURRENT_USER();
    SET NEW.EditDateTime= NOW();
  END IF;
END
$$$
CREATE TRIGGER `tgTerminalUser_AI` AFTER INSERT ON `TerminalRoleFunction` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';
  SET j = json_set(j, '$.before.Id', '', '$.after.Id', NEW.Id);
  SET j = json_set(j, '$.before.VendorId', '', '$.after.VendorId', NEW.VendorId);
  SET j = json_set(j, '$.before.RoleId', '', '$.after.RoleId', NEW.RoleId);
  SET j = json_set(j, '$.before.FunctionId', '', '$.after.FunctionId', NEW.FunctionId);
  SET j = json_set(j, '$.before.IsEnable', '', '$.after.IsEnable', NEW.IsEnable);
  SET j = json_set(j, '$.before.EditUserId', '', '$.after.EditUserId', NEW.EditUserId);
  SET j = json_set(j, '$.before.EditDateTime', '', '$.after.EditDateTime', NEW.EditDateTime);
  SET j = json_set(j, '$.before.Version', '', '$.after.Version', NEW.Version);
  call spSaveActionToLog('TerminalRoleFunction',  NEW.Id, 'insert', j,NEW.Version);
END
$$$
CREATE TRIGGER `tgTerminalUser_AU` AFTER UPDATE ON `TerminalUser` FOR EACH ROW begin
  declare j json default '{"before": {}, "after": {}}';
  if not NEW.Id <=> OLD.Id then SET j = json_set(j, '$.before.Id', OLD.Id, '$.after.Id', NEW.Id); end if;
  if not NEW.TerminalUserId <=> OLD.TerminalUserId then SET j = json_set(j, '$.before.TerminalUserId', OLD.TerminalUserId, '$.after.TerminalUserId', NEW.TerminalUserId); end if;
  if not NEW.VendorId <=> OLD.VendorId then SET j = json_set(j, '$.before.VendorId', OLD.VendorId, '$.after.VendorId', NEW.VendorId); end if;
  if not NEW.UserVendorId <=> OLD.UserVendorId then SET j = json_set(j, '$.before.UserVendorId', OLD.UserVendorId, '$.after.UserVendorId', NEW.UserVendorId); end if;
  if not NEW.UserRoleId <=> OLD.UserRoleId then SET j = json_set(j, '$.before.UserRoleId', OLD.UserRoleId, '$.after.UserRoleId', NEW.UserRoleId); end if;
  if not NEW.UserFIO <=> OLD.UserFIO then SET j = json_set(j, '$.before.UserFIO', OLD.UserFIO, '$.after.UserFIO', NEW.UserFIO); end if;
  if not NEW.UserFIOShort <=> OLD.UserFIOShort then SET j = json_set(j, '$.before.UserFIOShort', OLD.UserFIOShort, '$.after.UserFIOShort', NEW.UserFIOShort); end if;
  if not NEW.UserPersonnelNumber <=> OLD.UserPersonnelNumber then SET j = json_set(j, '$.before.UserPersonnelNumber', OLD.UserPersonnelNumber, '$.after.UserPersonnelNumber', NEW.UserPersonnelNumber); end if;
  if not NEW.UserPIN <=> OLD.UserPIN then SET j = json_set(j, '$.before.UserPIN', OLD.UserPIN, '$.after.UserPIN', NEW.UserPIN); end if;
  if not NEW.VendorOrganizationId <=> OLD.VendorOrganizationId then SET j = json_set(j, '$.before.VendorOrganizationId', OLD.VendorOrganizationId, '$.after.VendorOrganizationId', NEW.VendorOrganizationId); end if;
  if not NEW.IsDeleted <=> OLD.IsDeleted then SET j = json_set(j, '$.before.IsDeleted', OLD.IsDeleted, '$.after.IsDeleted', NEW.IsDeleted); end if;
  if not NEW.UserHash <=> OLD.UserHash then SET j = json_set(j, '$.before.UserHash', OLD.UserHash, '$.after.UserHash', NEW.UserHash); end if;
  if not NEW.UserPINHash <=> OLD.UserPINHash then SET j = json_set(j, '$.before.UserPINHash', OLD.UserPINHash, '$.after.UserPINHash', NEW.UserPINHash); end if;
  if not NEW.UserSign <=> OLD.UserSign then SET j = json_set(j, '$.before.UserSign', OLD.UserSign, '$.after.UserSign', NEW.UserSign); end if;
  if not NEW.APISecretKey <=> OLD.APISecretKey then SET j = json_set(j, '$.before.APISecretKey', OLD.APISecretKey, '$.after.APISecretKey', NEW.APISecretKey); end if;
  if not NEW.EditUserId <=> OLD.EditUserId then SET j = json_set(j, '$.before.EditUserId', OLD.EditUserId, '$.after.EditUserId', NEW.EditUserId); end if;
  if not NEW.EditDateTime <=> OLD.EditDateTime then SET j = json_set(j, '$.before.EditDateTime', OLD.EditDateTime, '$.after.EditDateTime', NEW.EditDateTime); end if;
  if not NEW.Version <=> OLD.Version
  then
    SET j = json_set(j, '$.before.Version', OLD.Version, '$.after.Version', NEW.Version);
    call spSaveActionToLog('TerminalUser',  NEW.Id, 'update', j,NEW.Version);
  end if;
END
$$$
CREATE TRIGGER `tgTerminalUser_BD` BEFORE DELETE ON `TerminalUser` FOR EACH ROW BEGIN
  SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'DELETE FROM TABLE IS BLOKED!';
END
$$$
CREATE TRIGGER `tgTerminalUser_BI` BEFORE INSERT ON `TerminalUser` FOR EACH ROW begin
  SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,"0"));
  SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
  SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
  SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
  SET NEW.Version = 1;
  SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
  SET NEW.EditUserId = CURRENT_USER();
END
$$$
CREATE TRIGGER `tgTerminalUser_BU` BEFORE UPDATE ON `TerminalUser` FOR EACH ROW begin
  SET @IncVersionFl = 0;
  IF OLD.VendorId!=NEW.VendorId OR OLD.UserVendorId!=NEW.UserVendorId THEN
    SET NEW.TerminalUserId = CONCAT(NEW.VendorId, LPAD(NEW.UserVendorId,6,"0"));
    SET @IncVersionFl = 1;
  END IF;
  IF OLD.UserRoleId!=NEW.UserRoleId THEN
    SET @IncVersionFl = 1;
  END IF;
  IF OLD.UserFIO!=NEW.UserFIO OR OLD.UserFIOShort!=NEW.UserFIOShort OR
     OLD.UserPersonnelNumber!=NEW.UserPersonnelNumber OR OLD.UserPIN!=NEW.UserPIN OR
     OLD.VendorOrganizationId!=NEW.VendorOrganizationId THEN
    SET NEW.UserHash = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPersonnelNumber),SHA1(NEW.UserPin)));
    SET NEW.UserPINHash = SHA1(CONCAT(NEW.UserPin,SHA1(CONCAT(NEW.UserPin,MD5(NEW.UserPin)))));
    SET NEW.UserSign = SHA1(CONCAT(SHA1(NEW.UserFIO),SHA1(NEW.UserPin),SHA1(NEW.UserPersonnelNumber)));
    SET @IncVersionFl = 1;
  END IF;
  IF OLD.IsDeleted!=NEW.IsDeleted THEN
    SET @IncVersionFl = 1;
  END IF;
  IF @IncVersionFl = 1 THEN
    SET NEW.Version = OLD.Version + 1;
    SET NEW.APISecretKey = SHA1(CONCAT(NEW.Version,NEW.TerminalUserId,NEW.UserRoleId,NEW.UserPin));
    SET NEW.EditUserId = CURRENT_USER();
    SET NEW.EditDateTime= NOW();
  END IF;
END
$$$
--
-- Процедуры
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `spSaveActionToLog` (IN `pTableName` VARCHAR(255), IN `pTableRecordId` BIGINT UNSIGNED, IN `pActionType` VARCHAR(255), IN `pUpdateData` JSON, IN `pTableRecordVersion` INT UNSIGNED)  begin
    -- if coalesce(json_length(pUpdateData, '$.before'), 0) = 0 and coalesce(json_length(pUpdateData, '$.after'), 0) = 0 then -- "стандартные" поля пустые...
    --   set pUpdateDatas = fJsonTrim(pUpdateData);
    -- end if;
    if json_length(pUpdateData) > 0 then
      insert ignore into TerminalActionLog (ActionType, SQLUserId, TableName, TableRecordId, TableRecordVersion, UpdateData )
      values (pActionType, user(), pTableName, pTableRecordId, pTableRecordVersion, pUpdateData);
    end if;
  end
$$$
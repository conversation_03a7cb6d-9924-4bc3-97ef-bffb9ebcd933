-- --------------------------------------------------------

--
-- Структура таблицы `admin_role`
--

CREATE TABLE `admin_role` (
  `role_title` varchar(255) NOT NULL COMMENT 'Роль',
  `role_description` text COMMENT 'Описание роли'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Роли администраторов';

-- --------------------------------------------------------

--
-- Структура таблицы `admin_user`
--

CREATE TABLE `admin_user` (
  `user_id` int(11) NOT NULL COMMENT 'ID пользователя',
  `login` varchar(255) NOT NULL COMMENT 'Логин',
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL COMMENT 'Пароль',
  `access_token` varchar(255) NOT NULL COMMENT 'Токен',
  `name` varchar(500) NOT NULL COMMENT 'ФИО',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак удаления пользователя'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Пользователь';

-- --------------------------------------------------------

--
-- Структура таблицы `admin_xref_user_role`
--

CREATE TABLE `admin_xref_user_role` (
  `user_id` int(11) UNSIGNED NOT NULL COMMENT 'ID пользователя',
  `role_title` varchar(50) NOT NULL COMMENT 'Роль'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь админы и роли';

-- --------------------------------------------------------

--
-- Структура таблицы `agent`
--

CREATE TABLE `agent` (
  `agent_id` int(10) UNSIGNED NOT NULL COMMENT 'ID агента',
  `title` varchar(255) DEFAULT NULL COMMENT 'Название агента',
  `booking_tmp_time` int(10) UNSIGNED DEFAULT NULL COMMENT 'Время временного бронирования. В минутах',
  `secret_key` tinytext COMMENT 'Секретный ключ для подписи запросов API',
  `unitiki_price_markup` decimal(10,2) DEFAULT '0.00' COMMENT 'Сервисный сбор unitiki (в %)',
  `unitiki_price_markup_fix` decimal(10,2) DEFAULT '0.00' COMMENT 'Сервисный сбор unitiki (фикс)',
  `reseller_price_max` decimal(10,2) DEFAULT '0.00' COMMENT 'Максимальный размер сервисного сбора реселлеров (в %)',
  `reseller_agent_commission` decimal(10,2) DEFAULT '0.00' COMMENT 'Вознаграждение реселлера vendor_agent_commission (в %)',
  `currency_id` int(11) DEFAULT NULL COMMENT 'ID валюты по умолчанию',
  `contract_number` varchar(255) DEFAULT NULL COMMENT 'Номер договора',
  `reference_list` text COMMENT 'Список реферальных ссылок',
  `is_agent_inner` tinyint(4) DEFAULT '0' COMMENT 'Признак, является ли агент нашим внутренним',
  `can_booking_tmp` tinyint(1) UNSIGNED DEFAULT '1' COMMENT 'Признак возможности временного бронировния агентом, 1 - Разрешено',
  `can_buy` tinyint(1) UNSIGNED DEFAULT '1' COMMENT 'Признак возможности покупки билетов агентом, 1 - Разрешено',
  `can_ticket_refund` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Признак возможности возвращать билет, 1 - Разрешено',
  `can_make_request_without_cache` tinyint(1) UNSIGNED DEFAULT '1' COMMENT 'Право на запрос методов API без кэша, 1 - Разрешено',
  `enabled_correction_price_agent_unitiki_markup` tinyint(1) UNSIGNED DEFAULT '1' COMMENT 'Округлять цену покупки билета у Unitiki в API (Подогнать наценку Unitiki так, что бы получилось целое число)',
  `is_blocked` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Признак блокировки. 0 - Не заблокирован, 1 - Заблокирован',
  `is_send_notice_ticket_refund` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при возврате билетов',
  `is_ignore_vendor` tinyint(1) UNSIGNED DEFAULT NULL COMMENT 'Флаг означаюший игорирование списка ведоров у агента (0 - не игнорируются, 1 - игнорируются)',
  `is_send_push_buy` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Отправлять уведомление при покупке с сайта на url_push_buy_success_site',
  `url_push_buy` varchar(255) DEFAULT NULL COMMENT 'URL для отправки уведомлений при покупке с сайта',
  `enabled_realtime_search` tinyint(3) UNSIGNED DEFAULT '0' COMMENT 'Разрешить realtime поиск',
  `apiReturnAllCities` tinyint(1) DEFAULT NULL COMMENT 'Выдавать все города в API'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Агенты';

-- --------------------------------------------------------

--
-- Структура таблицы `billing`
--

CREATE TABLE `billing` (
  `billing_id` int(10) UNSIGNED NOT NULL COMMENT 'ID платежного агрегатора',
  `title` varchar(50) NOT NULL COMMENT 'Название платежной системы'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Платежный агрегатор';

-- --------------------------------------------------------

--
-- Структура таблицы `billing_rules`
--

CREATE TABLE `billing_rules` (
  `id` int(20) UNSIGNED NOT NULL,
  `agent_id` int(10) UNSIGNED NOT NULL,
  `vendor_id` int(10) UNSIGNED NOT NULL,
  `enabled_service_markup` int(1) UNSIGNED DEFAULT NULL COMMENT 'Разрешен ли сервисный сбор у вендора',
  `unitiki_price_markup` decimal(10,4) UNSIGNED DEFAULT NULL COMMENT 'Сервисный сбор Unitiki',
  `unitiki_price_markup_fix` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сервисный сбор Unitiki (фикс)',
  `reseller_agent_commission` decimal(10,4) UNSIGNED DEFAULT NULL COMMENT 'Агентское вознаграждение реселлера',
  `reseller_price_max` decimal(10,4) UNSIGNED DEFAULT NULL COMMENT 'Максимальный размер сервисного сбора реселлера'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `card_identity`
--

CREATE TABLE `card_identity` (
  `card_identity_id` int(11) NOT NULL,
  `title` varchar(50) DEFAULT NULL,
  `enabled_default` tinyint(1) DEFAULT '0' COMMENT 'Документ доступен по умолчанию всем рейсам у которых не указаны определенные типы докуметов',
  `egis_doc_type_id` tinyint(4) DEFAULT NULL COMMENT 'Код документа в базе ЕГИС',
  `input_mask` varchar(32) NOT NULL COMMENT 'Маска ввода',
  `reg_mask` varchar(64) DEFAULT '.{25}' COMMENT 'Маска проверки через регулярное выражение',
  `use_numpad_only` tinyint(1) DEFAULT '0' COMMENT 'Признак использования только цифровой клавиатуры',
  `nationality` tinyint(1) DEFAULT '0' COMMENT 'Национальная принадлежность All(0), RussiaOnly(1), ExceptRussia(2)',
  `picture_idx` tinyint(4) DEFAULT '0' COMMENT 'Индекс изображения',
  `use_for_child` tinyint(1) DEFAULT '0' COMMENT 'Тип документа используется для детей'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Удостоверяющий документ';

-- --------------------------------------------------------

--
-- Структура таблицы `citizenship`
--

CREATE TABLE `citizenship` (
  `citizenship_id` int(11) NOT NULL COMMENT 'ID страны',
  `title` varchar(255) DEFAULT NULL COMMENT 'Название страны',
  `code_lat_a2` varchar(255) DEFAULT NULL COMMENT 'Латинский код, 2 буквенный',
  `code_num` varchar(255) DEFAULT NULL COMMENT 'Цифровой код'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Гражданство';

-- --------------------------------------------------------

--
-- Структура таблицы `city`
--

CREATE TABLE `city` (
  `city_id` int(11) NOT NULL COMMENT 'ID города',
  `city_type_id` smallint(5) UNSIGNED DEFAULT NULL COMMENT 'ID типа населенного пункта',
  `country_id` int(11) NOT NULL COMMENT 'ID Страны',
  `region` varchar(255) NOT NULL COMMENT 'Регион',
  `district` varchar(255) NOT NULL COMMENT 'Район',
  `title` varchar(255) NOT NULL COMMENT 'Название',
  `title_genitive` varchar(255) DEFAULT NULL COMMENT 'Название в родительном падеже',
  `title_accusative` varchar(255) DEFAULT NULL COMMENT 'Название в винительном падеже',
  `title_prepositional` varchar(255) DEFAULT NULL COMMENT 'Название в предложном падеже',
  `title_lat` varchar(255) DEFAULT NULL COMMENT 'Название литиницей',
  `title_genitive_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в родительном падеже',
  `title_accusative_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в винительном падеже',
  `title_prepositional_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в предложном падеже',
  `lng` varchar(255) NOT NULL COMMENT 'Долгота',
  `lat` varchar(255) NOT NULL COMMENT 'Широта',
  `city_title_search` varchar(255) NOT NULL COMMENT 'Название населенного пункта для поиска',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак верефикации города. 0 - Не верифицирован, 1 - Верифицирован',
  `code` varchar(255) DEFAULT NULL COMMENT 'Некий код нас.пункта',
  `region_id` int(11) DEFAULT NULL COMMENT 'ИД региона'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Город';

-- --------------------------------------------------------

--
-- Структура таблицы `city_import_problem`
--

CREATE TABLE `city_import_problem` (
  `partner_id` int(11) NOT NULL,
  `text` varchar(255) NOT NULL,
  `is_processed` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `city_import_result`
--

CREATE TABLE `city_import_result` (
  `partner_id` int(11) NOT NULL COMMENT 'партнер',
  `type_result` varchar(100) NOT NULL COMMENT 'тип записи',
  `params` json NOT NULL COMMENT 'параметры',
  `position` int(11) NOT NULL COMMENT 'порядковый номер',
  `text` varchar(255) NOT NULL COMMENT 'текст сообщения',
  `is_processed` tinyint(1) NOT NULL COMMENT 'признак обработки вручную'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `city_type`
--

CREATE TABLE `city_type` (
  `city_type_id` smallint(1) UNSIGNED NOT NULL COMMENT 'ID типа города',
  `title` varchar(255) NOT NULL COMMENT 'Заголовок типа города',
  `title_short` varchar(10) DEFAULT NULL COMMENT 'Краткое название типа города',
  `city_size` tinyint(3) DEFAULT '99' COMMENT 'Коэффициент численности НП. Чем меньше тем крупнее населенный пункт',
  `coordinates_error` float NOT NULL DEFAULT '0.05' COMMENT 'погрешность при определении коррдинат'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Типы населенных пунктов';

-- --------------------------------------------------------

--
-- Структура таблицы `country`
--

CREATE TABLE `country` (
  `country_id` int(11) NOT NULL COMMENT 'ID страны',
  `title` varchar(255) DEFAULT NULL COMMENT 'Название страны',
  `code_a2` varchar(255) DEFAULT NULL COMMENT 'Код страны в формате ISO 3166-1 alpha-2',
  `timezone` varchar(255) DEFAULT NULL COMMENT 'Часовой пояс'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Страны';

-- --------------------------------------------------------

--
-- Структура таблицы `currency`
--

CREATE TABLE `currency` (
  `currency_id` int(10) UNSIGNED NOT NULL COMMENT 'ID валюты',
  `title` varchar(255) DEFAULT NULL COMMENT 'Название',
  `title_short` varchar(50) DEFAULT NULL COMMENT 'Сокращенное название',
  `iso4217` varchar(50) DEFAULT NULL COMMENT 'Латинское сокращение названя',
  `iso4217810` varchar(50) DEFAULT NULL COMMENT 'Цифровой код валюты',
  `rate` decimal(10,8) UNSIGNED DEFAULT '1.00000000' COMMENT 'Курс'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Валюта';

--
-- Дамп данных таблицы `currency`
--

INSERT INTO `currency` (`currency_id`, `title`, `title_short`, `iso4217`, `iso4217810`, `rate`) VALUES
(1, 'Российский рубль', 'руб.', 'RUB', '643', '1.00000000'),
(2, 'Доллар', 'дол.', 'USD', '840', '65.94140000'),
(3, 'Евро', 'евро', 'EUR', '978', '73.63020000'),
(4, 'Драм', 'драм', 'AMD', '051', '0.14174940'),
(5, 'Украинская гривна', 'грн.', 'UAH', '980', '3.11853000');

-- --------------------------------------------------------

--
-- Структура таблицы `dispatch_ticket_status`
--

CREATE TABLE `dispatch_ticket_status` (
  `id` int(11) NOT NULL COMMENT 'Уникальный идентификатор записи',
  `UIPP` char(41) NOT NULL COMMENT 'Уникальный идентификатоор пассажирской перевозки',
  `ticket_id` int(10) DEFAULT NULL COMMENT 'ID билета из СДБП, если есть',
  `outer_ticket_id` varchar(50) DEFAULT NULL COMMENT 'ID билета из внешней системы(NSI)',
  `dispatch_status` tinyint(1) DEFAULT NULL COMMENT 'Активный диспетчерский статус билета',
  `change_datetime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время последнего изменения статуса',
  `dispatch_status_list` text COMMENT 'Список изменений статуса билета в формате JSON'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Учет изменения состояния билетов СДБП и внешних систем';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVAbonementCounters`
--

CREATE TABLE `EMVAbonementCounters` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `AbonementId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор абонемента',
  `CounterType` int(10) UNSIGNED NOT NULL COMMENT 'Тип счетчика',
  `ConstCount` int(10) UNSIGNED NOT NULL COMMENT 'Начальное число поездок на счетчике(при покупке)',
  `TripCount` int(10) UNSIGNED NOT NULL COMMENT 'Текущее число поездок(сколько истрачено)',
  `IsSeaFerry` tinyint(1) DEFAULT '0' COMMENT 'Морской паром',
  `IsSeaHovercrafts` tinyint(1) DEFAULT '0' COMMENT 'Морские суда на воздушной подушке',
  `IsSeaHydrofoils` tinyint(1) DEFAULT '0' COMMENT 'Морские суда на подводных крыльях',
  `IsSeaEkranoplan` tinyint(1) DEFAULT '0' COMMENT 'Морской экраноплан',
  `IsRiverFerry` tinyint(1) DEFAULT '0' COMMENT 'Речной паром',
  `IsRiverHovercrafts` tinyint(1) DEFAULT '0' COMMENT 'Речные суда на воздушной подушке',
  `IsRiverHydrofoils` tinyint(1) DEFAULT '0' COMMENT 'Речные суда на подводных крыльях',
  `IsRiverPlaningVessels` tinyint(1) DEFAULT '0' COMMENT 'Речные глиссирующие суда',
  `IsAircraft` tinyint(1) DEFAULT '0' COMMENT 'Самолёт',
  `IsHelicopter` tinyint(1) DEFAULT '0' COMMENT 'Вертолёт',
  `IsBus` tinyint(1) DEFAULT '0' COMMENT 'Автобус',
  `IsTrolleybus` tinyint(1) DEFAULT '0' COMMENT 'Троллейбус',
  `IsTram` tinyint(1) DEFAULT '0' COMMENT 'Трамвай',
  `IsTaxi` tinyint(1) DEFAULT '0' COMMENT 'Такси',
  `IsLongDistanceTrains` tinyint(1) DEFAULT '0' COMMENT 'Поезда дальнего следования',
  `IsSuburbanElectricTrain` tinyint(1) DEFAULT '0' COMMENT 'Пригородные электрички',
  `IsFunicular` tinyint(1) DEFAULT '0' COMMENT 'Фуникулёр',
  `IsCableCar` tinyint(1) DEFAULT '0' COMMENT 'Канатная дорога',
  `IsMetro` tinyint(1) DEFAULT '0' COMMENT 'Метрополитен',
  `IsPipeline` tinyint(1) DEFAULT '0' COMMENT 'Трубопроводный'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Счетчики абонементов';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVAbonementList`
--

CREATE TABLE `EMVAbonementList` (
  `AbonementId` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `WriteOffsId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор правила списания',
  `Name` varchar(64) DEFAULT NULL COMMENT 'Название',
  `Description` varchar(256) DEFAULT NULL COMMENT 'Расширенное описание',
  `AgentId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор агента, продавшего абонемент',
  `PANHash` varchar(64) NOT NULL COMMENT 'PANHash карты',
  `IsSocial` tinyint(4) DEFAULT '0' COMMENT 'Является социальным',
  `IsActive` tinyint(4) NOT NULL COMMENT 'Признак, что абонемент активен',
  `IsDayTripLimit` tinyint(4) DEFAULT '0' COMMENT 'Ограничение количества поездок в день',
  `MaxDayTrip` int(11) DEFAULT NULL COMMENT 'Максимальное количество поездок в день',
  `CurrentDayTrip` int(11) DEFAULT NULL COMMENT 'Совершено поездок за текущий день',
  `LastTripDateTime` timestamp NULL DEFAULT NULL COMMENT 'Дата и время последней зарегистрированной поездки',
  `ValidTimeType` tinyint(4) DEFAULT '1' COMMENT 'Тип срока действия(EMVValidType)',
  `ValidTimeStart` timestamp NULL DEFAULT NULL COMMENT 'Начало действия',
  `ValidTimeDays` int(11) DEFAULT '0' COMMENT 'Период действия(дней)',
  `ValidTimeEnd` timestamp NULL DEFAULT NULL COMMENT 'Окончание действия',
  `ValidTimeMaximum` timestamp NULL DEFAULT NULL COMMENT 'Максимальный срок действия абонемента. Только для типа vttIntervalAndDays',
  `SellPrice` decimal(10,3) DEFAULT NULL COMMENT 'Стоимость продажи в установленной валюте',
  `CurrencyId` int(10) UNSIGNED DEFAULT '1' COMMENT 'Валюта продажи',
  `SellDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время продажи',
  `LastCheckDateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время последней проверки абонемента на срок действия и счетчики'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Список проданных абонементов';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVAbonementTransaction`
--

CREATE TABLE `EMVAbonementTransaction` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `PANHash` varchar(64) NOT NULL COMMENT 'PANHash карты',
  `TransactionId` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор тарифицированной транзакции',
  `AbonementId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор абонемента примененного к транзакции',
  `CounterId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор примененного счетчика',
  `CounterBefore` int(10) UNSIGNED NOT NULL COMMENT 'Значение счетчика до тарификации',
  `CounterAfter` int(10) UNSIGNED NOT NULL COMMENT 'Значение счетчика после тарификации',
  `SourceCounter` json DEFAULT NULL COMMENT 'Состояние примененного счетчика до тарификации',
  `SourceTransaction` json DEFAULT NULL COMMENT 'Иходная транзакция',
  `TerminalId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор терминала на котором была проведена транзакция',
  `VendorId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор вендора к которому относится терминал',
  `TerminalOrganizationId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор организации к которой относится терминал',
  `TransporterName` varchar(255) DEFAULT NULL COMMENT 'Наименование перевозчика, осуществившего перевозку',
  `RouteName` varchar(255) DEFAULT NULL COMMENT 'Наименование маршрута',
  `RouteNumber` varchar(32) DEFAULT NULL COMMENT 'Номер маршрута',
  `TransportType` int(10) UNSIGNED NOT NULL COMMENT 'Тип транспорта',
  `TripDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время поездки',
  `TarifficationDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время тарификации',
  `IsActual` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'Статус транзакции. Активная - 1. Отменненная - 0. '
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Тарифицированные транзакции по абонементам';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVBINList`
--

CREATE TABLE `EMVBINList` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `BIN` varchar(6) NOT NULL COMMENT 'Маска диапазона или BIN',
  `IsBlocked` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак, что BIN блокирован',
  `CreateDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания записи',
  `Name` varchar(64) NOT NULL COMMENT 'Название диапазона',
  `Description` varchar(64) NOT NULL COMMENT 'Расширенное описание'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `EMVStopList`
--

CREATE TABLE `EMVStopList` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `PANHash` varchar(128) NOT NULL COMMENT 'PAN hash номера карты',
  `IsBlocked` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак, что карта блокирована',
  `IsActual` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Признак, что статус карты актуален',
  `CreateDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Таблица для хранения агрегированного стоп-листа по всем НСИ';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVWriteOffsBinding`
--

CREATE TABLE `EMVWriteOffsBinding` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `WriteOffsId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор правила списания',
  `VendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор вендора',
  `VendorOrganizationId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор организации вендора',
  `IsEnable` tinyint(3) UNSIGNED NOT NULL COMMENT 'Связка разрешена',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи',
  `Version` int(10) UNSIGNED DEFAULT NULL COMMENT 'Версия записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Доступ вендоров к темплайтам';

-- --------------------------------------------------------

--
-- Структура таблицы `EMVWriteOffsTemplate`
--

CREATE TABLE `EMVWriteOffsTemplate` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `Name` varchar(64) DEFAULT NULL COMMENT 'Название',
  `Description` varchar(256) DEFAULT NULL COMMENT 'Расширенное описание',
  `Type` tinyint(4) DEFAULT '0' COMMENT 'Тип темплайта(1-Абонемент.2-Тариф)',
  `IsSocial` tinyint(4) DEFAULT '0' COMMENT 'Является социальным',
  `RuleList` json DEFAULT NULL COMMENT 'Список правил списания за проезд',
  `ValidTimeType` tinyint(4) DEFAULT '1' COMMENT 'Тип срока действия(EMVValidType)',
  `ValidTimeStart` timestamp NULL DEFAULT NULL COMMENT 'Начало действия',
  `ValidTimeDays` int(11) DEFAULT '0' COMMENT 'Период действия(дней)',
  `ValidTimeEnd` timestamp NULL DEFAULT NULL COMMENT 'Окончание действия',
  `IsActive` tinyint(4) DEFAULT '0' COMMENT 'Продажи разрешены(1) ',
  `SellDateTimeStart` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время начала продаж',
  `SellDateTimeEnd` timestamp NULL DEFAULT NULL COMMENT 'Дата и время окончания продаж',
  `SellPrice` decimal(10,3) DEFAULT NULL COMMENT 'Стоимость продажи в установленной валюте',
  `CurrencyId` int(10) UNSIGNED DEFAULT '1' COMMENT 'Валюта продажи',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи',
  `Version` int(10) UNSIGNED DEFAULT NULL COMMENT 'Версия записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Список не зарегистрированных терминалов';

-- --------------------------------------------------------

--
-- Структура таблицы `geo_action`
--

CREATE TABLE `geo_action` (
  `action_id` int(11) UNSIGNED NOT NULL COMMENT 'ID записи',
  `action` tinyint(1) UNSIGNED NOT NULL COMMENT 'Событие. 1 - PUT (записать), 2 - MERGE (Мерж), 3 - REMOVE (удалить)',
  `object_type` tinyint(1) UNSIGNED NOT NULL COMMENT 'Тип объекта. 1 - Город, 2 - Станция',
  `object_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID объекта над которым происходили действия (action). Может быть NULL если создание нового города',
  `data` text NOT NULL COMMENT 'Данные объекта',
  `version` int(10) UNSIGNED NOT NULL COMMENT 'Версия структуры хранения GEO объектов'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Создание, редактирование, мерж гео объектов. Используется в основном в GDS.GEO';

-- --------------------------------------------------------

--
-- Структура таблицы `geo_diff_log`
--

CREATE TABLE `geo_diff_log` (
  `geo_diff_log` int(10) UNSIGNED NOT NULL,
  `object_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID объекта который изменился',
  `object_type` varchar(255) DEFAULT NULL COMMENT 'Тип объекта который изменился',
  `user_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID пользователя изменивший объект',
  `object_before` text COMMENT 'Объект в JSON До',
  `object_after` text COMMENT 'Объект в JSON После',
  `event_datetime` datetime DEFAULT NULL COMMENT 'Дата события'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Лог изменения ГЕО объектов (Городов, станций и т.п.)';

-- --------------------------------------------------------

--
-- Структура таблицы `helpdesk_comment`
--

CREATE TABLE `helpdesk_comment` (
  `helpdesk_comment_id` int(11) NOT NULL,
  `helpdesk_request_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `datetime_create` datetime NOT NULL,
  `type` int(11) NOT NULL COMMENT '0 обычный, 1 системный',
  `content` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `helpdesk_request`
--

CREATE TABLE `helpdesk_request` (
  `helpdesk_request_id` int(11) NOT NULL,
  `operation_id` int(11) DEFAULT NULL,
  `admin_user_id` int(11) DEFAULT NULL COMMENT 'ответственный исполнитель',
  `date_reminder` date DEFAULT NULL COMMENT 'дата когда нужно что то делать',
  `datetime_create` datetime NOT NULL COMMENT 'дата создания заявки',
  `datetime_update` datetime DEFAULT NULL COMMENT 'дата обновления заявки',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '0 new, 1 in progress, 2 done'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `HumanNameList`
--

CREATE TABLE `HumanNameList` (
  `id` int(11) NOT NULL COMMENT 'Уникальный идентификатор записи',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'Имя',
  `gender` tinyint(1) DEFAULT NULL COMMENT 'Пол имени',
  `country_id` int(11) UNSIGNED NOT NULL COMMENT 'Id страны для которой приведены имена'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `invoice`
--

CREATE TABLE `invoice` (
  `invoice_id` int(10) UNSIGNED NOT NULL COMMENT 'ID счета',
  `operation_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID операции',
  `outer_invoice_id` varchar(255) DEFAULT NULL COMMENT 'Внешний Invoice ID',
  `status` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Статус. 0 -Новый, 1 - Успех, 2 - Отмена, 3 - Возврат денег, 4 - Системная ошибка, 5 - Оплата после истечения времени',
  `error` tinyint(1) DEFAULT NULL,
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата создания счета',
  `datetime_payment` datetime DEFAULT NULL COMMENT 'Дата оплаты счета',
  `datetime_refund` datetime DEFAULT NULL COMMENT 'Дата возврата денег по счету',
  `billing_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID платежного агрегатора',
  `currency_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID валюты',
  `payment_type` tinyint(1) UNSIGNED DEFAULT NULL COMMENT '1 - Оплата из кошелька в Яндекс.Деньгах, 2 - Оплата с произвольной банковской карты, 3 - Платеж со счета мобильного телефона, 4 - Оплата наличными через кассы и терминалы, 5 - Оплата из кошелька в системе WebMoney, 6 - Оплата через Сбербанк: оплата по SMS или Сбербанк Онлайн, 7 - Оплата через мобильный терминал (mPOS), 8 - Оплата через Альфа-Клик, 9 - Оплата через MasterPass, 10 - Оплата через Промсвязьбанк',
  `price` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сумма счета',
  `price_billing` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сумма переводимая от оператора (биллинга)',
  `user_email` varchar(100) DEFAULT NULL COMMENT 'Почта клиента',
  `user_phone` varchar(30) DEFAULT NULL COMMENT 'Телефон',
  `url_buy_callback` varchar(255) DEFAULT NULL COMMENT 'URL для перехода после усмешной оплаты'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Счета';

-- --------------------------------------------------------

--
-- Структура таблицы `invoice_log`
--

CREATE TABLE `invoice_log` (
  `invoice_log_id` int(10) UNSIGNED NOT NULL COMMENT 'ID записи',
  `invoice_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID счета',
  `billing_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID биллинга',
  `answer` text COMMENT 'JSON ответ',
  `answer_unitiki` text COMMENT 'JSON ответ Unitiki платежке',
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата ответа'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Логирование счетов';

-- --------------------------------------------------------

--
-- Структура таблицы `make_site_map_history`
--

CREATE TABLE `make_site_map_history` (
  `city_id_start` int(11) NOT NULL COMMENT 'Id города отправления',
  `city_id_end` int(11) NOT NULL COMMENT 'Id города прибытия',
  `next_check_datetime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время последнего изменения статуса',
  `last_requiest_time` decimal(10,3) DEFAULT '0.000' COMMENT 'Время выполнения последнего запроса',
  `route_weight` decimal(5,3) DEFAULT '0.000' COMMENT 'Вес направления. Чем больше, тем чаще синхронизируется',
  `route_distance` decimal(9,2) DEFAULT '0.00' COMMENT 'Расстояние между станциями по прямой.',
  `fault_sync_count` tinyint(1) DEFAULT '0' COMMENT 'Количество неуспешных синхронизаций'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='История формирования структуры сайта';

-- --------------------------------------------------------

--
-- Структура таблицы `operation`
--

CREATE TABLE `operation` (
  `operation_id` int(11) NOT NULL COMMENT 'ID операции',
  `datetime_start` datetime DEFAULT NULL COMMENT 'Дата начала операции',
  `datetime_order` datetime DEFAULT NULL COMMENT 'Дата брони (status = 3)',
  `datetime_buy` datetime DEFAULT NULL COMMENT 'Дата покупки (status = 1)',
  `datetime_cancel` datetime DEFAULT NULL COMMENT 'Дата отмены (status = 2)',
  `datetime_to_cancel` datetime DEFAULT NULL COMMENT 'Дата автоматической отмены операции',
  `partner_id` int(11) NOT NULL COMMENT 'ID Партнера',
  `status` tinyint(1) DEFAULT '0' COMMENT 'Статус. 0 - Создан, 1 - Успех, 2 - Отмена, 3 - Временная бронь, 4 - Долгосрочная бронь',
  `user_id` int(11) DEFAULT NULL COMMENT 'ID пользователя',
  `reason` tinyint(1) DEFAULT '0' COMMENT 'Причина отмены. 1 - Ручная отмена временной брони, 2 - Ручная отмена брони, 3 - Ручная отмена новой операции, 4 - Автоматическая отмена при ошибке в новой операции, 5 - Автоматическая отмена временной брони, 6 - Автоматическая отмена брони, 7 - Ручная отмена успешной операции',
  `hash` varchar(255) DEFAULT NULL COMMENT 'Хэш',
  `agent_id` int(255) DEFAULT NULL COMMENT 'ID агента',
  `reference` varchar(255) DEFAULT NULL COMMENT 'Референс'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Операции с билетами';

-- --------------------------------------------------------

--
-- Структура таблицы `operation_log`
--

CREATE TABLE `operation_log` (
  `log_id` int(11) NOT NULL COMMENT 'ID',
  `operation_id` int(11) DEFAULT NULL COMMENT 'ID операции',
  `user_id` int(11) DEFAULT NULL COMMENT 'ID пользователя',
  `type` varchar(100) DEFAULT NULL COMMENT 'Тип действия',
  `data` text COMMENT 'Доп. данные',
  `date` datetime DEFAULT NULL COMMENT 'Время',
  `status_id` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Статус операции'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Логирование действий ';

-- --------------------------------------------------------

--
-- Структура таблицы `operation_reference_utm`
--

CREATE TABLE `operation_reference_utm` (
  `operation_id` int(11) NOT NULL,
  `utm_source` text,
  `utm_medium` text,
  `utm_term` text,
  `utm_content` text,
  `utm_campaign` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `organization`
--

CREATE TABLE `organization` (
  `organization_id` int(11) NOT NULL COMMENT 'ID организации',
  `partner_id` int(11) NOT NULL COMMENT 'ID партнера',
  `brand` varchar(255) DEFAULT NULL COMMENT 'Название бренда',
  `ownership` varchar(50) DEFAULT NULL COMMENT 'Форма собственности. ООО, ОАО, ЗАО, ИП',
  `title_full` varchar(255) DEFAULT NULL COMMENT 'Название полностью',
  `title_small` varchar(255) DEFAULT NULL COMMENT 'Название сокращено',
  `director_job` varchar(255) DEFAULT NULL COMMENT 'eg генеральный директор',
  `director_name` varchar(255) DEFAULT NULL COMMENT 'eg Иванов Иван Иванович',
  `director_acts_on` varchar(255) DEFAULT NULL COMMENT 'eg устава',
  `address_legal` text COMMENT 'Юридический адрес',
  `address_actual` text COMMENT 'Фактический адрес',
  `address_mail` text COMMENT 'Почтовый адрес',
  `swift` varchar(255) DEFAULT NULL COMMENT 'SWIFT',
  `ogrn` varchar(255) DEFAULT NULL COMMENT 'ОГРН',
  `inn` varchar(255) DEFAULT NULL COMMENT 'ИНН',
  `kpp` varchar(255) DEFAULT NULL COMMENT 'КПП',
  `fin_rs` varchar(255) DEFAULT NULL COMMENT 'Расчетный счет',
  `fin_ks` varchar(255) DEFAULT NULL COMMENT 'Корреспондентский счет',
  `fin_bik` varchar(255) DEFAULT NULL COMMENT 'БИК банка',
  `fin_bank` varchar(255) DEFAULT NULL COMMENT 'Название банка',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT 'Признак удаления'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Организация';

-- --------------------------------------------------------

--
-- Структура таблицы `partner`
--

CREATE TABLE `partner` (
  `partner_id` int(11) NOT NULL COMMENT 'ID партнера',
  `title` varchar(255) DEFAULT NULL COMMENT 'Название',
  `description` longtext COMMENT 'Описание',
  `email` varchar(255) DEFAULT NULL COMMENT 'Почта',
  `phone` varchar(255) DEFAULT NULL COMMENT 'Телефон',
  `telegram_chat_id` varchar(255) DEFAULT NULL,
  `use_subagent` tinyint(1) DEFAULT '0' COMMENT 'Признак, использовать ли субагентов. 0 - Нет, 1 - Да',
  `agent_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID агента',
  `statement_json` text COMMENT 'JSON настроек посадочной ведомости',
  `type_vendor` tinyint(1) DEFAULT '0' COMMENT 'Тип поставщика билетов. 0 - не поставщик, 1 - CRM, 2 - CRM+АРМК, 3 - API',
  `type_reseller` tinyint(1) DEFAULT '0' COMMENT 'Тип ресейлера билетов.  0 - не продает, 1 - API, 2 - API+АРМКРеселлер',
  `vendor_agent_commission` decimal(10,4) DEFAULT '0.0000' COMMENT 'Агентское вознагаждение которое отдает вендор Unitiki. Параметр для вендора. (в %)',
  `vendor_currency_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID валюты в которой происходит расчет с вендором',
  `enabled_service_markup` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Разрешен ли сервисный сбор',
  `contract_number` varchar(255) DEFAULT NULL COMMENT 'Номер договора',
  `time_stop_booking` int(11) DEFAULT NULL COMMENT 'Время  в минутах, за которое нужно останавливать букинг билетов',
  `is_enabled_booking_without_ticket_data` tinyint(1) DEFAULT '0' COMMENT 'Разрешить вендору букинг с неполными данными клиента',
  `vendor_secret_key` varchar(50) DEFAULT NULL COMMENT 'Key которым будет авторизовываться партнер в финстате и гео',
  `is_send_notice_ride` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления о малом количестве рейсов',
  `is_send_notice_buy_site` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при покупке билетов через сайт',
  `is_send_notice_buy_awpc` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при покупке билетов через АРМК',
  `is_send_notice_booking_awpc` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при бронировании билетов через АРМК',
  `is_send_notice_booking_tmp_site` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при временном бронировании билетов через сайт',
  `is_send_notice_ticket_refund` tinyint(1) DEFAULT '0' COMMENT 'Флаг, отправлять уведомления при возврате билетов',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT 'Признак удаления'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Партнер';

-- --------------------------------------------------------

--
-- Структура таблицы `payment`
--

CREATE TABLE `payment` (
  `payment_id` int(10) UNSIGNED NOT NULL COMMENT 'ID Выплаты',
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID партнера',
  `organization_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID организации',
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата создания выплаты',
  `datetime_success` datetime DEFAULT NULL COMMENT 'Дата успешной выплаты',
  `amount` decimal(12,2) DEFAULT NULL COMMENT 'Сумма',
  `currency_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID валюты',
  `status` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Статус. 0 - Новая выплата, 1 - В обработке(передана бухгалтерии), 2 - Успех',
  `type` tinyint(1) UNSIGNED DEFAULT NULL COMMENT '1 - Счет реселлеру, 2 - Выплата вендору, 3 - Счет вендору на выплату агенстких, 4 - Выплата реселлеру агентских'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Выплаты';

-- --------------------------------------------------------

--
-- Структура таблицы `payment_log`
--

CREATE TABLE `payment_log` (
  `log_id` int(11) NOT NULL COMMENT 'ID',
  `payment_id` int(11) DEFAULT NULL COMMENT 'ID выплаты',
  `user_id` int(11) DEFAULT NULL COMMENT 'ID пользователя',
  `type` varchar(100) DEFAULT NULL COMMENT 'Тип действия',
  `data` text COMMENT 'Доп. данные',
  `date` datetime DEFAULT NULL COMMENT 'Время',
  `status_id` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Статус выплаты'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Логирование действий ';

-- --------------------------------------------------------

--
-- Структура таблицы `push_buy_log`
--

CREATE TABLE `push_buy_log` (
  `log_id` int(10) UNSIGNED NOT NULL,
  `datetime_start` datetime DEFAULT NULL,
  `datetime_end` datetime DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `data` text,
  `answer` longtext,
  `error` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Лог отправки push уведомлений при покупке';

-- --------------------------------------------------------

--
-- Структура таблицы `reference`
--

CREATE TABLE `reference` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `table` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` text COLLATE utf8_unicode_ci NOT NULL,
  `id_column` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `columns` json NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `ReferenceList`
--

CREATE TABLE `ReferenceList` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `ReferenceName` varchar(32) NOT NULL COMMENT 'Наименование справочника',
  `RecordId` int(11) NOT NULL COMMENT 'Уникальный идентификатор записи в рамках справочника',
  `RecordName` varchar(64) NOT NULL COMMENT 'Наименование элемента справочника',
  `RecordDescription` varchar(64) NOT NULL COMMENT 'Расширенное описание элемента'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `region`
--

CREATE TABLE `region` (
  `region_id` int(10) UNSIGNED NOT NULL,
  `country_id` int(10) UNSIGNED NOT NULL COMMENT 'ID страны',
  `title` varchar(255) NOT NULL COMMENT 'Название',
  `title_short` varchar(255) DEFAULT NULL,
  `title_lat` varchar(255) NOT NULL COMMENT 'Название латиницей'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Таблица регионов';

-- --------------------------------------------------------

--
-- Структура таблицы `region_timezone`
--

CREATE TABLE `region_timezone` (
  `country_id` int(11) NOT NULL COMMENT 'ID страны',
  `region_title` varchar(255) NOT NULL COMMENT 'Название региона',
  `timezone` varchar(255) NOT NULL COMMENT 'Часовой пояс'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Часовой пояс региона';

-- --------------------------------------------------------

--
-- Структура таблицы `request_change`
--

CREATE TABLE `request_change` (
  `request_id` int(11) UNSIGNED NOT NULL COMMENT 'ID заявки',
  `user_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID пользователя',
  `partner_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID партнера',
  `type_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'Тип запроса. 1 - Изменение данных организации',
  `content` mediumtext NOT NULL COMMENT 'Контент. JSON',
  `date` datetime NOT NULL COMMENT 'Дата'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Заявки на изменение каких-либо данных';

-- --------------------------------------------------------

--
-- Структура таблицы `request_partner`
--

CREATE TABLE `request_partner` (
  `request_partner_id` int(11) NOT NULL COMMENT 'ID заявки',
  `phone` varchar(50) DEFAULT NULL COMMENT 'Телефон',
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата обращения'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Заявки на партера';

-- --------------------------------------------------------

--
-- Структура таблицы `ride_search_notification`
--

CREATE TABLE `ride_search_notification` (
  `search_notification_id` int(10) UNSIGNED NOT NULL COMMENT 'ID уведомления',
  `city_id_start` int(11) NOT NULL COMMENT 'ID города отправления',
  `city_id_end` int(11) NOT NULL COMMENT 'ID города прибытия',
  `date` datetime DEFAULT NULL COMMENT 'Дата и время запроса',
  `email` varchar(100) NOT NULL COMMENT 'Почта'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `ride_segment`
--

CREATE TABLE `ride_segment` (
  `ride_segment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'ID отрезка',
  `outer_id` varchar(255) DEFAULT NULL COMMENT 'Внеший ID отрезка из стороннего API',
  `station_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции отправления',
  `station_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции прибытия',
  `city_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города отправления',
  `city_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города прибытия',
  `outer_id_from` varchar(255) DEFAULT NULL COMMENT 'Внешний ID(ОТ) по которому происходит поиск',
  `outer_id_to` varchar(255) DEFAULT NULL COMMENT 'Внешний ID(ДО) по которому происходит поиск',
  `price` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Цена',
  `currency_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID валюты',
  `date_start` date DEFAULT NULL COMMENT 'Дата отправления',
  `time_start` time DEFAULT NULL COMMENT 'Время отправления',
  `date_end` date DEFAULT NULL COMMENT 'Дата прибытия',
  `time_end` time DEFAULT NULL COMMENT 'Время прибытия',
  `place_free_cnt` smallint(5) UNSIGNED DEFAULT NULL COMMENT 'Кол-во свободных мест',
  `distance` int(10) UNSIGNED DEFAULT NULL COMMENT 'Расстояние сегмента',
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID Партнера',
  `xref_id_station_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID xref внешней станции отправления',
  `xref_id_station_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID xref внешней станции прибытия',
  `stops_hash` varchar(40) DEFAULT NULL,
  `ride_hash` varchar(40) DEFAULT NULL,
  `guid` varchar(32) DEFAULT NULL COMMENT 'GUID сегмента'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Отрезки рейса';

-- --------------------------------------------------------

--
-- Структура таблицы `ride_segment_outer_detail`
--

CREATE TABLE `ride_segment_outer_detail` (
  `ride_segment_id` bigint(20) NOT NULL COMMENT 'ID ride_segment',
  `route_outer_id` int(11) DEFAULT NULL COMMENT 'Номер маршрута из внешней ГДС',
  `ride_outer_id` int(11) DEFAULT NULL COMMENT 'Номер рейса из внешней ГДС',
  `ride_segment_outer_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Внешний идентификатор сегмента рейса',
  `vendor_id` int(11) DEFAULT NULL COMMENT 'Идентификатор внешней гдс',
  `outer_detail` text COMMENT 'Внешние данные',
  `carrier_title` varchar(256) DEFAULT NULL COMMENT 'Информация о перевозчике',
  `bus_title` varchar(256) DEFAULT NULL COMMENT 'Информация о автобусе',
  `tariff_list` text COMMENT 'Список тарифов, услуг и товаров на рейсе в формате JSON',
  `jData` json DEFAULT NULL COMMENT 'Расширенная информация по рейсу JSON'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `ride_segment_tmp`
--

CREATE TABLE `ride_segment_tmp` (
  `ride_segment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'ID отрезка',
  `outer_id` varchar(255) DEFAULT NULL COMMENT 'Внеший ID отрезка из стороннего API',
  `ride_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID Рейса',
  `route_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID маршрута',
  `station_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции отправления',
  `station_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции прибытия',
  `city_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города отправления',
  `city_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города прибытия',
  `outer_id_from` varchar(255) DEFAULT NULL COMMENT 'Внешний ID(ОТ) по которому происходит поиск',
  `outer_id_to` varchar(255) DEFAULT NULL COMMENT 'Внешний ID(ДО) по которому происходит поиск',
  `price` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Цена',
  `currency_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID валюты',
  `date_start` date DEFAULT NULL COMMENT 'Дата отправления',
  `time_start` time DEFAULT NULL COMMENT 'Время отправления',
  `date_end` date DEFAULT NULL COMMENT 'Дата прибытия',
  `time_end` time DEFAULT NULL COMMENT 'Время прибытия',
  `place_free_cnt` smallint(5) UNSIGNED DEFAULT NULL COMMENT 'Кол-во свободных мест',
  `distance` int(10) UNSIGNED DEFAULT NULL COMMENT 'Расстояние сегмента',
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID Партнера',
  `route_station_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID начальной точки маршрута',
  `route_station_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID конечной точки маршрута'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Отрезки рейса';

-- --------------------------------------------------------

--
-- Структура таблицы `ride_segment_way_points`
--

CREATE TABLE `ride_segment_way_points` (
  `ride_segment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'ID ride_segment',
  `outer_id` varchar(255) DEFAULT NULL COMMENT 'Внешний идентификатор, hash',
  `outer_title` varchar(255) NOT NULL,
  `point_cnt` int(10) DEFAULT NULL COMMENT 'Количество остановок',
  `data` text COMMENT 'Список остановок'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Список остановок рейса';

-- --------------------------------------------------------

--
-- Структура таблицы `sap`
--

CREATE TABLE `sap` (
  `sap_id` int(11) NOT NULL COMMENT 'ИД записи',
  `sap_type` tinyint(1) DEFAULT '2' COMMENT 'Тип 0 - неопределен, 1 - Тариф на проезд/багаж, 2 - товар, 3 - услуга',
  `title` varchar(255) NOT NULL COMMENT 'Наименование',
  `category_id` int(11) DEFAULT '0' COMMENT 'Идентификатор категории учета(бух)-размер налога',
  `is_local` tinyint(1) DEFAULT '0' COMMENT 'Признак, что cервис продается только локально',
  `is_variable` tinyint(1) DEFAULT '1' COMMENT 'Признак, что стоимость может менятся в зависимомти от интервала рейса',
  `currency_id` int(11) DEFAULT '0' COMMENT 'ID валюты',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT 'Базовая стоимость',
  `info` text COMMENT 'Дополнительная информация и продукте',
  `image_url` text COMMENT 'Ссылка на картинку изображающую товар'
) ENGINE=InnoDB DEFAULT CHARSET=utf32 COMMENT='Локальный учет наименований товаров и услуг(services&product)';

-- --------------------------------------------------------

--
-- Структура таблицы `search`
--

CREATE TABLE `search` (
  `search_id` int(11) UNSIGNED NOT NULL,
  `job_count_total` int(11) UNSIGNED DEFAULT '0' COMMENT 'Общее кол-во job из таблицы search_job',
  `job_count_done` int(11) UNSIGNED DEFAULT '0' COMMENT 'Обработанное кол-во job',
  `city_id_start` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID города отправления',
  `city_id_end` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID города прибытия',
  `date_start` date DEFAULT NULL COMMENT 'Дата отправления рейса',
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата и время создания search',
  `agent_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID агента который сделал запрос на поиск',
  `ride_segment_found_cnt` int(11) UNSIGNED DEFAULT '0' COMMENT 'Кол-во найденных ride_segment'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Realtime поиск';

-- --------------------------------------------------------

--
-- Структура таблицы `search_job`
--

CREATE TABLE `search_job` (
  `job_id` int(10) UNSIGNED NOT NULL,
  `search_id` int(10) UNSIGNED DEFAULT NULL,
  `status` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '0 - Новый, 1 - Готово, 2 - В обратотке, 3 - Отменено',
  `city_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города отправления',
  `city_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города прибытия',
  `city_id_start_outer` varchar(255) DEFAULT NULL COMMENT 'Внеший ID города отправления',
  `city_id_end_outer` varchar(255) DEFAULT NULL COMMENT 'Внешний ID города прибытия',
  `date_start` date DEFAULT NULL COMMENT 'Дата отправления',
  `datetime_create` datetime DEFAULT NULL COMMENT 'Дата создания JOB',
  `datetime_done` datetime DEFAULT NULL COMMENT 'Дата завершения JOB',
  `vendor_id` int(11) DEFAULT NULL COMMENT 'ID вендора для которого создан JOB',
  `daemon_id` smallint(5) DEFAULT NULL,
  `ride_segment_found_cnt` int(11) UNSIGNED DEFAULT NULL COMMENT 'Кол-во найденных ride_segment на основе JOB',
  `processing_time` decimal(12,9) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Job''ы для realtime поиска';

-- --------------------------------------------------------

--
-- Структура таблицы `search_result`
--

CREATE TABLE `search_result` (
  `search_id` int(11) DEFAULT NULL,
  `ride_segment_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `search_stat`
--

CREATE TABLE `search_stat` (
  `event_date` date NOT NULL,
  `agent_id` int(11) NOT NULL,
  `city_id_start` int(11) NOT NULL,
  `city_id_end` int(11) NOT NULL,
  `station_id_start` int(11) NOT NULL,
  `station_id_end` int(11) NOT NULL,
  `ride_date` date NOT NULL COMMENT 'дата на которую искали',
  `search_total_cnt` int(11) NOT NULL,
  `search_success_cnt` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `seo_direction`
--

CREATE TABLE `seo_direction` (
  `direction_id` int(10) UNSIGNED NOT NULL COMMENT 'ID направления',
  `city_id` int(10) UNSIGNED NOT NULL COMMENT 'ID города',
  `title_1` varchar(255) DEFAULT NULL COMMENT 'Название направления (им. падеж)',
  `title_2` varchar(255) DEFAULT NULL COMMENT 'Название направления ',
  `title_lat` varchar(255) DEFAULT NULL COMMENT 'Название направления латиницей',
  `position` tinyint(3) UNSIGNED NOT NULL COMMENT 'Позиция'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Таблица направлений';

-- --------------------------------------------------------

--
-- Структура таблицы `seo_direction_item`
--

CREATE TABLE `seo_direction_item` (
  `direction_id` int(10) UNSIGNED NOT NULL COMMENT 'ID направления',
  `item_id` int(10) UNSIGNED NOT NULL COMMENT 'ID объекта (Город/Станция)',
  `item_type` tinyint(1) UNSIGNED NOT NULL COMMENT 'Тип объекта (0 - город, 1 - станция)',
  `position` tinyint(3) UNSIGNED NOT NULL COMMENT 'Позиция'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Объекты (город/станция) которые входят в направление';

-- --------------------------------------------------------

--
-- Структура таблицы `seo_ride`
--

CREATE TABLE `seo_ride` (
  `ride_hash` varchar(40) NOT NULL,
  `stops_hash` varchar(40) NOT NULL,
  `price_hash` varchar(40) DEFAULT NULL,
  `route_hash` varchar(40) DEFAULT NULL,
  `partner_id` int(11) NOT NULL,
  `date_start` date NOT NULL,
  `time_start` time NOT NULL,
  `datetime_start` datetime DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `time_end` time DEFAULT NULL,
  `datetime_end` datetime DEFAULT NULL,
  `timeshift` int(11) DEFAULT NULL,
  `carrier_title` varchar(255) DEFAULT NULL,
  `bus_title` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `seo_stops`
--

CREATE TABLE `seo_stops` (
  `stops_hash` varchar(40) NOT NULL,
  `city_id_start` int(11) NOT NULL,
  `city_id_end` int(11) NOT NULL,
  `station_id_start` int(11) NOT NULL,
  `station_id_end` int(11) NOT NULL,
  `point_cnt` int(11) NOT NULL,
  `timeshift` int(11) DEFAULT NULL,
  `city_list` text COMMENT 'упорядоченная нитка городов'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `sequence`
--

CREATE TABLE `sequence` (
  `sequence_id` varchar(40) NOT NULL COMMENT 'ключ',
  `value` bigint(20) NOT NULL COMMENT 'значение'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='таблица для хранения числовых последовательностей';

-- --------------------------------------------------------

--
-- Структура таблицы `services`
--

CREATE TABLE `services` (
  `service_id` int(11) NOT NULL COMMENT 'Уникальный идентификатор услуги',
  `partner_id` int(11) NOT NULL COMMENT 'ID партнера предоставляющего услугу',
  `outer_service_id` int(11) NOT NULL COMMENT 'Уникальный идентификатор услуги в учетной системе партнера',
  `service_type` tinyint(1) DEFAULT '2' COMMENT 'Тип 0 - неопределен, 4 - товар, 5 - услуга',
  `title` varchar(255) NOT NULL COMMENT 'Наименование',
  `category_id` int(11) DEFAULT '0' COMMENT 'Идентификатор категории учета(бух)',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT 'Базовая стоимость',
  `is_active` tinyint(1) DEFAULT '0' COMMENT 'Товар/услуга доступна к продаже',
  `info` text COMMENT 'Дополнительная информация и продукте',
  `refund_rule` text COMMENT 'Правила возврата услуги в формате JSON'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Учет товаров и услуг продающихся только на вокзале(ах) партнера';

-- --------------------------------------------------------

--
-- Структура таблицы `site_config`
--

CREATE TABLE `site_config` (
  `siteConfigId` tinyint(3) UNSIGNED NOT NULL DEFAULT '1' COMMENT 'ИД записи',
  `loginLenMin` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Минимальная длина Логина',
  `loginLenMax` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Максимальная длина Логина',
  `loginTriesMax` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Максимальное число попыток залогинеться (до блокировки)',
  `loginTriesLock` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Время блокирования входа при достижении макс.числа попыток залогинется (сек)',
  `pswLenMin` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Минимальная длина пароля',
  `pswLenMax` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Максимальная длина пароля',
  `rememberMeTime` int(11) DEFAULT NULL COMMENT 'Время жизни для "запомнить меня"/автовход (сек)',
  `pswLenMinForCashRegisterCfg` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Минимальная длина пароля для конфигурации кассы',
  `pswLenMaxForCashRegisterCfg` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Максимальная длина пароля для конфигурации кассы',
  `cashRegisterCfgAlg` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Номер алгоритма генерации конфигурации кассы',
  `checkApiRequestHashOff` tinyint(1) DEFAULT NULL COMMENT 'Флаг: Откдючить проверку хеша запроса к API',
  `redirectForMain` varchar(255) DEFAULT NULL COMMENT 'Путь к странице для автоперенаправления с главной',
  `supportEmail` varchar(255) DEFAULT NULL COMMENT 'Email тех.поддержки',
  `supportEmailNoReply` varchar(255) DEFAULT NULL COMMENT 'Email тех.поддержки (робот)',
  `supportTel` varchar(255) DEFAULT NULL COMMENT 'Телефон(ы) тех.поддержки',
  `sunIsLast` tinyint(1) DEFAULT NULL COMMENT 'Флаг: воскресенье последний день недели',
  `distanceRound` tinyint(4) DEFAULT NULL COMMENT 'Точность расстояния',
  `prefillingTicket` tinyint(1) DEFAULT NULL COMMENT 'Предзаполнение парметров билета',
  `boardingStatementForm` tinyint(4) DEFAULT NULL COMMENT 'Номер бланка посадочной ведомости',
  `noPlaceAge` tinyint(4) DEFAULT NULL COMMENT 'Возраст до которого можно выбирать пассажира без места',
  `buyTicketCntMax` tinyint(4) DEFAULT NULL COMMENT 'Максимальное количество билетов, которые может купить',
  `jData` text COMMENT 'Прочие, нестандартные настройки',
  `jLog` text COMMENT 'Настройки логгирования',
  `debug` smallint(5) UNSIGNED DEFAULT NULL COMMENT 'Флаги отладки',
  `user_id` int(11) DEFAULT NULL COMMENT 'Кто добавил/изменил',
  `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Время добавления записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Основные настройки сайта';

-- --------------------------------------------------------

--
-- Структура таблицы `site_google_analytics`
--

CREATE TABLE `site_google_analytics` (
  `operation_id` int(10) UNSIGNED NOT NULL,
  `tid` varchar(255) DEFAULT NULL,
  `cid` varchar(255) DEFAULT NULL,
  `version` varchar(255) DEFAULT NULL,
  `domain_depth` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `site_sync`
--

CREATE TABLE `site_sync` (
  `siteSyncID` int(11) NOT NULL COMMENT 'ИД записи',
  `partnerID` int(11) NOT NULL COMMENT 'ИД партнёра',
  `departureCityID` int(11) DEFAULT NULL COMMENT 'Город отправления',
  `arrivalCityID` int(11) DEFAULT NULL COMMENT 'Город назначения',
  `departureStationID` int(11) DEFAULT NULL COMMENT 'Станция отправления',
  `arrivalStationID` int(11) DEFAULT NULL COMMENT 'Станция назначения',
  `days` smallint(6) DEFAULT '1' COMMENT 'Количество дней для кеширование с даты "с..."',
  `duration` time DEFAULT NULL COMMENT 'Продолжительность кеширования (часов, минут)',
  `logOn` tinyint(1) DEFAULT NULL COMMENT 'Вкл логирования операции синхронизации',
  `syncOff` tinyint(1) DEFAULT '0' COMMENT 'Выкл синхронизации',
  `jOuterRequest` json DEFAULT NULL COMMENT 'Параметры для запроса к внешней ГДС',
  `user_id` int(11) DEFAULT NULL COMMENT 'ИД юзера, кто добавил',
  `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Время добавления записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Параметры кеширования/синхронизации внешних данных';

-- --------------------------------------------------------

--
-- Структура таблицы `site_sync_dates`
--

CREATE TABLE `site_sync_dates` (
  `siteSyncID` int(11) NOT NULL COMMENT 'ИД настройки синхронизации',
  `fromDate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата "с..."',
  `cacheTime` timestamp NULL DEFAULT NULL COMMENT 'Время последнего кеширования',
  `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Время добавления записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Даты синхронизации';

-- --------------------------------------------------------

--
-- Структура таблицы `sopp_request`
--

CREATE TABLE `sopp_request` (
  `id` int(11) NOT NULL COMMENT 'Уникальный идентификатор записи',
  `requestDateTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время запроса',
  `methodId` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'Идентифиикационный номер метода',
  `getParam` text COLLATE utf8_unicode_ci COMMENT 'GET параметры запроса',
  `postParam` text COLLATE utf8_unicode_ci COMMENT 'POST параметры запроса',
  `requestTime` decimal(10,3) DEFAULT '0.000' COMMENT 'Время выполнения запроса',
  `answer` text COLLATE utf8_unicode_ci COMMENT 'Ответ сервера'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `station`
--

CREATE TABLE `station` (
  `station_id` int(11) NOT NULL COMMENT 'ID станции',
  `city_id` int(11) NOT NULL COMMENT 'ID города',
  `title` varchar(255) NOT NULL COMMENT 'Название в именительном падеже',
  `description` text COMMENT 'Описание станции',
  `title_genitive` varchar(255) DEFAULT NULL COMMENT 'Название в родительном падеже',
  `title_accusative` varchar(255) DEFAULT NULL COMMENT 'Название в винительном падеже',
  `title_prepositional` varchar(255) DEFAULT NULL COMMENT 'Название в предложном падеже',
  `title_lat` varchar(255) DEFAULT NULL COMMENT 'Название литиницей',
  `title_genitive_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в родительном падеже',
  `title_accusative_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в винительном падеже',
  `title_prepositional_lat` varchar(255) DEFAULT NULL COMMENT 'Название латиницей в предложном падеже',
  `lng` varchar(255) DEFAULT NULL COMMENT 'Долгота',
  `lat` varchar(255) DEFAULT NULL COMMENT 'Широта',
  `address` varchar(255) NOT NULL COMMENT 'Адрес станции',
  `phone` varchar(255) DEFAULT NULL COMMENT 'Телефон станции',
  `site` varchar(255) DEFAULT NULL COMMENT 'Сайт станции',
  `okato` varchar(20) DEFAULT NULL COMMENT 'код окато',
  `is_verified` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Признак верефикации города. 0 - Не верифицирован, 1 - Верифицирован',
  `code` varchar(255) DEFAULT NULL COMMENT 'Некий код станции',
  `hash` varchar(32) DEFAULT NULL COMMENT 'Некий хеш станции',
  `region_id` int(11) DEFAULT NULL COMMENT 'ИД региона'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Станция';

-- --------------------------------------------------------

--
-- Структура таблицы `station_merge_log`
--

CREATE TABLE `station_merge_log` (
  `log_id` int(10) UNSIGNED NOT NULL COMMENT 'ID лога',
  `station_id_out` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции ИЗ',
  `station_id_in` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции В',
  `station_title_out` varchar(255) DEFAULT NULL COMMENT 'Название станции ИЗ',
  `station_title_in` varchar(255) DEFAULT NULL COMMENT 'Название станции В',
  `datetime_merge` datetime DEFAULT NULL COMMENT 'Дата мержа'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `telegram_hook`
--

CREATE TABLE `telegram_hook` (
  `telegram_hook_id` int(11) NOT NULL,
  `message` text,
  `request_datetime` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalActionLog`
--

CREATE TABLE `TerminalActionLog` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `ActionType` enum('unknown','insert','update','delete') NOT NULL DEFAULT 'unknown' COMMENT 'Тип действия',
  `SQLUserId` varchar(64) NOT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `TableName` varchar(64) NOT NULL COMMENT 'Название таблицы',
  `TableRecordId` bigint(20) UNSIGNED NOT NULL COMMENT 'Идентификатор редактируемой записи в таблице',
  `TableRecordVersion` int(10) UNSIGNED DEFAULT '0' COMMENT 'Версия записи(вычислимое)',
  `UpdateData` json DEFAULT NULL COMMENT 'Измененные поля(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи(вычислимое)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalGroup`
--

CREATE TABLE `TerminalGroup` (
  `Id` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор группы',
  `Name` varchar(64) NOT NULL COMMENT 'Название группы терминалов',
  `Description` varchar(254) DEFAULT NULL COMMENT 'Расширенное описание группы терминалов',
  `VendorName` varchar(64) DEFAULT NULL COMMENT 'Производитель терминала',
  `ModelName` varchar(64) DEFAULT NULL COMMENT 'Название терминала',
  `BaseOSName` varchar(64) DEFAULT NULL COMMENT 'Базовая ОС',
  `Type` tinyint(3) UNSIGNED DEFAULT '1' COMMENT 'Тип',
  `TemplateConfig` json NOT NULL COMMENT 'Темплайт конфигурации терминала',
  `IsDeleted` tinyint(1) DEFAULT '0' COMMENT 'Признак, что группа не используется',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи(вычислимое)',
  `Version` int(10) UNSIGNED DEFAULT '1' COMMENT 'Версия записи(вычислимое в триггере AU)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalList`
--

CREATE TABLE `TerminalList` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `TerminalId` bigint(20) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор терминала',
  `VendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор NSI(VendorId) откуда загружены настройки терминала',
  `TerminalVendorId` int(10) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор терминала в рамках NSI',
  `TerminalSerialNumber` varchar(32) NOT NULL COMMENT 'Серийный номер терминала',
  `FNSerialNumber` varchar(32) NOT NULL COMMENT 'Серийный номер установленного фискального накопителя',
  `TerminalDescription` varchar(254) DEFAULT NULL COMMENT 'Расширенное описание терминала.',
  `TerminalGroupId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор группы терминалов',
  `VendorOrganizationId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор организации в рамках НСИ',
  `IsDeleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак, что терминал не используется',
  `TerminalHash` varchar(64) NOT NULL COMMENT 'Уникальный хэш терминала',
  `TerminalSign` varchar(64) NOT NULL COMMENT 'Симметричный ключ шифрования',
  `TerminalConfig` json NOT NULL COMMENT 'Настройки передаваемые в терминал',
  `SendFlag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 - нужны логи',
  `EmailList` varchar(128) DEFAULT NULL COMMENT 'список email для логов',
  `TerminalVersion` varchar(64) DEFAULT NULL COMMENT 'версия ПО на терминале',
  `EditUserId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи',
  `Version` int(10) UNSIGNED DEFAULT NULL COMMENT 'Версия записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalLog`
--

CREATE TABLE `TerminalLog` (
  `TerminalLogId` int(11) NOT NULL COMMENT 'пк',
  `TerminalId` int(11) NOT NULL COMMENT 'ид терминала',
  `LogDump` longtext COMMENT 'лог',
  `LogDateTime` datetime DEFAULT NULL COMMENT 'дата последнего лога'
) ENGINE=InnoDB DEFAULT CHARSET=utf32;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalOrganizationList`
--

CREATE TABLE `TerminalOrganizationList` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `VendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор NSI(VendorId)',
  `VendorOrganizationId` int(10) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор организации в рамках НСИ',
  `Name` varchar(128) NOT NULL COMMENT 'Название организации',
  `NameShort` varchar(128) DEFAULT NULL COMMENT 'Сокращенное название организации',
  `Address` varchar(255) NOT NULL COMMENT 'Адрес регистрации',
  `Email` varchar(64) DEFAULT NULL COMMENT 'Электронный адрес',
  `INN` varchar(32) NOT NULL COMMENT 'ИНН Организации',
  `FNTaxCode` tinyint(3) UNSIGNED NOT NULL DEFAULT '1' COMMENT 'Код системы налогообложения',
  `FNTaxCodeDefault` tinyint(3) UNSIGNED NOT NULL DEFAULT '1' COMMENT 'Код системы налогообложения используемый в терминале поумолчанию',
  `FNOperatingMode` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Режим работы фискального накопителя',
  `FNIsGambling` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Предприятие проводит азартные игры',
  `FNIsLottery` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Предприятие проводит лотереи',
  `FNPaymentAgent` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Признак платежного агента',
  `OFDServer` varchar(128) DEFAULT NULL COMMENT 'Адрес сервера ОФД',
  `OFDServerPort` varchar(5) DEFAULT NULL COMMENT 'Порт сервера ОФД',
  `OFDName` varchar(255) DEFAULT NULL COMMENT 'Название ОФД',
  `OFDINN` varchar(32) DEFAULT NULL COMMENT 'ИНН ОФД',
  `OFDReceiptCheckURI` varchar(128) DEFAULT NULL COMMENT 'Адрес сайта для проверки фискального признака',
  `FnsServerAddress` varchar(128) DEFAULT 'nalog.ru' COMMENT 'Адрес сайта налогового органа',
  `SupportName` varchar(64) DEFAULT 'ООО Под контролем' COMMENT 'Наименование сопровождающей организации',
  `SupportPhone` varchar(32) DEFAULT '+7 (499) 677-17-03' COMMENT 'Контактный телефон сопровождающей организации',
  `SupportEmail` varchar(64) DEFAULT '<EMAIL>' COMMENT 'Электронный адрес сопровождающей организации',
  `IsDeleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак, что организация не используется',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи',
  `Version` int(10) UNSIGNED DEFAULT NULL COMMENT 'Версия записи'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalRawTransaction`
--

CREATE TABLE `TerminalRawTransaction` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `TerminalId` bigint(20) NOT NULL COMMENT 'Идентификатор терминала от которого получены транзакции',
  `AgentId` bigint(20) NOT NULL COMMENT 'Идентификатор пользователя терминала от которого получены транзакции',
  `LoadDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время загрузки пакета транзакций',
  `DataHash` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Контрольная сумма полученного пакета данных',
  `IsParsed` tinyint(1) DEFAULT '0' COMMENT 'Признак что транзакции обработаны',
  `ParsedDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время обработки пакета транзакций',
  `ParsedResult` json DEFAULT NULL COMMENT 'Результат обработки транзакций',
  `RawData` longtext COLLATE utf8_unicode_ci COMMENT 'Пакет сырых транзакций'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalRoleFunction`
--

CREATE TABLE `TerminalRoleFunction` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `VendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор NSI(VendorId) откуда загружены настройки терминала',
  `RoleId` int(11) DEFAULT '0' COMMENT 'Идентификатор роли пользователя',
  `FunctionId` int(11) DEFAULT '0' COMMENT 'Идентификатор роли пользователя',
  `IsEnable` tinyint(1) DEFAULT '0' COMMENT 'Признак, что пользователь удален(Заблокирован)',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи(вычислимое)',
  `Version` int(10) UNSIGNED DEFAULT '1' COMMENT 'Версия записи(вычислимое)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalTransaction`
--

CREATE TABLE `TerminalTransaction` (
  `TransactionId` bigint(20) NOT NULL COMMENT 'ПК транзакции',
  `TerminalId` bigint(20) NOT NULL COMMENT 'ИД терминала',
  `VendorId` int(11) NOT NULL COMMENT 'ИД партнера',
  `ParseDateTime` datetime NOT NULL COMMENT 'дата разбора транзакции',
  `NSIReceivedDateTime` datetime DEFAULT NULL COMMENT 'дата передачи в НСИ',
  `transactionType` varchar(2) NOT NULL COMMENT 'Тип транзакции',
  `transactionVersion` int(11) NOT NULL COMMENT 'Версия транзакции',
  `transactionTime` datetime NOT NULL COMMENT 'Дата и время формирования транзакции',
  `terminalSerialNumber` varchar(64) DEFAULT NULL COMMENT 'Серийный номер терминала',
  `terminalFNSerialNumber` varchar(32) DEFAULT NULL COMMENT 'Номер фискального накопителя',
  `terminalFNRegistrationNumber` varchar(32) DEFAULT NULL COMMENT 'Номер ККТ производителя',
  `terminalKKTSerialNumber` varchar(32) DEFAULT NULL COMMENT 'Регистрационный номер ККТ',
  `lat` varchar(16) DEFAULT NULL COMMENT 'Широта',
  `lon` varchar(16) DEFAULT NULL COMMENT 'Долгота',
  `terminalUserId` int(11) NOT NULL COMMENT 'Идентификационный номер пользователя терминала',
  `terminalUserPersonnelNumber` varchar(64) NOT NULL COMMENT 'Табельный номер пользователя терминала',
  `terminalUserFIO` varchar(128) NOT NULL COMMENT 'ФИО пользователя терминала',
  `terminalUserRoleId` int(11) NOT NULL COMMENT 'Роль пользователя терминала',
  `operatorCardUID` varchar(14) DEFAULT NULL COMMENT 'UID дежурной карты',
  `operatorCardPAN` varchar(16) DEFAULT NULL COMMENT 'PAN дежурной карты',
  `terminalOwnerName` varchar(128) NOT NULL COMMENT 'Наименование предприятия',
  `terminalOwnerINN` varchar(32) NOT NULL COMMENT 'ИНН предприятия',
  `terminalOrganizationId` bigint(20) DEFAULT NULL COMMENT 'Идентификатор предприятия',
  `terminalODFINN` varchar(32) DEFAULT NULL COMMENT 'ИНН оператора ФД',
  `transportTypeId` int(11) DEFAULT NULL COMMENT 'Тип транспорта',
  `routeTypeId` int(11) DEFAULT NULL COMMENT 'Тип маршрута',
  `isCycled` tinyint(1) DEFAULT NULL COMMENT 'Маршрут является кольцевым',
  `isBookingWithoutTicketData` tinyint(1) DEFAULT NULL COMMENT 'На маршруте разрешено бронирование без паспортных данных',
  `isStraight` tinyint(1) DEFAULT NULL COMMENT 'Признак, что рейс прямой',
  `shippingTypeId` int(11) DEFAULT NULL COMMENT 'Тип перевозки',
  `busGarageNumber` varchar(32) DEFAULT NULL COMMENT 'Гаражный номер',
  `busNumber` varchar(32) DEFAULT NULL COMMENT 'Государственный регистрационный знак',
  `routeName` varchar(150) DEFAULT NULL COMMENT 'Название маршрута',
  `routeNumber` varchar(255) DEFAULT NULL COMMENT 'Номер маршрута',
  `routeTariffingType` varchar(1) DEFAULT NULL COMMENT 'Тип тарификации на маршруте',
  `isRegulatedTarif` tinyint(1) DEFAULT NULL COMMENT 'Регулируемый тариф?',
  `rideId` int(11) DEFAULT NULL COMMENT 'Идентификатор рейса',
  `rideSegmentId` bigint(20) DEFAULT NULL COMMENT 'Идентификатор сегмента рейса',
  `ticketSeries` varchar(32) DEFAULT NULL COMMENT 'Серия билета',
  `ticketNumber` varchar(32) DEFAULT NULL COMMENT 'Номер билета',
  `rideDateTime` datetime DEFAULT NULL COMMENT 'Дата и время проезда',
  `workDayNumber` int(11) UNSIGNED NOT NULL COMMENT 'Номер смены фискального накопителя',
  `fiscalDocSignature` varchar(32) DEFAULT NULL COMMENT 'Фискальный признак документа',
  `fiscalDocNum` int(32) UNSIGNED DEFAULT NULL COMMENT 'Номер фискального документа в смену',
  `enterStationId` int(11) DEFAULT NULL COMMENT 'Id остановки входа',
  `exitStationId` int(11) DEFAULT NULL COMMENT 'Id остановки выхода',
  `enterStationName` varchar(255) DEFAULT NULL COMMENT 'Название остановки Входа',
  `exitStationName` varchar(255) DEFAULT NULL COMMENT 'Название остановки Выхода',
  `operationId` int(11) DEFAULT NULL COMMENT 'Идентификатор операции',
  `ticketId` int(11) DEFAULT NULL COMMENT 'Идентификатор билета',
  `finalTicketPrice` decimal(10,2) DEFAULT NULL COMMENT 'Стоимость проезда',
  `baseTicketPrice` decimal(10,2) DEFAULT NULL COMMENT 'Базовая стоимость проезда (базовый тариф)',
  `discountId` int(11) DEFAULT NULL COMMENT 'Идентификатор примененного правила обслуживания (скидки)',
  `sumForDiscount` decimal(10,2) DEFAULT NULL COMMENT 'Сумма скидки',
  `sellTariffType` int(11) DEFAULT NULL COMMENT 'Тип тарифа',
  `sellTariffId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор тарифа',
  `paymentType` int(11) DEFAULT NULL COMMENT 'Тип средства платежа',
  `personalPassengerData` json DEFAULT NULL COMMENT 'данные пассажира',
  `transportCardTransactionData` json DEFAULT NULL COMMENT 'Данные транспортной транзакции',
  `sellTransportCardCategory` varchar(6) DEFAULT NULL COMMENT 'Категория транспортной карты',
  `sellTransportCardUID` varchar(14) DEFAULT NULL COMMENT 'UID транспортной карты',
  `sellTransportCardPAN` varchar(16) DEFAULT NULL COMMENT 'PAN транспортной карты',
  `sellTransportCardNumber` varchar(64) DEFAULT NULL COMMENT 'Номер транспортной карты',
  `hash` varchar(64) NOT NULL COMMENT 'ХЭШ записи подписанный секретным ключом пользователя терминала',
  `IsTariffed` tinyint(4) DEFAULT '0' COMMENT 'Признак, что транзакция протарифицирована с учетом абонементов. 0 - не тарифицировалась, 1 - протафифицирована без ошибок, 2 - сошибками',
  `TarifficationErrors` json DEFAULT NULL COMMENT 'Ошибки тарификации',
  `TarifficationDateTime` timestamp NULL DEFAULT NULL COMMENT 'Дата и время тарификации по абонементам',
  `TarifficationTime` decimal(10,5) DEFAULT NULL COMMENT 'Время затраченное на тарификацию транзакции в секундах',
  `AbonementId` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор абонемента по которому была протарифицирована транзакция',
  `PANHash` varchar(64) DEFAULT NULL COMMENT 'хэш банковской карты',
  `IsWriteOffRequired` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'Признак, что требуется списание средств с карты'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalUser`
--

CREATE TABLE `TerminalUser` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `TerminalUserId` varchar(20) NOT NULL COMMENT 'Уникальный идентификатор пользователя в системе(вычислимое)',
  `VendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор NSI(VendorId) откуда загружены настройки терминала',
  `UserVendorId` int(10) UNSIGNED NOT NULL COMMENT 'Идентификатор пользователя в рамках NSI',
  `UserRoleId` int(11) DEFAULT '0' COMMENT 'Идентификатор роли пользователя',
  `UserFIO` varchar(128) NOT NULL COMMENT 'ФИО пользователя сокращенное',
  `UserFIOShort` varchar(64) DEFAULT NULL COMMENT 'ФИО пользователя',
  `UserPersonnelNumber` varchar(64) NOT NULL COMMENT 'Табельный номер пользователя',
  `UserPIN` varchar(16) NOT NULL COMMENT 'Пин-код пользователя',
  `VendorOrganizationId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор организации в рамках НСИ',
  `IsDeleted` tinyint(1) DEFAULT '0' COMMENT 'Признак, что пользователь удален(Заблокирован)',
  `UserHash` varchar(64) NOT NULL COMMENT 'ХЭШ идентификатор пользователя (вычислимое)',
  `UserPINHash` varchar(64) NOT NULL COMMENT 'ХЭШ от пин-кода(вычислимое)',
  `UserSign` varchar(64) NOT NULL COMMENT 'Ключ для шифрования конфигурации пользователя при передаче(вычислимое)',
  `APISecretKey` varchar(64) NOT NULL COMMENT 'Секретный ключ для доступа к АПИ(вычислимое)',
  `EditUserId` varchar(64) DEFAULT NULL COMMENT 'Идентификатор пользователя создавшего(изменившего) запись(вычислимое)',
  `EditDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата и время создания(изменения) записи(вычислимое)',
  `Version` int(10) UNSIGNED DEFAULT '1' COMMENT 'Версия записи(вычислимое)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalUserPhoto`
--

CREATE TABLE `TerminalUserPhoto` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `TerminalUserId` bigint(20) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор пользователя',
  `Photo` longblob COMMENT 'Фотография водителя',
  `PhotoHash` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'ХЭШ фотографии'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `TerminalUserRides`
--

CREATE TABLE `TerminalUserRides` (
  `Id` bigint(20) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `TerminalUserId` bigint(20) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор пользователя',
  `RideSegmentId` bigint(20) UNSIGNED NOT NULL COMMENT 'Уникальный идентификатор сегмента рейса',
  `RideSegmentDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата и время начала рейса',
  `OutgoId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Идентификатор выхода(если есть)',
  `OutgoNumber` int(11) DEFAULT NULL COMMENT 'Номер выхода',
  `OutgoName` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Название выхода'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Структура таблицы `ticket`
--

CREATE TABLE `ticket` (
  `ticket_id` int(10) UNSIGNED NOT NULL COMMENT 'ID билета',
  `outer_ticket_id` varchar(255) DEFAULT NULL COMMENT 'Внешний ID билета полученный из API',
  `outer_detail` text COMMENT 'JSON внешних данных ',
  `partner_id` int(11) NOT NULL COMMENT 'ID Партнера',
  `user_id` int(11) DEFAULT NULL COMMENT 'ID пользователя',
  `operation_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID операции',
  `agent_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID агента',
  `position` tinyint(2) DEFAULT NULL COMMENT 'Место в автобусе',
  `status` tinyint(1) UNSIGNED DEFAULT '3' COMMENT 'Статус. 1 - Успех, 3 - Временная бронь, 4 - Долгосрочная бронь',
  `card_identity_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Документ',
  `series_number` varchar(50) DEFAULT NULL COMMENT 'Серия и номер',
  `name` varchar(255) DEFAULT NULL COMMENT 'ФИО',
  `birthday` date DEFAULT NULL COMMENT 'Дата рождения',
  `gender_id` tinyint(1) DEFAULT NULL COMMENT '0 женщина, 1 мужчина',
  `phone` varchar(30) DEFAULT NULL COMMENT 'Номер телефона',
  `email` varchar(100) DEFAULT NULL COMMENT 'Почта',
  `citizenship_id` int(11) DEFAULT NULL COMMENT 'Гражданство',
  `currency_source_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID валюты которая указана в рейсе',
  `price_source_tariff` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Номинал билета указанная в рейсе',
  `currency_vendor_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID валюты в которой происходит расчет с вендором',
  `price_vendor_tariff` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Номинал билета в валюте вендора',
  `price_vendor_fee` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Агентские от вендора',
  `price_vendor_markup` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сервисный сбор вендора',
  `price_vendor_markup_hidden` decimal(10,2) UNSIGNED DEFAULT '0.00' COMMENT 'Скрытая комиссия вендора',
  `currency_agent_id` int(11) UNSIGNED DEFAULT NULL COMMENT 'ID валюты расчета с агентом',
  `price_agent_tariff` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сумма номинала билета в валюте агента',
  `price_agent_vendor_fee` decimal(10,2) UNSIGNED DEFAULT '0.00' COMMENT 'Агентские от вендора в валюте агента',
  `price_agent_fee` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Агентское вознаграждение реселлеру',
  `price_agent_unitiki_markup` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сервисный сбор Unitiki',
  `price_agent_vendor_markup` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Сервисный сбор вендора',
  `price_agent_vendor_markup_hidden` decimal(10,2) UNSIGNED DEFAULT '0.00' COMMENT 'Скрытая комиссия вендора',
  `price_agent_max` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Максимальная цена продажи билета',
  `price_agent` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Фактическая цена за которую продан билет',
  `price_received` decimal(10,2) UNSIGNED DEFAULT NULL COMMENT 'Деньги полученные от платежной системы',
  `discount_reason` tinyint(1) UNSIGNED DEFAULT '0' COMMENT '0 - Дисконта нет, 1 - Дисконт по инициативе кассира, 2 - Автоматический дисконт по возрасту, 3 - Билет без места',
  `discount_reason_details` int(11) UNSIGNED DEFAULT NULL COMMENT 'Поле зависит от того что выбрано в discount_reason. Для случая автоматического дисконта - там id правила которое было применено',
  `datetime_buy` datetime DEFAULT NULL COMMENT 'Дата покупки билета',
  `datetime_cancel` datetime DEFAULT NULL COMMENT 'Дата отмены покупки',
  `station_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции отправления',
  `station_id_end` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции прибытия',
  `datetime_start` datetime DEFAULT NULL COMMENT 'Дата и время отправления',
  `datetime_end` datetime DEFAULT NULL COMMENT 'Дата и время прибытия',
  `payment_vendor_id` int(10) DEFAULT NULL COMMENT 'ID выплаты вендору',
  `bill_reseller_id` int(10) DEFAULT NULL COMMENT 'ID счета реселлеру',
  `payment_reseller_id` int(10) DEFAULT NULL COMMENT 'ID выплаты реселлеру агентских',
  `bill_vendor_id` int(10) DEFAULT NULL COMMENT 'ID счета вендору на выплату агентских',
  `payment_reseller_reference_id` int(10) DEFAULT NULL COMMENT 'ID выплаты реселлеру-реферальщику',
  `native_blank_number` varchar(255) DEFAULT NULL COMMENT 'Номер и серия билета от поставщика',
  `addition_service_list` text COMMENT 'Список допов купленный совместно с билетом в формате JSON',
  `addition_service_list_refund` text COMMENT 'Список возвращенных допов из ранее купленных в формате JSON',
  `tariff_info` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Билеты';

-- --------------------------------------------------------

--
-- Структура таблицы `ticket_comment`
--

CREATE TABLE `ticket_comment` (
  `ticket_id` int(11) UNSIGNED NOT NULL COMMENT 'ID билета',
  `comment` text COMMENT 'Комментарий к билету'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Комментарии к билету';

-- --------------------------------------------------------

--
-- Структура таблицы `ticket_data`
--

CREATE TABLE `ticket_data` (
  `operation_id` int(10) UNSIGNED NOT NULL COMMENT 'ID операции',
  `is_outer` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0 - Внутренняя, 1 - Внешняя операция',
  `data` text COMMENT 'JSON данных билета'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Данные билетов для renew операций';

-- --------------------------------------------------------

--
-- Структура таблицы `ticket_detail`
--

CREATE TABLE `ticket_detail` (
  `ticket_id` int(11) NOT NULL COMMENT 'ID билета',
  `route_name` varchar(255) DEFAULT NULL COMMENT 'Название маршрута',
  `carrier_title` varchar(255) DEFAULT NULL COMMENT 'Название перевозчика'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Дополнительные данные для билета';

-- --------------------------------------------------------

--
-- Структура таблицы `ticket_refund`
--

CREATE TABLE `ticket_refund` (
  `ticket_id` int(10) UNSIGNED NOT NULL,
  `agent_id` int(10) UNSIGNED NOT NULL COMMENT 'ID агента, сделавший возврат',
  `vendor_id` int(10) UNSIGNED NOT NULL COMMENT 'ID вендора билета',
  `currency_vendor_id` int(10) UNSIGNED DEFAULT NULL,
  `price_vendor_tariff_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_vendor_fee_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_vendor_markup_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_vendor_markup_hidden_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `currency_agent_id` int(10) UNSIGNED DEFAULT NULL,
  `price_agent_tariff_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_vendor_fee_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_fee_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_unitiki_markup_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_vendor_markup_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_vendor_markup_hidden_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `price_agent_refund` decimal(10,2) UNSIGNED DEFAULT NULL,
  `datetime_refund` datetime DEFAULT NULL,
  `payment_reseller_id_refund` int(10) DEFAULT NULL COMMENT 'ID выплаты реселлеру по возврату',
  `bill_reseller_id_refund` int(10) DEFAULT NULL COMMENT 'ID счета реселлеру по возврату',
  `bill_vendor_id_refund` int(10) DEFAULT NULL COMMENT 'ID счета вендору по  возрату',
  `payment_vendor_id_refund` int(10) DEFAULT NULL COMMENT 'ID выплаты вендору по возврату',
  `payment_customer_id_refund` int(10) DEFAULT NULL COMMENT 'ID выплаты клиенту по возврату'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Возврат билетов';

-- --------------------------------------------------------

--
-- Структура таблицы `UnknownContactInfo`
--

CREATE TABLE `UnknownContactInfo` (
  `Id` int(10) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `RequestDateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата время создания записи',
  `UnknownTerminalId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Ссылка на запись в таблице UnknownTerminal\r\n',
  `FIO` varchar(128) NOT NULL COMMENT 'ФИО контакта',
  `Phone` varchar(32) NOT NULL COMMENT 'Номер телефона',
  `Email` varchar(64) DEFAULT NULL COMMENT 'Электронный адрес',
  `CompanyName` varchar(128) DEFAULT NULL COMMENT 'Название модели',
  `ContactTime` varchar(64) DEFAULT NULL COMMENT 'Удобное время для звонка. MSK',
  `IsCallBack` tinyint(4) DEFAULT '0' COMMENT 'Был ли обратный звонок',
  `CallBackResult` text COMMENT 'Результат обратного звонка',
  `CallBackTime` timestamp NULL DEFAULT NULL COMMENT 'Дата обратного звонка',
  `CallUserId` int(10) UNSIGNED DEFAULT NULL COMMENT 'Пользователь проводивший обратный звонок'
) ENGINE=InnoDB DEFAULT CHARSET=utf32 COMMENT='Учет заявок для контакта продавца';

-- --------------------------------------------------------

--
-- Структура таблицы `UnknownTerminal`
--

CREATE TABLE `UnknownTerminal` (
  `Id` int(10) UNSIGNED NOT NULL COMMENT 'Ключ записи',
  `RequestDateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата время создания записи',
  `SerialNumber` varchar(32) NOT NULL COMMENT 'Серийный номер терминала',
  `RegNumber` varchar(32) DEFAULT NULL COMMENT 'Регистрационный номер терминала(РН ККТ)',
  `VendorName` varchar(64) DEFAULT NULL COMMENT 'Производитель терминала',
  `ModelName` varchar(64) DEFAULT NULL COMMENT 'Название модели',
  `Lat` decimal(12,8) DEFAULT NULL COMMENT 'Широта',
  `Lon` decimal(12,8) DEFAULT NULL COMMENT 'Долгота',
  `FNIsPresent` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Признак наличия фискального накопителя',
  `FNSerialNumber` varchar(32) DEFAULT NULL COMMENT 'Срийный номер ФН',
  `FNINN` varchar(32) DEFAULT NULL COMMENT 'ИНН предприятия приписанный в ФН',
  `FNTerminalOwnerName` varchar(64) DEFAULT NULL COMMENT 'Владелец терминала',
  `FNTerminalOwnerAddress` varchar(128) DEFAULT NULL COMMENT 'Адрес организации',
  `FNTerminalOwnerPhysicalAddress` varchar(128) DEFAULT NULL COMMENT 'Адрес расчетов'
) ENGINE=InnoDB DEFAULT CHARSET=utf32 COMMENT='Список не зарегистрированных терминалов';

-- --------------------------------------------------------

--
-- Структура таблицы `user`
--

CREATE TABLE `user` (
  `user_id` int(11) NOT NULL COMMENT 'ИД записи',
  `partner_id` int(11) NOT NULL COMMENT 'ID партнера',
  `login` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `access_token` varchar(255) DEFAULT NULL,
  `name` varchar(500) DEFAULT NULL,
  `avatar` varchar(500) DEFAULT NULL COMMENT 'Аватар',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Признак удаления пользователя'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Список юзеров';

-- --------------------------------------------------------

--
-- Структура таблицы `vendor_api_config`
--

CREATE TABLE `vendor_api_config` (
  `vendor_id` int(10) UNSIGNED NOT NULL COMMENT 'ID вендора(партнера)',
  `vendor_title` varchar(255) DEFAULT NULL COMMENT 'Текстовое название вендора',
  `connector` text COMMENT 'Путь к коннектору вендора',
  `booking_tmp_cancel_manual` tinyint(3) UNSIGNED DEFAULT '1' COMMENT 'Отмена временной брони вручную',
  `booking_tmp_cancel_auto` tinyint(3) UNSIGNED DEFAULT '0' COMMENT 'Отмена временной брони автоматически',
  `buy_ticket_cnt_max` tinyint(3) UNSIGNED DEFAULT '5' COMMENT 'Максимальное кол-во билетов которое можено купить',
  `debug` tinyint(3) UNSIGNED DEFAULT '1' COMMENT 'Логирование в базу',
  `debug_txt` tinyint(3) UNSIGNED DEFAULT '0' COMMENT 'Логирование в файл',
  `url` text COMMENT 'URL  для запросов к внешнему API',
  `login` varchar(255) DEFAULT NULL COMMENT 'Логин  для запросов к внешнему API',
  `password` varchar(255) DEFAULT NULL COMMENT 'Пароль для запросов к внешнему API',
  `ticket_folder_tmp` varchar(255) DEFAULT NULL COMMENT 'Временная папка для билетов',
  `data` text COMMENT 'Дополнительные настройки в JSON формате'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Настройки для внешних API';

-- --------------------------------------------------------

--
-- Структура таблицы `vendor_conditions`
--

CREATE TABLE `vendor_conditions` (
  `vendor_condition_id` int(10) UNSIGNED NOT NULL COMMENT 'ID условия',
  `vendor_id` int(10) UNSIGNED NOT NULL COMMENT 'ID партнера',
  `station_id_start` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID станции отправления',
  `baggage_info` text COMMENT 'Условия провоза багажа',
  `ticket_refund_info` text COMMENT 'Условия возврата билетов',
  `ticket_paper_required` tinyint(1) UNSIGNED DEFAULT NULL COMMENT 'Требование электронного  билета в бумаге (0 - не нужен, 1 - нужен)',
  `forgot_ticket_info` text COMMENT 'Информация о том, что делать если билет забыл'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Информация о богаже, возврате билетов, требовании эл. билета в бумаге и что делать если билет забыли';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_agent_vendor`
--

CREATE TABLE `xref_agent_vendor` (
  `agent_id` int(11) UNSIGNED NOT NULL COMMENT 'ID агента',
  `vendor_id` int(11) UNSIGNED NOT NULL COMMENT 'ID вендора'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь агента с вендором';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_city_partner`
--

CREATE TABLE `xref_city_partner` (
  `id` int(10) UNSIGNED NOT NULL,
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID партнера(вендора) от которого получен город',
  `city_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города в базе Unitiki',
  `outer_id` varchar(255) DEFAULT NULL COMMENT 'Внешний ID города',
  `outer_title` varchar(255) DEFAULT NULL COMMENT 'Внешнее название города',
  `source` text COMMENT 'JSON объекта пришедший из API',
  `datetime_merge` datetime DEFAULT NULL COMMENT 'Время последнего мержа',
  `is_start` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Признак отправления',
  `is_end` tinyint(3) UNSIGNED DEFAULT NULL COMMENT 'Признак прибытия',
  `as_active` tinyint(1) UNSIGNED DEFAULT '1' COMMENT 'Был ли этот город при последней загрузке городов',
  `is_deleted` tinyint(1) UNSIGNED DEFAULT '0' COMMENT 'Признак удаления'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь города и вендора';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_city_partner_moder`
--

CREATE TABLE `xref_city_partner_moder` (
  `id` int(10) UNSIGNED NOT NULL,
  `xref_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID из таблицы xref_city_partner',
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID партнера внешнего города',
  `city_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID города в базе Unitiki',
  `datetime_merge` datetime DEFAULT NULL COMMENT 'Время последнего мержа',
  `user_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID пользователя (модератора)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь города и вендора';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_group_role`
--

CREATE TABLE `xref_group_role` (
  `group_id` int(11) NOT NULL,
  `role_title` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь групп и ролей';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_station_partner`
--

CREATE TABLE `xref_station_partner` (
  `id` int(10) UNSIGNED NOT NULL,
  `partner_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID партнера(вендора)',
  `outer_city_id` varchar(255) DEFAULT NULL COMMENT 'Внешний ID города которому принадлежит станция',
  `outer_station_id` varchar(255) DEFAULT NULL COMMENT 'Внешний ID станции',
  `outer_station_title` varchar(255) DEFAULT NULL COMMENT 'Внешний title станции',
  `station_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Наш ID станции '
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Внешние станции';

-- --------------------------------------------------------

--
-- Структура таблицы `xref_user_group`
--

CREATE TABLE `xref_user_group` (
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Связь пользователей и групп';

-- --------------------------------------------------------

--
-- Структура таблицы `_api_log`
--

CREATE TABLE `_api_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `date` datetime NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `error` int(11) UNSIGNED NOT NULL,
  `error_message` longtext NOT NULL,
  `script` varchar(255) NOT NULL,
  `ip` varchar(255) NOT NULL,
  `get` longtext NOT NULL,
  `post` longtext NOT NULL,
  `files` longtext NOT NULL,
  `server` longtext NOT NULL,
  `answer` longtext NOT NULL,
  `total_time` varchar(255) NOT NULL,
  `total_sql_time` varchar(255) NOT NULL,
  `total_sql_cnt` varchar(255) NOT NULL,
  `total_sql_log` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `_api_outer_log`
--

CREATE TABLE `_api_outer_log` (
  `id` bigint(20) NOT NULL,
  `vendor_id` int(11) DEFAULT NULL COMMENT 'ID вендора кому отправляется запрос',
  `agent_id` int(11) DEFAULT NULL COMMENT 'ID агента сделавший запрос. Если NULL, то запрос из консоли',
  `operation_id` int(11) DEFAULT NULL COMMENT 'ID ???????? ??????? ??????????? ??????',
  `datetime_request` datetime DEFAULT NULL COMMENT 'Дата и время запроса',
  `method` varchar(255) DEFAULT NULL COMMENT 'Метод',
  `params` text COMMENT 'Параметры запроса',
  `answer` text COMMENT 'SUCCESS Ответ',
  `error` text COMMENT 'FAIL Ответ',
  `request_time` decimal(12,9) DEFAULT NULL COMMENT 'Время запроса'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Логирование запросов к внешним API';

-- --------------------------------------------------------

--
-- Структура таблицы `_log_vendorapi_buy`
--

CREATE TABLE `_log_vendorapi_buy` (
  `id` bigint(20) NOT NULL,
  `date` datetime NOT NULL COMMENT 'дата/время запроса',
  `vendor_id` int(11) NOT NULL COMMENT 'id поставщика',
  `agent_id` int(11) DEFAULT NULL,
  `datetime_start` datetime DEFAULT NULL,
  `station_id_start` int(11) NOT NULL,
  `station_id_end` int(11) NOT NULL,
  `outer_id_from` varchar(255) DEFAULT NULL,
  `outer_id_to` varchar(255) DEFAULT NULL,
  `request_time` decimal(12,9) NOT NULL,
  `try` tinyint(4) NOT NULL DEFAULT '1',
  `result` tinyint(4) NOT NULL COMMENT '0 fail, 1 success'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `_log_vendorapi_checkride`
--

CREATE TABLE `_log_vendorapi_checkride` (
  `id` bigint(20) NOT NULL,
  `date` datetime NOT NULL COMMENT 'дата/время запроса',
  `vendor_id` int(11) NOT NULL COMMENT 'id поставщика',
  `agent_id` int(11) DEFAULT NULL,
  `datetime_start` datetime DEFAULT NULL,
  `station_id_start` int(11) NOT NULL,
  `station_id_end` int(11) NOT NULL,
  `outer_id_from` varchar(255) DEFAULT NULL,
  `outer_id_to` varchar(255) DEFAULT NULL,
  `request_time` decimal(12,9) NOT NULL,
  `try` tinyint(4) NOT NULL DEFAULT '1',
  `result` tinyint(4) NOT NULL COMMENT '0 fail, 1 success'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Структура таблицы `_log_vendorapi_tmpbook`
--

CREATE TABLE `_log_vendorapi_tmpbook` (
  `id` bigint(20) NOT NULL,
  `date` datetime NOT NULL COMMENT 'дата/время запроса',
  `vendor_id` int(11) NOT NULL COMMENT 'id поставщика',
  `agent_id` int(11) DEFAULT NULL,
  `datetime_start` datetime DEFAULT NULL,
  `station_id_start` int(11) NOT NULL,
  `station_id_end` int(11) NOT NULL,
  `outer_id_from` varchar(255) DEFAULT NULL,
  `outer_id_to` varchar(255) DEFAULT NULL,
  `request_time` decimal(12,9) NOT NULL,
  `try` tinyint(4) NOT NULL DEFAULT '1',
  `result` tinyint(4) NOT NULL COMMENT '0 fail, 1 success'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Структура для представления `view_sync_progress`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `view_sync_progress`  AS  (select count(0) AS `Направлений`,round(((count(0) / (select count(0) from `make_site_map_history`)) * 100),2) AS `Процент от общего числа`,`make_site_map_history`.`next_check_datetime` AS `Следующая синхронизация`,avg(`make_site_map_history`.`last_requiest_time`) AS `Среднее время синхронизации`,max(`make_site_map_history`.`last_requiest_time`) AS `Максимальное время синхронизации`,sum(`make_site_map_history`.`fault_sync_count`) AS `Всего ошибок синхронизации`,avg(`make_site_map_history`.`route_weight`) AS `Средний вес маршрутов` from `make_site_map_history` where 1 group by `make_site_map_history`.`next_check_datetime` order by `make_site_map_history`.`next_check_datetime`) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_cities_c1`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_cities_c1`  AS  (select `c1`.`city_id` AS `_c1_city_id`,`c1`.`city_type_id` AS `_c1_city_type_id`,`c1`.`country_id` AS `_c1_country_id`,`c1`.`region` AS `_c1_region`,`c1`.`district` AS `_c1_district`,`c1`.`title` AS `_c1_title`,`c1`.`title_genitive` AS `_c1_title_genitive`,`c1`.`title_accusative` AS `_c1_title_accusative`,`c1`.`title_prepositional` AS `_c1_title_prepositional`,`c1`.`title_lat` AS `_c1_title_lat`,`c1`.`title_genitive_lat` AS `_c1_title_genitive_lat`,`c1`.`title_accusative_lat` AS `_c1_title_accusative_lat`,`c1`.`title_prepositional_lat` AS `_c1_title_prepositional_lat`,`c1`.`lng` AS `_c1_lng`,`c1`.`lat` AS `_c1_lat`,`c1`.`city_title_search` AS `_c1_city_title_search`,`c1`.`is_verified` AS `_c1_is_verified`,`c1`.`code` AS `_c1_code`,`c1`.`region_id` AS `_c1_region_id`,`c1_t`.`city_type_id` AS `_c1_t_city_type_id`,`c1_t`.`title` AS `_c1_t_title`,`c1_t`.`title_short` AS `_c1_t_title_short`,`c1_c`.`country_id` AS `_c1_c_country_id`,`c1_c`.`title` AS `_c1_c_title`,`c1_c`.`code_a2` AS `_c1_c_code_a2`,`c1_c`.`timezone` AS `_c1_c_timezone` from ((`city` `c1` left join `city_type` `c1_t` on((`c1_t`.`city_type_id` = `c1`.`city_type_id`))) left join `country` `c1_c` on((`c1_c`.`country_id` = `c1`.`country_id`)))) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_cities_c2`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_cities_c2`  AS  (select `c2`.`city_id` AS `_c2_city_id`,`c2`.`city_type_id` AS `_c2_city_type_id`,`c2`.`country_id` AS `_c2_country_id`,`c2`.`region` AS `_c2_region`,`c2`.`district` AS `_c2_district`,`c2`.`title` AS `_c2_title`,`c2`.`title_genitive` AS `_c2_title_genitive`,`c2`.`title_accusative` AS `_c2_title_accusative`,`c2`.`title_prepositional` AS `_c2_title_prepositional`,`c2`.`title_lat` AS `_c2_title_lat`,`c2`.`title_genitive_lat` AS `_c2_title_genitive_lat`,`c2`.`title_accusative_lat` AS `_c2_title_accusative_lat`,`c2`.`title_prepositional_lat` AS `_c2_title_prepositional_lat`,`c2`.`lng` AS `_c2_lng`,`c2`.`lat` AS `_c2_lat`,`c2`.`city_title_search` AS `_c2_city_title_search`,`c2`.`is_verified` AS `_c2_is_verified`,`c2`.`code` AS `_c2_code`,`c2`.`region_id` AS `_c2_region_id`,`c2_t`.`city_type_id` AS `_c2_t_city_type_id`,`c2_t`.`title` AS `_c2_t_title`,`c2_t`.`title_short` AS `_c2_t_title_short`,`c2_c`.`country_id` AS `_c2_c_country_id`,`c2_c`.`title` AS `_c2_c_title`,`c2_c`.`code_a2` AS `_c2_c_code_a2`,`c2_c`.`timezone` AS `_c2_c_timezone` from ((`city` `c2` left join `city_type` `c2_t` on((`c2_t`.`city_type_id` = `c2`.`city_type_id`))) left join `country` `c2_c` on((`c2_c`.`country_id` = `c2`.`country_id`)))) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_countries_co`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_countries_co`  AS  (select `_co`.`country_id` AS `_co_country_id`,`_co`.`title` AS `_co_title`,`_co`.`code_a2` AS `_co_code_a2`,`_co`.`timezone` AS `_co_timezone`,`_co`.`code_a2` AS `_co_code` from `country` `_co`) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_partners_pa`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_partners_pa`  AS  (select `pa`.`partner_id` AS `_pa_partner_id`,`pa`.`title` AS `_pa_title`,`pa`.`description` AS `_pa_description`,`pa`.`email` AS `_pa_email`,`pa`.`phone` AS `_pa_phone`,`pa`.`telegram_chat_id` AS `_pa_telegram_chat_id`,`pa`.`agent_id` AS `_pa_agent_id`,`pa`.`statement_json` AS `_pa_statement_json`,`pa`.`type_vendor` AS `_pa_type_vendor`,`pa`.`type_reseller` AS `_pa_type_reseller`,`pa`.`vendor_agent_commission` AS `_pa_vendor_agent_commission`,`pa`.`vendor_currency_id` AS `_pa_vendor_currency_id`,`pa`.`enabled_service_markup` AS `_pa_enabled_service_markup`,`pa`.`contract_number` AS `_pa_contract_number`,`pa`.`time_stop_booking` AS `_pa_time_stop_booking`,`pa`.`is_enabled_booking_without_ticket_data` AS `_pa_is_enabled_booking_without_ticket_data`,`pa`.`vendor_secret_key` AS `_pa_vendor_secret_key`,`pa`.`is_send_notice_ticket_refund` AS `_pa_is_send_notice_ticket_refund`,`pa`.`is_deleted` AS `_pa_is_deleted`,`pa_a`.`agent_id` AS `_pa_a_agent_id`,`pa_a`.`title` AS `_pa_a_title`,`pa_a`.`booking_tmp_time` AS `_pa_a_booking_tmp_time`,`pa_a`.`secret_key` AS `_pa_a_secret_key`,`pa_a`.`unitiki_price_markup` AS `_pa_a_unitiki_price_markup`,`pa_a`.`unitiki_price_markup_fix` AS `_pa_a_unitiki_price_markup_fix`,`pa_a`.`reseller_price_max` AS `_pa_a_reseller_price_max`,`pa_a`.`reseller_agent_commission` AS `_pa_a_reseller_agent_commission`,`pa_a`.`currency_id` AS `_pa_a_currency_id`,`pa_a`.`contract_number` AS `_pa_a_contract_number`,`pa_a`.`reference_list` AS `_pa_a_reference_list`,`pa_a`.`is_agent_inner` AS `_pa_a_is_agent_inner`,`pa_a`.`can_booking_tmp` AS `_pa_a_can_booking_tmp`,`pa_a`.`can_buy` AS `_pa_a_can_buy`,`pa_a`.`can_ticket_refund` AS `_pa_a_can_ticket_refund`,`pa_a`.`can_make_request_without_cache` AS `_pa_a_can_make_request_without_cache`,`pa_a`.`enabled_correction_price_agent_unitiki_markup` AS `_pa_a_enabled_correction_price_agent_unitiki_markup`,`pa_a`.`is_blocked` AS `_pa_a_is_blocked`,`pa_a`.`is_send_notice_ticket_refund` AS `_pa_a_is_send_notice_ticket_refund`,`pa_a`.`is_ignore_vendor` AS `_pa_a_is_ignore_vendor`,`pa_ap`.`vendor_id` AS `_pa_ap_vendor_id`,`pa_ap`.`vendor_title` AS `_pa_ap_vendor_title`,`pa_ap`.`connector` AS `_pa_ap_connector`,`pa_ap`.`booking_tmp_cancel_manual` AS `_pa_ap_booking_tmp_cancel_manual`,`pa_ap`.`booking_tmp_cancel_auto` AS `_pa_ap_booking_tmp_cancel_auto`,`pa_ap`.`buy_ticket_cnt_max` AS `_pa_ap_buy_ticket_cnt_max`,`pa_ap`.`debug` AS `_pa_ap_debug`,`pa_ap`.`debug_txt` AS `_pa_ap_debug_txt`,`pa_ap`.`url` AS `_pa_ap_url`,`pa_ap`.`login` AS `_pa_ap_login`,`pa_ap`.`password` AS `_pa_ap_password`,`pa_ap`.`ticket_folder_tmp` AS `_pa_ap_ticket_folder_tmp`,`pa_ap`.`data` AS `_pa_ap_data` from ((`partner` `pa` left join `agent` `pa_a` on((`pa_a`.`agent_id` = `pa`.`agent_id`))) left join `vendor_api_config` `pa_ap` on((`pa_ap`.`vendor_id` = `pa`.`partner_id`)))) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_stations_s1`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_stations_s1`  AS  (select `s1`.`station_id` AS `_s1_station_id`,`s1`.`city_id` AS `_s1_city_id`,`s1`.`title` AS `_s1_title`,`s1`.`description` AS `_s1_description`,`s1`.`title_genitive` AS `_s1_title_genitive`,`s1`.`title_accusative` AS `_s1_title_accusative`,`s1`.`title_prepositional` AS `_s1_title_prepositional`,`s1`.`title_lat` AS `_s1_title_lat`,`s1`.`title_genitive_lat` AS `_s1_title_genitive_lat`,`s1`.`title_accusative_lat` AS `_s1_title_accusative_lat`,`s1`.`title_prepositional_lat` AS `_s1_title_prepositional_lat`,`s1`.`lng` AS `_s1_lng`,`s1`.`lat` AS `_s1_lat`,`s1`.`address` AS `_s1_address`,`s1`.`phone` AS `_s1_phone`,`s1`.`site` AS `_s1_site`,`s1`.`is_verified` AS `_s1_is_verified`,`s1`.`code` AS `_s1_code`,`s1`.`hash` AS `_s1_hash`,`s1`.`region_id` AS `_s1_region_id` from `station` `s1`) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_stations_s2`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_stations_s2`  AS  (select `s2`.`station_id` AS `_s2_station_id`,`s2`.`city_id` AS `_s2_city_id`,`s2`.`title` AS `_s2_title`,`s2`.`description` AS `_s2_description`,`s2`.`title_genitive` AS `_s2_title_genitive`,`s2`.`title_accusative` AS `_s2_title_accusative`,`s2`.`title_prepositional` AS `_s2_title_prepositional`,`s2`.`title_lat` AS `_s2_title_lat`,`s2`.`title_genitive_lat` AS `_s2_title_genitive_lat`,`s2`.`title_accusative_lat` AS `_s2_title_accusative_lat`,`s2`.`title_prepositional_lat` AS `_s2_title_prepositional_lat`,`s2`.`lng` AS `_s2_lng`,`s2`.`lat` AS `_s2_lat`,`s2`.`address` AS `_s2_address`,`s2`.`phone` AS `_s2_phone`,`s2`.`site` AS `_s2_site`,`s2`.`is_verified` AS `_s2_is_verified`,`s2`.`code` AS `_s2_code`,`s2`.`hash` AS `_s2_hash`,`s2`.`region_id` AS `_s2_region_id` from `station` `s2`) ;

-- --------------------------------------------------------

--
-- Структура для представления `v_users_u`
--

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_users_u`  AS  (select `u`.`user_id` AS `_u_user_id`,`u`.`name` AS `_u_name`,`u`.`avatar` AS `_u_avatar`,`u`.`is_deleted` AS `_u_is_deleted`,`u`.`name` AS `_u_fio` from `user` `u`) ;

--
-- Индексы сохранённых таблиц
--

--
-- Индексы таблицы `admin_role`
--
ALTER TABLE `admin_role`
  ADD PRIMARY KEY (`role_title`);

--
-- Индексы таблицы `admin_user`
--
ALTER TABLE `admin_user`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `login` (`login`,`password`,`is_deleted`),
  ADD KEY `partner_id` (`is_deleted`),
  ADD KEY `access_token_is_blocked` (`access_token`,`is_deleted`),
  ADD KEY `user_idx` (`user_id`);

--
-- Индексы таблицы `admin_xref_user_role`
--
ALTER TABLE `admin_xref_user_role`
  ADD PRIMARY KEY (`user_id`,`role_title`);

--
-- Индексы таблицы `agent`
--
ALTER TABLE `agent`
  ADD PRIMARY KEY (`agent_id`),
  ADD KEY `agent_id_is_blocked` (`agent_id`,`is_blocked`);

--
-- Индексы таблицы `billing`
--
ALTER TABLE `billing`
  ADD PRIMARY KEY (`billing_id`);

--
-- Индексы таблицы `billing_rules`
--
ALTER TABLE `billing_rules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `agent_id_vendor_id` (`agent_id`,`vendor_id`),
  ADD KEY `agent_id` (`agent_id`);

--
-- Индексы таблицы `card_identity`
--
ALTER TABLE `card_identity`
  ADD PRIMARY KEY (`card_identity_id`);

--
-- Индексы таблицы `citizenship`
--
ALTER TABLE `citizenship`
  ADD PRIMARY KEY (`citizenship_id`),
  ADD UNIQUE KEY `code_lat_a2` (`code_lat_a2`),
  ADD UNIQUE KEY `code_num` (`code_num`),
  ADD KEY `title` (`title`);

--
-- Индексы таблицы `city`
--
ALTER TABLE `city`
  ADD PRIMARY KEY (`city_id`),
  ADD UNIQUE KEY `country_id_region_title` (`city_type_id`,`country_id`,`region`,`district`,`title`),
  ADD KEY `city_title_search` (`city_title_search`),
  ADD KEY `fk_cities_co` (`country_id`),
  ADD KEY `city_id` (`city_id`);

--
-- Индексы таблицы `city_import_problem`
--
ALTER TABLE `city_import_problem`
  ADD PRIMARY KEY (`partner_id`,`text`);

--
-- Индексы таблицы `city_import_result`
--
ALTER TABLE `city_import_result`
  ADD PRIMARY KEY (`partner_id`,`type_result`,`position`);

--
-- Индексы таблицы `city_type`
--
ALTER TABLE `city_type`
  ADD PRIMARY KEY (`city_type_id`);

--
-- Индексы таблицы `country`
--
ALTER TABLE `country`
  ADD PRIMARY KEY (`country_id`),
  ADD KEY `code_a2` (`code_a2`) USING BTREE,
  ADD KEY `title` (`title`) USING BTREE;

--
-- Индексы таблицы `currency`
--
ALTER TABLE `currency`
  ADD PRIMARY KEY (`currency_id`),
  ADD UNIQUE KEY `iso4217` (`iso4217`);

--
-- Индексы таблицы `dispatch_ticket_status`
--
ALTER TABLE `dispatch_ticket_status`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uipp_idx` (`UIPP`),
  ADD KEY `ticket_id_idx` (`ticket_id`),
  ADD KEY `outer_ticket_id_idx` (`outer_ticket_id`);

--
-- Индексы таблицы `EMVAbonementCounters`
--
ALTER TABLE `EMVAbonementCounters`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `EMVAbonementCounters_AbonementId_idx` (`AbonementId`);

--
-- Индексы таблицы `EMVAbonementList`
--
ALTER TABLE `EMVAbonementList`
  ADD PRIMARY KEY (`AbonementId`),
  ADD KEY `EMVAbonementList_PANHash_idx` (`PANHash`),
  ADD KEY `EMVAbonementList_G1_idx` (`PANHash`,`IsActive`),
  ADD KEY `EMVAbonementList_G2_idx` (`PANHash`,`ValidTimeStart`,`ValidTimeEnd`),
  ADD KEY `EMVAbonementList_G3_idx` (`IsActive`,`ValidTimeEnd`);

--
-- Индексы таблицы `EMVAbonementTransaction`
--
ALTER TABLE `EMVAbonementTransaction`
  ADD PRIMARY KEY (`Id`),
  ADD UNIQUE KEY `TransactionId` (`TransactionId`),
  ADD KEY `EMVAbonementTransaction_PANHash_idx` (`PANHash`),
  ADD KEY `EMVAbonementTransaction_TransactionId_idx` (`TransactionId`),
  ADD KEY `EMVAbonementTransaction_AbonementId_idx` (`AbonementId`),
  ADD KEY `EMVAbonementTransaction_CounterId_idx` (`CounterId`);

--
-- Индексы таблицы `EMVBINList`
--
ALTER TABLE `EMVBINList`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `EMVStopList_IsBlocked_idx` (`IsBlocked`),
  ADD KEY `EMVStopList_BIN_idx` (`BIN`);

--
-- Индексы таблицы `EMVStopList`
--
ALTER TABLE `EMVStopList`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `EMVStopList_IsActual_idx` (`IsActual`),
  ADD KEY `EMVStopList_IsBlocked_idx` (`IsBlocked`),
  ADD KEY `EMVStopList_PANHash_idx` (`PANHash`),
  ADD KEY `EMVStopList_index1_idx` (`PANHash`,`IsActual`);

--
-- Индексы таблицы `EMVWriteOffsBinding`
--
ALTER TABLE `EMVWriteOffsBinding`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `EMVWriteOffsBinding_WriteOffsId_idx` (`WriteOffsId`),
  ADD KEY `EMVWriteOffsBinding_IsEnable_idx` (`IsEnable`),
  ADD KEY `EMVWriteOffsBinding_G1_idx` (`VendorId`,`IsEnable`);

--
-- Индексы таблицы `EMVWriteOffsTemplate`
--
ALTER TABLE `EMVWriteOffsTemplate`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `EMVWriteOffsTemplate_IsSocial_idx` (`IsSocial`),
  ADD KEY `EMVWriteOffsTemplate_IsActive_idx` (`IsActive`),
  ADD KEY `EMVWriteOffsTemplate_SellDateTimeEnd_idx` (`SellDateTimeEnd`),
  ADD KEY `EMVWriteOffsTemplate_GR1_idx` (`IsActive`,`SellDateTimeEnd`);

--
-- Индексы таблицы `geo_action`
--
ALTER TABLE `geo_action`
  ADD PRIMARY KEY (`action_id`);

--
-- Индексы таблицы `geo_diff_log`
--
ALTER TABLE `geo_diff_log`
  ADD PRIMARY KEY (`geo_diff_log`);

--
-- Индексы таблицы `helpdesk_comment`
--
ALTER TABLE `helpdesk_comment`
  ADD PRIMARY KEY (`helpdesk_comment_id`),
  ADD KEY `helpdesk_request_id` (`helpdesk_request_id`),
  ADD KEY `user_id` (`user_id`,`datetime_create`);

--
-- Индексы таблицы `helpdesk_request`
--
ALTER TABLE `helpdesk_request`
  ADD PRIMARY KEY (`helpdesk_request_id`),
  ADD KEY `operation_id` (`operation_id`),
  ADD KEY `admin_user_id` (`admin_user_id`,`date_reminder`,`status`);

--
-- Индексы таблицы `HumanNameList`
--
ALTER TABLE `HumanNameList`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name_idx` (`name`),
  ADD KEY `gender_country_idx` (`gender`,`country_id`);

--
-- Индексы таблицы `invoice`
--
ALTER TABLE `invoice`
  ADD PRIMARY KEY (`invoice_id`);

--
-- Индексы таблицы `invoice_log`
--
ALTER TABLE `invoice_log`
  ADD PRIMARY KEY (`invoice_log_id`);

--
-- Индексы таблицы `make_site_map_history`
--
ALTER TABLE `make_site_map_history`
  ADD UNIQUE KEY `city_id_start_end_idx` (`city_id_start`,`city_id_end`),
  ADD KEY `city_id_start_idx` (`city_id_start`),
  ADD KEY `city_id_end_idx` (`city_id_end`);

--
-- Индексы таблицы `operation`
--
ALTER TABLE `operation`
  ADD PRIMARY KEY (`operation_id`),
  ADD KEY `status` (`status`,`user_id`),
  ADD KEY `datetime_to_cancel_status` (`datetime_to_cancel`,`status`),
  ADD KEY `datetime_start` (`datetime_start`,`status`,`reason`,`agent_id`),
  ADD KEY `datetime_buy_status_reference` (`datetime_buy`,`reference`,`status`),
  ADD KEY `status_reference` (`status`,`reference`);

--
-- Индексы таблицы `operation_log`
--
ALTER TABLE `operation_log`
  ADD PRIMARY KEY (`log_id`);

--
-- Индексы таблицы `organization`
--
ALTER TABLE `organization`
  ADD PRIMARY KEY (`organization_id`),
  ADD KEY `partner_id` (`partner_id`);

--
-- Индексы таблицы `partner`
--
ALTER TABLE `partner`
  ADD PRIMARY KEY (`partner_id`),
  ADD KEY `partner_id_is_deleted` (`partner_id`,`is_deleted`);

--
-- Индексы таблицы `payment`
--
ALTER TABLE `payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `partner_id` (`partner_id`);

--
-- Индексы таблицы `payment_log`
--
ALTER TABLE `payment_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `payment_id` (`payment_id`);

--
-- Индексы таблицы `push_buy_log`
--
ALTER TABLE `push_buy_log`
  ADD PRIMARY KEY (`log_id`);

--
-- Индексы таблицы `reference`
--
ALTER TABLE `reference`
  ADD PRIMARY KEY (`id`);

--
-- Индексы таблицы `ReferenceList`
--
ALTER TABLE `ReferenceList`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `ReferenceList_ReferenceName_idx` (`ReferenceName`),
  ADD KEY `ReferenceList_GroupIndex1_idx` (`ReferenceName`,`RecordId`);

--
-- Индексы таблицы `region`
--
ALTER TABLE `region`
  ADD PRIMARY KEY (`region_id`),
  ADD UNIQUE KEY `country_id_title` (`country_id`,`title`),
  ADD KEY `title_lat` (`title_lat`);

--
-- Индексы таблицы `region_timezone`
--
ALTER TABLE `region_timezone`
  ADD PRIMARY KEY (`country_id`,`region_title`);

--
-- Индексы таблицы `request_change`
--
ALTER TABLE `request_change`
  ADD PRIMARY KEY (`request_id`);

--
-- Индексы таблицы `request_partner`
--
ALTER TABLE `request_partner`
  ADD PRIMARY KEY (`request_partner_id`);

--
-- Индексы таблицы `ride_search_notification`
--
ALTER TABLE `ride_search_notification`
  ADD PRIMARY KEY (`search_notification_id`);

--
-- Индексы таблицы `ride_segment`
--
ALTER TABLE `ride_segment`
  ADD PRIMARY KEY (`ride_segment_id`),
  ADD UNIQUE KEY `outer_id_2` (`outer_id`,`partner_id`),
  ADD UNIQUE KEY `uk_ride_segment` (`guid`),
  ADD KEY `city_id_start_2` (`city_id_start`,`city_id_end`,`partner_id`),
  ADD KEY `city_id_start_3` (`city_id_start`,`station_id_end`,`partner_id`),
  ADD KEY `station_id_start` (`station_id_start`,`city_id_end`,`partner_id`),
  ADD KEY `station_id_start_2` (`station_id_start`,`station_id_end`,`partner_id`),
  ADD KEY `stops_hash` (`stops_hash`),
  ADD KEY `ride_hash` (`ride_hash`),
  ADD KEY `search_fromstation` (`station_id_start`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_tostation` (`station_id_end`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_tocity` (`city_id_end`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_fromcity` (`city_id_start`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_fromcity_tocity` (`city_id_start`,`city_id_end`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_fromcity_tostation` (`city_id_start`,`station_id_end`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_fromstation_tocity` (`station_id_start`,`city_id_end`,`date_start`,`time_start`,`partner_id`),
  ADD KEY `search_fromstation_tostation` (`station_id_start`,`station_id_end`,`date_start`,`time_start`,`partner_id`);

--
-- Индексы таблицы `ride_segment_outer_detail`
--
ALTER TABLE `ride_segment_outer_detail`
  ADD PRIMARY KEY (`ride_segment_id`),
  ADD KEY `idx_vendor_route` (`vendor_id`,`route_outer_id`),
  ADD KEY `ride_outer_id` (`ride_outer_id`),
  ADD KEY `ride_segment_outer_id_idx` (`ride_segment_outer_id`) USING BTREE,
  ADD KEY `ride_segment_outer_detail_group1_idx` (`vendor_id`,`ride_segment_outer_id`),
  ADD KEY `ride_segment_outer_detail_group2_idx` (`vendor_id`,`ride_outer_id`);

--
-- Индексы таблицы `ride_segment_tmp`
--
ALTER TABLE `ride_segment_tmp`
  ADD PRIMARY KEY (`ride_segment_id`);

--
-- Индексы таблицы `ride_segment_way_points`
--
ALTER TABLE `ride_segment_way_points`
  ADD PRIMARY KEY (`ride_segment_id`);

--
-- Индексы таблицы `sap`
--
ALTER TABLE `sap`
  ADD PRIMARY KEY (`sap_id`);

--
-- Индексы таблицы `search`
--
ALTER TABLE `search`
  ADD PRIMARY KEY (`search_id`);

--
-- Индексы таблицы `search_job`
--
ALTER TABLE `search_job`
  ADD PRIMARY KEY (`job_id`);

--
-- Индексы таблицы `search_result`
--
ALTER TABLE `search_result`
  ADD UNIQUE KEY `search_id_ride_segment_id` (`search_id`,`ride_segment_id`);

--
-- Индексы таблицы `search_stat`
--
ALTER TABLE `search_stat`
  ADD PRIMARY KEY (`event_date`,`agent_id`,`city_id_start`,`city_id_end`,`station_id_start`,`station_id_end`,`ride_date`);

--
-- Индексы таблицы `seo_direction`
--
ALTER TABLE `seo_direction`
  ADD PRIMARY KEY (`direction_id`),
  ADD KEY `title_lat` (`title_lat`);

--
-- Индексы таблицы `seo_direction_item`
--
ALTER TABLE `seo_direction_item`
  ADD PRIMARY KEY (`direction_id`,`item_id`,`item_type`);

--
-- Индексы таблицы `seo_ride`
--
ALTER TABLE `seo_ride`
  ADD PRIMARY KEY (`ride_hash`),
  ADD KEY `route_hash` (`stops_hash`),
  ADD KEY `partner_id` (`partner_id`),
  ADD KEY `date_start` (`date_start`),
  ADD KEY `price_hash` (`price_hash`),
  ADD KEY `route_hash_2` (`route_hash`);

--
-- Индексы таблицы `seo_stops`
--
ALTER TABLE `seo_stops`
  ADD PRIMARY KEY (`stops_hash`),
  ADD KEY `city_id_start` (`city_id_start`,`city_id_end`),
  ADD KEY `station_id_start` (`station_id_start`,`station_id_end`);

--
-- Индексы таблицы `sequence`
--
ALTER TABLE `sequence`
  ADD PRIMARY KEY (`sequence_id`);

--
-- Индексы таблицы `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`service_id`),
  ADD KEY `partnerid_idx` (`partner_id`),
  ADD KEY `serviceid_partnerid_idx` (`service_id`,`partner_id`);

--
-- Индексы таблицы `site_config`
--
ALTER TABLE `site_config`
  ADD PRIMARY KEY (`siteConfigId`),
  ADD KEY `fk_site_config_u` (`user_id`);

--
-- Индексы таблицы `site_google_analytics`
--
ALTER TABLE `site_google_analytics`
  ADD PRIMARY KEY (`operation_id`);

--
-- Индексы таблицы `site_sync`
--
ALTER TABLE `site_sync`
  ADD PRIMARY KEY (`siteSyncID`),
  ADD UNIQUE KEY `uk_site_sync_c` (`partnerID`,`departureCityID`,`arrivalCityID`) COMMENT 'Для запросов ''город-город''',
  ADD UNIQUE KEY `uk_site_sync_s` (`partnerID`,`departureStationID`,`arrivalStationID`) COMMENT 'Для запросов ''станция-станция''',
  ADD KEY `fk_site_sync_c1` (`departureCityID`),
  ADD KEY `fk_site_sync_c2` (`arrivalCityID`),
  ADD KEY `fk_site_sync_s1` (`departureStationID`),
  ADD KEY `fk_site_sync_s2` (`arrivalStationID`),
  ADD KEY `fk_site_sync_u` (`user_id`);

--
-- Индексы таблицы `site_sync_dates`
--
ALTER TABLE `site_sync_dates`
  ADD PRIMARY KEY (`siteSyncID`,`fromDate`);

--
-- Индексы таблицы `sopp_request`
--
ALTER TABLE `sopp_request`
  ADD PRIMARY KEY (`id`),
  ADD KEY `requestDateTime_idx` (`requestDateTime`);

--
-- Индексы таблицы `station`
--
ALTER TABLE `station`
  ADD PRIMARY KEY (`station_id`),
  ADD UNIQUE KEY `uk_city` (`city_id`,`title`,`address`),
  ADD KEY `city_id_title_lat` (`city_id`,`title_lat`),
  ADD KEY `k_station` (`city_id`,`title`) USING BTREE;

--
-- Индексы таблицы `station_merge_log`
--
ALTER TABLE `station_merge_log`
  ADD PRIMARY KEY (`log_id`);

--
-- Индексы таблицы `telegram_hook`
--
ALTER TABLE `telegram_hook`
  ADD PRIMARY KEY (`telegram_hook_id`);

--
-- Индексы таблицы `TerminalActionLog`
--
ALTER TABLE `TerminalActionLog`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalActionLog_ActionType_idx` (`ActionType`),
  ADD KEY `TerminalActionLog_SQLUserId_idx` (`SQLUserId`),
  ADD KEY `TerminalActionLog_TableName_idx` (`TableName`),
  ADD KEY `TerminalActionLog_EditDateTime_idx` (`EditDateTime`),
  ADD KEY `TerminalActionLog_index1_idx` (`TableName`,`TableRecordId`,`TableRecordVersion`);

--
-- Индексы таблицы `TerminalGroup`
--
ALTER TABLE `TerminalGroup`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalGroup_Name_idx` (`Name`),
  ADD KEY `TerminalGroup_Type_idx` (`Type`),
  ADD KEY `TerminalGroup_IsDeleted_idx` (`IsDeleted`),
  ADD KEY `TerminalGroup_EditUserId_EditDateTime_idx` (`EditUserId`,`EditDateTime`),
  ADD KEY `TerminalGroup_Id_IsDeleted_idx` (`Id`,`IsDeleted`);

--
-- Индексы таблицы `TerminalList`
--
ALTER TABLE `TerminalList`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalList_TerminalId_idx` (`TerminalId`),
  ADD KEY `TerminalList_TerminalHash_idx` (`TerminalHash`),
  ADD KEY `TerminalList_TerminalSerialNumber_idx` (`TerminalSerialNumber`),
  ADD KEY `TerminalList_FNSerialNumber_idx` (`FNSerialNumber`),
  ADD KEY `TerminalList_EditUserId_idx` (`EditUserId`),
  ADD KEY `TerminalList_VendorOrganizationId_idx` (`VendorOrganizationId`),
  ADD KEY `TerminalList_IsDeleted_idx` (`IsDeleted`),
  ADD KEY `TerminalList_EditDateTime_idx` (`EditDateTime`),
  ADD KEY `TerminalList_EditUserId_EditDateTime_idx` (`EditUserId`,`EditDateTime`),
  ADD KEY `TerminalList_TerminalId_IsDeleted_idx` (`TerminalId`,`IsDeleted`),
  ADD KEY `TerminalList_VendorOrganizationId_TerminalId_idx` (`VendorOrganizationId`,`TerminalId`),
  ADD KEY `TerminalList_VendorId_VendorOrganizationId_idx` (`VendorId`,`VendorOrganizationId`);

--
-- Индексы таблицы `TerminalLog`
--
ALTER TABLE `TerminalLog`
  ADD PRIMARY KEY (`TerminalLogId`);

--
-- Индексы таблицы `TerminalOrganizationList`
--
ALTER TABLE `TerminalOrganizationList`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalOrganizationList_Index1_idx` (`VendorId`,`VendorOrganizationId`),
  ADD KEY `TerminalOrganizationList_IsDeleted_idx` (`IsDeleted`),
  ADD KEY `TerminalOrganizationList_EditDateTime_idx` (`EditDateTime`),
  ADD KEY `TerminalOrganizationList_EditUserId_EditDateTime_idx` (`EditUserId`,`EditDateTime`);

--
-- Индексы таблицы `TerminalRawTransaction`
--
ALTER TABLE `TerminalRawTransaction`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalRawTransaction_ReferenceName_idx` (`LoadDateTime`),
  ADD KEY `TerminalRawTransaction_GroupIndex1_idx` (`TerminalId`,`AgentId`,`LoadDateTime`),
  ADD KEY `TerminalRawTransaction_GroupIndex2_idx` (`LoadDateTime`,`IsParsed`);

--
-- Индексы таблицы `TerminalRoleFunction`
--
ALTER TABLE `TerminalRoleFunction`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalRoleFunction_VendorId_idx` (`VendorId`),
  ADD KEY `TerminalRoleFunction_index1_idx` (`VendorId`,`RoleId`),
  ADD KEY `TerminalRoleFunction_EditUserId_idx` (`EditUserId`),
  ADD KEY `TerminalRoleFunction_EditDateTime_idx` (`EditDateTime`),
  ADD KEY `TerminalRoleFunction_index2_idx` (`EditUserId`,`EditDateTime`);

--
-- Индексы таблицы `TerminalTransaction`
--
ALTER TABLE `TerminalTransaction`
  ADD PRIMARY KEY (`TransactionId`),
  ADD UNIQUE KEY `Terminal_TransactionTime_Ticket_idx` (`transactionTime`,`terminalSerialNumber`,`ticketNumber`,`ticketSeries`) USING BTREE,
  ADD KEY `TerminalTransaction_IsAbonementChecked_idx` (`IsTariffed`),
  ADD KEY `TerminalTransaction_Group1_idx` (`IsTariffed`,`TarifficationDateTime`),
  ADD KEY `TerminalTransaction_Group2_idx` (`NSIReceivedDateTime`,`IsTariffed`,`paymentType`),
  ADD KEY `PANHash` (`PANHash`),
  ADD KEY `PANHash_2` (`PANHash`,`IsTariffed`);

--
-- Индексы таблицы `TerminalUser`
--
ALTER TABLE `TerminalUser`
  ADD PRIMARY KEY (`Id`),
  ADD UNIQUE KEY `TerminalUserId` (`TerminalUserId`),
  ADD KEY `TerminalUser_UserFIO_idx` (`UserFIO`),
  ADD KEY `TerminalUser_UserPersonnelNumber_idx` (`UserPersonnelNumber`),
  ADD KEY `TerminalUser_UserHash_idx` (`UserHash`),
  ADD KEY `TerminalUser_TerminalUserId_idx` (`TerminalUserId`),
  ADD KEY `TerminalUser_VendorOrganizationId_idx` (`VendorOrganizationId`),
  ADD KEY `TerminalUser_EditUserId_idx` (`EditUserId`),
  ADD KEY `TerminalUser_EditDateTime_idx` (`EditDateTime`),
  ADD KEY `TerminalUser_EditUserId_EditDateTime_idx` (`EditUserId`,`EditDateTime`),
  ADD KEY `TerminalUser_TerminalUserId_IsDeleted_idx` (`TerminalUserId`,`IsDeleted`),
  ADD KEY `TerminalUser_VendorOrganizationId_TerminalUserId_idx` (`VendorOrganizationId`,`TerminalUserId`),
  ADD KEY `TerminalUser_VendorId_VendorOrganizationId_idx` (`VendorId`,`VendorOrganizationId`);

--
-- Индексы таблицы `TerminalUserPhoto`
--
ALTER TABLE `TerminalUserPhoto`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `TerminalUserPhoto_TerminalUserId_idx` (`TerminalUserId`);

--
-- Индексы таблицы `TerminalUserRides`
--
ALTER TABLE `TerminalUserRides`
  ADD PRIMARY KEY (`Id`),
  ADD UNIQUE KEY `TerminalUserRides_GroupIndex1_idx` (`TerminalUserId`,`RideSegmentId`),
  ADD KEY `TerminalUserRides_TerminalUserId_idx` (`TerminalUserId`),
  ADD KEY `TerminalUserRides_RideSegmentId_idx` (`RideSegmentId`),
  ADD KEY `TerminalUserRides_RideSegmentDateTime_idx` (`RideSegmentDateTime`),
  ADD KEY `TerminalUserRides_GroupIndex2_idx` (`RideSegmentDateTime`,`RideSegmentId`) USING BTREE;

--
-- Индексы таблицы `ticket`
--
ALTER TABLE `ticket`
  ADD PRIMARY KEY (`ticket_id`),
  ADD KEY `partner_id_agent_id_payment_id` (`partner_id`,`payment_vendor_id`,`agent_id`),
  ADD KEY `outer_ticket_id` (`outer_ticket_id`),
  ADD KEY `partner_operation` (`partner_id`,`operation_id`),
  ADD KEY `operation_id_payment_reseller_reference_id` (`payment_reseller_reference_id`,`operation_id`),
  ADD KEY `fk_ticket_a` (`agent_id`),
  ADD KEY `native_blank_number_idx` (`native_blank_number`);

--
-- Индексы таблицы `ticket_comment`
--
ALTER TABLE `ticket_comment`
  ADD PRIMARY KEY (`ticket_id`);

--
-- Индексы таблицы `ticket_data`
--
ALTER TABLE `ticket_data`
  ADD PRIMARY KEY (`operation_id`);

--
-- Индексы таблицы `ticket_detail`
--
ALTER TABLE `ticket_detail`
  ADD PRIMARY KEY (`ticket_id`);

--
-- Индексы таблицы `ticket_refund`
--
ALTER TABLE `ticket_refund`
  ADD PRIMARY KEY (`ticket_id`);

--
-- Индексы таблицы `UnknownContactInfo`
--
ALTER TABLE `UnknownContactInfo`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `UnknownContactInfo_CallBackTime_index` (`CallBackTime`),
  ADD KEY `UnknownContactInfo_CallUserId_index` (`CallUserId`),
  ADD KEY `UnknownContactInfo_Email_index` (`Email`),
  ADD KEY `UnknownContactInfo_FIO_Phone_index` (`FIO`,`Phone`),
  ADD KEY `UnknownContactInfo_FIO_index` (`FIO`),
  ADD KEY `UnknownContactInfo_IsCallBack_CallBackTime_index` (`IsCallBack`,`CallBackTime`),
  ADD KEY `UnknownContactInfo_IsCallBack_index` (`IsCallBack`),
  ADD KEY `UnknownContactInfo_Phone_index` (`Phone`),
  ADD KEY `UnknownContactInfo_RequestDateTime_index` (`RequestDateTime`),
  ADD KEY `UnknownContactInfo_UnknownTerminalId_index` (`UnknownTerminalId`);

--
-- Индексы таблицы `UnknownTerminal`
--
ALTER TABLE `UnknownTerminal`
  ADD PRIMARY KEY (`Id`),
  ADD KEY `UnknownTerminal_FNIsPresent_index` (`FNIsPresent`),
  ADD KEY `UnknownTerminal_FNSerialNumber_index` (`FNSerialNumber`),
  ADD KEY `UnknownTerminal_RegNumber_index` (`RegNumber`),
  ADD KEY `UnknownTerminal_RequestDateTime_index` (`RequestDateTime`),
  ADD KEY `UnknownTerminal_SerialNumber_index` (`SerialNumber`);

--
-- Индексы таблицы `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`user_id`);

--
-- Индексы таблицы `vendor_api_config`
--
ALTER TABLE `vendor_api_config`
  ADD PRIMARY KEY (`vendor_id`),
  ADD UNIQUE KEY `partner_title` (`vendor_title`);

--
-- Индексы таблицы `vendor_conditions`
--
ALTER TABLE `vendor_conditions`
  ADD PRIMARY KEY (`vendor_condition_id`),
  ADD UNIQUE KEY `vendor_id_station_id_start_unique` (`vendor_id`,`station_id_start`),
  ADD KEY `vendor_id` (`vendor_id`),
  ADD KEY `vendor_id_station_start_id` (`vendor_id`,`station_id_start`);

--
-- Индексы таблицы `xref_agent_vendor`
--
ALTER TABLE `xref_agent_vendor`
  ADD PRIMARY KEY (`agent_id`,`vendor_id`);

--
-- Индексы таблицы `xref_city_partner`
--
ALTER TABLE `xref_city_partner`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `partner_id_outer_id` (`partner_id`,`outer_id`);

--
-- Индексы таблицы `xref_city_partner_moder`
--
ALTER TABLE `xref_city_partner_moder`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `xref_id_user_id` (`xref_id`,`user_id`);

--
-- Индексы таблицы `xref_group_role`
--
ALTER TABLE `xref_group_role`
  ADD PRIMARY KEY (`group_id`,`role_title`);

--
-- Индексы таблицы `xref_station_partner`
--
ALTER TABLE `xref_station_partner`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `partner_id_outer_id_outer_city_id_outer_station_title` (`partner_id`,`outer_station_id`,`outer_city_id`,`outer_station_title`);

--
-- Индексы таблицы `xref_user_group`
--
ALTER TABLE `xref_user_group`
  ADD PRIMARY KEY (`group_id`,`user_id`);

--
-- Индексы таблицы `_api_log`
--
ALTER TABLE `_api_log`
  ADD PRIMARY KEY (`id`);

--
-- Индексы таблицы `_api_outer_log`
--
ALTER TABLE `_api_outer_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `vendor_id_agent_id_result` (`vendor_id`,`agent_id`);

--
-- Индексы таблицы `_log_vendorapi_buy`
--
ALTER TABLE `_log_vendorapi_buy`
  ADD PRIMARY KEY (`id`),
  ADD KEY `date` (`date`,`vendor_id`,`try`,`result`);

--
-- Индексы таблицы `_log_vendorapi_checkride`
--
ALTER TABLE `_log_vendorapi_checkride`
  ADD PRIMARY KEY (`id`),
  ADD KEY `date` (`date`,`vendor_id`,`try`,`result`);

--
-- Индексы таблицы `_log_vendorapi_tmpbook`
--
ALTER TABLE `_log_vendorapi_tmpbook`
  ADD PRIMARY KEY (`id`),
  ADD KEY `date` (`date`,`vendor_id`,`try`,`result`);

--
-- AUTO_INCREMENT для сохранённых таблиц
--

--
-- AUTO_INCREMENT для таблицы `admin_user`
--
ALTER TABLE `admin_user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID пользователя';

--
-- AUTO_INCREMENT для таблицы `agent`
--
ALTER TABLE `agent`
  MODIFY `agent_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID агента';

--
-- AUTO_INCREMENT для таблицы `billing_rules`
--
ALTER TABLE `billing_rules`
  MODIFY `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `citizenship`
--
ALTER TABLE `citizenship`
  MODIFY `citizenship_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID страны';

--
-- AUTO_INCREMENT для таблицы `city`
--
ALTER TABLE `city`
  MODIFY `city_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID города';

--
-- AUTO_INCREMENT для таблицы `country`
--
ALTER TABLE `country`
  MODIFY `country_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID страны';

--
-- AUTO_INCREMENT для таблицы `dispatch_ticket_status`
--
ALTER TABLE `dispatch_ticket_status`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Уникальный идентификатор записи';

--
-- AUTO_INCREMENT для таблицы `EMVAbonementCounters`
--
ALTER TABLE `EMVAbonementCounters`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVAbonementList`
--
ALTER TABLE `EMVAbonementList`
  MODIFY `AbonementId` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVAbonementTransaction`
--
ALTER TABLE `EMVAbonementTransaction`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVBINList`
--
ALTER TABLE `EMVBINList`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVStopList`
--
ALTER TABLE `EMVStopList`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVWriteOffsBinding`
--
ALTER TABLE `EMVWriteOffsBinding`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `EMVWriteOffsTemplate`
--
ALTER TABLE `EMVWriteOffsTemplate`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `geo_action`
--
ALTER TABLE `geo_action`
  MODIFY `action_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID записи';

--
-- AUTO_INCREMENT для таблицы `geo_diff_log`
--
ALTER TABLE `geo_diff_log`
  MODIFY `geo_diff_log` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `helpdesk_comment`
--
ALTER TABLE `helpdesk_comment`
  MODIFY `helpdesk_comment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `helpdesk_request`
--
ALTER TABLE `helpdesk_request`
  MODIFY `helpdesk_request_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `HumanNameList`
--
ALTER TABLE `HumanNameList`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Уникальный идентификатор записи';

--
-- AUTO_INCREMENT для таблицы `invoice`
--
ALTER TABLE `invoice`
  MODIFY `invoice_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID счета';

--
-- AUTO_INCREMENT для таблицы `invoice_log`
--
ALTER TABLE `invoice_log`
  MODIFY `invoice_log_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID записи';

--
-- AUTO_INCREMENT для таблицы `operation`
--
ALTER TABLE `operation`
  MODIFY `operation_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID операции';

--
-- AUTO_INCREMENT для таблицы `operation_log`
--
ALTER TABLE `operation_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- AUTO_INCREMENT для таблицы `organization`
--
ALTER TABLE `organization`
  MODIFY `organization_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID организации';

--
-- AUTO_INCREMENT для таблицы `partner`
--
ALTER TABLE `partner`
  MODIFY `partner_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID партнера';

--
-- AUTO_INCREMENT для таблицы `payment`
--
ALTER TABLE `payment`
  MODIFY `payment_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID Выплаты';

--
-- AUTO_INCREMENT для таблицы `payment_log`
--
ALTER TABLE `payment_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- AUTO_INCREMENT для таблицы `push_buy_log`
--
ALTER TABLE `push_buy_log`
  MODIFY `log_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `reference`
--
ALTER TABLE `reference`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `ReferenceList`
--
ALTER TABLE `ReferenceList`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `region`
--
ALTER TABLE `region`
  MODIFY `region_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `request_change`
--
ALTER TABLE `request_change`
  MODIFY `request_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID заявки';

--
-- AUTO_INCREMENT для таблицы `request_partner`
--
ALTER TABLE `request_partner`
  MODIFY `request_partner_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID заявки';

--
-- AUTO_INCREMENT для таблицы `ride_search_notification`
--
ALTER TABLE `ride_search_notification`
  MODIFY `search_notification_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID уведомления';

--
-- AUTO_INCREMENT для таблицы `ride_segment`
--
ALTER TABLE `ride_segment`
  MODIFY `ride_segment_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID отрезка';

--
-- AUTO_INCREMENT для таблицы `sap`
--
ALTER TABLE `sap`
  MODIFY `sap_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ИД записи';

--
-- AUTO_INCREMENT для таблицы `search`
--
ALTER TABLE `search`
  MODIFY `search_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `search_job`
--
ALTER TABLE `search_job`
  MODIFY `job_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `seo_direction`
--
ALTER TABLE `seo_direction`
  MODIFY `direction_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID направления';

--
-- AUTO_INCREMENT для таблицы `services`
--
ALTER TABLE `services`
  MODIFY `service_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Уникальный идентификатор услуги';

--
-- AUTO_INCREMENT для таблицы `site_sync`
--
ALTER TABLE `site_sync`
  MODIFY `siteSyncID` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ИД записи';

--
-- AUTO_INCREMENT для таблицы `sopp_request`
--
ALTER TABLE `sopp_request`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Уникальный идентификатор записи';

--
-- AUTO_INCREMENT для таблицы `station`
--
ALTER TABLE `station`
  MODIFY `station_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID станции';

--
-- AUTO_INCREMENT для таблицы `station_merge_log`
--
ALTER TABLE `station_merge_log`
  MODIFY `log_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID лога';

--
-- AUTO_INCREMENT для таблицы `telegram_hook`
--
ALTER TABLE `telegram_hook`
  MODIFY `telegram_hook_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `TerminalActionLog`
--
ALTER TABLE `TerminalActionLog`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalGroup`
--
ALTER TABLE `TerminalGroup`
  MODIFY `Id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Идентификатор группы';

--
-- AUTO_INCREMENT для таблицы `TerminalList`
--
ALTER TABLE `TerminalList`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalLog`
--
ALTER TABLE `TerminalLog`
  MODIFY `TerminalLogId` int(11) NOT NULL AUTO_INCREMENT COMMENT 'пк';

--
-- AUTO_INCREMENT для таблицы `TerminalOrganizationList`
--
ALTER TABLE `TerminalOrganizationList`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalRawTransaction`
--
ALTER TABLE `TerminalRawTransaction`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalRoleFunction`
--
ALTER TABLE `TerminalRoleFunction`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalTransaction`
--
ALTER TABLE `TerminalTransaction`
  MODIFY `TransactionId` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ПК транзакции';

--
-- AUTO_INCREMENT для таблицы `TerminalUser`
--
ALTER TABLE `TerminalUser`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalUserPhoto`
--
ALTER TABLE `TerminalUserPhoto`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `TerminalUserRides`
--
ALTER TABLE `TerminalUserRides`
  MODIFY `Id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `ticket`
--
ALTER TABLE `ticket`
  MODIFY `ticket_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID билета';

--
-- AUTO_INCREMENT для таблицы `UnknownContactInfo`
--
ALTER TABLE `UnknownContactInfo`
  MODIFY `Id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `UnknownTerminal`
--
ALTER TABLE `UnknownTerminal`
  MODIFY `Id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Ключ записи';

--
-- AUTO_INCREMENT для таблицы `user`
--
ALTER TABLE `user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ИД записи';

--
-- AUTO_INCREMENT для таблицы `vendor_conditions`
--
ALTER TABLE `vendor_conditions`
  MODIFY `vendor_condition_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID условия';

--
-- AUTO_INCREMENT для таблицы `xref_city_partner`
--
ALTER TABLE `xref_city_partner`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `xref_city_partner_moder`
--
ALTER TABLE `xref_city_partner_moder`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `xref_station_partner`
--
ALTER TABLE `xref_station_partner`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `_api_log`
--
ALTER TABLE `_api_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `_api_outer_log`
--
ALTER TABLE `_api_outer_log`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `_log_vendorapi_buy`
--
ALTER TABLE `_log_vendorapi_buy`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `_log_vendorapi_checkride`
--
ALTER TABLE `_log_vendorapi_checkride`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT для таблицы `_log_vendorapi_tmpbook`
--
ALTER TABLE `_log_vendorapi_tmpbook`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- Ограничения внешнего ключа сохраненных таблиц
--

--
-- Ограничения внешнего ключа таблицы `city`
--
ALTER TABLE `city`
  ADD CONSTRAINT `fk_cities_co` FOREIGN KEY (`country_id`) REFERENCES `country` (`country_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_cities_t` FOREIGN KEY (`city_type_id`) REFERENCES `city_type` (`city_type_id`) ON UPDATE CASCADE;

--
-- Ограничения внешнего ключа таблицы `site_config`
--
ALTER TABLE `site_config`
  ADD CONSTRAINT `fk_site_config_u` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);

--
-- Ограничения внешнего ключа таблицы `site_sync`
--
ALTER TABLE `site_sync`
  ADD CONSTRAINT `fk_site_sync_c1` FOREIGN KEY (`departureCityID`) REFERENCES `city` (`city_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_site_sync_c2` FOREIGN KEY (`arrivalCityID`) REFERENCES `city` (`city_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_site_sync_p` FOREIGN KEY (`partnerID`) REFERENCES `partner` (`partner_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_site_sync_s1` FOREIGN KEY (`departureStationID`) REFERENCES `station` (`station_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_site_sync_s2` FOREIGN KEY (`arrivalStationID`) REFERENCES `station` (`station_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_site_sync_u` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON UPDATE CASCADE;

--
-- Ограничения внешнего ключа таблицы `site_sync_dates`
--
ALTER TABLE `site_sync_dates`
  ADD CONSTRAINT `fk_site_sync_dates` FOREIGN KEY (`siteSyncID`) REFERENCES `site_sync` (`siteSyncID`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Ограничения внешнего ключа таблицы `ticket`
--
ALTER TABLE `ticket`
  ADD CONSTRAINT `fk_ticket_a` FOREIGN KEY (`agent_id`) REFERENCES `agent` (`agent_id`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_ticket_p` FOREIGN KEY (`partner_id`) REFERENCES `partner` (`partner_id`) ON UPDATE CASCADE;

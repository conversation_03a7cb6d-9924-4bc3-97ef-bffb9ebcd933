--
-- Дамп данных таблицы `admin_role`
--

INSERT INTO `admin_role` (`role_title`, `role_description`) VALUES
('agent_manage', 'Управление агентами'),
('billing_rules_manage', 'Управления наценками в сочетании Агент - Вендор'),

('card_master', 'Администратор персобюро '),
('card_view', 'Пользователь персобюро'),
('city_manage', 'Управление городами'),
('city_merge', 'Мерж городов'),
('city_outer_manage', 'Управление городами пришедших из чужих API'),
('emv_manage', 'Управление абонементами'),
('helpdesk_manage', 'Управление техподдержкой'),
('manual_sync', 'Ручная синхронизация с НСИ'),
('map_route_view', 'Просмотр карты маршрутов'),
('operation_view', 'Просмотр операций с сайта'),
('outer_city_moder', 'Модератор для внешних городов'),
('partner_manage', 'Управление партнерами'),
('pay_manage', 'Управление выплатами'),
('pay_view', 'Просмотр выплат'),
('reference_manage', 'Редактирование справочников '),
('reference_view', 'Просмотр справочников'),
('seo_manage', 'Управление СЕО'),
('station_manage', 'Управление станциями'),
('station_merge', 'Мерж станций'),
('station_outer_manage', 'Управление станциями пришедших из чужих API'),
('stat_search_view', 'Просмотр статистики поиска'),
('supp_manage', 'Просмотр данных по выдаче УИР и УИПП'),
('supp_show_efsmkpp_name', 'Показывать название системы, как ЕФС МКПП'),
('terminal_master', 'Управление терминалами.'),
('terminal_view', 'Просмотр списка терминалов '),
('tickets_manage', 'Управление билетами'),
('ticket_refund_manual', 'Ручной возврат билета'),
('user_admin_manage', 'Управление администраторами'),
('vendor_conditions_manage', 'Добавление дополнительной инфрмации о рейсе вендора'),
('view_money_stat', 'Посмотр финансовой статистики'),
('view_telegram_hook', 'Просмотр сообщений из telegram');

--
-- Дамп данных таблицы `admin_user`
--

INSERT INTO `admin_user` (`user_id`, `login`, `email`, `password`, `access_token`, `name`, `is_deleted`) VALUES
(1, 'st_admin', '<EMAIL>', '9c0c023a0c2a376600a743b88117c0fe', '1-59112dfa92752663aa7dd795261b562f6a8338df540df72958a68e0d0b0e471ca7f2a927', 'Администратор', 0),
(2, 'st_install', '<EMAIL>', 'd793ca44623dbe2bacf6ca7bb0e5dfa4', '2-a1eb9bb422b9b1183a8c4d6aac4cb582e0813bc75d86a0fffbf2092c5088a7395e3252b0', 'Отдел внедрения', 0),
(3, 'st_support', '<EMAIL>', '1d0141fd4845f1753332ce1d3ef678b1', '3-23f72c3619cecde5f8fa99ae6e56110dd4dad878c5e2039ca0a35f32d9027ff5c374e8c6', 'Отдел сопровождения', 0),
(4, 'klabukov-dm', '<EMAIL>', '7558efb39427d95795a1fcbf77e3167a', '4-747401461d123676f76798c7a13a03dccd4f750b46d40dddf4aeb9876e23f2cb5589ced9', 'Клабуков Дмитрий', 0);

ALTER TABLE `admin_user` AUTO_INCREMENT=4;

--
-- Дамп данных таблицы `admin_xref_user_role`
--

INSERT INTO `admin_xref_user_role` (`user_id`, `role_title`) VALUES
(1, 'agent_manage'),
(1, 'city_manage'),
(1, 'city_merge'),
(1, 'city_outer_manage'),
(1, 'outer_city_moder'),
(1, 'partner_manage'),
(1, 'reference_view'),
(1, 'station_manage'),
(1, 'station_merge'),
(1, 'terminal_master'),
(1, 'terminal_view'),
(1, 'transaction_parse_error_view'),
(1, 'user_admin_manage'),
(2, 'city_manage'),
(2, 'city_merge'),
(2, 'city_outer_manage'),
(2, 'reference_manage'),
(2, 'station_manage'),
(2, 'station_merge'),
(2, 'station_outer_manage'),
(2, 'terminal_master'),
(2, 'terminal_view'),
(3, 'city_manage'),
(3, 'city_merge'),
(3, 'city_outer_manage'),
(3, 'reference_view'),
(3, 'station_manage'),
(3, 'station_merge'),
(3, 'station_outer_manage'),
(3, 'terminal_master'),
(3, 'terminal_view'),
(4, 'agent_manage'),
(4, 'city_manage'),
(4, 'city_merge'),
(4, 'city_outer_manage'),
(4, 'outer_city_moder'),
(4, 'partner_manage'),
(4, 'reference_view'),
(4, 'station_manage'),
(4, 'station_merge'),
(4, 'terminal_master'),
(4, 'terminal_view'),
(4, 'transaction_parse_error_view'),
(4, 'user_admin_manage');

--
-- Дамп данных таблицы `card_identity`
--

INSERT INTO `card_identity` (`card_identity_id`, `title`, `enabled_default`, `egis_doc_type_id`, `input_mask`, `reg_mask`, `use_numpad_only`, `nationality`, `picture_idx`, `use_for_child`) VALUES
(1, 'Паспорт гражданина Российской Федерации', 1, 0, '#### ######', '\\d{4} \\d{6}', 1, 1, 1, 0),
(2, 'Общегражданский заграничный паспорт гр. РФ', 1, 2, '#########', '\\d{9}', 1, 1, 2, 1),
(3, 'Паспорт иностранного гражданина', 1, 3, '#########################', '.{6,25}', 0, 2, 3, 1),
(4, 'Свидетельство о рождении', 1, 4, '*************************', '[IVXLCDM]{1,10}-[А-Яа-я]{2} \\d{6}', 0, 1, 4, 1),
(5, 'Военный билет военнослужащего срочной службы', 1, 8, '## #######', '[А-Яа-я]{2} \\d{6,7}', 0, 1, 5, 0),
(6, 'Удостоверение личности военнослужащего', 1, 5, '## #######', '[А-Яа-я]{2} \\d{6,7}', 0, 1, 6, 0),
(7, 'Свидетельство о возвращении из стран СНГ', 1, 14, '#########################', '.{6,25}', 0, 1, 7, 0),
(8, 'Дипломатический паспорт', 1, 12, '#########', '\\d{9}', 1, 0, 8, 0),
(9, 'Паспорт служебный(кроме паспорта моряка и дип.)', 1, 13, '## #######', '[А-Яа-я]{2} \\d{6,7}', 0, 0, 9, 0),
(10, 'Паспорт моряка', 1, 1, '## ######', '[А-Яа-я]{2} \\d{6}', 0, 0, 10, 0),
(11, 'Удостоверение личности лица без гражданства', 1, 6, '#########################', '.{6,25}', 0, 0, 11, 0),
(12, 'Вид на жительство', 1, 9, '#########################', '.{6,25}', 0, 1, 12, 0),
(13, 'Временное удостоверение личности, выдаваемое ОВД', 1, 7, '#########################', '.{6,25}', 0, 1, 13, 0),
(14, 'Паспорт СССР', 1, 11, '#-## ######', '[IVXLCDM]{1,10}-[А-Яа-я]{2} \\d{6}', 0, 1, 14, 0),
(15, 'Справка об освобождении из мест лишения свободы', 1, 10, '#########################', '.{6,25}', 0, 1, 15, 0),
(16, 'Справка об утере паспорта', 1, 15, '#########################', '.{6,25}', 0, 1, 16, 0),
(17, 'Свидетельство о предоставлении временного убежища', 1, 18, '##-### #######', '[А-Яа-я]{2}-\\d{3} \\d{7}', 0, 1, 17, 0),
(19, 'Другие', 1, 99, '#########################', '.{6,25}', 0, 0, 19, 0);

--
-- Дамп данных таблицы `citizenship`
--

INSERT INTO `citizenship` (`citizenship_id`, `title`, `code_lat_a2`, `code_num`) VALUES
(1, 'Абхазия', 'AB', '895'),
(2, 'Австралия', 'AU', '036'),
(3, 'Австрия', 'AT', '040'),
(4, 'Азербайджан', 'AZ', '031'),
(6, 'Албания', 'AL', '008'),
(7, 'Алжир', 'DZ', '012'),
(8, 'Американские Виргинские Острова', 'VI', '850'),
(9, 'Американское Самоа', 'AS', '016'),
(10, 'Ангилья', 'AI', '660'),
(11, 'Ангола', 'AO', '024'),
(12, 'Андорра', 'AD', '020'),
(13, 'Антарктида', 'AQ', '010'),
(14, 'Антигуа и Барбуда', 'AG', '028'),
(15, 'Аргентина', 'AR', '032'),
(16, 'Армения', 'AM', '051'),
(17, 'Аруба', 'AW', '533'),
(18, 'Афганистан', 'AF', '004'),
(19, 'Багамы', 'BS', '044'),
(20, 'Бангладеш', 'BD', '050'),
(21, 'Барбадос', 'BB', '052'),
(22, 'Бахрейн', 'BH', '048'),
(23, 'Беларусь', 'BY', '112'),
(24, 'Белиз', 'BZ', '084'),
(25, 'Бельгия', 'BE', '056'),
(26, 'Бенин', 'BJ', '204'),
(27, 'Бермуды', 'BM', '060'),
(28, 'Болгария', 'BG', '100'),
(29, 'Боливия', 'BO', '068'),
(30, 'Босния и Герцеговина', 'BA', '070'),
(31, 'Ботсвана', 'BW', '072'),
(32, 'Бразилия', 'BR', '076'),
(33, 'Британская территория в Индийском океане', 'IO', '086'),
(34, 'Бруней', 'BN', '096'),
(35, 'Буркина Фасо', 'BF', '854'),
(36, 'Бурунди', 'BI', '108'),
(37, 'Бутан', 'BT', '064'),
(38, 'Вануату', 'VU', '548'),
(39, 'Ватикан', 'VA', '336'),
(40, 'Великобритания', 'GB', '826'),
(41, 'Венгрия', 'HU', '348'),
(42, 'Венесуэла', 'VE', '862'),
(43, 'Виргинские Острова', 'VG', '092'),
(44, 'Внешние малые острова США', 'UM', '581'),
(45, 'Вьетнам', 'VN', '704'),
(46, 'Габон', 'GA', '266'),
(47, 'Гаити', 'HT', '332'),
(48, 'Гайана', 'GY', '328'),
(49, 'Гамбия', 'GM', '270'),
(50, 'Гана', 'GH', '288'),
(51, 'Гваделупа', 'GP', '312'),
(52, 'Гватемала', 'GT', '320'),
(53, 'Гвинея', 'GN', '324'),
(54, 'Гвинея-Бисау', 'GW', '624'),
(55, 'Германия', 'DE', '276'),
(56, 'Гернси', 'GG', '831'),
(57, 'Гибралтар', 'GI', '292'),
(58, 'Гондурас', 'HN', '340'),
(59, 'Гонконг', 'HK', '344'),
(60, 'Гренада', 'GD', '308'),
(61, 'Гренландия', 'GL', '304'),
(62, 'Греция', 'GR', '300'),
(63, 'Грузия', 'GE', '268'),
(64, 'Гуам', 'GU', '316'),
(65, 'Дания', 'DK', '208'),
(66, 'Джерси', 'JE', '832'),
(67, 'Джибути', 'DJ', '262'),
(68, 'Доминика', 'DM', '212'),
(69, 'Доминиканская Республика', 'DO', '214'),
(70, 'ДР Конго', 'CD', '180'),
(71, 'Египет', 'EG', '818'),
(72, 'Замбия', 'ZM', '894'),
(73, 'Западная Сахара', 'EH', '732'),
(74, 'Зимбабве', 'ZW', '716'),
(75, 'Израиль', 'IL', '376'),
(76, 'Индия', 'IN', '356'),
(77, 'Индонезия', 'ID', '360'),
(78, 'Иордания', 'JO', '400'),
(79, 'Ирак', 'IQ', '368'),
(80, 'Иран', 'IR', '364'),
(81, 'Ирландия', 'IE', '372'),
(82, 'Исландия', 'IS', '352'),
(83, 'Испания', 'ES', '724'),
(84, 'Италия', 'IT', '380'),
(85, 'Йемен', 'YE', '887'),
(86, 'Кабо-Верде', 'CV', '132'),
(87, 'Казахстан', 'KZ', '398'),
(88, 'Каймановы Острова', 'KY', '136'),
(89, 'Камбоджа', 'KH', '116'),
(90, 'Камерун', 'CM', '120'),
(91, 'Канада', 'CA', '124'),
(92, 'Катар', 'QA', '634'),
(93, 'Кения', 'KE', '404'),
(94, 'Кипр', 'CY', '196'),
(95, 'Киргизия', 'KG', '417'),
(96, 'Кирибати', 'KI', '296'),
(97, 'Китай', 'CN', '156'),
(98, 'Кокосовые Острова', 'CC', '166'),
(99, 'Колумбия', 'CO', '170'),
(100, 'Коморы', 'KM', '174'),
(101, 'Конго', 'CG', '178'),
(102, 'Коста-Рика', 'CR', '188'),
(103, 'Кот-д-Ивуар', 'CI', '384'),
(104, 'Куба', 'CU', '192'),
(105, 'Кувейт', 'KW', '414'),
(106, 'Лаос', 'LA', '418'),
(107, 'Латвия', 'LV', '428'),
(108, 'Лесото', 'LS', '426'),
(109, 'Либерия', 'LR', '430'),
(110, 'Ливан', 'LB', '422'),
(111, 'Ливия', 'LY', '434'),
(112, 'Литва', 'LT', '440'),
(113, 'Лихтенштейн', 'LE', '438'),
(114, 'Люксембург', 'LU', '442'),
(115, 'Маврикий', 'MU', '480'),
(116, 'Мавритания', 'MR', '478'),
(117, 'Мадагаскар', 'MG', '450'),
(118, 'Майотта', 'YT', '175'),
(119, 'Макао', 'MO', '446'),
(120, 'Македония', 'MK', '807'),
(121, 'Малави', 'MW', '454'),
(122, 'Малайзия', 'MY', '458'),
(123, 'Мали', 'ML', '466'),
(124, 'Мальдивы', 'MV', '462'),
(125, 'Мальта', 'MT', '470'),
(126, 'Марокко', 'MA', '504'),
(127, 'Мартиника', 'MQ', '474'),
(128, 'Маршалловы Острова', 'MH', '584'),
(129, 'Мексика', 'MX', '484'),
(130, 'Микронезия', 'FM', '583'),
(131, 'Мозамбик', 'MZ', '508'),
(132, 'Молдавия', 'MD', '498'),
(133, 'Монако', 'MC', '492'),
(134, 'Монголия', 'MN', '496'),
(135, 'Монтсеррат', 'MS', '500'),
(136, 'Мьянма', 'MM', '104'),
(137, 'Намибия', 'NA', '516'),
(138, 'Науру', 'NR', '520'),
(139, 'Непал', 'NP', '524'),
(140, 'Нигер', 'NE', '562'),
(141, 'Нигерия', 'NG', '566'),
(142, 'Нидерландские Антилы', 'AN', '532'),
(143, 'Нидерланды', 'NL', '528'),
(144, 'Никарагуа', 'NI', '558'),
(145, 'Ниуэ', 'NU', '570'),
(146, 'Новая Зеландия', 'NZ', '554'),
(147, 'Новая Каледония', 'NC', '540'),
(148, 'Норвегия', 'NO', '578'),
(149, 'Норфолк', 'NF', '574'),
(150, 'ОАЭ', 'AE', '784'),
(151, 'Оман', 'OM', '512'),
(152, 'Остров Буве', 'BV', '074'),
(153, 'Остров Мэн', 'IM', '833'),
(154, 'Остров Рождества', 'CX', '162'),
(155, 'Остров Херд и Острова Макдональд', 'HM', '334'),
(156, 'Острова Кука', 'CK', '184'),
(157, 'Острова Теркс и Кайкос', 'TC', '796'),
(158, 'Пакистан', 'PK', '586'),
(159, 'Палау', 'PW', '585'),
(161, 'Панама', 'PA', '591'),
(162, 'Папуа-Новая Гвинея', 'PG', '598'),
(163, 'Парагвай', 'PY', '600'),
(164, 'Перу', 'PE', '604'),
(165, 'Питкерн', 'PN', '612'),
(166, 'Польша', 'PL', '616'),
(167, 'Португалия', 'PT', '620'),
(168, 'Пуэрто-Рико', 'PR', '630'),
(169, 'Реюньон', 'RE', '638'),
(170, 'Россия', 'RU', '643'),
(171, 'Руанда', 'RW', '646'),
(172, 'Румыния', 'RO', '642'),
(173, 'Сальвадор', 'SV', '222'),
(174, 'Самоа', 'WS', '882'),
(175, 'Сан-Марино', 'SM', '674'),
(176, 'Сан-Томе и Принсипи', 'ST', '678'),
(177, 'Саудовская аравия', 'SA', '682'),
(178, 'Свазиленд', 'SZ', '748'),
(179, 'Святая Елена', 'SH', '654'),
(180, 'Северная Корея', 'KP', '408'),
(181, 'Северные Марианские Острова', 'MP', '580'),
(182, 'Сейшелы', 'SC', '690'),
(183, 'Сен-Бартелеми', 'BL', '652'),
(184, 'Сен-Мартен', 'MF', '663'),
(185, 'Сен-Пьер и Микелон', 'PM', '666'),
(186, 'Сенегал', 'SN', '686'),
(187, 'Сент-Винсент и Гренадины', 'VC', '670'),
(188, 'Сент-Китс и Невис', 'KN', '659'),
(189, 'Сент-Люсия', 'LC', '662'),
(190, 'Сербия', 'RS', '688'),
(191, 'Сингапур', 'SG', '702'),
(192, 'Сирия', 'SY', '760'),
(193, 'Словакия', 'SK', '703'),
(194, 'Словения', 'SI', '705'),
(195, 'Соломоновы острова', 'SB', '090'),
(196, 'Сомали', 'SO', '706'),
(197, 'Судан', 'SD', '736'),
(198, 'Суринам', 'SR', '740'),
(199, 'США', 'US', '840'),
(200, 'Сьерра-леоне', 'SL', '694'),
(201, 'Таджикистан', 'TJ', '762'),
(202, 'Таиланд', 'TH', '764'),
(203, 'Тайвань', 'TW', '158'),
(204, 'Танзания', 'TZ', '834'),
(205, 'Тимор-Лесте', 'TP', '626'),
(206, 'Того', 'TG', '768'),
(207, 'Токелау', 'TK', '772'),
(208, 'Тонга', 'TO', '776'),
(209, 'Тринидад и Тобаго', 'TT', '780'),
(210, 'Тувалу', 'TV', '798'),
(211, 'Тунис', 'TN', '788'),
(212, 'Туркмения', 'TM', '795'),
(213, 'Турция', 'TR', '792'),
(214, 'Уганда', 'UG', '800'),
(215, 'Узбекистан', 'UZ', '860'),
(216, 'Украина', 'UA', '804'),
(217, 'Уоллис и Футуна', 'WF', '876'),
(218, 'Уругвай', 'UY', '858'),
(219, 'Фарерские Острова', 'FO', '234'),
(220, 'Фиджи', 'FJ', '242'),
(221, 'Филиппины', 'PH', '608'),
(222, 'Финляндия', 'FI', '246'),
(223, 'Фолклендские Острова', 'FK', '238'),
(224, 'Франция', 'FR', '250'),
(225, 'Французская Гвиана', 'GF', '254'),
(226, 'Французская Полинезия', 'PF', '258'),
(227, 'Французские Южные Территории', 'TF', '260'),
(228, 'Хорватия', 'HR', '191'),
(229, 'ЦАР', 'CF', '140'),
(230, 'Чад', 'TD', '148'),
(231, 'Черногория', 'ME', '499'),
(232, 'Чехия', 'CZ', '203'),
(233, 'Чили', 'CL', '152'),
(234, 'Швейцария', 'CH', '756'),
(235, 'Швеция', 'SE', '752'),
(236, 'Шпицберген и Ян Майен', 'SJ', '744'),
(237, 'Шри-Ланка', 'LK', '144'),
(238, 'Эквадор', 'EC', '218'),
(239, 'Экваториальная Гвинея', 'GQ', '226'),
(240, 'Эритрея', 'ER', '232'),
(241, 'Эстония', 'EE', '233'),
(242, 'Эфиопия', 'ET', '231'),
(243, 'Южная Африка', 'ZA', '710'),
(244, 'Южная Джорджия и Южные Сандвичевы Острова', 'GS', '239'),
(245, 'Южная Корея', 'KR', '410'),
(246, 'Южная Осетия', 'OS', '896'),
(247, 'Ямайка', 'JM', '388'),
(248, 'Япония', 'JP', '392');

--
-- Дамп данных таблицы `city_type`
--

INSERT INTO `city_type` (`city_type_id`, `title`, `title_short`, `city_size`, `coordinates_error`) VALUES
(1, 'Город', 'г', 2, 0.1),
(2, 'Город федерального значения', 'г', 1, 0.1),
(3, 'Село', 'с', 4, 0.05),
(4, 'Деревня', 'д', 5, 0.05),
(5, 'Поселок городского типа', 'пгт', 3, 0.05),
(6, 'Хутор', 'хут', 6, 0.05),
(7, 'Поселок сельского типа', 'пст', 4, 0.05),
(8, 'Поселок', 'пос', 4, 0.05),
(9, 'Садовое товарищество', 'сад.т', 7, 0.05),
(10, 'Населенный пункт', 'нп', 7, 0.05),
(11, 'Станция', 'ст', 8, 0.05),
(12, 'Коттеджный поселок', 'кот.п', 8, 0.05),
(13, 'Жилой комплекс', 'жк', 9, 0.05),
(14, 'Станица', 'ст-ца', 5, 0.05),
(15, 'Поселок при станции', 'п/ст', 5, 0.05),
(16, 'Дачный поселок', 'дп', 7, 0.05),
(17, 'Рабочий поселок', 'рп', 5, 0.05);

--
-- Дамп данных таблицы `country`
--

INSERT INTO `country` (`country_id`, `title`, `code_a2`, `timezone`) VALUES
(1, 'Абхазия', 'AB', 'Asia/Tbilisi'),
(2, 'Австралия', 'AU', 'Australia/Darwin'),
(3, 'Австрия', 'AT', 'Europe/Vienna'),
(4, 'Азербайджан', 'AZ', 'Asia/Baku'),
(5, 'Аландские острова', 'AX', NULL),
(6, 'Албания', 'AL', 'Europe/Tirane'),
(7, 'Алжир', 'DZ', 'Africa/Algiers'),
(9, 'Американское Самоа', 'AS', NULL),
(10, 'Ангилья', 'AI', NULL),
(11, 'Ангола', 'AO', NULL),
(12, 'Андорра', 'AD', NULL),
(13, 'Антигуа', NULL, NULL),
(14, 'Антигуа и Барбуда', 'AG', NULL),
(15, 'Нидерландские Антильские острова', 'AN', NULL),
(16, 'Аргентина', 'AR', 'America/Argentina/Buenos_Aires'),
(17, 'Армения', 'AM', 'Asia/Yerevan'),
(18, 'Аруба', 'AW', NULL),
(19, 'Афганистан', 'AF', 'Asia/Kabul'),
(20, 'Багамские острова', 'BS', NULL),
(21, 'Бангладеш', 'BD', 'Asia/Dacca'),
(22, 'Барбадос', 'BB', 'America/Barbados'),
(23, 'Бахрейн', 'BH', 'Asia/Bahrain'),
(24, 'Белоруссия', 'BY', 'Europe/Minsk'),
(25, 'Белиз', 'BZ', NULL),
(26, 'Бельгия', 'BE', 'Europe/Brussels'),
(27, 'Бенин', 'BJ', 'Africa/Porto-Novo'),
(28, 'Бермудские острова', 'BM', NULL),
(29, 'Болгария', 'BG', 'Europe/Sofia'),
(30, 'Боливия', 'BO', 'America/La_Paz'),
(31, 'Бонайре', 'BQ', NULL),
(32, 'Босния и Герцеговина', 'BA', 'Europe/Sarajevo'),
(33, 'Ботсвана', 'BW', NULL),
(34, 'Бразилия', 'BR', 'America/Cuiaba'),
(35, 'Британская территория в Индийском океане', 'IO', NULL),
(36, 'Бруней', 'BN', NULL),
(37, 'Буве остров', 'BV', NULL),
(38, 'Буркина-Фасо', 'BF', 'Africa/Ouagadougou'),
(39, 'Бурунди', 'BI', NULL),
(41, 'Вануату', 'VU', NULL),
(42, 'Ватикан', 'VA', NULL),
(43, 'Великобритания', 'GB', 'Europe/London'),
(44, 'Венгрия', 'HU', 'Europe/Budapest'),
(45, 'Венесуэла', 'VE', 'America/Caracas'),
(46, 'Виргинские острова, Британские', 'VG', NULL),
(47, 'Виргинские острова, США', 'VI', 'America/St_Thomas'),
(48, 'Восточный Тимор', 'TL', 'Asia/Dili'),
(49, 'Вьетнам', 'VN', 'Asia/Saigon'),
(50, 'Габон', 'GA', NULL),
(51, 'Гаити', 'HT', 'America/Port-au-Prince '),
(52, 'Гайана', 'GY', NULL),
(53, 'Гамбия', 'GM', NULL),
(54, 'Гана', 'GH', 'Africa/Accra'),
(55, 'Гваделупа', 'GP', NULL),
(56, 'Гватемала', 'GT', 'America/Guatemala'),
(57, 'Гвинея', 'GN', 'Africa/Conakry'),
(58, 'Гвинея-Бисау', 'GW', 'Africa/Bissau'),
(59, 'Германия', 'DE', 'Europe/Berlin'),
(60, 'Гибралтар', 'GI', NULL),
(61, 'Гондурас', 'HN', NULL),
(62, 'Гонконг', 'HK', NULL),
(63, 'Гренада', 'GD', NULL),
(64, 'Гренландия', 'GL', NULL),
(65, 'Греция', 'GR', 'Europe/Athens'),
(66, 'Грузия', 'GE', 'Asia/Tbilisi'),
(67, 'Гуам', 'GU', NULL),
(69, 'Дания', 'DK', 'Europe/Copenhagen'),
(70, 'Демократическая Республика Конго (Заир)', 'CD', 'Africa/Kinshasa'),
(71, 'Джибути', 'DJ', NULL),
(72, 'Доминика', 'DM', NULL),
(73, 'Доминиканская Республика', 'DO', NULL),
(74, 'Египет', 'EG', 'Africa/Cairo'),
(76, 'Замбия', 'ZM', NULL),
(77, 'Западная Сахара', 'EH', NULL),
(78, 'Зимбабве', 'ZW', 'Africa/Harare'),
(79, 'Израиль', 'IL', 'Asia/Tel_Aviv'),
(80, 'Индия', 'IN', 'Asia/Kolkata '),
(81, 'Индонезия', 'ID', 'Asia/Jakarta'),
(82, 'Иордания', 'JO', 'Asia/Amman'),
(83, 'Ирак', 'IQ', 'Asia/Baghdad'),
(84, 'Иран', 'IR', 'Asia/Tehran'),
(85, 'Ирландия', 'IE', 'Europe/Dublin'),
(86, 'Исландия', 'IS', NULL),
(87, 'Испания', 'ES', 'Europe/Madrid'),
(88, 'Италия', 'IT', 'Europe/Rome'),
(89, 'Йемен', 'YE', 'Asia/Aden'),
(91, 'Казахстан', 'KZ', 'Asia/Oral'),
(92, 'Каймановы острова', 'KY', NULL),
(93, 'Камбоджа', 'KH', 'Asia/Phnom_Penh'),
(94, 'Камерун', 'CM', 'Africa/Douala'),
(95, 'Канада', 'CA', 'America/Chicago'),
(96, 'Кабо-Верде', 'CV', NULL),
(97, 'Катар', 'QA', NULL),
(98, 'Кения', 'KE', NULL),
(99, 'Кипр', 'CY', NULL),
(100, 'Киргизия', 'KG', 'Asia/Bishkek'),
(101, 'Кирибати', 'KI', NULL),
(102, 'Китай (КНР)', 'CN', 'Asia/Shanghai'),
(103, 'Кокосовые острова', 'CC', NULL),
(104, 'Колумбия', 'CO', NULL),
(105, 'Коморские острова', 'KM', NULL),
(106, 'Республика Конго', 'CG', 'Africa/Brazzaville'),
(107, 'Северная Корея (КНДР)', 'KP', NULL),
(108, 'Королевство Бутан', 'BT', 'Asia/Thimphu'),
(109, 'Косово', NULL, NULL),
(110, 'Коста-Рика', 'CR', 'America/Costa_Rica'),
(111, 'Кот-д`Ивуар', 'CI', 'Africa/Abidjan'),
(112, 'Крит', NULL, NULL),
(113, 'Куба', 'CU', 'America/Havana'),
(114, 'Кувейт', 'KW', NULL),
(115, 'Кука острова', 'CK', NULL),
(116, 'Кюрасао', 'CW', NULL),
(117, 'Лаос', 'LA', 'Asia/Vientiane'),
(118, 'Латвия', 'LV', 'Europe/Riga'),
(119, 'Лесото', 'LS', NULL),
(120, 'Либерия', 'LR', NULL),
(121, 'Ливан', 'LB', 'Asia/Beirut'),
(122, 'Ливия', 'LY', 'Africa/Tripoli'),
(123, 'Литва', 'LT', 'Europe/Vilnius'),
(124, 'Лихтенштейн', 'LI', NULL),
(125, 'Люксембург', 'LU', 'Europe/Luxembourg'),
(126, 'Маврикий', 'MU', 'Indian/Mauritius'),
(127, 'Мавритания', 'MR', NULL),
(128, 'Мадагаскар', 'MG', NULL),
(129, 'Майотта', 'YT', NULL),
(130, 'Макао', 'MO', NULL),
(132, 'Македония', 'MK', 'Europe/Skopje'),
(133, 'Малави', 'MW', NULL),
(134, 'Малайзия', 'MY', 'Asia/Kuala_Lumpur'),
(135, 'Мали', 'ML', 'Africa/Bamako'),
(136, 'Мальдивы', 'MV', NULL),
(137, 'Мальта', 'MT', NULL),
(138, 'Марокко', 'MA', 'Africa/Casablanca'),
(139, 'Мартиника', 'MQ', NULL),
(140, 'Маршаловы острова', 'MH', NULL),
(141, 'Мексика', 'MX', 'America/Mexico_City'),
(142, 'Микронезия', 'FM', NULL),
(143, 'Мозамбик', 'MZ', 'Africa/Maputo '),
(144, 'Молдавия', 'MD', 'Europe/Tiraspol'),
(145, 'Монако', 'MC', 'Europe/Monaco'),
(146, 'Монголия', 'MN', 'Asia/Ulan_Bator'),
(147, 'Монтсеррат', 'MS', NULL),
(148, 'Мьянма', 'MM', 'Asia/Rangoon'),
(149, 'Намибия', 'NA', NULL),
(150, 'Ниуэ', 'NU', NULL),
(151, 'Науру', 'NR', NULL),
(152, 'Непал', 'NP', 'Asia/Kathmandu'),
(153, 'Нигер', 'NE', 'Africa/Niamey'),
(154, 'Нигерия', 'NG', 'Africa/Lagos'),
(156, 'Нидерланды', 'NL', 'Europe/Amsterdam'),
(157, 'Никарагуа', 'NI', NULL),
(159, 'Новая Зеландия', 'NZ', 'Pacific/Auckland'),
(160, 'Новая Каледония', 'NC', NULL),
(161, 'Норвегия', 'NO', 'Europe/Oslo'),
(162, 'Норфолк остров', 'NF', NULL),
(163, 'Объединенные Арабские Эмираты (ОАЭ)', 'AE', NULL),
(164, 'Оман', 'OM', 'Asia/Muscat'),
(165, 'Пакистан', 'PK', 'Asia/Karachi'),
(166, 'Палау', 'PW', NULL),
(167, 'Панама', 'PA', NULL),
(168, 'Папуа-Новая Гвинея', 'PG', 'Pacific/Port_Moresby'),
(169, 'Парагвай', 'PY', 'America/Asuncion'),
(170, 'Перу', 'PE', 'America/Lima'),
(171, 'Питкерн остров', 'PN', NULL),
(172, 'Польша', 'PL', 'Europe/Warsaw'),
(173, 'Португалия', 'PT', 'Europe/Lisbon'),
(174, 'Пуэрто-Рико', 'PR', 'America/Puerto_Rico'),
(175, 'Реюньон (Франция)', 'RE', NULL),
(176, 'Рождества остров', 'CX', NULL),
(177, 'Россия', 'RU', 'Europe/Moscow'),
(178, 'Руанда', 'RW', 'Africa/Kigali'),
(179, 'Румыния', 'RO', 'Europe/Bucharest'),
(180, 'Сальвадор', 'SV', 'America/El_Salvador'),
(181, 'Самоа', 'WS', NULL),
(182, 'Сен-Мартен (Франция)', 'MF', NULL),
(183, 'Сан-Марино', 'SM', NULL),
(184, 'Сан-Томе и Принсипи', 'ST', 'Africa/Sao_Tome'),
(185, 'Саудовская Аравия', 'SA', NULL),
(186, 'Св.Елены остров', 'SH', NULL),
(187, 'Свазиленд', 'SZ', NULL),
(188, 'Северные Марианские острова', 'MP', 'Pacific/Saipan'),
(189, 'Турецкая Республика Северного Кипра (ТРСК)', NULL, NULL),
(190, 'Сейшельские острова', 'SC', NULL),
(191, 'Сенегал', 'SN', 'Africa/Dakar'),
(192, 'Сен-Бартелеми', 'BL', NULL),
(193, 'Сент-Китс и Невис', 'KN', NULL),
(194, 'Сент-Винсент и Гренадины', 'VC', NULL),
(195, 'Сент-Люсия', 'LC', NULL),
(196, 'Сент-Пьер и Микелон', 'PM', NULL),
(197, 'Сербия', 'RS', 'Europe/Belgrade'),
(198, 'Сингапур', 'SG', NULL),
(199, 'Сирия', 'SY', 'Asia/Damascus'),
(200, 'Словакия', 'SK', 'Europe/Bratislava'),
(201, 'Словения', 'SI', 'Europe/Ljubljana'),
(202, 'Соломоновы острова', 'SB', NULL),
(203, 'Сомали', 'SO', 'Africa/Mogadishu'),
(204, 'Судан', 'SD', 'Africa/Khartoum'),
(205, 'Суринам', 'SR', 'America/Paramaribo'),
(206, 'Соединенные Штаты Америки (США)', 'US', 'America/New_York'),
(207, 'Сьерра-Леоне', 'SL', 'Africa/Freetown'),
(208, 'Таджикистан', 'TJ', 'Asia/Dushanbe'),
(209, 'Таиланд', 'TH', 'Asia/Bangkok'),
(210, 'Тайвань', 'TW', 'Asia/Taipei'),
(211, 'Танзания', 'TZ', NULL),
(212, 'Теркс и Кайкос острова', 'TC', NULL),
(214, 'Того', 'TG', NULL),
(215, 'Токелау', 'TK', NULL),
(216, 'Тонга', 'TO', 'Pacific/Tongatapu'),
(217, 'Тринидад и Тобаго', 'TT', 'America/Port_of_Spain '),
(218, 'Тувалу', 'TV', NULL),
(219, 'Тунис', 'TN', 'Africa/Tunis'),
(220, 'Туркменистан', 'TM', 'Asia/Ashgabat'),
(221, 'Турция', 'TR', 'Europe/Istanbul'),
(222, 'Уганда', 'UG', NULL),
(223, 'Узбекистан', 'UZ', 'Asia/Tashkent'),
(224, 'Украина', 'UA', 'Europe/Kiev'),
(225, 'Уоллис и Футуна острова', 'WF', NULL),
(226, 'Уругвай', 'UY', 'America/Montevideo'),
(227, 'Фиджи', 'FJ', NULL),
(228, 'Филиппины', 'PH', 'Asia/Manila'),
(229, 'Финляндия', 'FI', 'Europe/Helsinki'),
(230, 'Фолклендские острова (Мальвинские)', 'FK', 'Atlantic/Stanley'),
(231, 'Франция', 'FR', 'Europe/Paris'),
(232, 'Французская Гвиана', 'GF', NULL),
(233, 'Французская Полинезия', 'PF', NULL),
(234, 'Хорватия', 'HR', 'Europe/Zagreb'),
(235, 'Центрально-Африканская Республика (ЦАР)', 'CF', 'Africa/Bangui'),
(236, 'Чад', 'TD', 'Africa/Ndjamena'),
(237, 'Черногория', 'ME', 'Europe/Podgorica'),
(238, 'Чехия', 'CZ', 'Europe/Prague'),
(239, 'Чили', 'CL', NULL),
(240, 'Швейцария', 'CH', 'Europe/Zurich'),
(241, 'Швеция', 'SE', 'Europe/Stockholm'),
(242, 'Шри-Ланка', 'LK', 'Asia/Colombo'),
(243, 'Эквадор', 'EC', 'America/Guayaquil'),
(244, 'Экваториальная Гвинея', 'GQ', NULL),
(245, 'Эритрея', 'ER', 'Africa/Asmara'),
(246, 'Эстония', 'EE', 'Europe/Tallinn'),
(247, 'Эфиопия', 'ET', 'Africa/Addis_Ababa'),
(248, 'Южные Сендвичевы острова', 'GS', NULL),
(249, 'Южная Корея', 'KR', 'Asia/Seoul'),
(250, 'Южно-Африканская Республика (ЮАР)', 'ZA', 'Africa/Johannesburg'),
(251, 'Ямайка', 'JM', NULL),
(252, 'Ян-Майен остров', 'SJ', NULL),
(253, 'Япония', 'JP', 'Asia/Tokyo'),
(254, 'Таити', NULL, NULL),
(255, 'Фарерские острова', 'FO', NULL),
(256, 'Бутан', NULL, NULL),
(257, 'Палестина', 'PS', NULL),
(258, 'Сен-Мартен (Нидерланды)', 'SX', NULL),
(259, 'Сербия и Черногория', NULL, NULL),
(260, 'Северная Ирландия', NULL, NULL),
(261, '', NULL, NULL),
(263, 'Южный Судан', 'SS', 'Africa/Khartoum'),
(264, 'ОСТРОВ МЭН', 'IM', 'Europe/Isle_of_Man'),
(266, 'ОСТРОВ ХЕРД И ОСТРОВА МАКДОНАЛЬД', 'HM', NULL),
(267, 'АНТАРКТИДА', 'AQ', NULL),
(268, 'ДЖЕРСИ', 'JE', NULL),
(269, 'ГЕРНСИ', 'GG', NULL),
(270, 'ФРАНЦУЗСКИЕ ЮЖНЫЕ ТЕРРИТОРИИ', 'TF', NULL),
(271, 'МАЛЫЕ ТИХООКЕАНСКИЕ ОТДАЛЕННЫЕ ОСТРОВА СШ', 'UM', NULL),
(10001, 'Абхазия', 'AB', 'Asia/Tbilisi');

--
-- Дамп данных таблицы `city`
--
/*
INSERT INTO `city` (`city_id`, `city_type_id`, `country_id`, `region`, `district`, `title`, `title_genitive`, `title_accusative`, `title_prepositional`, `title_lat`, `title_genitive_lat`, `title_accusative_lat`, `title_prepositional_lat`, `lng`, `lat`, `city_title_search`, `is_verified`, `code`, `region_id`) VALUES
(1, 1, 177, '', '', 'Москва', NULL, NULL, NULL, 'moskva', NULL, NULL, NULL, '37.617635', '55.755814', 'Москва', 1, NULL, NULL),
(2, 1, 177, 'Ярославская область', '', 'Ярославль', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '39.89489065046975', '57.626435545714614', 'Ярославль', 1, NULL, NULL),
(3, 1, 177, 'Кировская область', '', 'Киров', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '49.668014', '58.603591', 'Киров', 1, NULL, NULL),
(4, 1, 177, 'Алтайский край', 'муниципальное образование Город Барнаул', 'Барнаул', NULL, NULL, NULL, 'barnaul', NULL, NULL, NULL, '83.75753402709961', '53.350961187161886', 'Барнаул', 1, NULL, NULL),
(5, 1, 177, 'Ярославская область', '', 'Переславль-Залесский', NULL, NULL, NULL, 'pereslavl-zalesskiy', NULL, NULL, NULL, '38.85298', '56.739613', 'Переславль-Залесский', 1, NULL, NULL),
(6, 1, 177, 'Ярославская область', 'Ростовский район', 'Ростов', NULL, NULL, NULL, 'rostov', NULL, NULL, NULL, '39.414526', '57.185866', 'Ростов', 1, NULL, NULL),
(8, 1, 177, 'Белгородская область', 'городской округ Старооскольский', 'Старый Оскол', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '37.833447', '51.298075', 'Старый Оскол', 0, NULL, NULL),
(9, 1, 177, 'Белгородская область', 'Валуйский городской округ', 'Валуйки', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '38.099914', '50.211194', 'Валуйки', 0, NULL, NULL),
(10, 1, 177, 'Московская область', 'Одинцовский городской округ', 'Звенигород', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '36.86685993152291', '55.73115365070425', 'Звенигород', 0, NULL, NULL);
*/

--
-- Дамп данных таблицы `HumanNameList`
--

INSERT INTO `HumanNameList` (`id`, `name`, `gender`, `country_id`) VALUES
(1, 'Абрам', 1, 170),
(2, 'Аваз', 1, 170),
(3, 'Аввакум', 1, 170),
(4, 'Август', 1, 170),
(5, 'Авдей', 1, 170),
(6, 'Авраам', 1, 170),
(7, 'Автандил', 1, 170),
(8, 'Агап', 1, 170),
(9, 'Агафон', 1, 170),
(10, 'Аггей', 1, 170),
(11, 'Адам', 1, 170),
(12, 'Адис', 1, 170),
(13, 'Адольф', 1, 170),
(14, 'Адриан', 1, 170),
(15, 'Азамат', 1, 170),
(16, 'Азарий', 1, 170),
(17, 'Азат', 1, 170),
(18, 'Айдар', 1, 170),
(19, 'Айнур', 1, 170),
(20, 'Айрат', 1, 170),
(21, 'Акакий', 1, 170),
(22, 'Аким', 1, 170),
(23, 'Алан', 1, 170),
(24, 'Александр', 1, 170),
(25, 'Алексей', 1, 170),
(26, 'Али', 1, 170),
(27, 'Алихан', 1, 170),
(28, 'Алмаз', 1, 170),
(29, 'Альберт', 1, 170),
(30, 'Альфред', 1, 170),
(31, 'Амадей', 1, 170),
(32, 'Амадеус', 1, 170),
(33, 'Амаяк', 1, 170),
(34, 'Амин', 1, 170),
(35, 'Амвросий', 1, 170),
(36, 'Анатолий', 1, 170),
(37, 'Анвар', 1, 170),
(38, 'Ангел', 1, 170),
(39, 'Андрей', 1, 170),
(40, 'Андрэ', 1, 170),
(41, 'Аникита', 1, 170),
(42, 'Антон', 1, 170),
(43, 'Ануфрий', 1, 170),
(44, 'Анфим', 1, 170),
(45, 'Аполлинарий', 1, 170),
(46, 'Арам', 1, 170),
(47, 'Аристарх', 1, 170),
(48, 'Аркадий', 1, 170),
(49, 'Арман', 1, 170),
(50, 'Армен', 1, 170),
(51, 'Арно', 1, 170),
(52, 'Арнольд', 1, 170),
(53, 'Арон', 1, 170),
(54, 'Арсен', 1, 170),
(55, 'Арсений', 1, 170),
(56, 'Арслан', 1, 170),
(57, 'Артем', 1, 170),
(58, 'Артемий', 1, 170),
(59, 'Артур', 1, 170),
(60, 'Архип', 1, 170),
(61, 'Аскольд', 1, 170),
(62, 'Афанасий', 1, 170),
(63, 'Ахмет', 1, 170),
(64, 'Ашот', 1, 170),
(65, 'Бахрам', 1, 170),
(66, 'Бежен', 1, 170),
(67, 'Бенедикт', 1, 170),
(68, 'Берек', 1, 170),
(69, 'Бернар', 1, 170),
(70, 'Богдан', 1, 170),
(71, 'Боголюб', 1, 170),
(72, 'Бонифаций', 1, 170),
(73, 'Бореслав', 1, 170),
(74, 'Борис', 1, 170),
(75, 'Борислав', 1, 170),
(76, 'Боян', 1, 170),
(77, 'Бронислав', 1, 170),
(78, 'Бруно', 1, 170),
(79, 'Булат', 1, 170),
(80, 'Вадим', 1, 170),
(81, 'Валентин', 1, 170),
(82, 'Валерий', 1, 170),
(83, 'Вальдемар', 1, 170),
(84, 'Вальтер', 1, 170),
(85, 'Вардан', 1, 170),
(86, 'Варлаам', 1, 170),
(87, 'Варфоломей', 1, 170),
(88, 'Василий', 1, 170),
(89, 'Ватслав', 1, 170),
(90, 'Велизар', 1, 170),
(91, 'Велор', 1, 170),
(92, 'Венедикт', 1, 170),
(93, 'Вениамин', 1, 170),
(94, 'Викентий', 1, 170),
(95, 'Виктор', 1, 170),
(96, 'Вилен', 1, 170),
(97, 'Вилли', 1, 170),
(98, 'Вильгельм', 1, 170),
(99, 'Виссарион', 1, 170),
(100, 'Виталий', 1, 170),
(101, 'Витаутас', 1, 170),
(102, 'Витольд', 1, 170),
(103, 'Владимир', 1, 170),
(104, 'Владислав', 1, 170),
(105, 'Владлен', 1, 170),
(106, 'Влас', 1, 170),
(107, 'Володар', 1, 170),
(108, 'Всеволод', 1, 170),
(109, 'Вячеслав', 1, 170),
(110, 'Гавриил', 1, 170),
(111, 'Галактион', 1, 170),
(112, 'Гамлет', 1, 170),
(113, 'Гарри', 1, 170),
(114, 'Гаяс', 1, 170),
(115, 'Гевор', 1, 170),
(116, 'Геворг', 1, 170),
(117, 'Геннадий', 1, 170),
(118, 'Генри', 1, 170),
(119, 'Генрих', 1, 170),
(120, 'Георгий', 1, 170),
(121, 'Геральд', 1, 170),
(122, 'Герасим', 1, 170),
(123, 'Герман', 1, 170),
(124, 'Глеб', 1, 170),
(125, 'Гоар', 1, 170),
(126, 'Гордей', 1, 170),
(127, 'Гордон', 1, 170),
(128, 'Горислав', 1, 170),
(129, 'Градимир', 1, 170),
(130, 'Григорий', 1, 170),
(131, 'Гурий', 1, 170),
(132, 'Густав', 1, 170),
(133, 'Давид', 1, 170),
(134, 'Давлат', 1, 170),
(135, 'Дамир', 1, 170),
(136, 'Даниил', 1, 170),
(137, 'Данислав', 1, 170),
(138, 'Даньяр', 1, 170),
(139, 'Демид', 1, 170),
(140, 'Демьян', 1, 170),
(141, 'Денис', 1, 170),
(142, 'Джамал', 1, 170),
(143, 'Джеймс', 1, 170),
(144, 'Джереми', 1, 170),
(145, 'Джозеф', 1, 170),
(146, 'Джордан', 1, 170),
(147, 'Джорж', 1, 170),
(148, 'Дик', 1, 170),
(149, 'Динар', 1, 170),
(150, 'Динасий', 1, 170),
(151, 'Дмитрий', 1, 170),
(152, 'Добрыня', 1, 170),
(153, 'Дональд', 1, 170),
(154, 'Донат', 1, 170),
(155, 'Донатос', 1, 170),
(156, 'Дорофей', 1, 170),
(157, 'Евгений', 1, 170),
(158, 'Евграф', 1, 170),
(159, 'Евдоким', 1, 170),
(160, 'Евсей', 1, 170),
(161, 'Евстафий', 1, 170),
(162, 'Егор', 1, 170),
(163, 'Елизар', 1, 170),
(164, 'Елисей', 1, 170),
(165, 'Емельян', 1, 170),
(166, 'Еремей', 1, 170),
(167, 'Ермолай', 1, 170),
(168, 'Ерофей', 1, 170),
(169, 'Ефим', 1, 170),
(170, 'Ефрем', 1, 170),
(171, 'Жан', 1, 170),
(172, 'Ждан', 1, 170),
(173, 'Жерар', 1, 170),
(174, 'Закир', 1, 170),
(175, 'Замир', 1, 170),
(176, 'Заур', 1, 170),
(177, 'Захар', 1, 170),
(178, 'Зенон', 1, 170),
(179, 'Зигмунд', 1, 170),
(180, 'Зиновий', 1, 170),
(181, 'Зураб', 1, 170),
(182, 'Ибрагим', 1, 170),
(183, 'Иван', 1, 170),
(184, 'Игнат', 1, 170),
(185, 'Игнатий', 1, 170),
(186, 'Игорь', 1, 170),
(187, 'Иероним', 1, 170),
(188, 'Измаил', 1, 170),
(189, 'Израиль', 1, 170),
(190, 'Илиан', 1, 170),
(191, 'Илларион', 1, 170),
(192, 'Ильхам', 1, 170),
(193, 'Ильшат', 1, 170),
(194, 'Илья', 1, 170),
(195, 'Ильяс', 1, 170),
(196, 'Инокентий', 1, 170),
(197, 'Иоанн', 1, 170),
(198, 'Иоаким', 1, 170),
(199, 'Ион', 1, 170),
(200, 'Иосиф', 1, 170),
(201, 'Ипполит', 1, 170),
(202, 'Ираклий', 1, 170),
(203, 'Иса', 1, 170),
(204, 'Исаак', 1, 170),
(205, 'Исидор', 1, 170),
(206, 'Искандер', 1, 170),
(207, 'Ислам', 1, 170),
(208, 'Исмаил', 1, 170),
(209, 'Казбек', 1, 170),
(210, 'Казимир', 1, 170),
(211, 'Камиль', 1, 170),
(212, 'Карен', 1, 170),
(213, 'Карим', 1, 170),
(214, 'Карл', 1, 170),
(215, 'Ким', 1, 170),
(216, 'Кир', 1, 170),
(217, 'Кирилл', 1, 170),
(218, 'Клавдий', 1, 170),
(219, 'Клаус', 1, 170),
(220, 'Клим', 1, 170),
(221, 'Климент', 1, 170),
(222, 'Клод', 1, 170),
(223, 'Кондрат', 1, 170),
(224, 'Константин', 1, 170),
(225, 'Корней', 1, 170),
(226, 'Корнилий', 1, 170),
(227, 'Кузьма', 1, 170),
(228, 'Лавр', 1, 170),
(229, 'Лаврентий', 1, 170),
(230, 'Лазарь', 1, 170),
(231, 'Лев', 1, 170),
(232, 'Леван', 1, 170),
(233, 'Левон', 1, 170),
(234, 'Ленар', 1, 170),
(235, 'Леон', 1, 170),
(236, 'Леонард', 1, 170),
(237, 'Леонид', 1, 170),
(238, 'Леонтий', 1, 170),
(239, 'Леопольд', 1, 170),
(240, 'Лука', 1, 170),
(241, 'Лукьян', 1, 170),
(242, 'Любим', 1, 170),
(243, 'Любомир', 1, 170),
(244, 'Людвиг', 1, 170),
(245, 'Люсьен', 1, 170),
(246, 'Люций', 1, 170),
(247, 'Мавлюда', 1, 170),
(248, 'Мадлен', 1, 170),
(249, 'Май', 1, 170),
(250, 'Майкл', 1, 170),
(251, 'Макар', 1, 170),
(252, 'Макарий', 1, 170),
(253, 'Максим', 1, 170),
(254, 'Максимильян', 1, 170),
(255, 'Максуд', 1, 170),
(256, 'Мансур', 1, 170),
(257, 'Мануил', 1, 170),
(258, 'Мар', 1, 170),
(259, 'Марат', 1, 170),
(260, 'Мариан', 1, 170),
(261, 'Марк', 1, 170),
(262, 'Марсель', 1, 170),
(263, 'Мартин', 1, 170),
(264, 'Матвей', 1, 170),
(265, 'Махмуд', 1, 170),
(266, 'Мераб', 1, 170),
(267, 'Мефодий', 1, 170),
(268, 'Мечеслав', 1, 170),
(269, 'Микула', 1, 170),
(270, 'Милан', 1, 170),
(271, 'Мирон', 1, 170),
(272, 'Мирослав', 1, 170),
(273, 'Митрофан', 1, 170),
(274, 'Михаил', 1, 170),
(275, 'Мишлов', 1, 170),
(276, 'Модест', 1, 170),
(277, 'Моисей', 1, 170),
(278, 'Мстислав', 1, 170),
(279, 'Мурат', 1, 170),
(280, 'Муслим', 1, 170),
(281, 'Мухаммед', 1, 170),
(282, 'Назар', 1, 170),
(283, 'Назарий', 1, 170),
(284, 'Наиль', 1, 170),
(285, 'Натан', 1, 170),
(286, 'Наум', 1, 170),
(287, 'Нестор', 1, 170),
(288, 'Никанор', 1, 170),
(289, 'Никита', 1, 170),
(290, 'Никифор', 1, 170),
(291, 'Никодим', 1, 170),
(292, 'Никола', 1, 170),
(293, 'Николай', 1, 170),
(294, 'Никон', 1, 170),
(295, 'Нильс', 1, 170),
(296, 'Нисон', 1, 170),
(297, 'Нифонт', 1, 170),
(298, 'Норманн', 1, 170),
(299, 'Овидий', 1, 170),
(300, 'Олан', 1, 170),
(301, 'Олег', 1, 170),
(302, 'Олесь', 1, 170),
(303, 'Онисим', 1, 170),
(304, 'Орест', 1, 170),
(305, 'Орландо', 1, 170),
(306, 'Осип', 1, 170),
(307, 'Оскар', 1, 170),
(308, 'Остап', 1, 170),
(309, 'Павел', 1, 170),
(310, 'Панкрат', 1, 170),
(311, 'Парамон', 1, 170),
(312, 'Петр', 1, 170),
(313, 'Платон', 1, 170),
(314, 'Порфирий', 1, 170),
(315, 'Потап', 1, 170),
(316, 'Прокофий', 1, 170),
(317, 'Прохор', 1, 170),
(318, 'Равиль', 1, 170),
(319, 'Радий', 1, 170),
(320, 'Радик', 1, 170),
(321, 'Радомир', 1, 170),
(322, 'Радослав', 1, 170),
(323, 'Разиль', 1, 170),
(324, 'Райан', 1, 170),
(325, 'Раймонд', 1, 170),
(326, 'Раис', 1, 170),
(327, 'Рамазан', 1, 170),
(328, 'Рамиз', 1, 170),
(329, 'Рамиль', 1, 170),
(330, 'Рамон', 1, 170),
(331, 'Ранель', 1, 170),
(332, 'Расим', 1, 170),
(333, 'Расул', 1, 170),
(334, 'Ратибор', 1, 170),
(335, 'Ратмир', 1, 170),
(336, 'Рафаил', 1, 170),
(337, 'Рафаэль', 1, 170),
(338, 'Рафик', 1, 170),
(339, 'Рашид', 1, 170),
(340, 'Рем', 1, 170),
(341, 'Ринат', 1, 170),
(342, 'Рифат', 1, 170),
(343, 'Рихард', 1, 170),
(344, 'Ричард', 1, 170),
(345, 'Роберт', 1, 170),
(346, 'Родион', 1, 170),
(347, 'Ролан', 1, 170),
(348, 'Роман', 1, 170),
(349, 'Ростислав', 1, 170),
(350, 'Рубен', 1, 170),
(351, 'Рудольф', 1, 170),
(352, 'Руслан', 1, 170),
(353, 'Рустам', 1, 170),
(354, 'Руфин', 1, 170),
(355, 'Рушан', 1, 170),
(356, 'Рэй', 1, 170),
(357, 'Сабир', 1, 170),
(358, 'Савва', 1, 170),
(359, 'Савелий', 1, 170),
(360, 'Самвел', 1, 170),
(361, 'Самсон', 1, 170),
(362, 'Самуил', 1, 170),
(363, 'Святослав', 1, 170),
(364, 'Севастьян', 1, 170),
(365, 'Северин', 1, 170),
(366, 'Семен', 1, 170),
(367, 'Серафим', 1, 170),
(368, 'Сергей', 1, 170),
(369, 'Сидор', 1, 170),
(370, 'Сократ', 1, 170),
(371, 'Соломон', 1, 170),
(372, 'Спартак', 1, 170),
(373, 'Стакрат', 1, 170),
(374, 'Станислав', 1, 170),
(375, 'Степан', 1, 170),
(376, 'Стефан', 1, 170),
(377, 'Стивен', 1, 170),
(378, 'Стоян', 1, 170),
(379, 'Султан', 1, 170),
(380, 'Тагир', 1, 170),
(381, 'Таис', 1, 170),
(382, 'Тайлер', 1, 170),
(383, 'Талик', 1, 170),
(384, 'Тамаз', 1, 170),
(385, 'Тамерлан', 1, 170),
(386, 'Тарас', 1, 170),
(387, 'Тельман', 1, 170),
(388, 'Теодор', 1, 170),
(389, 'Терентий', 1, 170),
(390, 'Тибор', 1, 170),
(391, 'Тиграм', 1, 170),
(392, 'Тигран', 1, 170),
(393, 'Тигрий', 1, 170),
(394, 'Тимофей', 1, 170),
(395, 'Тимур', 1, 170),
(396, 'Тит', 1, 170),
(397, 'Тихон', 1, 170),
(398, 'Томас', 1, 170),
(399, 'Трифон', 1, 170),
(400, 'Трофим', 1, 170),
(401, 'Ульманас', 1, 170),
(402, 'Умар', 1, 170),
(403, 'Устин', 1, 170),
(404, 'Фадей', 1, 170),
(405, 'Фазиль', 1, 170),
(406, 'Фанис', 1, 170),
(407, 'Фарид', 1, 170),
(408, 'Фархад', 1, 170),
(409, 'Федор', 1, 170),
(410, 'Федот', 1, 170),
(411, 'Феликс', 1, 170),
(412, 'Феодосий', 1, 170),
(413, 'Фердинанд', 1, 170),
(414, 'Фидель', 1, 170),
(415, 'Филимон', 1, 170),
(416, 'Филипп', 1, 170),
(417, 'Флорентий', 1, 170),
(418, 'Фома', 1, 170),
(419, 'Франц', 1, 170),
(420, 'Фред', 1, 170),
(421, 'Фридрих', 1, 170),
(422, 'Фуад', 1, 170),
(423, 'Хабиб', 1, 170),
(424, 'Хаким', 1, 170),
(425, 'Харитон', 1, 170),
(426, 'Христиан', 1, 170),
(427, 'Христос', 1, 170),
(428, 'Христофор', 1, 170),
(429, 'Цезарь', 1, 170),
(430, 'Чарльз', 1, 170),
(431, 'Чеслав', 1, 170),
(432, 'Чингиз', 1, 170),
(433, 'Шамиль', 1, 170),
(434, 'Шарль', 1, 170),
(435, 'Шерлок', 1, 170),
(436, 'Эдвард', 1, 170),
(437, 'Эдгар', 1, 170),
(438, 'Эдмунд', 1, 170),
(439, 'Эдуард', 1, 170),
(440, 'Эльдар', 1, 170),
(441, 'Эмиль', 1, 170),
(442, 'Эмин', 1, 170),
(443, 'Эммануил', 1, 170),
(444, 'Эраст', 1, 170),
(445, 'Эрик', 1, 170),
(446, 'Эрнест', 1, 170),
(447, 'Юлиан', 1, 170),
(448, 'Юлий', 1, 170),
(449, 'Юнус', 1, 170),
(450, 'Юрий', 1, 170),
(451, 'Юхим', 1, 170),
(452, 'Яков', 1, 170),
(453, 'Ян', 1, 170),
(454, 'Януарий', 1, 170),
(455, 'Ярослав', 1, 170),
(456, 'Ясон', 1, 170),
(457, 'Августа', 0, 170),
(458, 'Авдотья', 0, 170),
(459, 'Аврора', 0, 170),
(460, 'Агата', 0, 170),
(461, 'Агапия', 0, 170),
(462, 'Агафья', 0, 170),
(463, 'Аглая', 0, 170),
(464, 'Агнесса', 0, 170),
(465, 'Агния', 0, 170),
(466, 'Агриппина', 0, 170),
(467, 'Агунда', 0, 170),
(468, 'Ада', 0, 170),
(469, 'Аделина', 0, 170),
(470, 'Аделаида', 0, 170),
(471, 'Адель', 0, 170),
(472, 'Адиля', 0, 170),
(473, 'Адриана', 0, 170),
(474, 'Аза', 0, 170),
(475, 'Азалия', 0, 170),
(476, 'Азиза', 0, 170),
(477, 'Айгуль', 0, 170),
(478, 'Айлин', 0, 170),
(479, 'Айнагуль', 0, 170),
(480, 'Аида', 0, 170),
(481, 'Айжан', 0, 170),
(482, 'Аксинья', 0, 170),
(483, 'Акулина', 0, 170),
(484, 'Алана', 0, 170),
(485, 'Алевтина', 0, 170),
(486, 'Александра', 0, 170),
(487, 'Алена', 0, 170),
(488, 'Алико', 0, 170),
(489, 'Алина', 0, 170),
(490, 'Алиса', 0, 170),
(491, 'Алия', 0, 170),
(492, 'Алла', 0, 170),
(493, 'Алсу', 0, 170),
(494, 'Альба', 0, 170),
(495, 'Альберта', 0, 170),
(496, 'Альбина', 0, 170),
(497, 'Альвина', 0, 170),
(498, 'Альфия', 0, 170),
(499, 'Альфреда', 0, 170),
(500, 'Аля', 0, 170),
(501, 'Амаль', 0, 170),
(502, 'Амелия', 0, 170),
(503, 'Амина', 0, 170),
(504, 'Амира', 0, 170),
(505, 'Анаит', 0, 170),
(506, 'Анастасия', 0, 170),
(507, 'Ангелина', 0, 170),
(508, 'Анеля', 0, 170),
(509, 'Анжела', 0, 170),
(510, 'Анжелика', 0, 170),
(511, 'Анисья', 0, 170),
(512, 'Анита', 0, 170),
(513, 'Анна', 0, 170),
(514, 'Антонина', 0, 170),
(515, 'Анфиса', 0, 170),
(516, 'Аполлинария', 0, 170),
(517, 'Арабелла', 0, 170),
(518, 'Ариадна', 0, 170),
(519, 'Ариана', 0, 170),
(520, 'Арина', 0, 170),
(521, 'Архелия', 0, 170),
(522, 'Асель', 0, 170),
(523, 'Асия', 0, 170),
(524, 'Ассоль', 0, 170),
(525, 'Астра', 0, 170),
(526, 'Астрид', 0, 170),
(527, 'Ася', 0, 170),
(528, 'Аурелия', 0, 170),
(529, 'Афанасия', 0, 170),
(530, 'Аэлита', 0, 170),
(531, 'Беатриса', 0, 170),
(532, 'Белинда', 0, 170),
(533, 'Белла', 0, 170),
(534, 'Берта', 0, 170),
(535, 'Бирута', 0, 170),
(536, 'Богдана', 0, 170),
(537, 'Божена', 0, 170),
(538, 'Борислава', 0, 170),
(539, 'Бронислава', 0, 170),
(540, 'Валентина', 0, 170),
(541, 'Валерия', 0, 170),
(542, 'Ванда', 0, 170),
(543, 'Ванесса', 0, 170),
(544, 'Варвара', 0, 170),
(545, 'Василина', 0, 170),
(546, 'Василиса', 0, 170),
(547, 'Венера', 0, 170),
(548, 'Вера', 0, 170),
(549, 'Вероника', 0, 170),
(550, 'Веселина', 0, 170),
(551, 'Весна', 0, 170),
(552, 'Веста', 0, 170),
(553, 'Вета', 0, 170),
(554, 'Вида', 0, 170),
(555, 'Викторина', 0, 170),
(556, 'Виктория', 0, 170),
(557, 'Вилена', 0, 170),
(558, 'Вилора', 0, 170),
(559, 'Виолетта', 0, 170),
(560, 'Виргиния', 0, 170),
(561, 'Виринея', 0, 170),
(562, 'Вита', 0, 170),
(563, 'Виталина', 0, 170),
(564, 'Влада', 0, 170),
(565, 'Владислава', 0, 170),
(566, 'Владлена', 0, 170),
(567, 'Габриэлла', 0, 170),
(568, 'Галина', 0, 170),
(569, 'Галия', 0, 170),
(570, 'Гаянэ', 0, 170),
(571, 'Гелена', 0, 170),
(572, 'Гелла', 0, 170),
(573, 'Генриетта', 0, 170),
(574, 'Георгина', 0, 170),
(575, 'Гера', 0, 170),
(576, 'Гертруда', 0, 170),
(577, 'Глафира', 0, 170),
(578, 'Глория', 0, 170),
(579, 'Гортензия', 0, 170),
(580, 'Гражина', 0, 170),
(581, 'Грета', 0, 170),
(582, 'Гузель', 0, 170),
(583, 'Гулия', 0, 170),
(584, 'Гульмира', 0, 170),
(585, 'Гульназ', 0, 170),
(586, 'Гульнара', 0, 170),
(587, 'Гульшат', 0, 170),
(588, 'Дайна', 0, 170),
(589, 'Далия', 0, 170),
(590, 'Дамира', 0, 170),
(591, 'Дана', 0, 170),
(592, 'Даниэла', 0, 170),
(593, 'Данута', 0, 170),
(594, 'Дара', 0, 170),
(595, 'Дарина', 0, 170),
(596, 'Дарья', 0, 170),
(597, 'Даяна', 0, 170),
(598, 'Дебора', 0, 170),
(599, 'Джамиля', 0, 170),
(600, 'Джемма', 0, 170),
(601, 'Дженнифер', 0, 170),
(602, 'Джессика', 0, 170),
(603, 'Джулия', 0, 170),
(604, 'Джульетта', 0, 170),
(605, 'Диана', 0, 170),
(606, 'Дилара', 0, 170),
(607, 'Дильназ', 0, 170),
(608, 'Дильнара', 0, 170),
(609, 'Диля', 0, 170),
(610, 'Дина', 0, 170),
(611, 'Динара', 0, 170),
(612, 'Диодора', 0, 170),
(613, 'Дионисия', 0, 170),
(614, 'Долорес', 0, 170),
(615, 'Доля', 0, 170),
(616, 'Доминика', 0, 170),
(617, 'Дора', 0, 170),
(618, 'Ева', 0, 170),
(619, 'Евангелина', 0, 170),
(620, 'Евгения', 0, 170),
(621, 'Евдокия', 0, 170),
(622, 'Екатерина', 0, 170),
(623, 'Елена', 0, 170),
(624, 'Елизавета', 0, 170),
(625, 'Есения', 0, 170),
(626, 'Ефимия', 0, 170),
(627, 'Жанна', 0, 170),
(628, 'Жасмин', 0, 170),
(629, 'Жозефина', 0, 170),
(630, 'Забава', 0, 170),
(631, 'Заира', 0, 170),
(632, 'Замира', 0, 170),
(633, 'Зара', 0, 170),
(634, 'Зарема', 0, 170),
(635, 'Зарина', 0, 170),
(636, 'Захария', 0, 170),
(637, 'Земфира', 0, 170),
(638, 'Зинаида', 0, 170),
(639, 'Зита', 0, 170),
(640, 'Злата', 0, 170),
(641, 'Зоряна', 0, 170),
(642, 'Зоя', 0, 170),
(643, 'Зульфия', 0, 170),
(644, 'Зухра', 0, 170),
(645, 'Иванна', 0, 170),
(646, 'Иветта', 0, 170),
(647, 'Ивона', 0, 170),
(648, 'Ида', 0, 170),
(649, 'Изабелла', 0, 170),
(650, 'Изольда', 0, 170),
(651, 'Илария', 0, 170),
(652, 'Илиана', 0, 170),
(653, 'Илона', 0, 170),
(654, 'Инара', 0, 170),
(655, 'Инга', 0, 170),
(656, 'Ингеборга', 0, 170),
(657, 'Индира', 0, 170),
(658, 'Инесса', 0, 170),
(659, 'Инна', 0, 170),
(660, 'Иоанна', 0, 170),
(661, 'Иоланта', 0, 170),
(662, 'Ираида', 0, 170),
(663, 'Ирина', 0, 170),
(664, 'Ирма', 0, 170),
(665, 'Искра', 0, 170),
(666, 'Ия', 0, 170),
(667, 'Калерия', 0, 170),
(668, 'Камилла', 0, 170),
(669, 'Капитолина', 0, 170),
(670, 'Карима', 0, 170),
(671, 'Карина', 0, 170),
(672, 'Каролина', 0, 170),
(673, 'Катарина', 0, 170),
(674, 'Кира', 0, 170),
(675, 'Клавдия', 0, 170),
(676, 'Клара', 0, 170),
(677, 'Кларисса', 0, 170),
(678, 'Климентина', 0, 170),
(679, 'Констанция', 0, 170),
(680, 'Кора', 0, 170),
(681, 'Корнелия', 0, 170),
(682, 'Кристина', 0, 170),
(683, 'Ксения', 0, 170),
(684, 'Лада', 0, 170),
(685, 'Лайма', 0, 170),
(686, 'Лана', 0, 170),
(687, 'Лара', 0, 170),
(688, 'Лариса', 0, 170),
(689, 'Лаура', 0, 170),
(690, 'Лейла', 0, 170),
(691, 'Лейсан', 0, 170),
(692, 'Леокадия', 0, 170),
(693, 'Леонида', 0, 170),
(694, 'Лера', 0, 170),
(695, 'Леся', 0, 170),
(696, 'Лиана', 0, 170),
(697, 'Лидия', 0, 170),
(698, 'Лиза', 0, 170),
(699, 'Лика', 0, 170),
(700, 'Лилиана', 0, 170),
(701, 'Лилия', 0, 170),
(702, 'Лина', 0, 170),
(703, 'Линда', 0, 170),
(704, 'Лиора', 0, 170),
(705, 'Лира', 0, 170),
(706, 'Лия', 0, 170),
(707, 'Лола', 0, 170),
(708, 'Лолита', 0, 170),
(709, 'Лора', 0, 170),
(710, 'Луиза', 0, 170),
(711, 'Лукерья', 0, 170),
(712, 'Любовь', 0, 170),
(713, 'Людмила', 0, 170),
(714, 'Ляля', 0, 170),
(715, 'Люция', 0, 170),
(716, 'Магда', 0, 170),
(717, 'Магдалина', 0, 170),
(718, 'Мадина', 0, 170),
(719, 'Майя', 0, 170),
(720, 'Малика', 0, 170),
(721, 'Мальвина', 0, 170),
(722, 'Мара', 0, 170),
(723, 'Маргарита', 0, 170),
(724, 'Марианна', 0, 170),
(725, 'Марика', 0, 170),
(726, 'Марина', 0, 170),
(727, 'Мария', 0, 170),
(728, 'Марселина', 0, 170),
(729, 'Марта', 0, 170),
(730, 'Маруся', 0, 170),
(731, 'Марфа', 0, 170),
(732, 'Марьям', 0, 170),
(733, 'Матильда', 0, 170),
(734, 'Мелания', 0, 170),
(735, 'Мелисса', 0, 170),
(736, 'Мика', 0, 170),
(737, 'Мила', 0, 170),
(738, 'Милада', 0, 170),
(739, 'Милана', 0, 170),
(740, 'Милена', 0, 170),
(741, 'Милица', 0, 170),
(742, 'Милолика', 0, 170),
(743, 'Милослава', 0, 170),
(744, 'Мира', 0, 170),
(745, 'Мирослава', 0, 170),
(746, 'Мирра', 0, 170),
(747, 'Моника', 0, 170),
(748, 'Муза', 0, 170),
(749, 'Мэри', 0, 170),
(750, 'Надежда', 0, 170),
(751, 'Назира', 0, 170),
(752, 'Наиля', 0, 170),
(753, 'Наима', 0, 170),
(754, 'Нана', 0, 170),
(755, 'Наоми', 0, 170),
(756, 'Наталья', 0, 170),
(757, 'Нателла', 0, 170),
(758, 'Нелли', 0, 170),
(759, 'Неонила', 0, 170),
(760, 'Ника', 0, 170),
(761, 'Николь', 0, 170),
(762, 'Нина', 0, 170),
(763, 'Нинель', 0, 170),
(764, 'Нонна', 0, 170),
(765, 'Нора', 0, 170),
(766, 'Нурия', 0, 170),
(767, 'Одетта', 0, 170),
(768, 'Оксана', 0, 170),
(769, 'Октябрина', 0, 170),
(770, 'Олеся', 0, 170),
(771, 'Оливия', 0, 170),
(772, 'Ольга', 0, 170),
(773, 'Офелия', 0, 170),
(774, 'Павла', 0, 170),
(775, 'Павлина', 0, 170),
(776, 'Памела', 0, 170),
(777, 'Патриция', 0, 170),
(778, 'Пелагея', 0, 170),
(779, 'Перизат', 0, 170),
(780, 'Полина', 0, 170),
(781, 'Прасковья', 0, 170),
(782, 'Рада', 0, 170),
(783, 'Радмила', 0, 170),
(784, 'Раиса', 0, 170),
(785, 'Ревекка', 0, 170),
(786, 'Регина', 0, 170),
(787, 'Рема', 0, 170),
(788, 'Рената', 0, 170),
(789, 'Римма', 0, 170),
(790, 'Рина', 0, 170),
(791, 'Рита', 0, 170),
(792, 'Рогнеда', 0, 170),
(793, 'Роберта', 0, 170),
(794, 'Роза', 0, 170),
(795, 'Роксана', 0, 170),
(796, 'Ростислава', 0, 170),
(797, 'Рузалия', 0, 170),
(798, 'Рузанна', 0, 170),
(799, 'Рузиля', 0, 170),
(800, 'Румия', 0, 170),
(801, 'Русалина', 0, 170),
(802, 'Руслана', 0, 170),
(803, 'Руфина', 0, 170),
(804, 'Сабина', 0, 170),
(805, 'Сабрина', 0, 170),
(806, 'Сажида', 0, 170),
(807, 'Саида', 0, 170),
(808, 'Саломея', 0, 170),
(809, 'Самира', 0, 170),
(810, 'Сандра', 0, 170),
(811, 'Сания', 0, 170),
(812, 'Санта', 0, 170),
(813, 'Сара', 0, 170),
(814, 'Сати', 0, 170),
(815, 'Светлана', 0, 170),
(816, 'Святослава', 0, 170),
(817, 'Севара', 0, 170),
(818, 'Северина', 0, 170),
(819, 'Селена', 0, 170),
(820, 'Серафима', 0, 170),
(821, 'Сильва', 0, 170),
(822, 'Сима', 0, 170),
(823, 'Симона', 0, 170),
(824, 'Слава', 0, 170),
(825, 'Снежана', 0, 170),
(826, 'Соня', 0, 170),
(827, 'София', 0, 170),
(828, 'Станислава', 0, 170),
(829, 'Стелла', 0, 170),
(830, 'Стефания', 0, 170),
(831, 'Сусанна', 0, 170),
(832, 'Таира', 0, 170),
(833, 'Таисия', 0, 170),
(834, 'Тала', 0, 170),
(835, 'Тамара', 0, 170),
(836, 'Тамила', 0, 170),
(837, 'Тара', 0, 170),
(838, 'Татьяна', 0, 170),
(839, 'Тереза', 0, 170),
(840, 'Тина', 0, 170),
(841, 'Тора', 0, 170),
(842, 'Ульяна', 0, 170),
(843, 'Урсула', 0, 170),
(844, 'Устина', 0, 170),
(845, 'Устинья', 0, 170),
(846, 'Фаиза', 0, 170),
(847, 'Фаина', 0, 170),
(848, 'Фания', 0, 170),
(849, 'Фаня', 0, 170),
(850, 'Фарида', 0, 170),
(851, 'Фатима', 0, 170),
(852, 'Фая', 0, 170),
(853, 'Фекла', 0, 170),
(854, 'Фелиция', 0, 170),
(855, 'Феруза', 0, 170),
(856, 'Физура', 0, 170),
(857, 'Флора', 0, 170),
(858, 'Франсуаза', 0, 170),
(859, 'Фрида', 0, 170),
(860, 'Харита', 0, 170),
(861, 'Хилари', 0, 170),
(862, 'Хильда', 0, 170),
(863, 'Хлоя', 0, 170),
(864, 'Христина', 0, 170),
(865, 'Цветана', 0, 170),
(866, 'Челси', 0, 170),
(867, 'Чеслава', 0, 170),
(868, 'Чулпан', 0, 170),
(869, 'Шакира', 0, 170),
(870, 'Шарлотта', 0, 170),
(871, 'Шейла', 0, 170),
(872, 'Шелли', 0, 170),
(873, 'Шерил', 0, 170),
(874, 'Эвелина', 0, 170),
(875, 'Эвита', 0, 170),
(876, 'Эдда', 0, 170),
(877, 'Эдита', 0, 170),
(878, 'Элеонора', 0, 170),
(879, 'Элиана', 0, 170),
(880, 'Элиза', 0, 170),
(881, 'Элина', 0, 170),
(882, 'Элла', 0, 170),
(883, 'Эллада', 0, 170),
(884, 'Элоиза', 0, 170),
(885, 'Эльвина', 0, 170),
(886, 'Эльвира', 0, 170),
(887, 'Эльга', 0, 170),
(888, 'Эльза', 0, 170),
(889, 'Эльмира', 0, 170),
(890, 'Эльнара', 0, 170),
(891, 'Эля', 0, 170),
(892, 'Эмилия', 0, 170),
(893, 'Эмма', 0, 170),
(894, 'Эмили', 0, 170),
(895, 'Эрика', 0, 170),
(896, 'Эрнестина', 0, 170),
(897, 'Эсмеральда', 0, 170),
(898, 'Этель', 0, 170),
(899, 'Этери', 0, 170),
(900, 'Юзефа', 0, 170),
(901, 'Юлия', 0, 170),
(902, 'Юна', 0, 170),
(903, 'Юния', 0, 170),
(904, 'Юнона', 0, 170),
(905, 'Ядвига', 0, 170),
(906, 'Яна', 0, 170),
(907, 'Янина', 0, 170),
(908, 'Ярина', 0, 170),
(909, 'Ярослава', 0, 170),
(910, 'Ясмина', 0, 170);

--
-- Дамп данных таблицы `reference`
--

INSERT INTO `reference` (`id`, `name`, `table`, `description`, `id_column`, `columns`, `created_at`, `updated_at`) VALUES
(1, 'Роли пользователей системы', 'admin_role', '', 'role_title', '{\"role_title\": true, \"role_description\": true}', '2018-11-23 21:12:03', '2018-11-23 21:12:03'),
(2, 'Страны', 'country', '', 'country_id', '{\"title\": true, \"code_a2\": true, \"timezone\": true, \"country_id\": false}', '2018-11-29 02:25:48', '2018-11-29 02:25:48'),
(3, 'Справочник удостоверяющих документов', 'card_identity', '', 'card_identity_id', '{\"title\": true, \"reg_mask\": true, \"input_mask\": true, \"nationality\": true, \"picture_idx\": true, \"enabled_default\": true, \"use_numpad_only\": true, \"card_identity_id\": false, \"egis_doc_type_id\": true}', '2019-02-05 19:06:24', '2019-02-05 19:06:24'),
(4, 'Справочники ', 'ReferenceList', '', 'Id', '{\"Id\": false, \"RecordId\": true, \"RecordName\": true, \"ReferenceName\": true, \"RecordDescription\": true}', '2019-02-19 16:56:40', '2019-02-19 16:56:40');

--
-- Дамп данных таблицы `ReferenceList`
--

INSERT INTO `ReferenceList` (`Id`, `ReferenceName`, `RecordId`, `RecordName`, `RecordDescription`) VALUES
(1, 'terminal-type', 0, 'unknown', 'Тип терминала не определен'),
(2, 'terminal-type', 1, 'onLineCashMobile', 'Переносной терминал. Оснащен фискальным накопителем.'),
(3, 'terminal-type', 2, 'offLineMobileValidator', 'Переносной терминал, загружающий всю необходимую, для работы, ин'),
(4, 'terminal-type', 3, 'offLineStationarValidator', 'Не переносной, смонтированный в транспортном средстве, терминал '),
(5, 'terminal-type', 4, 'onLineSelfServiceMachine', 'Устройство самообслуживания'),
(6, 'terminal-type', 5, 'onLineWebCash', 'Онлайн веб касса'),
(7, 'user-role', 0, 'urConductor', 'Кондуктор'),
(8, 'user-role', 1, 'urTiketSeller', 'Кассир, продавец билетов'),
(9, 'user-role', 2, 'urAdministrator', 'Администратор, технический специалист'),
(10, 'user-role', 3, 'urController', 'КРУ, Контроллер'),
(11, 'user-role', 4, 'urCashKeeper', 'Кассир, приемщик выручки'),
(12, 'terminal-tariff-type', 1, 'ttBase', 'Основной(базовый) тариф на проезд'),
(13, 'terminal-tariff-type', 2, 'ttExtended', 'Дополнительный тариф на проезд'),
(14, 'terminal-tariff-type', 3, 'ttBaggage', 'Багажный'),
(15, 'terminal-tariff-type', 4, 'ttProduct', 'Товар'),
(16, 'terminal-tariff-type', 5, 'ttService', 'Услуга'),
(17, 'terminal-parameter-type', 0, 'tpTypeInt', 'Целое'),
(18, 'terminal-parameter-type', 1, 'tpTypeReal', 'С плавающей точкой'),
(19, 'terminal-parameter-type', 2, 'tpTypeStr', 'Строка'),
(20, 'terminal-parameter-type', 3, 'tpTypeBool', 'Логическое'),
(21, 'terminal-parameter-type', 4, 'tpTypeList', 'Список'),
(22, 'FNTaxCode', 1, 'Common', 'Общая(ОСН)'),
(23, 'FNTaxCode', 2, 'Simplified', 'Упрощенная доход(УСН)'),
(24, 'FNTaxCode', 4, 'SimplifiedWithExpense', 'Упрощенная доход минус расход'),
(25, 'FNTaxCode', 8, 'ENVD', 'Единый налог на вмененный доход(ЕНВД)'),
(26, 'FNTaxCode', 16, 'CommonAgricultural', 'Единый сельскохозяйственный налог'),
(27, 'FNTaxCode', 32, 'Patent', 'Патентная система налогообложения'),
(28, 'FNOperatingMode', 0, 'Default', 'Основной режим работы'),
(29, 'FNOperatingMode', 1, 'Encryption', 'Шифрование'),
(30, 'FNOperatingMode', 2, 'Autonomous', 'Автономный режим'),
(31, 'FNOperatingMode', 4, 'Automatic', 'Автоматический режим'),
(32, 'FNOperatingMode', 8, 'Service', 'Применение в сфере услуг'),
(33, 'FNOperatingMode', 16, 'BSOMode', 'Режим БСО (иначе - режим чеков)'),
(34, 'FNOperatingMode', 32, 'InternetUsing', 'Применение в Интернет'),
(35, 'FNPaymentAgent', 0, 'None', 'Агентом не является'),
(36, 'FNPaymentAgent', 1, 'BankPayAgent', 'Банковский платёжный агент'),
(37, 'FNPaymentAgent', 2, 'BankPaySubAgent', 'Банковский платёжный субагент'),
(38, 'FNPaymentAgent', 4, 'PayAgent', 'Платёжный агент'),
(39, 'FNPaymentAgent', 8, 'PaySubAgent', 'Платёжный субагент'),
(40, 'FNPaymentAgent', 16, 'Attorney', 'Поверенный'),
(41, 'FNPaymentAgent', 32, 'CommissionAgent', 'Комиссионер'),
(42, 'FNPaymentAgent', 64, 'Agent', 'Иной агент'),
(43, 'terminal-stat-server', 1, 'tStatServerLogstash', 'Сервер статистики на базе LogStatsh'),
(44, 'terminal-stat-server', 0, 'tStatServerAPI', 'Сервер статистики на базе API терминалов'),
(45, 'terminal-parameter-type', 5, 'tpTypeReference', 'Справочник'),
(46, 'terminal-tables', 1, 'TerminalList', 'Список терминалов'),
(47, 'terminal-tables', 2, 'TerminalUser', 'Список пользователей'),
(48, 'terminal-tables', 3, 'TerminalOrganizationList', 'Список организаций'),
(49, 'terminal-tables', 4, 'TerminalUserPhoto', 'Список фотографий пользователей'),
(50, 'terminal-parameter-type', 6, 'tpTypeCalc', 'Вычисляемое поле '),
(51, 'RouteMode', 1, 'Municipal', 'Муниципальный'),
(52, 'RouteMode', 2, 'Inter-municipal', 'Межмуниципальный'),
(53, 'RouteMode', 3, 'Interregional', 'Межсубъектный'),
(54, 'RouteMode', 4, 'Adjacent-interregional', 'Смежный межсубъектный'),
(55, 'RouteMode', 5, 'International', 'Международный'),
(56, 'RouteMode', 6, 'For-own-needs', 'Для собственных нужд'),
(57, 'RouteType', 1, 'Regular', 'Регулярная'),
(58, 'RouteType', 2, 'Custom', 'Заказная'),
(59, 'RouteType', 3, 'Seasonal', 'Сезонная'),
(60, 'TransportType', 1, 'ttSeaFerry', 'Морской паром'),
(61, 'TransportType', 2, 'ttSeaHovercrafts', 'Морские суда на воздушной подушке'),
(62, 'TransportType', 3, 'ttSeaHydrofoils', 'Морские суда на подводных крыльях'),
(63, 'TransportType', 4, 'ttSeaEkranoplan', 'Морской экраноплан'),
(64, 'TransportType', 5, 'ttRiverFerry', 'Речной паром'),
(65, 'TransportType', 6, 'ttRiverHovercrafts', 'Речные суда на воздушной подушке'),
(66, 'TransportType', 7, 'ttRiverHydrofoils', 'Речные суда на подводных крыльях'),
(67, 'TransportType', 8, 'ttRiverPlaningVessels', 'Речные глиссирующие суда'),
(68, 'TransportType', 9, 'ttAircraft', 'Самолёт'),
(69, 'TransportType', 10, 'ttHelicopter', 'Вертолёт'),
(70, 'TransportType', 11, 'ttBus', 'Автобус'),
(71, 'TransportType', 12, 'ttTrolleybus', 'Троллейбус'),
(72, 'TransportType', 13, 'ttTram', 'Трамвай'),
(73, 'TransportType', 14, 'ttTaxi', 'Такси'),
(74, 'TransportType', 15, 'ttLongDistanceTrains', 'Поезда дальнего следования'),
(75, 'TransportType', 16, 'ttSuburbanElectricTrain', 'Пригородные электрички'),
(76, 'TransportType', 17, 'ttFunicular', 'Фуникулёр'),
(77, 'TransportType', 18, 'ttCableCar', 'Канатная дорога'),
(78, 'TransportType', 19, 'ttMetro', 'Метрополитен'),
(79, 'TransportType', 20, 'ttPipeline', 'Трубопроводный'),
(80, 'user-role', 5, 'urDriver', 'Водитель'),
(81, 'bus-brand', 1, 'bbANDARE', 'ANDARE'),
(82, 'bus-brand', 2, 'bbANKAI', 'ANKAI'),
(83, 'bus-brand', 3, 'bbASIA', 'ASIA'),
(84, 'bus-brand', 4, 'bbAYATS', 'AYATS'),
(85, 'bus-brand', 5, 'bbBERKHOF', 'BERKHOF'),
(86, 'bus-brand', 6, 'bbBOVA', 'BOVA'),
(87, 'bus-brand', 7, 'bbCITROEN', 'CITROEN'),
(88, 'bus-brand', 8, 'bbDAEWOO', 'DAEWOO'),
(89, 'bus-brand', 9, 'bbDAF', 'DAF'),
(90, 'bus-brand', 10, 'bbDODGE', 'DODGE'),
(91, 'bus-brand', 11, 'bbDONGFENG', 'DONGFENG'),
(92, 'bus-brand', 12, 'bbDROGMOLLER', 'DROGMOLLER'),
(93, 'bus-brand', 13, 'bbEOS', 'EOS'),
(94, 'bus-brand', 14, 'bbFIAT', 'FIAT'),
(95, 'bus-brand', 15, 'bbFORD', 'FORD'),
(96, 'bus-brand', 16, 'bbFST', 'FST'),
(97, 'bus-brand', 17, 'bbGOLDENDRAGON', 'GOLDEN DRAGON'),
(98, 'bus-brand', 18, 'bbHIGER', 'HIGER'),
(99, 'bus-brand', 19, 'bbHYUNDAI', 'HYUNDAI'),
(100, 'bus-brand', 20, 'bbIKARBUS', 'IKARBUS'),
(101, 'bus-brand', 21, 'bbIKARUS', 'IKARUS'),
(102, 'bus-brand', 22, 'bbIRISBUS', 'IRISBUS'),
(103, 'bus-brand', 23, 'bbIVECO', 'IVECO'),
(104, 'bus-brand', 24, 'bbJAC', 'JAC'),
(105, 'bus-brand', 25, 'bbKIA', 'KIA'),
(106, 'bus-brand', 26, 'bbKINGLONG', 'KING-LONG'),
(107, 'bus-brand', 27, 'bbMAN', 'MAN'),
(108, 'bus-brand', 28, 'bbMERCEDESBENZ', 'MERCEDES-BENZ'),
(109, 'bus-brand', 29, 'bbMITSUBISHI', 'MITSUBISHI'),
(110, 'bus-brand', 30, 'bbMUDAN', 'MUDAN'),
(111, 'bus-brand', 31, 'bbNEOPLAN', 'NEOPLAN'),
(112, 'bus-brand', 32, 'bbRENAULT', 'RENAULT'),
(113, 'bus-brand', 33, 'bbPEUGEOT', 'PEUGEOT'),
(114, 'bus-brand', 34, 'bbSCANIA', 'SCANIA'),
(115, 'bus-brand', 35, 'bbSETRA', 'SETRA'),
(116, 'bus-brand', 36, 'bbSHENLONG', 'SHENLONG'),
(117, 'bus-brand', 37, 'bbSHUCHI', 'SHUCHI'),
(118, 'bus-brand', 38, 'bbSSANGYONG', 'SSANG YONG'),
(119, 'bus-brand', 39, 'bbTEMSA', 'TEMSA'),
(120, 'bus-brand', 40, 'bbTOYOTA', 'TOYOTA'),
(121, 'bus-brand', 41, 'bbVANHOOL', 'VANHOOL'),
(122, 'bus-brand', 42, 'bbVDLBUS', 'VDL BUS'),
(123, 'bus-brand', 43, 'bbVOLKSWAGEN', 'VOLKSWAGEN'),
(124, 'bus-brand', 44, 'bbVOLVO', 'VOLVO'),
(125, 'bus-brand', 45, 'bbYUTONG', 'YUTONG'),
(126, 'bus-brand', 46, 'bbZHONGTONG', 'ZHONG TONG'),
(127, 'bus-brand', 47, 'bbZONDA', 'ZONDA'),
(128, 'bus-brand', 48, 'bbBAZ', 'БАЗ'),
(129, 'bus-brand', 49, 'bbBOGDAN', 'БОГДАН'),
(130, 'bus-brand', 50, 'bbVITAYZ', 'ВИТЯЗЬ'),
(131, 'bus-brand', 51, 'bbVOLZHANIN', 'ВОЛЖАНИН'),
(132, 'bus-brand', 52, 'bbGOLAZ', 'ГОЛАЗ'),
(133, 'bus-brand', 53, 'bbKAVZ', 'КАВЗ'),
(134, 'bus-brand', 54, 'bbLAZ', 'ЛАЗ'),
(135, 'bus-brand', 55, 'bbLIAZ', 'ЛИАЗ'),
(136, 'bus-brand', 56, 'bbMAZ', 'МАЗ'),
(137, 'bus-brand', 57, 'bbMARZ', 'МАРЗ'),
(138, 'bus-brand', 58, 'bbNEMAN', 'НЕМАН'),
(139, 'bus-brand', 59, 'bbNEFAZ', 'НЕФАЗ'),
(140, 'bus-brand', 60, 'bbPAZ', 'ПАЗ'),
(141, 'BusCapacity', 21, 'BusM1', 'Автобус (М1)'),
(142, 'BusCapacity', 22, 'BusM2', 'Автобус (М2)'),
(143, 'BusCapacity', 23, 'BusM3', 'Автобус (М3)'),
(144, 'bus-device-type', 1, 'dtsmartCamera', 'Смарт-камера'),
(145, 'bus-device-type', 2, 'dtMBU', 'МБУ'),
(146, 'bus-class', 1, 'bcOMV', 'ОМВ'),
(147, 'bus-class', 2, 'bcMV', 'МВ'),
(148, 'bus-class', 3, 'bcSV', 'СВ'),
(149, 'bus-class', 4, 'bcBV', 'БВ'),
(150, 'bus-class', 5, 'bcOBV', 'ОБВ'),
(151, 'bus-eco-class', 1, 'ecEuro1', 'Евро-1'),
(152, 'bus-eco-class', 2, 'ecEuro2', 'Евро-2'),
(153, 'bus-eco-class', 3, 'ecEuro3', 'Евро-3'),
(154, 'bus-eco-class', 4, 'ecEuro4', 'Евро-4'),
(155, 'bus-eco-class', 5, 'ecEuro5', 'Евро-5'),
(156, 'user-role-function', 0, 'urfUnknown', 'Не известная функция'),
(157, 'user-role-function', 1, 'urfLogin', 'Вход в приложение'),
(158, 'user-role-function', 2, 'urfLauncherClose', 'Закрытие лаунчера'),
(159, 'user-role-function', 3, 'urfChat', 'Онлайн чат'),
(160, 'user-role-function', 4, 'urfPersonalTask', 'Получение планового задания'),
(161, 'user-role-function', 5, 'urfDayOpen', 'Открытие смены'),
(162, 'user-role-function', 6, 'urfDayClose', 'Закрытие смены'),
(163, 'user-role-function', 7, 'urfCitySale', 'Продажа билетов Город'),
(164, 'user-role-function', 8, 'urfInterstateSale', 'Продажа билетов ГДС'),
(165, 'user-role-function', 9, 'urfTicketRefund', 'Возврат билетов'),
(166, 'user-role-function', 10, 'urfCashIn', 'Внесение наличных'),
(167, 'user-role-function', 11, 'urfCashOut', 'Изъятие наличных'),
(168, 'user-role-function', 12, 'urfXReport', 'Печать Х-отчёта'),
(169, 'user-role-function', 13, 'urfZReport', 'Печать Z-отчёта'),
(170, 'user-role-function', 14, 'urfCategoryReport', 'Печать отчёта по категориям'),
(171, 'user-role-function', 15, 'urfCopyPrint', 'Печать копии чека'),
(172, 'user-role-function', 16, 'urfTicketControl', 'Проверка билетов'),
(173, 'user-role-function', 17, 'urfViolationReg', 'Регистрация нарушения'),
(174, 'user-role-function', 18, 'urfDepartureReg', 'Регистрация отправления'),
(175, 'user-role-function', 19, 'urfArrivalReg', 'Регистрация прибытия'),
(176, 'user-role-function', 20, 'urfDepartureChange', 'Перенос отправления'),
(177, 'user-role-function', 21, 'urfPerronChange', 'Смена перрона отправления'),
(178, 'user-role-function', 22, 'urfArrivalChange', 'Перенос прибытия'),
(179, 'user-role-function', 23, 'urfBoardingStatus', 'Регистрация на посадку'),
(180, 'user-role-function', 24, 'urfArrivalStatus', 'Регистрация прибытия'),
(181, 'user-role-function', 25, 'urfBackup', 'Резервное копирование данных'),
(182, 'user-role-function', 26, 'urfConfigDiag', 'Диагностика конфигурации'),
(183, 'user-role-function', 27, 'urfStationarConfigure', 'Настройка взаимодействия с ТОР-3'),
(184, 'bus-device-type', 3, 'dtTourniket', 'Турникет'),
(185, 'bus-device-type', 4, 'dtValidator', 'Валидатор'),
(186, 'route-type-service', 1, 'rtsAutomobile', 'Автомобильный'),
(187, 'route-type-service', 2, 'rtsRailWaySuburb', 'ЖД пригород'),
(188, 'route-type-service', 3, 'rtsRailwayFar', 'ЖД дальний'),
(189, 'route-type-service', 4, 'rtsRiver', 'Речной'),
(190, 'route-type-service', 5, 'rtsSea', 'Морской'),
(191, 'route-type-service', 6, 'rtsFerry', 'Паромный'),
(192, 'route-type-service', 7, 'rtsMetro', 'Внеуличный (метро)'),
(193, 'route-type-service', 8, 'rtsAvia', 'Авиационный'),
(194, 'time-bind-ride-transaction', 30, '30min', 'Кол-во минут для привязки транзакции к рейсу'),
(195, 'bus-brand', 61, 'bbANHUI ', 'ANHUI'),
(196, 'bus-device-type', 5, 'dtMUPR', 'МУПР'),
(197, 'sdbp-server-list', 2, 'https://api-mobile.sdbp2.securegateway.ru', 'sdbp2.prod'),
(198, 'sdbp-server-list', 3, 'https://api-mobile.sdbp.securegateway.ru', 'sdbp.prod'),
(199, 'sdbp-server-list', 1, 'https://api-mobile.sdbp-new.test.xz-lab.ru', 'sdbp-new.test'),
(200, 'EMVWriteOffsType', 0, 'woUnknown', 'EMV тип списания не определен'),
(201, 'EMVWriteOffsType', 1, 'woAbonement', 'EMV абонемент'),
(202, 'EMVWriteOffsType', 2, 'woTariff', 'EMV тариф'),
(203, 'EMVValidType', 0, 'vttUnknown', 'Тип срока действия списания не определен'),
(204, 'EMVValidType', 1, 'vttInterval', 'Списание в заданный период времени'),
(205, 'EMVValidType', 2, 'vttDays', 'Списание в период(дней) от первой поездки'),
(206, 'EMVValidType', 3, 'vttIntervalAndDays', 'Списание в период(дней) от первой поезки,  но не позднее даты'),
(207, 'EMVWriteOffsCounterType', 1, 'woctAll', 'Один счетчик на все виды транспорта'),
(208, 'EMVWriteOffsCounterType', 2, 'woctAllowList', 'Один счетчик на группу разрешенных видов транспорта'),
(209, 'EMVWriteOffsCounterType', 3, 'woctSingleCount', 'Для каждого перечисленного вида транспорта свой счетчик'),
(210, 'EMVWriteOffsCounterType', 4, 'woctAllUnlimited', 'Неограниченное число поездок'),
(211, 'EMVWriteOffsCounterType', 5, 'woctAllowListUnlimited', 'Неограниченное число поездок на группу разрешенных видов транспо'),
(212, 'EMVWriteOffsTariffType', 1, 'wottSimple', 'Обычный тариф'),
(213, 'EMVWriteOffsTariffType', 2, 'wottMTT', 'MTT тариф (только для Visa)'),
(214, 'EMVCountTripType', 1, 'cttDay', 'Учет поездок за день'),
(215, 'EMVCountTripType', 2, 'cttMonth', 'Учет поездок за календарный месяц'),
(216, 'EMVCountTripType', 3, 'cttPeriodDay', 'Учет поездок за преиод(дней) начиная с первой поездки'),
(217, 'EMVCountTripType', 4, 'cttPeriodMinutes', 'Учет поездок за приод(в минутах) начиная с первой поездки'),
(218, 'EMVSocialCardType', 1, 'sctSingle', 'Социальная/льготная карта'),
(219, 'EMVSocialCardType', 2, 'sctPlusOne', 'Социальная карта с сопровождающим'),
(220, 'sdbp-server-list', 4, 'https://api-mobile.sdbp.utc12.securegateway.ru', 'sdbp.utc12.prod'),
(221, 'user-role', 6, 'urProductSeller', 'Продавец товаров'),
(222, 'user-role-function', 28, 'urfSendLog', 'Отправка логов на сервер'),
(223, 'user-role-function', 29, 'urfProductSell', 'Продажа товаров'),
(224, 'TerminalTransactionPaymentType', 0, 'ttptCash', 'Наличные'),
(225, 'TerminalTransactionPaymentType', 1, 'ttptMifare', 'Транспортная карта Mifare'),
(226, 'TerminalTransactionPaymentType', 2, 'ttptUltraLite', 'Транспортная карта Ultra lite '),
(227, 'TerminalTransactionPaymentType', 3, 'ttptEMV', 'Банковская карта');

--
-- Дамп данных таблицы `region`
--

INSERT INTO `region` (`region_id`, `country_id`, `title`, `title_short`, `title_lat`) VALUES
(1, 206, 'Вайоминг', NULL, 'Wyoming'),
(2, 206, 'Западная Виргиния', NULL, 'West Virginia'),
(3, 206, 'Висконсин', NULL, 'Wisconsin'),
(4, 206, 'Вашингтон', NULL, 'Washington'),
(5, 206, 'Вермонт', NULL, 'Vermont'),
(7, 206, 'Виргиния', NULL, 'Virginia'),
(8, 206, 'Юта', NULL, 'Utah'),
(10, 206, 'Техас', NULL, 'Texas'),
(11, 206, 'Теннесси', NULL, 'Tennessee'),
(12, 206, 'Южная Дакота', NULL, 'South Dakota'),
(13, 206, 'Южная Каролина', NULL, 'South Carolina'),
(14, 206, 'Род-Айленд', NULL, 'Rhode Island'),
(16, 206, 'Пенсильвания', NULL, 'Pennsylvania'),
(17, 206, 'Орегон', NULL, 'Oregon'),
(18, 206, 'Оклахома', NULL, 'Oklahoma'),
(19, 206, 'Огайо', NULL, 'Ohio'),
(20, 206, 'Нью-Йорк', NULL, 'New York'),
(21, 206, 'Невада', NULL, 'Nevada'),
(23, 206, 'Нью-Джерси', NULL, 'New Jersey'),
(25, 206, 'Небраска', NULL, 'Nebraska'),
(26, 206, 'Северная Дакота', NULL, 'North Dakota'),
(27, 206, 'Северная Каролина', NULL, 'North Carolina'),
(29, 206, 'Миссисипи', NULL, 'Mississippi'),
(31, 206, 'Миссури', NULL, 'Missouri'),
(32, 206, 'Миннесота', NULL, 'Minnesota'),
(33, 206, 'Мичиган', NULL, 'Michigan'),
(34, 206, 'Мэн', NULL, 'Maine'),
(35, 206, 'Мэриленд', NULL, 'Maryland'),
(36, 206, 'Массачусетс', NULL, 'Massachusetts'),
(37, 206, 'Луизиана', NULL, 'Louisiana'),
(38, 206, 'Кентуки', NULL, 'Kentucky'),
(39, 206, 'Канзас', NULL, 'Kansas'),
(40, 206, 'Индиана', NULL, 'Indiana'),
(41, 206, 'Иллинойс', NULL, 'Illinois'),
(42, 206, 'Айдахо', NULL, 'Idaho'),
(43, 206, 'Айова', NULL, 'Iowa'),
(46, 206, 'Джорджия', NULL, 'Georgia'),
(47, 206, 'Флорида', NULL, 'Florida'),
(49, 206, 'Округ Колумбия', NULL, 'District of Columbia'),
(50, 206, 'Коннектикут', NULL, 'Connecticut'),
(51, 206, 'Колорадо', NULL, 'Colorado'),
(52, 206, 'Калифорния', NULL, 'California'),
(53, 206, 'Аризона', NULL, 'Arizona'),
(55, 206, 'Арканзас', NULL, 'Arkansas'),
(56, 206, 'Алабама', NULL, 'Alabama'),
(57, 206, 'Аляска', NULL, 'Alaska'),
(61, 95, 'Альберта', NULL, 'Alberta'),
(62, 95, 'Британская Колумбия', NULL, 'British Columbia'),
(63, 95, 'Квебек', NULL, 'Québec'),
(64, 95, 'Манитоба', NULL, 'Manitoba'),
(65, 95, 'Новая Шотландия', NULL, 'Nova Scotia'),
(66, 95, 'Нунавут', NULL, 'Nunavut'),
(67, 95, 'Нью-Брансуик', NULL, 'New Brunswick/Nouveau-Brunswick'),
(68, 95, 'Ньюфаундленд и Лабрадор', NULL, 'Newfoundland and Labrador'),
(69, 95, 'Онтарио', NULL, 'Ontario'),
(70, 95, 'Остров Принца Эдуарда', NULL, 'Prince Edward Island'),
(71, 95, 'Саскачеван', NULL, 'Saskatchewan'),
(72, 95, 'Северо-Западные территории', NULL, 'Northwest Territories'),
(73, 95, 'Юкон', NULL, 'Yukon'),
(74, 2, 'Новый Южный Уэльс', NULL, 'State of New South Wales'),
(75, 2, 'Квинсленд', NULL, 'State of Queensland'),
(76, 2, 'Южная Австралия', NULL, 'State of South Australia'),
(77, 2, 'Тасмания', NULL, 'State of Tasmania'),
(78, 2, 'Виктория', NULL, 'State of Victoria'),
(79, 2, 'Западная Австралия', NULL, 'State of Western Australia'),
(80, 2, 'Северная территория', NULL, 'Northern Territory'),
(81, 2, 'Австралийская столичная территория', NULL, 'Australian Capital Territory'),
(86, 177, 'Адыгея Республика', 'Адыгея', 'Respublika Adygeya'),
(87, 177, 'Алтай Республика', 'Алтай', 'Respublika Altay'),
(88, 177, 'Алтайский Край', NULL, 'Altayskiy Kray'),
(89, 177, 'Амурская Область', NULL, 'Amurskaya Oblast’'),
(90, 177, 'Архангельская Область', NULL, 'Arkhangel’skaya Oblast’'),
(91, 177, 'Астраханская Область', NULL, 'Astrakhanskaya Oblast’'),
(92, 177, 'Башкортостан Республика', 'Башкирия', 'Bashkortostan'),
(93, 177, 'Белгородская Область', NULL, 'Belgorodskaya Oblast’'),
(94, 177, 'Брянская Область', NULL, 'Bryanskaya Oblast’'),
(95, 177, 'Бурятия Республика', 'Бурятия', 'Respublika Buryatiya'),
(96, 177, 'Владимирская Область', NULL, 'Vladimirskaya Oblast’'),
(97, 177, 'Волгоградская Область', NULL, 'Volgogradskaya Oblast’'),
(98, 177, 'Вологодская Область', NULL, 'Vologodskaya Oblast’'),
(99, 177, 'Воронежская Область', NULL, 'Voronezhskaya Oblast’'),
(101, 177, 'Дагестан Республика', 'Дагестан', 'Dagestan'),
(102, 177, 'Еврейская Автономная область', NULL, 'Yevreyskaya Avtonomnaya Oblast’'),
(103, 177, 'Забайкальский Край', NULL, 'Zabaykal’skiy Kray'),
(104, 177, 'Ивановская Область', NULL, 'Ivanovskaya Oblast’'),
(105, 177, 'Ингушетия Республика', 'Ингушетия', 'Respublika Ingushetiya'),
(106, 177, 'Иркутская Область', NULL, 'Irkutskaya Oblast’'),
(107, 177, 'Кабардино-Балкарская Республика', 'Кабардино-Балкария', 'Kabardino-Balkarskaya Respublika'),
(109, 177, 'Калининградская Область', NULL, 'Kaliningradskaya Oblast’'),
(110, 177, 'Калмыкия Республика', 'Калмыкия', 'Kalmykiya'),
(111, 177, 'Калужская Область', NULL, 'Kaluzhskaya Oblast’'),
(112, 177, 'Камчатский Край', NULL, 'Kamtchatski Kray'),
(113, 177, 'Карачаево-Черкесская Республика', 'Карачаево-Черкессия', 'Karachayevo-Cherkesiya'),
(114, 177, 'Карелия Республика', 'Карелия', 'Respublika Kareliya'),
(115, 177, 'Кемеровская Область', NULL, 'Kemerovskaya Oblast’'),
(116, 177, 'Кировская Область', NULL, 'Kirovskaya Oblast’'),
(117, 177, 'Коми Республика', 'Коми', 'Komi'),
(118, 177, 'Костромская Область', NULL, 'Kostromskaya Oblast’'),
(119, 177, 'Краснодарский Край', NULL, 'Krasnodarskiy Kray'),
(120, 177, 'Красноярский Край', NULL, 'Krasnoyarskiy Kray'),
(121, 177, 'Курганская Область', NULL, 'Kurganskaya Oblast’'),
(122, 177, 'Курская Область', NULL, 'Kurskaya Oblast’'),
(123, 177, 'Ленинградская Область', NULL, 'Leningradskaya Oblast’'),
(124, 177, 'Липецкая Область', NULL, 'Lipetskaya Oblast’'),
(125, 177, 'Магаданская Область', NULL, 'Magadanskaya Oblast’'),
(126, 177, 'Марий Эл Республика', 'Марий Эл', 'Respublika Mariy-El'),
(127, 177, 'Мордовия Республика', 'Мордовия', 'Respublika Mordoviya'),
(129, 177, 'Московская Область', NULL, 'Moscow Oblast'),
(130, 177, 'Мурманская Область', NULL, 'Murmanskaya Oblast’'),
(131, 177, 'Нижегородская Область', NULL, 'Nizhegorodskaya Oblast’'),
(132, 177, 'Новгородская Область', NULL, 'Novgorodskaya Oblast’'),
(133, 177, 'Новосибирская Область', NULL, 'Novosibirskaya Oblast’'),
(134, 177, 'Омская Область', NULL, 'Omskaya Oblast’'),
(135, 177, 'Оренбургская Область', NULL, 'Orenburgskaya Oblast’'),
(136, 177, 'Орловская Область', NULL, 'Orlovskaya Oblast’'),
(137, 177, 'Пензенская Область', NULL, 'Penzenskaya Oblast’'),
(138, 177, 'Пермский Край', NULL, 'Perm Krai'),
(139, 177, 'Приморский Край', NULL, 'Primorskiy Kray'),
(140, 177, 'Псковская Область', NULL, 'Pskovskaya Oblast’'),
(141, 177, 'Ростовская Область', NULL, 'Rostovskaya Oblast’'),
(142, 177, 'Рязанская Область', NULL, 'Ryazanskaya Oblast’'),
(143, 177, 'Самарская Область', NULL, 'Samarskaya Oblast’'),
(145, 177, 'Саратовская Область', NULL, 'Saratovskaya Oblast’'),
(147, 177, 'Сахалинская Область', NULL, 'Sakhalinskaya Oblast’'),
(148, 177, 'Свердловская Область', NULL, 'Sverdlovskaya Oblast’'),
(150, 177, 'Смоленская Область', NULL, 'Smolenskaya Oblast’'),
(151, 177, 'Ставропольский Край', NULL, 'Stavropol’skiy Kray'),
(152, 177, 'Тамбовская Область', NULL, 'Tambovskaya Oblast’'),
(153, 177, 'Татарстан Республика', 'Татарстан', 'Tatarstan'),
(154, 177, 'Тверская Область', NULL, 'Tverskaya Oblast’'),
(155, 177, 'Томская Область', NULL, 'Tomskaya Oblast’'),
(156, 177, 'Тульская Область', NULL, 'Tul’skaya Oblast’'),
(157, 177, 'Тыва Республика', 'Тыва', 'Respublika Tyva'),
(158, 177, 'Тюменская Область', NULL, 'Tyumenskaya Oblast’'),
(159, 177, 'Удмуртская Республика', 'Удмуртия', 'Udmurtskaya Respublika'),
(160, 177, 'Ульяновская Область', NULL, 'Ulyanovsk Oblast'),
(161, 177, 'Хабаровский Край', NULL, 'Khabarovskiy Kray'),
(162, 177, 'Хакасия Республика', 'Хакасия', 'Respublika Khakasiya'),
(163, 177, 'Челябинская Область', NULL, 'Chelyabinskaya Oblast’'),
(164, 177, 'Чеченская Республика', 'Чечня', 'Chechenskaya Respublika'),
(166, 177, 'Чукотский Автономный округ', NULL, 'Chukotskiy Avtonomnyy Okrug'),
(168, 177, 'Ярославская Область', NULL, 'Yaroslavskaya Oblast’'),
(177, 17, 'Ширакская область', NULL, 'Shiraki Marz'),
(178, 17, 'Тавушская область', NULL, 'Tavushi Marz'),
(179, 17, 'Лорийская область', NULL, 'Lorru Marz'),
(180, 17, 'Котайкская область', NULL, 'Kotayk’i Marz'),
(182, 17, 'Гехаркуникская область', NULL, 'Geghark’unik’i Marz'),
(183, 17, 'Армавирская область', NULL, 'Armaviri Marz'),
(184, 17, 'Араратская область', NULL, 'Ararati Marz'),
(185, 17, 'Арагацотнская область', NULL, 'Aragatsotni Marz'),
(186, 24, 'Могилевская область', NULL, 'Mogilyov Oblast'),
(187, 24, 'Минская Область', NULL, 'Minsk Oblast'),
(188, 24, 'Гродненская Область', NULL, 'Grodno Oblast'),
(189, 24, 'Гомельская Область', NULL, 'Gomel Oblast'),
(191, 24, 'Брестская Область', NULL, 'Brest Oblast'),
(192, 91, 'Южно-Казахстанская область', NULL, 'South Kazakhstan'),
(193, 91, 'Северо-Казахстанская область', NULL, 'North Kazakhstan'),
(194, 91, 'Павлодарская Область', NULL, 'Pavlodar Oblysy'),
(195, 91, 'Мангистауская область', NULL, 'Mangistauskaya Oblast’'),
(196, 91, 'Кызылординская Область', NULL, 'Qyzylorda Oblysy'),
(198, 91, 'Карагандинская область', NULL, 'Qaraghandy Oblysy'),
(199, 91, 'Западно-Казахстанская область', NULL, 'West Kazakhstan'),
(200, 91, 'Жамбылская область', NULL, 'Zhambyl Oblysy'),
(201, 91, 'Восточно-Казахстанская область', NULL, 'East Kazakhstan'),
(202, 91, 'Атырауская область', NULL, 'Atyraū Oblysy'),
(203, 91, 'Алматинская Область', NULL, 'Almaty Oblysy'),
(204, 91, 'Актюбинская область', NULL, 'Aktyubinskaya Oblast’'),
(205, 91, 'Акмолинская область', NULL, 'Aqmola Oblysy'),
(206, 100, 'Чуйская Область', NULL, 'Chuyskaya Oblast’'),
(207, 100, 'Ошская Область', NULL, 'Osh Oblasty'),
(208, 100, 'Иссык-Кульская область', NULL, 'Issyk-Kul Region'),
(209, 100, 'Баткенская Область', NULL, 'Batken'),
(312, 144, 'Чимишлийский район', NULL, 'Cimişlia'),
(313, 144, 'Хынчештский район', NULL, 'Hînceşti'),
(314, 144, 'Флорештский район', NULL, 'Floreşti'),
(315, 144, 'Фалештский район', NULL, 'Făleşti'),
(316, 144, 'Унгенский район', NULL, 'Raionul Ungheni'),
(317, 144, 'Сынжерейский район', NULL, 'Sîngerei'),
(318, 144, 'Страшенский район', NULL, 'Raionul Străşeni'),
(319, 144, 'Сорокский район', NULL, 'Raionul Soroca'),
(320, 144, 'Рышканский район', NULL, 'Rîşcani'),
(322, 144, 'Оргеевский район', NULL, 'Orhei'),
(323, 144, 'Окницкий район', NULL, 'Raionul Ocniţa'),
(324, 144, 'Новоаненский район', NULL, 'Anenii Noi'),
(329, 144, 'Кагульский район', NULL, 'Raionul Cahul'),
(330, 144, 'Единецкий район', NULL, 'Raionul Edineţ'),
(332, 144, 'Дрокиевский район', NULL, 'Raionul Drochia'),
(333, 144, 'Дондюшанский район', NULL, 'Donduşeni'),
(334, 144, 'Глодянский район', NULL, 'Glodeni'),
(335, 144, 'Гагаузия', NULL, 'Găgăuzia'),
(336, 144, 'Бричанский район', NULL, 'Briceni'),
(337, 144, 'Бессарабский район', NULL, 'Basarabeasca'),
(425, 223, 'Наманганская область', NULL, 'Namangan Province'),
(430, 223, 'Бухарская область', NULL, 'Bukhara Province'),
(431, 223, 'Андижанская область', NULL, 'Andijan'),
(432, 224, 'Киев', NULL, 'Misto Kyyiv'),
(433, 224, 'Черновицкая область', NULL, 'Chernivets’ka Oblast’'),
(434, 224, 'Черниговская область', NULL, 'Chernihivs’ka Oblast’'),
(435, 224, 'Черкасская область', NULL, 'Cherkas’ka Oblast’'),
(436, 224, 'Хмельницкая область', NULL, 'Khmel’nyts’ka Oblast’'),
(437, 224, 'Херсонская область', NULL, 'Khersons’ka Oblast’'),
(438, 224, 'Харьковская область', NULL, 'Kharkivs’ka Oblast’'),
(439, 224, 'Тернопольская область', NULL, 'Ternopil’s’ka Oblast’'),
(440, 224, 'Сумская область', NULL, 'Sums’ka Oblast’'),
(442, 224, 'Полтавская область', NULL, 'Poltavs’ka Oblast’'),
(443, 224, 'Одесская область', NULL, 'Odes’ka Oblast’'),
(444, 224, 'Николаевская область', NULL, 'Mykolayivs’ka Oblast’'),
(445, 224, 'Львовская область', NULL, 'L’vivs’ka Oblast’'),
(446, 224, 'Луганская область', NULL, 'Luhans’ka Oblast’'),
(447, 224, 'Кировоградская область', NULL, 'Kirovohrads’ka Oblast’'),
(448, 224, 'Киевская область', NULL, 'Kyiv Oblast'),
(449, 224, 'Ивано-Франковская область', NULL, 'Ivano-Frankivs’ka Oblast’'),
(450, 224, 'Запорожская область', NULL, 'Zaporiz’ka Oblast’'),
(451, 224, 'Закарпатская область', NULL, 'Zakarpattia Oblast'),
(452, 224, 'Житомирская область', NULL, 'Zhytomyrs’ka Oblast’'),
(453, 224, 'Донецкая область', NULL, 'Donets’ka Oblast’'),
(454, 224, 'Днепропетровская область', NULL, "Dnipropetrovska Oblast'"),
(455, 224, 'Волынская область', NULL, 'Volyns’ka Oblast’'),
(456, 224, 'Винницкая область', NULL, 'Vinnyts’ka Oblast’'),
(459, 177, 'Ямало-Ненецкий Автономный округ', NULL, 'Yamalo-Nenetskiy Avtonomnyy Okrug'),
(481, 177, 'Ненецкий Автономный округ', NULL, 'Nenetskiy Avtonomnyy Okrug'),
(2074, 34, 'Мату-Гросу', NULL, 'Mato Grosso'),
(2075, 250, 'Лимпопо', NULL, 'Limpopo'),
(2078, 34, 'Минас-Жерайс', NULL, 'Minas Gerais'),
(2081, 134, 'Кедах', NULL, 'Kedah'),
(2097, 87, 'Андалусия', NULL, 'Andalucía'),
(2099, 34, 'Пара', NULL, 'Pará'),
(2100, 240, 'Тичино', NULL, 'Ticino'),
(2102, 168, 'Западная Новая Британия', NULL, 'West New Britain Province'),
(2122, 156, 'Северная Голландия', NULL, 'Provincie Noord-Holland'),
(2131, 43, 'Уэльс', NULL, 'Wales'),
(2133, 43, 'Англия', NULL, 'England'),
(2138, 102, 'Шэньси', NULL, 'Shaanxi'),
(2141, 88, 'Марке', NULL, 'Marche'),
(2149, 228, 'Западные Висайи', NULL, 'Western Visayas'),
(2154, 253, 'Аомори', NULL, 'Aomori-ken'),
(2171, 34, 'Амазонас', NULL, 'Amazonas'),
(2178, 241, 'Норрботтен', NULL, 'Norrbotten'),
(2189, 170, 'Арекипа', NULL, 'Arequipa'),
(2199, 34, 'Токантинс', NULL, 'Tocantins'),
(2201, 80, 'Телангана', NULL, 'Telangana'),
(2203, 64, 'Сермерсоок', NULL, 'Sermersooq'),
(2219, 89, 'Шабва', NULL, 'Shabwah'),
(2226, 253, 'Канагава', NULL, 'Kanagawa'),
(2227, 64, 'Каасуитсуп', NULL, 'Qaasuitsup'),
(2231, 80, 'Махараштра', NULL, 'State of Mahārāshtra'),
(2235, 84, 'Хузестан', NULL, 'Khuzestan'),
(2236, 80, 'Гуджарат', NULL, 'State of Gujarāt'),
(2238, 61, 'Грасьяс-а-Дьос', NULL, 'Departamento de Gracias a Dios'),
(2239, 159, 'Кентербери', NULL, 'Canterbury'),
(2248, 170, 'Аякучо', NULL, 'Ayacucho'),
(2249, 221, 'Чанкыры', NULL, 'Çankırı'),
(2251, 227, 'Западный округ', NULL, 'Western Division'),
(2255, 84, 'Мазендеран', NULL, 'Ostān-e Māzandarān'),
(2260, 80, 'Западная Бенгалия', NULL, 'West Bengal'),
(2264, 165, 'Синд', NULL, 'Sindh'),
(2269, 221, 'Мардин', NULL, 'Mardin'),
(2274, 191, 'Тамбакунда', NULL, 'Tambacounda'),
(2277, 235, 'Мбому', NULL, 'Mbomou'),
(2281, 220, 'Балканский велаят', NULL, 'Balkan'),
(2282, 43, 'Северная Ирландия', NULL, 'Northern Ireland'),
(2297, 80, 'Карнатака', NULL, 'State of Karnataka'),
(2302, 84, 'Хормозган', NULL, 'Hormozgan'),
(2305, 81, 'Западная Ява', NULL, 'Jawa Barat'),
(2324, 88, 'Апулия', NULL, 'Puglia'),
(2329, 43, 'Шотландия', NULL, 'Scotland'),
(2337, 221, 'Кахраманмараш', NULL, 'Kahramanmaraş'),
(2338, 221, 'Самсун', NULL, 'Samsun'),
(2339, 221, 'Трабзон', NULL, 'Trabzon'),
(2340, 221, 'Ван', NULL, 'Van'),
(2341, 221, 'Кыркларели', NULL, 'Kırklareli'),
(2342, 221, 'Айдын', NULL, 'Aydın'),
(2344, 221, 'Маниса', NULL, 'Manisa'),
(2355, 235, 'Уам', NULL, 'Ouham'),
(2356, 221, 'Батман', NULL, 'Batman'),
(2383, 34, 'Пернамбуку', NULL, 'Pernambuco'),
(2386, 221, 'Кастамону', NULL, 'Kastamonu'),
(2391, 11, 'Бенгела', NULL, 'Benguela'),
(2397, 235, 'Мамбере-Кадеи', NULL, 'Mambéré-Kadéï'),
(2400, 59, 'Берлин', NULL, 'Land Berlin'),
(2409, 80, 'Чхаттисгарх', NULL, 'State of Chhattīsgarh'),
(2410, 87, 'Страна Басков', NULL, 'Euskal Autonomia Erkidegoa'),
(2412, 235, 'Вакага', NULL, 'Vakaga'),
(2432, 17, 'Сюникская область', NULL, 'Syunik’i Marz'),
(2433, 231, 'Бретань', NULL, 'Bretagne'),
(2436, 231, 'Земли Луары', NULL, 'Pays de la Loire'),
(2438, 221, 'Эскишехир', NULL, 'Eskişehir'),
(2444, 236, 'Хаджер-Ламис', NULL, 'Hadjer-Lamis'),
(2446, 81, 'Северный Сулавеси', NULL, 'Sulawesi Utara'),
(2450, 17, 'Вайоцдзорская область', NULL, 'Vayots’ Dzor'),
(2453, 24, 'Минск', NULL, 'Horad Minsk'),
(2456, 221, 'Орду', NULL, 'Ordu'),
(2457, 221, 'Ушак', NULL, 'Uşak'),
(2458, 221, 'Кютахья', NULL, 'Kütahya'),
(2460, 221, 'Бартын', NULL, 'Bartın'),
(2461, 221, 'Конья', NULL, 'Konya'),
(2462, 221, 'Йозгат', NULL, 'Yozgat'),
(2464, 221, 'Денизли', NULL, 'Denizli'),
(2465, 221, 'Сивас', NULL, 'Sivas'),
(2468, 100, 'Джалал-Абадская область', NULL, 'Jalal-Abad oblast'),
(2469, 221, 'Эдирне', NULL, 'Edirne'),
(2474, 221, 'Кайсери', NULL, 'Kayseri'),
(2475, 221, 'Гиресун', NULL, 'Giresun'),
(2476, 221, 'Болу', NULL, 'Bolu'),
(2477, 221, 'Ардахан', NULL, 'Ardahan'),
(2479, 221, 'Ыгдыр', NULL, 'Iğdır'),
(2480, 221, 'Битлис', NULL, 'Bitlis'),
(2481, 221, 'Балыкесир', NULL, 'Balıkesir'),
(2483, 221, 'Артвин', NULL, 'Artvin'),
(2485, 221, 'Адана', NULL, 'Adana'),
(2486, 221, 'Муш', NULL, 'Muş'),
(2487, 221, 'Мугла', NULL, 'Muğla'),
(2488, 221, 'Бингёль', NULL, 'Bingöl'),
(2491, 221, 'Синоп', NULL, 'Sinop'),
(2492, 221, 'Текирдаг', NULL, 'Tekirdağ'),
(2494, 221, 'Чорум', NULL, 'Çorum'),
(2495, 221, 'Ыспарта', NULL, 'Isparta'),
(2496, 221, 'Газиантеп', NULL, 'Gaziantep'),
(2497, 221, 'Аксарай', NULL, 'Aksaray'),
(2499, 221, 'Османие', NULL, 'Osmaniye'),
(2500, 100, 'Таласская Область', NULL, 'Talas'),
(2501, 220, 'Дашогузский велаят', NULL, 'Daşoguz Welaýaty'),
(2502, 220, 'Ахалский велаят', NULL, 'Ahal'),
(2503, 221, 'Ялова', NULL, 'Yalova'),
(2504, 221, 'Чанаккале', NULL, 'Çanakkale'),
(2506, 220, 'Лебапский велаят', NULL, 'Lebap'),
(2508, 221, 'Мерсин', NULL, 'Mersin'),
(2509, 221, 'Амасья', NULL, 'Amasya'),
(2510, 221, 'Сиирт', NULL, 'Siirt'),
(2516, 221, 'Эрзурум', NULL, 'Erzurum'),
(2517, 221, 'Тунджели', NULL, 'Tunceli'),
(2518, 221, 'Биледжик', NULL, 'Bilecik'),
(2519, 221, 'Ширнак', NULL, 'Şırnak'),
(2520, 221, 'Бурдур', NULL, 'Burdur'),
(2521, 221, 'Малатья', NULL, 'Malatya'),
(2522, 221, 'Эрзинджан', NULL, 'Erzincan'),
(2523, 221, 'Стамбул', NULL, 'İstanbul'),
(2528, 34, 'Гояс', NULL, 'Goiás'),
(2530, 11, 'Мошико', NULL, 'Moxico'),
(2537, 34, 'Мараньян', NULL, 'Maranhão'),
(2567, 84, 'Фарс', NULL, 'Fars'),
(2589, 80, 'Трипура', NULL, 'Tripura'),
(2592, 141, 'Агуаскальентес', NULL, 'Estado de Aguascalientes'),
(2593, 253, 'Окинава', NULL, 'Okinawa'),
(2600, 206, 'Гавайи', NULL, 'Hawaii'),
(2603, 159, 'Нортленд', NULL, 'Northland'),
(2606, 80, 'Мизорам', NULL, 'Mizoram'),
(2611, 253, 'Акита', NULL, 'Akita'),
(2617, 141, 'Сонора', NULL, 'Estado de Sonora'),
(2620, 159, 'Отаго', NULL, 'Otago'),
(2622, 170, 'Укаяли', NULL, 'Ucayali'),
(2634, 220, 'Марыйский велаят', NULL, 'Mary'),
(2636, 253, 'Гумма', NULL, 'Gunma-ken'),
(2641, 221, 'Кыршехир', NULL, 'Kırşehir'),
(2642, 221, 'Кырыккале', NULL, 'Kırıkkale'),
(2653, 241, 'Даларна', NULL, 'Dalarna'),
(2656, 19, 'Гильменд', NULL, 'Helmand'),
(2657, 106, 'Кювет', NULL, 'Cuvette'),
(2660, 59, 'Бремен', NULL, 'Bremen'),
(2661, 235, 'Верхнее Котто', NULL, 'Haute-Kotto'),
(2664, 59, 'Северный Рейн-Вестфалия', NULL, 'Nordrhein-Westfalen'),
(2682, 87, 'Кастилия и Леон', NULL, 'Castilla y León'),
(2691, 80, 'Химачал-Прадеш', NULL, 'State of Himāchal Pradesh'),
(2694, 80, 'Мадхья-Прадеш', NULL, 'Madhya Pradesh'),
(2695, 172, 'Куявско-Поморское воеводство', NULL, 'Województwo Kujawsko-Pomorskie'),
(2707, 172, 'Мазовецкое воеводство', NULL, 'Województwo Mazowieckie'),
(2710, 159, 'Веллингтон', NULL, 'Wellington'),
(2711, 3, 'Вена', NULL, 'Wien'),
(2718, 48, 'Викеке', NULL, 'Viqueque'),
(2724, 149, 'Кхомас', NULL, 'Khomas'),
(2735, 172, 'Нижнесилезское воеводство', NULL, 'Województwo Dolnośląskie'),
(2737, 102, 'Хубэй', NULL, 'Hubei Sheng'),
(2743, 80, 'Ассам', NULL, 'Assam'),
(2745, 241, 'Евлеборг', NULL, 'Gävleborgs län'),
(2754, 59, 'Нижняя Саксония', NULL, 'Lower Saxony'),
(2760, 19, 'Пактия', NULL, 'Wilāyat-e Paktiyā'),
(2767, 80, 'Бихар', NULL, 'State of Bihār'),
(2775, 88, 'Лигурия', NULL, 'Liguria'),
(2779, 19, 'Герат', NULL, 'Herāt'),
(2780, 241, 'Вестра-Гёталанд', NULL, 'Västra Götalands län'),
(2783, 159, 'Гисборн', NULL, 'Gisborne'),
(2786, 149, 'Омахеке', NULL, 'Omaheke'),
(2795, 3, 'Штирия', NULL, 'Steiermark'),
(2798, 11, 'Уамбо', NULL, 'Huambo'),
(2806, 81, 'Северная Суматра', NULL, 'Sumatera Utara'),
(2815, 191, 'Дакар', NULL, 'Dakar'),
(2821, 80, 'Даман и Диу', NULL, 'Union Territory of Damān and Diu'),
(2836, 80, 'Джаркханд', NULL, 'State of Jharkhand'),
(2838, 242, 'Северная провинция', NULL, 'Northern Province'),
(2848, 134, 'Джохор', NULL, 'Johor'),
(2852, 80, 'Нагаленд', NULL, 'State of Nāgāland'),
(2882, 240, 'Женева', NULL, 'Genève'),
(2883, 172, 'Подкарпатское воеводство', NULL, 'Województwo Podkarpackie'),
(2884, 87, 'Каталония', NULL, 'Catalunya'),
(2890, 3, 'Зальцбург', NULL, 'Salzburg'),
(2907, 191, 'Зигиншор', NULL, 'Ziguinchor'),
(2914, 253, 'Тоттори', NULL, 'Tottori'),
(2918, 170, 'Лорето', NULL, 'Loreto'),
(2924, 228, 'Северный Минданао', NULL, 'Northern Mindanao'),
(2926, 80, 'Манипур', NULL, 'Manipur'),
(2927, 106, 'Ликуала', NULL, 'Likouala'),
(2928, 159, 'Саутленд', NULL, 'Southland'),
(2930, 3, 'Тироль', NULL, 'Tirol'),
(2951, 11, 'Кабинда', NULL, 'Cabinda'),
(2969, 34, 'Пиауи', NULL, 'Piauí'),
(2976, 191, 'Каолак', NULL, 'Kaolack'),
(2981, 59, 'Баден-Вюртемберг', NULL, 'Baden-Württemberg Region'),
(2982, 241, 'Эребру', NULL, 'Örebro län'),
(2990, 59, 'Гессен', NULL, 'Hessen'),
(2994, 88, 'Сицилия', NULL, 'Sicilia'),
(2995, 172, 'Силезское воеводство', NULL, 'Województwo Śląskie'),
(3019, 253, 'Фукуока', NULL, 'Fukuoka Prefecture'),
(3023, 3, 'Каринтия', NULL, 'Kärnten'),
(3028, 80, 'Керала', NULL, 'State of Kerala'),
(3029, 80, 'Тамилнад', NULL, 'State of Tamil Nādu'),
(3033, 191, 'Колда', NULL, 'Kolda'),
(3035, 250, 'Мпумаланга', NULL, 'Mpumalanga'),
(3055, 172, 'Малопольское воеводство', NULL, 'Województwo Małopolskie'),
(3068, 141, 'Керетаро', NULL, 'Estado de Querétaro'),
(3071, 253, 'Кумамото', NULL, 'Kumamoto'),
(3073, 19, 'Кундуз', NULL, 'Kunduz'),
(3076, 253, 'Вакаяма', NULL, 'Wakayama'),
(3091, 214, 'Кара', NULL, 'Kara'),
(3093, 88, 'Калабрия', NULL, 'Calabria'),
(3097, 228, 'Илокос', NULL, 'Ilocos'),
(3101, 221, 'Караман', NULL, 'Karaman'),
(3115, 170, 'Лима', NULL, 'Lima'),
(3118, 3, 'Верхняя Австрия', NULL, 'Oberösterreich'),
(3119, 173, 'Лиссабон', NULL, 'Distrito de Lisboa'),
(3120, 87, 'Риоха', NULL, 'La Rioja'),
(3121, 172, 'Лодзинское воеводство', NULL, 'Województwo Łódzkie'),
(3123, 214, 'Приморская область', NULL, 'Maritime'),
(3143, 19, 'Балх', NULL, 'Balkh'),
(3144, 119, 'Масеру', NULL, 'Maseru'),
(3157, 228, 'Автономный регион в Мусульманском Минданао', NULL, 'Autonomous Region in Muslim Mindanao'),
(3160, 81, 'Восточная Ява', NULL, 'Jawa Timur'),
(3180, 168, 'Восточная Новая Британия', NULL, 'East New Britain Province'),
(3188, 191, 'Матам', NULL, 'Matam'),
(3191, 253, 'Нагано', NULL, 'Nagano-ken'),
(3193, 84, 'Хорасан-Резави', NULL, 'Ostān-e Khorāsān-e Raẕavī'),
(3199, 87, 'Мелилья', NULL, 'Melilla'),
(3202, 59, 'Бавария', NULL, 'Bavaria'),
(3212, 253, 'Миядзаки', NULL, 'Miyazaki'),
(3215, 149, 'Ошикото', NULL, 'Oshikoto'),
(3218, 253, 'Хоккайдо', NULL, 'Hokkaido'),
(3228, 105, 'Нгазиджа', NULL, 'Grande Comore'),
(3236, 87, 'Мурсия', NULL, 'Región de Murcia'),
(3245, 143, 'Нампула', NULL, 'Nampula'),
(3247, 64, 'Куяллек', NULL, 'Kujalleq'),
(3252, 88, 'Кампания', NULL, 'Campania'),
(3259, 253, 'Ниигата', NULL, 'Niigata-ken'),
(3263, 242, 'Центральная провинция', NULL, 'Central Province'),
(3272, 71, 'Обок', NULL, 'Obock'),
(3276, 253, 'Окаяма', NULL, 'Okayama-ken'),
(3278, 149, 'Ошана', NULL, 'Oshana'),
(3300, 81, 'Южная Суматра', NULL, 'Sumatera Selatan'),
(3305, 87, 'Наварра', NULL, 'Navarra'),
(3312, 227, 'Центральный округ', NULL, 'Central Division'),
(3317, 88, 'Абруццо', NULL, 'Abruzzo'),
(3318, 88, 'Тоскана', NULL, 'Toscana'),
(3320, 170, 'Ика', NULL, 'Ica'),
(3321, 170, 'Пьюра', NULL, 'Piura'),
(3331, 172, 'Великопольское воеводство', NULL, 'Województwo Wielkopolskie'),
(3334, 80, 'Пудучерри', NULL, 'Union Territory of Puducherry'),
(3372, 206, 'Делавэр', NULL, 'Delaware'),
(3375, 167, 'Эмбера-Воунаан', NULL, 'Emberá-Wounaan'),
(3381, 156, 'Южная Голландия', NULL, 'Provincie Zuid-Holland'),
(3385, 59, 'Саар', NULL, 'Saarland'),
(3409, 87, 'Кантабрия', NULL, 'Cantabria'),
(3412, 239, 'Столичная область', NULL, 'Región Metropolitana de Santiago'),
(3416, 170, 'Анкаш', NULL, 'Ancash'),
(3419, 87, 'Арагон', NULL, 'Aragon'),
(3431, 253, 'Мияги', NULL, 'Miyagi-ken'),
(3474, 71, 'Таджура', NULL, 'Tadjourah'),
(3478, 253, 'Кагава', NULL, 'Kagawa-ken'),
(3479, 170, 'Такна', NULL, 'Tacna'),
(3484, 170, 'Тумбес', NULL, 'Tumbes'),
(3495, 80, 'Аруначал-Прадеш', NULL, 'State of Arunāchal Pradesh'),
(3509, 253, 'Хёго', NULL, 'Hyōgo'),
(3524, 88, 'Пьемонт', NULL, 'Piemonte'),
(3528, 253, 'Ямагути', NULL, 'Yamaguchi'),
(3549, 253, 'Фукуи', NULL, 'Fukui'),
(3550, 253, 'Фукусима', NULL, 'Fukushima-ken'),
(3554, 241, 'Халланд', NULL, 'Halland'),
(3557, 253, 'Иватэ', NULL, 'Iwate'),
(3562, 170, 'Хунин', NULL, 'Junín'),
(3565, 80, 'Харьяна', NULL, 'State of Haryāna'),
(3576, 49, 'Хошимин', NULL, 'Ho Chi Minh City'),
(3580, 170, 'Пуно', NULL, 'Puno'),
(3599, 80, 'Мегхалая', NULL, 'Meghālaya'),
(3601, 172, 'Западнопоморское воеводство', NULL, 'Województwo Zachodniopomorskie'),
(3609, 253, 'Тотиги', NULL, 'Tochigi'),
(3614, 179, 'Вранча', NULL, 'Vrancea'),
(3619, 156, 'Утрехт', NULL, 'Provincie Utrecht'),
(3620, 59, 'Бранденбург', NULL, 'Brandenburg'),
(3625, 172, 'Подляское воеводство', NULL, 'Województwo Podlaskie'),
(3626, 172, 'Опольское воеводство', NULL, 'Województwo Opolskie'),
(3627, 172, 'Люблинское воеводство', NULL, 'Województwo Lubelskie'),
(3628, 172, 'Варминско-Мазурское воеводство', NULL, 'Województwo Warmińsko-Mazurskie'),
(3630, 3, 'Нижняя Австрия', NULL, 'Niederösterreich'),
(3632, 59, 'Саксония-Анхальт', NULL, 'Saxony-Anhalt'),
(3635, 246, 'Вырумаа', NULL, 'Võrumaa'),
(3642, 246, 'Пярнумаа', NULL, 'Pärnumaa'),
(3645, 144, 'Ниспоренский район', NULL, 'Nisporeni'),
(3650, 144, 'Кантемирский район', NULL, 'Cantemir'),
(3666, 208, 'Согдийская область', NULL, 'Viloyati Sughd'),
(3673, 246, 'Валгамаа', NULL, 'Valgamaa'),
(3675, 59, 'Рейнланд-Пфальц', NULL, 'Rheinland-Pfalz'),
(3677, 246, 'Рапламаа', NULL, 'Raplamaa'),
(3686, 144, 'Резинский район', NULL, 'Rezina'),
(3695, 3, 'Бургенланд', NULL, 'Burgenland'),
(3696, 144, 'Шолданештский район', NULL, 'Şoldăneşti'),
(3703, 179, 'Прахова', NULL, 'Prahova'),
(3737, 29, 'Софийская область', NULL, 'Sofia'),
(3806, 144, 'Леовский район', NULL, 'Leova'),
(3816, 231, 'Прованс — Альпы — Лазурный Берег', NULL, "Provence-Alpes-Côte d'Azur"),
(3820, 253, 'Гифу', NULL, 'Gifu'),
(3840, 156, 'Гронинген', NULL, 'Provincie Groningen'),
(3843, 156, 'Гелдерланд', NULL, 'Provincie Gelderland'),
(3846, 172, 'Свентокшишское воеводство', NULL, 'Województwo Świętokrzyskie'),
(3857, 235, 'Нижнее Котто', NULL, 'Basse-Kotto'),
(3868, 239, 'Тарапака', NULL, 'Región de Tarapacá'),
(4002, 224, 'Ровненская область', NULL, 'Rivnens’ka Oblast’'),
(4006, 100, 'Бишкек', NULL, 'Gorod Bishkek'),
(4013, 229, 'Лапландия', NULL, 'Lapland'),
(4020, 44, 'Яс-Надькун-Сольнок', NULL, 'Jász-Nagykun-Szolnok'),
(4022, 44, 'Фейер', NULL, 'Fejér megye'),
(4025, 44, 'Зала', NULL, 'Zala megye'),
(4026, 44, 'Комаром-Эстергом', NULL, 'Komárom-Esztergom'),
(4030, 44, 'Бач-Кишкун', NULL, 'Bács-Kiskun county'),
(4040, 88, 'Венеция', NULL, 'Veneto'),
(4063, 172, 'Поморское воеводство', NULL, 'Województwo Pomorskie'),
(4069, 69, 'Южная Дания', NULL, 'Region Syddanmark'),
(4070, 69, 'Центральная Ютландия', NULL, 'Region Midtjylland'),
(4074, 156, 'Лимбург', NULL, 'Provincie Limburg'),
(4075, 240, 'Юра', NULL, 'Jura'),
(4076, 156, 'Северный Брабант', NULL, 'Provincie Noord-Brabant'),
(4078, 3, 'Форарльберг', NULL, 'Vorarlberg'),
(4079, 59, 'Шлезвиг-Гольштейн', NULL, 'Schleswig-Holstein'),
(4096, 118, '', NULL, 'Jaunpiebalgas Novads'),
(4097, 82, '', NULL, 'Muḩāfaz̧at al ‘Aqabah'),
(4098, 262, '', NULL, 'Latjoor'),
(4099, 30, '', NULL, 'El Beni'),
(4100, 237, '', NULL, 'Opština Rožaje'),
(4101, 161, '', NULL, 'Aust-Agder fylke'),
(4102, 257, 'Сектор Газа', NULL, 'Gaza Strip'),
(4103, 57, '', NULL, 'Conakry Region'),
(4104, 179, '', NULL, 'Judeţul Mehedinţi'),
(4105, 122, '', NULL, 'Sha‘bīyat an Nuqāţ al Khams'),
(4106, 173, '', NULL, 'Distrito de Viana do Castelo'),
(4107, 209, '', NULL, 'Changwat Uttaradit'),
(4108, 243, '', NULL, 'Provincia de El Oro'),
(4109, 137, '', NULL, 'Ta’ Kerċem'),
(4110, 253, 'Сидзуока', NULL, 'Shizuoka'),
(4111, 81, '', NULL, 'Propinsi Bengkulu'),
(4112, 121, '', NULL, 'Mohafazat Liban-Nord'),
(4113, 184, 'Сан-Томе', NULL, 'São Tomé'),
(4114, 61, '', NULL, 'Departamento de Lempira'),
(4115, 38, '', NULL, 'Centre'),
(4116, 128, '', NULL, 'Analanjirofo Region'),
(4117, 204, '', NULL, 'Khartoum'),
(4118, 38, 'Centre-Est', NULL, 'Centre-Est'),
(4119, 128, 'Boeny Region', NULL, 'Boeny Region'),
(4120, 20, '', NULL, 'Bimini District'),
(4121, 201, '', NULL, 'Dolenjske Toplice'),
(4122, 215, '', NULL, 'Atafu'),
(4123, 255, '', NULL, 'Streymoyar Sýsla'),
(4124, 118, 'Saldus Municipality', NULL, 'Saldus Municipality'),
(4125, 194, '', NULL, 'Grenadines'),
(4126, 201, 'Vrhnika', NULL, 'Vrhnika'),
(4127, 88, 'Базиликата', NULL, 'Basilicata'),
(4128, 193, '', NULL, 'Saint John Figtree'),
(4129, 98, '', NULL, 'Trans Nzoia'),
(4130, 98, 'Vihiga', NULL, 'Vihiga'),
(4131, 19, 'Урузган', NULL, 'Uruzgān'),
(4132, 16, '', NULL, 'Tierra del Fuego Province'),
(4133, 303, '', NULL, 'Alderney'),
(4134, 209, 'Changwat Chon Buri', NULL, 'Changwat Chon Buri'),
(4135, 119, 'Бута-Буте', NULL, 'Butha-Buthe'),
(4136, 141, '', NULL, 'Estado de Tabasco'),
(4137, 167, '', NULL, 'Provincia de Panamá'),
(4138, 25, '', NULL, 'Cayo District'),
(4139, 262, 'Maiwut', NULL, 'Maiwut'),
(4140, 106, '', NULL, 'Pool'),
(4141, 157, '', NULL, 'Departamento de Chinandega'),
(4142, 34, 'Эспириту-Санту', NULL, 'Espírito Santo'),
(4143, 226, '', NULL, 'Departamento de San José'),
(4144, 118, 'Mālpils Novads', NULL, 'Mālpils Novads'),
(4145, 201, 'Grosuplje', NULL, 'Grosuplje'),
(4146, 118, 'Борисоглебск', NULL, 'Daugavpils'),
(4147, 51, '', NULL, 'Sud-Est'),
(4148, 201, 'Dornava', NULL, 'Dornava'),
(4149, 16, 'La Rioja Province', NULL, 'La Rioja Province'),
(4150, 219, '', NULL, 'Gouvernorat de Bizerte'),
(4151, 174, '', NULL, 'Añasco'),
(4152, 16, 'Corrientes Province', NULL, 'Corrientes Province'),
(4153, 120, '', NULL, 'Nimba County'),
(4154, 244, '', NULL, 'Provincia de Bioko Sur'),
(4155, 240, '', NULL, 'Kanton Basel-Stadt'),
(4156, 137, 'Il-Furjana', NULL, 'Il-Furjana'),
(4157, 118, 'Вентспилс', NULL, 'Ventspils'),
(4158, 240, 'Kanton Basel-Landschaft', NULL, 'Kanton Basel-Landschaft'),
(4159, 201, 'Moravske Toplice', NULL, 'Moravske Toplice'),
(4160, 137, 'Айнсилем', NULL, 'Għajnsielem'),
(4161, 179, 'Judeţul Călăraşi', NULL, 'Judeţul Călăraşi'),
(4162, 142, '', NULL, 'State of Yap'),
(4163, 247, '', NULL, 'Somali Region'),
(4164, 4, '', NULL, 'Quba Rayon'),
(4165, 146, 'Kosogol’skiy Aymak', NULL, 'Hövsgöl Aymag'),
(4166, 219, 'Gouvernorat de Nabeul', NULL, 'Gouvernorat de Nabeul'),
(4167, 38, 'Nord', NULL, 'Nord'),
(4168, 76, '', NULL, 'Eastern Province'),
(4169, 195, '', NULL, 'Canaries'),
(4170, 49, '', NULL, 'Thành Phố Hà Nội'),
(4171, 201, 'Подвелка', NULL, 'Podvelka'),
(4172, 128, 'Haute Matsiatra Region', NULL, 'Haute Matsiatra Region'),
(4173, 300, '', NULL, 'Malew'),
(4174, 98, 'Narok', NULL, 'Narok'),
(4175, 146, '', NULL, 'Dornogovĭ Aymag'),
(4176, 180, '', NULL, 'Departamento de La Paz'),
(4177, 104, '', NULL, 'Departamento del Atlántico'),
(4178, 166, '', NULL, 'State of Koror'),
(4179, 30, 'Departamento de Pando', NULL, 'Departamento de Pando'),
(4180, 201, 'Dobrova-Polhov Gradec', NULL, 'Dobrova-Polhov Gradec'),
(4181, 137, 'Saint Venera', NULL, 'Saint Venera'),
(4182, 234, '', NULL, 'Grad Zagreb'),
(4183, 66, 'Квемо-Картли', NULL, 'Kvemo Kartli'),
(4184, 48, '', NULL, 'Oecusse'),
(4185, 253, 'Ибараки', NULL, 'Ibaraki'),
(4186, 211, '', NULL, 'Njombe Region'),
(4187, 201, 'Občina Trnovska vas', NULL, 'Občina Trnovska vas'),
(4188, 81, 'Бантен', NULL, 'Banten'),
(4189, 74, '', NULL, 'Muḩāfaz̧at al Iskandarīyah'),
(4190, 253, '', NULL, 'Ehime'),
(4191, 201, 'Kranjska Gora', NULL, 'Kranjska Gora'),
(4192, 4, 'Ismayilli Rayon', NULL, 'Ismayilli Rayon'),
(4193, 7, '', NULL, 'Oran'),
(4194, 80, 'Раджастхан', NULL, 'State of Rājasthān'),
(4195, 19, '', NULL, 'Wilāyat-e Bāmyān'),
(4196, 58, '', NULL, 'Bolama'),
(4197, 102, '', NULL, 'Shanghai Shi'),
(4198, 201, 'Občina Šenčur', NULL, 'Občina Šenčur'),
(4199, 74, 'South Sinai Governorate', NULL, 'South Sinai Governorate'),
(4200, 51, 'Nord-Ouest', NULL, 'Nord-Ouest'),
(4201, 219, 'Gouvernorat de Médenine', NULL, 'Gouvernorat de Médenine'),
(4202, 118, 'Stopiņu Novads', NULL, 'Stopiņu Novads'),
(4203, 72, '', NULL, 'Saint Joseph'),
(4204, 4, 'Xocalı Rayonu', NULL, 'Xocalı Rayonu'),
(4205, 72, 'Saint John', NULL, 'Saint John'),
(4206, 240, 'Kanton Schaffhausen', NULL, 'Kanton Schaffhausen'),
(4207, 107, '', NULL, 'Hwanghae-namdo'),
(4208, 140, '', NULL, 'Ailuk Atoll'),
(4209, 253, 'Сига', NULL, 'Shiga'),
(4210, 49, 'Tỉnh Thanh Hóa', NULL, 'Tỉnh Thanh Hóa'),
(4211, 72, 'Saint Andrew', NULL, 'Saint Andrew'),
(4212, 209, 'Changwat Nakhon Ratchasima', NULL, 'Changwat Nakhon Ratchasima'),
(4213, 209, 'Changwat Nakhon Phanom', NULL, 'Changwat Nakhon Phanom'),
(4214, 62, '', NULL, 'Sai Kung District'),
(4215, 20, 'Grand Cay District', NULL, 'Grand Cay District'),
(4216, 174, 'Utuado Municipio', NULL, 'Utuado Municipio'),
(4217, 154, '', NULL, 'Benue State'),
(4218, 147, '', NULL, 'Parish of Saint Anthony'),
(4219, 16, 'Río Negro Province', NULL, 'Río Negro Province'),
(4220, 209, 'Changwat Nakhon Nayok', NULL, 'Changwat Nakhon Nayok'),
(4221, 19, 'Velāyat-e Khowst', NULL, 'Velāyat-e Khowst'),
(4222, 174, 'Guánica Municipio', NULL, 'Guánica Municipio'),
(4223, 167, 'Гуна-Яла', NULL, 'Guna Yala'),
(4224, 16, 'Ciudad Autónoma de Buenos Aires', NULL, 'Ciudad Autónoma de Buenos Aires'),
(4225, 79, '', NULL, 'Southern District'),
(4226, 81, 'Provinsi Sumatera Barat', NULL, 'Provinsi Sumatera Barat'),
(4227, 249, '', NULL, 'Jeollanam-do'),
(4228, 170, 'Мадре-де-Дьос', NULL, 'Madre de Dios'),
(4229, 201, 'Občina Črna na Koroškem', NULL, 'Občina Črna na Koroškem'),
(4230, 66, '', NULL, "K'alak'i T'bilisi"),
(4231, 115, '', NULL, 'Atiu'),
(4232, 61, 'Departamento de Cortés', NULL, 'Departamento de Cortés'),
(4233, 73, '', NULL, 'Provincia de Barahona'),
(4234, 211, 'Kigoma Region', NULL, 'Kigoma Region'),
(4235, 7, 'Wilaya de Souk Ahras', NULL, 'Wilaya de Souk Ahras'),
(4236, 84, '', NULL, 'Sistan and Baluchestan'),
(4237, 164, '', NULL, 'Al Batinah North Governorate'),
(4238, 241, '', NULL, 'Västmanlands län'),
(4239, 126, '', NULL, 'Port Louis District'),
(4240, 98, 'Kitui', NULL, 'Kitui'),
(4241, 305, '', NULL, 'Kingman Reef'),
(4242, 62, 'Yau Tsim Mong', NULL, 'Yau Tsim Mong'),
(4243, 140, 'Rongrik Atoll', NULL, 'Rongrik Atoll'),
(4244, 141, 'Estado de Nayarit', NULL, 'Estado de Nayarit'),
(4245, 98, 'Kirinyaga', NULL, 'Kirinyaga'),
(4246, 67, '', NULL, 'Dededo Municipality'),
(4247, 118, 'Zilupes Novads', NULL, 'Zilupes Novads'),
(4248, 113, '', NULL, 'Provincia de Matanzas'),
(4249, 249, 'Daegu', NULL, 'Daegu'),
(4250, 201, 'Občina Središče ob Dravi', NULL, 'Občina Središče ob Dravi'),
(4251, 151, '', NULL, 'Anabar District'),
(4252, 167, 'Provincia de Colón', NULL, 'Provincia de Colón'),
(4253, 136, '', NULL, 'Vaavu Atholhu'),
(4254, 67, 'Hagåtña Municipality', NULL, 'Hagåtña Municipality'),
(4255, 132, 'Илинден', NULL, 'Ilinden'),
(4256, 73, 'Provincia de San Juan', NULL, 'Provincia de San Juan'),
(4257, 98, 'Mombasa', NULL, 'Mombasa'),
(4258, 101, '', NULL, 'Phoenix Islands'),
(4259, 56, '', NULL, 'Departamento de Sololá'),
(4260, 29, '', NULL, 'Haskovo'),
(4261, 80, '', NULL, 'National Capital Territory of Delhi'),
(4262, 94, '', NULL, 'South Region'),
(4263, 132, '', NULL, 'Tearce'),
(4264, 141, 'Estado de Durango', NULL, 'Estado de Durango'),
(4265, 137, 'L-Imqabba', NULL, 'L-Imqabba'),
(4266, 262, 'Southern Liech', NULL, 'Southern Liech'),
(4267, 242, '', NULL, 'North Central Province'),
(4268, 118, 'Alūksnes Novads', NULL, 'Alūksnes Novads'),
(4269, 114, '', NULL, 'Al Asimah Governorate'),
(4270, 229, 'Варсинайс-Суоми', NULL, 'Varsinais-Suomi'),
(4271, 137, 'Il-Kalkara', NULL, 'Il-Kalkara'),
(4272, 19, 'Kandahār', NULL, 'Kandahār'),
(4273, 226, 'Departamento de Cerro Largo', NULL, 'Departamento de Cerro Largo'),
(4274, 239, '', NULL, 'Aysén'),
(4275, 11, '', NULL, 'Malanje Province'),
(4276, 122, 'Al Marqab', NULL, 'Al Marqab'),
(4277, 44, '', NULL, 'Bekes County'),
(4278, 229, 'Пяйят-Хяме', NULL, 'Päijänne-Tavastland'),
(4279, 19, 'Parwān', NULL, 'Parwān'),
(4280, 201, 'Vransko', NULL, 'Vransko'),
(4281, 39, '', NULL, 'Kayanza Province'),
(4282, 45, '', NULL, 'Dependencias Federales'),
(4283, 165, 'Азад Кашмир', NULL, 'Azad Kashmir'),
(4284, 142, 'State of Kosrae', NULL, 'State of Kosrae'),
(4285, 240, 'Kanton Zürich', NULL, 'Kanton Zürich'),
(4286, 100, 'Нарынская Область', NULL, 'Naryn oblast'),
(4287, 179, 'Suceava', NULL, 'Suceava'),
(4288, 118, 'Pāvilostas Novads', NULL, 'Pāvilostas Novads'),
(4289, 174, 'Cidra Municipio', NULL, 'Cidra Municipio'),
(4290, 148, '', NULL, 'Yangon Region'),
(4291, 128, 'Melaky Region', NULL, 'Melaky Region'),
(4292, 85, '', NULL, 'Connaught'),
(4293, 178, '', NULL, 'Eastern Province'),
(4294, 228, '', NULL, 'Caraga'),
(4295, 65, '', NULL, 'Thessaly'),
(4296, 34, 'Федеральный округ', NULL, 'Federal District'),
(4297, 201, 'Pesnica', NULL, 'Pesnica'),
(4298, 50, '', NULL, 'Province du Woleu-Ntem'),
(4299, 201, 'Rogatec', NULL, 'Rogatec'),
(4300, 165, '', NULL, 'Islamabad'),
(4301, 22, '', NULL, 'Saint Peter'),
(4302, 143, '', NULL, 'Cabo Delgado Province'),
(4303, 20, 'Ragged Island District', NULL, 'Ragged Island District'),
(4304, 217, '', NULL, 'Arima'),
(4305, 22, 'Saint Lucy', NULL, 'Saint Lucy'),
(4306, 168, '', NULL, 'Northern Province'),
(4307, 174, 'Aguas Buenas', NULL, 'Aguas Buenas'),
(4308, 4, 'Naftalan City', NULL, 'Naftalan City'),
(4309, 7, 'Wilaya de Bordj Bou Arréridj', NULL, 'Wilaya de Bordj Bou Arréridj'),
(4310, 82, 'Muḩāfaz̧at aţ Ţafīlah', NULL, 'Muḩāfaz̧at aţ Ţafīlah'),
(4311, 201, 'Občina Juršinci', NULL, 'Občina Juršinci'),
(4312, 251, '', NULL, 'Saint Elizabeth'),
(4313, 137, 'Birżebbuġa', NULL, 'Birżebbuġa'),
(4314, 82, 'Al Mafraq', NULL, 'Al Mafraq'),
(4315, 118, 'Mazsalacas Novads', NULL, 'Mazsalacas Novads'),
(4316, 201, 'Puconci', NULL, 'Puconci'),
(4317, 211, 'Mara Region', NULL, 'Mara Region'),
(4318, 183, '', NULL, 'Castello di Domagnano'),
(4319, 4, 'Khizi Rayon', NULL, 'Khizi Rayon'),
(4320, 74, 'Damietta Governorate', NULL, 'Damietta Governorate'),
(4321, 113, 'Provincia de Holguín', NULL, 'Provincia de Holguín'),
(4322, 7, 'Wilaya de Tindouf', NULL, 'Wilaya de Tindouf'),
(4323, 56, 'Departamento de Totonicapán', NULL, 'Departamento de Totonicapán'),
(4324, 177, 'Северная Осетия - Алания Республика', 'Осетия', 'North Ossetia'),
(4325, 177, 'Крым Республика', 'Крым', 'Republic of Crimea'),
(4326, 193, 'Christ Church Nichola Town', NULL, 'Christ Church Nichola Town'),
(4327, 28, '', NULL, 'Warwick Parish'),
(4328, 28, 'Southampton Parish', NULL, 'Southampton Parish'),
(4329, 237, 'Мойковац', NULL, 'Mojkovac'),
(4330, 174, 'Quebradillas Municipio', NULL, 'Quebradillas Municipio'),
(4331, 221, '', NULL, 'İzmir'),
(4332, 28, 'Saint George', NULL, 'Saint George'),
(4333, 137, 'Ir-Rabat', NULL, 'Ir-Rabat'),
(4334, 139, '', NULL, 'Martinique'),
(4335, 146, 'Dundgovĭ Aymag', NULL, 'Dundgovĭ Aymag'),
(4336, 71, '', NULL, 'Ali Sabieh Region'),
(4337, 188, '', NULL, 'Tinian Municipality'),
(4338, 201, 'Občina Šentjur', NULL, 'Občina Šentjur'),
(4339, 300, 'Peel', NULL, 'Peel'),
(4340, 159, '', NULL, 'Waikato'),
(4341, 243, 'Provincia del Azuay', NULL, 'Provincia del Azuay'),
(4342, 29, 'Габровская область', NULL, 'Gabrovo'),
(4343, 166, 'State of Ngarchelong', NULL, 'State of Ngarchelong'),
(4344, 89, '', NULL, 'Muḩāfaz̧at Ḩajjah'),
(4345, 66, 'Шида-Картли', NULL, 'Shida Kartli'),
(4346, 76, 'Copperbelt Province', NULL, 'Copperbelt Province'),
(4347, 96, '', NULL, 'Concelho do Tarrafal de São Nicolau'),
(4348, 80, 'Union Territory of Lakshadweep', NULL, 'Union Territory of Lakshadweep'),
(4349, 29, 'Oblast Targovishte', NULL, 'Oblast Targovishte'),
(4350, 29, 'Oblast Vidin', NULL, 'Oblast Vidin'),
(4351, 141, 'Estado de Colima', NULL, 'Estado de Colima'),
(4352, 201, 'Občina Mengeš', NULL, 'Občina Mengeš'),
(4353, 7, 'Wilaya d’ Alger', NULL, 'Wilaya d’ Alger'),
(4354, 94, 'Adamaoua Region', NULL, 'Adamaoua Region'),
(4355, 185, '', NULL, 'Hai’l Region'),
(4356, 118, 'Rēzeknes Novads', NULL, 'Rēzeknes Novads'),
(4357, 29, 'Oblast Razgrad', NULL, 'Oblast Razgrad'),
(4358, 209, 'Changwat Kanchanaburi', NULL, 'Changwat Kanchanaburi'),
(4359, 203, '', NULL, 'Gobolka Shabeellaha Dhexe'),
(4360, 19, 'Samangān', NULL, 'Samangān'),
(4361, 249, 'Jeollabuk-do', NULL, 'Jeollabuk-do'),
(4362, 136, 'Alifu Atholhu', NULL, 'Alifu Atholhu'),
(4363, 45, 'Estado Cojedes', NULL, 'Estado Cojedes'),
(4364, 132, 'Čair', NULL, 'Čair'),
(4365, 170, '', NULL, 'Cajamarca'),
(4366, 44, 'Budapest', NULL, 'Budapest'),
(4367, 125, '', NULL, 'Wiltz'),
(4368, 115, 'Mangaia', NULL, 'Mangaia'),
(4369, 132, 'Opština Centar', NULL, 'Opština Centar'),
(4370, 67, 'Tamuning-Tumon-Harmon Municipality', NULL, 'Tamuning-Tumon-Harmon Municipality'),
(4371, 4, 'Shusha', NULL, 'Shusha'),
(4372, 20, 'South Andros', NULL, 'South Andros'),
(4373, 151, 'Ijuw District', NULL, 'Ijuw District'),
(4374, 62, 'Central and Western District', NULL, 'Central and Western District'),
(4375, 115, 'Palmerston', NULL, 'Palmerston'),
(4376, 19, 'Wilāyat-e Lōgar', NULL, 'Wilāyat-e Lōgar'),
(4377, 151, 'Denigomodu District', NULL, 'Denigomodu District'),
(4378, 20, 'West Grand Bahama District', NULL, 'West Grand Bahama District'),
(4379, 233, '', NULL, 'Îles Australes'),
(4380, 132, 'Долнени', NULL, 'Dolneni'),
(4381, 129, '', NULL, 'Mamoudzou'),
(4382, 30, 'Departamento de Chuquisaca', NULL, 'Departamento de Chuquisaca'),
(4383, 303, 'Saint Pierre du Bois', NULL, 'Saint Pierre du Bois'),
(4384, 129, 'Mtsamboro', NULL, 'Mtsamboro'),
(4385, 140, 'Namu Atoll', NULL, 'Namu Atoll'),
(4386, 154, 'Lagos State', NULL, 'Lagos State'),
(4387, 132, 'Gevgelija', NULL, 'Gevgelija'),
(4388, 209, 'Changwat Chumphon', NULL, 'Changwat Chumphon'),
(4389, 4, 'Yardymli Rayon', NULL, 'Yardymli Rayon'),
(4390, 163, '', NULL, 'Al Fujayrah'),
(4391, 140, 'Utrik Atoll', NULL, 'Utrik Atoll'),
(4392, 118, 'Kārsavas Novads', NULL, 'Kārsavas Novads'),
(4393, 140, 'Wotje Atoll', NULL, 'Wotje Atoll'),
(4394, 138, '', NULL, 'Béni Mellal-Khénifra'),
(4395, 219, 'Gouvernorat de l’Ariana', NULL, 'Gouvernorat de l’Ariana'),
(4396, 235, '', NULL, 'Ombella-Mpoko'),
(4397, 179, 'Vaslui', NULL, 'Vaslui'),
(4398, 262, 'Boma', NULL, 'Boma'),
(4399, 89, 'Muḩāfaz̧at Abyan', NULL, 'Muḩāfaz̧at Abyan'),
(4400, 49, 'Tỉnh Khánh Hòa', NULL, 'Tỉnh Khánh Hòa'),
(4401, 118, 'Tukuma Rajons', NULL, 'Tukuma Rajons'),
(4402, 262, 'Bieh', NULL, 'Bieh'),
(4403, 154, 'Ekiti State', NULL, 'Ekiti State'),
(4404, 39, 'Muramvya Province', NULL, 'Muramvya Province'),
(4405, 63, '', NULL, 'Saint David'),
(4406, 122, 'Эль-Джуфра', NULL, 'Al Jufrah'),
(4407, 154, 'Zamfara State', NULL, 'Zamfara State'),
(4408, 98, 'Busia', NULL, 'Busia'),
(4409, 174, 'Ceiba Municipio', NULL, 'Ceiba Municipio'),
(4410, 39, 'Cibitoke Province', NULL, 'Cibitoke Province'),
(4411, 148, 'Chin State', NULL, 'Chin State'),
(4412, 132, 'Opština Vevčani', NULL, 'Opština Vevčani'),
(4413, 93, '', NULL, 'Tboung Khmum'),
(4414, 78, '', NULL, 'Manicaland Province'),
(4415, 65, 'Mount Athos', NULL, 'Mount Athos'),
(4416, 89, 'Muḩāfaz̧at Raymah', NULL, 'Muḩāfaz̧at Raymah'),
(4417, 181, '', NULL, 'Gagaifomauga'),
(4418, 137, 'Lija', NULL, 'Lija'),
(4419, 137, 'Luqa', NULL, 'Luqa'),
(4420, 14, '', NULL, 'Parish of Saint Philip'),
(4421, 52, '', NULL, 'Mahaica-Berbice Region'),
(4422, 300, 'Andreas', NULL, 'Andreas'),
(4423, 247, 'Amhara Region', NULL, 'Amhara Region'),
(4424, 300, 'Ballaugh', NULL, 'Ballaugh'),
(4425, 137, 'Il-Mellieħa', NULL, 'Il-Mellieħa'),
(4426, 201, 'Ankaran', NULL, 'Ankaran'),
(4427, 80, 'Дадра и Нагар-Хавели', NULL, 'Union Territory of Dādra and Nagar Haveli'),
(4428, 118, 'Rucavas Novads', NULL, 'Rucavas Novads'),
(4429, 174, 'Corozal Municipio', NULL, 'Corozal Municipio'),
(4430, 137, 'Paola', NULL, 'Paola'),
(4431, 85, 'Ольстер', NULL, 'Ulster'),
(4432, 234, 'Međimurska Županija', NULL, 'Međimurska Županija'),
(4433, 29, 'Varna', NULL, 'Varna'),
(4434, 82, 'Ирбид', NULL, 'Irbid'),
(4435, 73, 'Provincia de Hermanas Mirabal', NULL, 'Provincia de Hermanas Mirabal'),
(4436, 70, '', NULL, 'Province de l’Équateur'),
(4437, 174, 'Loíza Municipio', NULL, 'Loíza Municipio'),
(4438, 11, 'Huila Province', NULL, 'Huila Province'),
(4439, 179, 'Judeţul Cluj', NULL, 'Judeţul Cluj'),
(4440, 19, 'Wilāyat-e Kunaṟ', NULL, 'Wilāyat-e Kunaṟ'),
(4441, 125, 'Clervaux', NULL, 'Clervaux'),
(4442, 84, 'Ostān-e Gīlān', NULL, 'Ostān-e Gīlān'),
(4443, 226, 'Departamento de Rocha', NULL, 'Departamento de Rocha'),
(4444, 132, 'Plasnica', NULL, 'Plasnica'),
(4445, 179, 'Judeţul Caraş-Severin', NULL, 'Judeţul Caraş-Severin'),
(4446, 154, 'Kwara State', NULL, 'Kwara State'),
(4447, 7, 'Wilaya de Tissemsilt', NULL, 'Wilaya de Tissemsilt'),
(4448, 7, 'Wilaya de Guelma', NULL, 'Wilaya de Guelma'),
(4449, 132, 'Saraj', NULL, 'Saraj'),
(4450, 73, 'Provincia de Pedernales', NULL, 'Provincia de Pedernales'),
(4451, 118, 'Kocēnu Novads', NULL, 'Kocēnu Novads'),
(4452, 113, 'Provincia de Cienfuegos', NULL, 'Provincia de Cienfuegos'),
(4453, 70, 'Mai Ndombe', NULL, 'Mai Ndombe'),
(4454, 179, 'Judeţul Vâlcea', NULL, 'Judeţul Vâlcea'),
(4455, 49, 'Tỉnh Bình Phước', NULL, 'Tỉnh Bình Phước'),
(4456, 110, '', NULL, 'Provincia de Limón'),
(4457, 14, 'Редонда', NULL, 'Redonda'),
(4458, 234, 'Osječko-Baranjska Županija', NULL, 'Osječko-Baranjska Županija'),
(4459, 93, 'Takeo', NULL, 'Takeo'),
(4460, 98, 'Homa Bay', NULL, 'Homa Bay'),
(4461, 300, 'Jurby', NULL, 'Jurby'),
(4462, 74, 'Muḩāfaz̧at ash Sharqīyah', NULL, 'Muḩāfaz̧at ash Sharqīyah'),
(4463, 188, 'Rota Municipality', NULL, 'Rota Municipality'),
(4464, 21, '', NULL, 'Barisal Division'),
(4465, 45, 'Estado Monagas', NULL, 'Estado Monagas'),
(4466, 70, 'Монгала', NULL, 'Mongala'),
(4467, 10, '', NULL, 'Sandy Hill'),
(4468, 201, 'Občina Žiri', NULL, 'Občina Žiri'),
(4469, 111, '', NULL, 'Yamoussoukro Autonomous District'),
(4470, 29, 'Oblast Pleven', NULL, 'Oblast Pleven'),
(4471, 29, 'Blagoevgrad', NULL, 'Blagoevgrad'),
(4472, 111, 'Bas-Sassandra', NULL, 'Bas-Sassandra'),
(4473, 26, '', NULL, 'Flanders'),
(4474, 104, 'Departamento de Arauca', NULL, 'Departamento de Arauca'),
(4475, 166, 'State of Ngaraard', NULL, 'State of Ngaraard'),
(4476, 179, 'Arad', NULL, 'Arad'),
(4477, 74, 'Muḩāfaz̧at al Minyā', NULL, 'Muḩāfaz̧at al Minyā'),
(4478, 201, 'Ribnica', NULL, 'Ribnica'),
(4479, 111, 'Lagunes', NULL, 'Lagunes'),
(4480, 201, 'Občina Bovec', NULL, 'Občina Bovec'),
(4481, 209, 'Changwat Phichit', NULL, 'Changwat Phichit'),
(4482, 70, 'Province du Kasaï Oriental', NULL, 'Province du Kasaï Oriental'),
(4483, 89, 'Ibb', NULL, 'Ibb'),
(4484, 223, '', NULL, 'Toshkent Shahri'),
(4485, 170, 'Cusco', NULL, 'Cusco'),
(4486, 12, '', NULL, 'La Massana'),
(4487, 166, 'State of Ngiwal', NULL, 'State of Ngiwal'),
(4488, 36, '', NULL, 'Belait District'),
(4489, 118, 'Gulbenes Rajons', NULL, 'Gulbenes Rajons'),
(4490, 154, 'Kano State', NULL, 'Kano State'),
(4491, 201, 'Vodice', NULL, 'Vodice'),
(4492, 12, 'Andorra la Vella', NULL, 'Andorra la Vella'),
(4493, 204, 'South Darfur', NULL, 'South Darfur'),
(4494, 118, 'Carnikavas Novads', NULL, 'Carnikavas Novads'),
(4495, 140, 'Ailinglaplap Atoll', NULL, 'Ailinglaplap Atoll'),
(4496, 91, 'Город Астана', NULL, 'Astana Qalasy'),
(4497, 52, 'Demerara-Mahaica Region', NULL, 'Demerara-Mahaica Region'),
(4498, 201, 'Občina Tišina', NULL, 'Občina Tišina'),
(4499, 20, 'Hope Town District', NULL, 'Hope Town District'),
(4500, 39, 'Kirundo Province', NULL, 'Kirundo Province'),
(4501, 149, '', NULL, 'Kavango East Region'),
(4502, 87, 'Кастилия — Ла-Манча', NULL, 'Castilla-La Mancha'),
(4503, 16, 'Formosa Province', NULL, 'Formosa Province'),
(4504, 221, 'Килис', NULL, 'Kilis'),
(4505, 209, 'Changwat Mukdahan', NULL, 'Changwat Mukdahan'),
(4506, 122, 'Sha‘bīyat Sabhā', NULL, 'Sha‘bīyat Sabhā'),
(4507, 94, 'Littoral Region', NULL, 'Littoral Region'),
(4508, 169, '', NULL, 'Departamento de Boquerón'),
(4509, 107, 'Chagang-do', NULL, 'Chagang-do'),
(4510, 236, '', NULL, 'Logone Oriental Region'),
(4511, 245, '', NULL, 'Gash-Barka Region'),
(4512, 151, 'Nibok District', NULL, 'Nibok District'),
(4513, 73, 'Provincia de Azua', NULL, 'Provincia de Azua'),
(4514, 125, 'Remich', NULL, 'Remich'),
(4515, 219, 'Gouvernorat de Zaghouan', NULL, 'Gouvernorat de Zaghouan'),
(4516, 132, 'Debar', NULL, 'Debar'),
(4517, 174, 'Municipio de Isabela', NULL, 'Municipio de Isabela'),
(4518, 225, '', NULL, 'Uvéa'),
(4519, 62, 'Southern', NULL, 'Southern'),
(4520, 127, '', NULL, 'Hodh El Gharbi'),
(4521, 132, 'Demir Hisar', NULL, 'Demir Hisar'),
(4522, 39, 'Gitega Province', NULL, 'Gitega Province'),
(4523, 209, 'Changwat Ubon Ratchathani', NULL, 'Changwat Ubon Ratchathani'),
(4524, 126, 'Pamplemousses District', NULL, 'Pamplemousses District'),
(4525, 236, 'Batha Region', NULL, 'Batha Region'),
(4526, 126, 'Moka District', NULL, 'Moka District'),
(4527, 302, '', NULL, 'Saint Martin'),
(4528, 303, 'Saint Saviour', NULL, 'Saint Saviour'),
(4529, 201, 'Občina Štore', NULL, 'Občina Štore'),
(4530, 199, 'Мухафаза Эс-Сувейда', NULL, 'As-Suwayda Governorate'),
(4531, 167, 'Provincia del Darién', NULL, 'Provincia del Darién'),
(4532, 102, 'Jiangxi Sheng', NULL, 'Jiangxi Sheng'),
(4533, 190, '', NULL, 'English River'),
(4534, 6, '', NULL, 'Qarku i Tiranës'),
(4535, 219, 'Gouvernorat de Kairouan', NULL, 'Gouvernorat de Kairouan'),
(4536, 69, '', NULL, 'Region Hovedstaden'),
(4537, 198, '', NULL, 'Central Singapore Community Development Council'),
(4538, 204, 'Northern', NULL, 'Northern'),
(4539, 229, 'Северная Карелия', NULL, 'Pohjois-Karjala'),
(4540, 93, 'Сиануквиль', NULL, 'Sihanoukville'),
(4541, 153, '', NULL, 'Tillaberi Region'),
(4542, 34, '', NULL, 'Paraíba'),
(4543, 170, 'Apurimac', NULL, 'Apurimac'),
(4544, 78, 'Mashonaland Central Province', NULL, 'Mashonaland Central Province'),
(4545, 45, 'Estado Portuguesa', NULL, 'Estado Portuguesa'),
(4546, 84, 'Центральный остан', NULL, 'Ostān-e Markazī'),
(4547, 226, 'Departamento de Durazno', NULL, 'Departamento de Durazno'),
(4548, 81, 'Provinsi Kalimantan Timur', NULL, 'Provinsi Kalimantan Timur'),
(4549, 179, 'Judeţul Brăila', NULL, 'Judeţul Brăila'),
(4550, 137, 'Kirkop', NULL, 'Kirkop'),
(4551, 9, '', NULL, 'Eastern District'),
(4552, 179, 'Teleorman', NULL, 'Teleorman'),
(4553, 93, 'Баттамбанг', NULL, 'Battambang'),
(4554, 173, 'Distrito de Bragança', NULL, 'Distrito de Bragança'),
(4555, 137, 'Marsaskala', NULL, 'Marsaskala'),
(4556, 14, 'Parish of Saint Peter', NULL, 'Parish of Saint Peter'),
(4557, 128, 'Alaotra Mangoro Region', NULL, 'Alaotra Mangoro Region'),
(4558, 194, 'Parish of Charlotte', NULL, 'Parish of Charlotte'),
(4559, 193, 'Saint John Capesterre', NULL, 'Saint John Capesterre'),
(4560, 237, 'Tivat', NULL, 'Tivat'),
(4561, 7, 'Wilaya de Relizane', NULL, 'Wilaya de Relizane'),
(4562, 206, 'Монтана', NULL, 'Montana'),
(4563, 199, 'Мухафаза Ракка', NULL, 'Ar-Raqqah Governorate'),
(4564, 119, '', NULL, 'Mafeteng District'),
(4565, 39, 'Bujumbura Mairie Province', NULL, 'Bujumbura Mairie Province'),
(4566, 144, 'Суворовский Район', NULL, 'Raionul Ştefan Vodă'),
(4567, 118, 'Ropažu Novads', NULL, 'Ropažu Novads'),
(4568, 161, 'Hordaland Fylke', NULL, 'Hordaland Fylke'),
(4569, 29, 'Lovech', NULL, 'Lovech'),
(4570, 4, 'Aghsu Rayon', NULL, 'Aghsu Rayon'),
(4571, 301, '', NULL, 'Mariehamns stad'),
(4572, 49, 'Tỉnh Quảng Ninh', NULL, 'Tỉnh Quảng Ninh'),
(4573, 127, 'Адрар', NULL, 'Adrar'),
(4574, 50, 'Province de l’Ogooué-Ivindo', NULL, 'Province de l’Ogooué-Ivindo'),
(4575, 104, 'Departamento de Norte de Santander', NULL, 'Departamento de Norte de Santander'),
(4576, 201, 'Dravograd', NULL, 'Dravograd');
INSERT INTO `region` (`region_id`, `country_id`, `title`, `title_short`, `title_lat`) VALUES
(4577, 262, 'Yei River', NULL, 'Yei River'),
(4578, 7, 'Wilaya de Tlemcen', NULL, 'Wilaya de Tlemcen'),
(4579, 211, 'Dodoma Region', NULL, 'Dodoma Region'),
(4580, 137, 'Birkirkara', NULL, 'Birkirkara'),
(4581, 4, 'Shamakhi Rayon', NULL, 'Shamakhi Rayon'),
(4582, 211, 'Dar es Salaam Region', NULL, 'Dar es Salaam Region'),
(4583, 41, '', NULL, 'Torba Province'),
(4584, 118, 'Ventspils Municipality', NULL, 'Ventspils Municipality'),
(4585, 134, '', NULL, 'Kuala Lumpur'),
(4586, 244, 'Provincia de Kié-Ntem', NULL, 'Provincia de Kié-Ntem'),
(4587, 20, 'Long Island', NULL, 'Long Island'),
(4588, 45, 'Estado Bolívar', NULL, 'Estado Bolívar'),
(4589, 235, 'Préfecture de l’Ouham-Pendé', NULL, 'Préfecture de l’Ouham-Pendé'),
(4590, 159, 'Chatham Islands', NULL, 'Chatham Islands'),
(4591, 229, 'Северная Остроботния', NULL, 'Pohjois-Pohjanmaa'),
(4592, 4, 'Jabrayil District', NULL, 'Jabrayil District'),
(4593, 56, 'Departamento de Suchitepéquez', NULL, 'Departamento de Suchitepéquez'),
(4594, 174, 'Rincón Municipio', NULL, 'Rincón Municipio'),
(4595, 118, 'Aizputes Novads', NULL, 'Aizputes Novads'),
(4596, 243, 'Provincia de Pichincha', NULL, 'Provincia de Pichincha'),
(4597, 98, 'Bomet', NULL, 'Bomet'),
(4598, 234, 'Primorsko-Goranska Županija', NULL, 'Primorsko-Goranska Županija'),
(4599, 166, 'State of Ngatpang', NULL, 'State of Ngatpang'),
(4600, 89, 'Muḩāfaz̧at Ma’rib', NULL, 'Muḩāfaz̧at Ma’rib'),
(4601, 201, 'Vipava', NULL, 'Vipava'),
(4602, 19, 'Wilāyat-e Nīmrōz', NULL, 'Wilāyat-e Nīmrōz'),
(4603, 234, 'Šibensko-Kninska Županija', NULL, 'Šibensko-Kninska Županija'),
(4604, 118, 'Екабпилсский край', NULL, 'Jēkabpils Municipality'),
(4605, 201, 'Sevnica', NULL, 'Sevnica'),
(4606, 166, 'State of Aimeliik', NULL, 'State of Aimeliik'),
(4607, 128, 'Atsimo-Atsinanana Region', NULL, 'Atsimo-Atsinanana Region'),
(4608, 201, 'Selnica ob Dravi', NULL, 'Selnica ob Dravi'),
(4609, 28, 'Devonshire Parish', NULL, 'Devonshire Parish'),
(4610, 84, 'Ostān-e Zanjān', NULL, 'Ostān-e Zanjān'),
(4611, 201, 'Občina Miklavž na Dravskem Polju', NULL, 'Občina Miklavž na Dravskem Polju'),
(4612, 49, 'Tỉnh Hòa Bình', NULL, 'Tỉnh Hòa Bình'),
(4613, 27, 'Алибори', NULL, 'Alibori'),
(4614, 223, 'Toshkent Viloyati', NULL, 'Toshkent Viloyati'),
(4615, 62, 'Tsuen Wan District', NULL, 'Tsuen Wan District'),
(4616, 209, 'Changwat Pattani', NULL, 'Changwat Pattani'),
(4617, 137, 'Ta’ Xbiex', NULL, 'Ta’ Xbiex'),
(4618, 36, 'Temburong District', NULL, 'Temburong District'),
(4619, 72, 'Saint Patrick', NULL, 'Saint Patrick'),
(4620, 149, 'Karas', NULL, 'Karas'),
(4621, 118, 'Salaspils Novads', NULL, 'Salaspils Novads'),
(4622, 149, 'Охангвена', NULL, 'Ohangwena'),
(4623, 209, 'Changwat Nonthaburi', NULL, 'Changwat Nonthaburi'),
(4624, 164, 'Az̧ Z̧āhirah', NULL, 'Az̧ Z̧āhirah'),
(4625, 73, 'Provincia de Hato Mayor', NULL, 'Provincia de Hato Mayor'),
(4626, 222, '', NULL, 'Western Region'),
(4627, 132, 'Лозово', NULL, 'Lozovo'),
(4628, 20, 'Central Eleuthera District', NULL, 'Central Eleuthera District'),
(4629, 238, 'Южноморавский край', NULL, 'Jihomoravský kraj'),
(4630, 7, 'Wilaya de Constantine', NULL, 'Wilaya de Constantine'),
(4631, 54, '', NULL, 'Eastern Region'),
(4632, 132, 'Berovo', NULL, 'Berovo'),
(4633, 71, 'Arta Region', NULL, 'Arta Region'),
(4634, 132, 'Bitola', NULL, 'Bitola'),
(4635, 84, 'Ostān-e Kohgīlūyeh va Bowyer Aḩmad', NULL, 'Ostān-e Kohgīlūyeh va Bowyer Aḩmad'),
(4636, 231, '', NULL, 'Normandy'),
(4637, 11, 'Cuanza Norte Province', NULL, 'Cuanza Norte Province'),
(4638, 201, 'Občina Šalovci', NULL, 'Občina Šalovci'),
(4639, 161, 'Østfold fylke', NULL, 'Østfold fylke'),
(4640, 81, 'Sulawesi Tenggara', NULL, 'Sulawesi Tenggara'),
(4641, 20, 'Rum Cay', NULL, 'Rum Cay'),
(4642, 238, '', NULL, 'Pardubický kraj'),
(4643, 221, 'Bayburt', NULL, 'Bayburt'),
(4644, 118, 'Aknīstes Novads', NULL, 'Aknīstes Novads'),
(4645, 132, 'Opština Centar Župa', NULL, 'Opština Centar Župa'),
(4646, 210, '', NULL, 'Taiwan'),
(4647, 97, '', NULL, 'Baladīyat ar Rayyān'),
(4648, 80, 'Гоа', NULL, 'Goa'),
(4649, 73, 'Provincia de Valverde', NULL, 'Provincia de Valverde'),
(4650, 115, 'Mitiaro', NULL, 'Mitiaro'),
(4651, 97, 'Baladīyat ad Dawḩah', NULL, 'Baladīyat ad Dawḩah'),
(4652, 67, 'Yona Municipality', NULL, 'Yona Municipality'),
(4653, 305, 'Johnston Atoll', NULL, 'Johnston Atoll'),
(4654, 201, 'Občina Semič', NULL, 'Občina Semič'),
(4655, 304, '', NULL, 'La Terre-Adélie'),
(4656, 118, 'Viļānu Novads', NULL, 'Viļānu Novads'),
(4657, 201, 'Mokronog-Trebelno', NULL, 'Mokronog-Trebelno'),
(4658, 201, 'Lenart', NULL, 'Lenart'),
(4659, 83, '', NULL, 'Muḩāfaz̧at al Muthanná'),
(4660, 62, 'Коулун-Сити', NULL, 'Kowloon City'),
(4661, 201, 'Občina Rečica ob Savinji', NULL, 'Občina Rečica ob Savinji'),
(4662, 65, 'Ionian Islands', NULL, 'Ionian Islands'),
(4663, 136, 'Seenu Atholhu', NULL, 'Seenu Atholhu'),
(4664, 174, 'Juana Díaz Municipio', NULL, 'Juana Díaz Municipio'),
(4665, 186, 'Остров Вознесения', NULL, 'Ascension'),
(4666, 45, 'Estado Aragua', NULL, 'Estado Aragua'),
(4667, 49, 'Tỉnh Lào Cai', NULL, 'Tỉnh Lào Cai'),
(4668, 33, '', NULL, 'North East District'),
(4669, 240, 'Kanton Appenzell Ausserrhoden', NULL, 'Kanton Appenzell Ausserrhoden'),
(4670, 7, 'Wilaya de Tiaret', NULL, 'Wilaya de Tiaret'),
(4671, 219, 'Gouvernorat de Kef', NULL, 'Gouvernorat de Kef'),
(4672, 118, 'Apes Novads', NULL, 'Apes Novads'),
(4673, 191, '', NULL, 'Fatick'),
(4674, 33, 'Kweneng District', NULL, 'Kweneng District'),
(4675, 170, 'Región de Huánuco', NULL, 'Región de Huánuco'),
(4676, 154, 'Kebbi State', NULL, 'Kebbi State'),
(4677, 201, 'Horjul', NULL, 'Horjul'),
(4678, 148, 'Sagaing Region', NULL, 'Sagaing Region'),
(4679, 74, 'Muḩāfaz̧at Banī Suwayf', NULL, 'Muḩāfaz̧at Banī Suwayf'),
(4680, 168, 'National Capital District', NULL, 'National Capital District'),
(4681, 174, 'Ponce Municipio', NULL, 'Ponce Municipio'),
(4682, 201, 'Celje', NULL, 'Celje'),
(4683, 73, 'Distrito Nacional', NULL, 'Distrito Nacional'),
(4684, 167, 'Provincia de Veraguas', NULL, 'Provincia de Veraguas'),
(4685, 96, 'Concelho da Praia', NULL, 'Concelho da Praia'),
(4686, 49, 'Ðắk Nông', NULL, 'Ðắk Nông'),
(4687, 49, 'Tỉnh Ðiện Biên', NULL, 'Tỉnh Ðiện Biên'),
(4688, 73, 'Provincia de Monseñor Nouel', NULL, 'Provincia de Monseñor Nouel'),
(4689, 169, 'Departamento de Concepción', NULL, 'Departamento de Concepción'),
(4690, 118, 'Talsi Municipality', NULL, 'Talsi Municipality'),
(4691, 118, 'Dundagas Novads', NULL, 'Dundagas Novads'),
(4692, 146, 'Ulaanbaatar Hot', NULL, 'Ulaanbaatar Hot'),
(4693, 180, 'Departamento de Sonsonate', NULL, 'Departamento de Sonsonate'),
(4694, 121, 'Mohafazat Béqaa', NULL, 'Mohafazat Béqaa'),
(4695, 20, 'Acklins Island District', NULL, 'Acklins Island District'),
(4696, 56, 'Departamento del Quiché', NULL, 'Departamento del Quiché'),
(4697, 209, 'Changwat Saraburi', NULL, 'Changwat Saraburi'),
(4698, 118, 'Cesvaines Novads', NULL, 'Cesvaines Novads'),
(4699, 7, 'Wilaya de Sidi Bel Abbès', NULL, 'Wilaya de Sidi Bel Abbès'),
(4700, 243, 'Provincia de Santa Elena', NULL, 'Provincia de Santa Elena'),
(4701, 174, 'Municipio de Juncos', NULL, 'Municipio de Juncos'),
(4702, 132, 'Opština Kruševo', NULL, 'Opština Kruševo'),
(4703, 81, 'Provinsi Nusa Tenggara Timur', NULL, 'Provinsi Nusa Tenggara Timur'),
(4704, 47, '', NULL, 'Saint Thomas Island'),
(4705, 127, 'Ассаба', NULL, 'Assaba'),
(4706, 118, 'Līgatnes Novads', NULL, 'Līgatnes Novads'),
(4707, 243, 'Provincia del Tungurahua', NULL, 'Provincia del Tungurahua'),
(4708, 84, 'Ostān-e Qazvīn', NULL, 'Ostān-e Qazvīn'),
(4709, 257, '', NULL, 'West Bank'),
(4710, 118, 'Saulkrastu Novads', NULL, 'Saulkrastu Novads'),
(4711, 118, 'Alojas Novads', NULL, 'Alojas Novads'),
(4712, 49, 'Tỉnh Lâm Đồng', NULL, 'Tỉnh Lâm Đồng'),
(4713, 217, 'Sangre Grande', NULL, 'Sangre Grande'),
(4714, 123, '', NULL, 'Klaipėda County'),
(4715, 93, 'Кампот', NULL, 'Kampot'),
(4716, 185, 'Al Minţaqah ash Sharqīyah', NULL, 'Al Minţaqah ash Sharqīyah'),
(4717, 39, 'Karuzi Province', NULL, 'Karuzi Province'),
(4718, 88, 'Умбрия', NULL, 'Umbria'),
(4719, 137, 'Qormi', NULL, 'Qormi'),
(4720, 235, 'Préfecture de la Kémo', NULL, 'Préfecture de la Kémo'),
(4721, 7, 'Wilaya de Tamanrasset', NULL, 'Wilaya de Tamanrasset'),
(4722, 10, 'Blowing Point', NULL, 'Blowing Point'),
(4723, 228, 'National Capital Region', NULL, 'National Capital Region'),
(4724, 201, 'Gorenja Vas-Poljane', NULL, 'Gorenja Vas-Poljane'),
(4725, 122, 'Tripoli', NULL, 'Tripoli'),
(4726, 166, 'State of Melekeok', NULL, 'State of Melekeok'),
(4727, 23, '', NULL, 'Capital Governorate'),
(4728, 66, 'Гурия', NULL, 'Guria'),
(4729, 180, 'Departamento de Cabañas', NULL, 'Departamento de Cabañas'),
(4730, 154, 'Cross River State', NULL, 'Cross River State'),
(4731, 66, 'Мцхета-Мтианети', NULL, 'Mtskheta-Mtianeti'),
(4732, 237, 'Cetinje', NULL, 'Cetinje'),
(4733, 89, 'Muḩāfaz̧at al Jawf', NULL, 'Muḩāfaz̧at al Jawf'),
(4734, 132, 'Rosoman', NULL, 'Rosoman'),
(4735, 209, 'Changwat Nong Khai', NULL, 'Changwat Nong Khai'),
(4736, 209, 'Changwat Nan', NULL, 'Changwat Nan'),
(4737, 118, 'Ādažu Novads', NULL, 'Ādažu Novads'),
(4738, 102, 'Inner Mongolia Autonomous Region', NULL, 'Inner Mongolia Autonomous Region'),
(4739, 238, 'Южночешский край', NULL, 'Jihočeský kraj'),
(4740, 117, '', NULL, 'Khammouan'),
(4741, 58, 'Bissau Region', NULL, 'Bissau Region'),
(4742, 79, 'Central District', NULL, 'Central District'),
(4743, 118, 'Preiļi Municipality', NULL, 'Preiļi Municipality'),
(4744, 174, 'Villalba Municipio', NULL, 'Villalba Municipio'),
(4745, 141, 'Estado de Coahuila de Zaragoza', NULL, 'Estado de Coahuila de Zaragoza'),
(4746, 200, '', NULL, 'Banska Bystrica'),
(4747, 140, 'Jaluit Atoll', NULL, 'Jaluit Atoll'),
(4748, 118, 'Екабпилс', NULL, 'Jēkabpils'),
(4749, 61, 'Departamento de Copán', NULL, 'Departamento de Copán'),
(4750, 200, 'Trencin', NULL, 'Trencin'),
(4751, 140, 'Lae Atoll', NULL, 'Lae Atoll'),
(4752, 201, 'Občina Šmartno ob Paki', NULL, 'Občina Šmartno ob Paki'),
(4753, 104, 'Departamento del Putumayo', NULL, 'Departamento del Putumayo'),
(4754, 92, '', NULL, 'East End'),
(4755, 154, 'Enugu State', NULL, 'Enugu State'),
(4756, 240, 'Kanton Obwalden', NULL, 'Kanton Obwalden'),
(4757, 97, 'Baladīyat az̧ Z̧a‘āyin', NULL, 'Baladīyat az̧ Z̧a‘āyin'),
(4758, 227, 'Восточный округ', NULL, 'Eastern Division'),
(4759, 83, 'Muḩāfaz̧at al Qādisīyah', NULL, 'Muḩāfaz̧at al Qādisīyah'),
(4760, 201, 'Občina Šentjernej', NULL, 'Občina Šentjernej'),
(4761, 256, '', NULL, 'Gasa'),
(4762, 219, 'Gouvernorat de Sousse', NULL, 'Gouvernorat de Sousse'),
(4763, 190, 'Pointe Larue', NULL, 'Pointe Larue'),
(4764, 115, 'Rarotonga', NULL, 'Rarotonga'),
(4765, 132, 'Drugovo', NULL, 'Drugovo'),
(4766, 173, 'Distrito de Portalegre', NULL, 'Distrito de Portalegre'),
(4767, 242, 'Province of Sabaragamuwa', NULL, 'Province of Sabaragamuwa'),
(4768, 253, 'Ōsaka-fu', NULL, 'Ōsaka-fu'),
(4769, 190, 'Mont Buxton', NULL, 'Mont Buxton'),
(4770, 151, 'Aiwo District', NULL, 'Aiwo District'),
(4771, 4, 'Salyan Rayon', NULL, 'Salyan Rayon'),
(4772, 236, 'Ennedi-Ouest', NULL, 'Ennedi-Ouest'),
(4773, 201, 'Kungota', NULL, 'Kungota'),
(4774, 243, 'Provincia de Santo Domingo de los Tsáchilas', NULL, 'Provincia de Santo Domingo de los Tsáchilas'),
(4775, 174, 'Cataño Municipio', NULL, 'Cataño Municipio'),
(4776, 219, 'Gouvernorat de Mahdia', NULL, 'Gouvernorat de Mahdia'),
(4777, 209, 'Changwat Trang', NULL, 'Changwat Trang'),
(4778, 195, 'Laborie', NULL, 'Laborie'),
(4779, 229, 'Северное Саво', NULL, 'Pohjois-Savo'),
(4780, 78, 'Matabeleland North Province', NULL, 'Matabeleland North Province'),
(4781, 195, 'Шуазёль', NULL, 'Choiseul'),
(4782, 195, 'Кастри', NULL, 'Castries'),
(4783, 159, 'Manawatu-Wanganui', NULL, 'Manawatu-Wanganui'),
(4784, 91, 'Город Алматы', NULL, 'Almaty Qalasy'),
(4785, 96, 'Concelho de São Vicente', NULL, 'Concelho de São Vicente'),
(4786, 56, 'Departamento de San Marcos', NULL, 'Departamento de San Marcos'),
(4787, 132, 'Заяс', NULL, 'Zajas'),
(4788, 70, 'Kwango', NULL, 'Kwango'),
(4789, 168, 'Morobe Province', NULL, 'Morobe Province'),
(4790, 7, 'El Oued', NULL, 'El Oued'),
(4791, 185, 'Al Madīnah al Munawwarah', NULL, 'Al Madīnah al Munawwarah'),
(4792, 229, 'Сатакунта', NULL, 'Satakunta'),
(4793, 193, 'Trinity Palmetto Point', NULL, 'Trinity Palmetto Point'),
(4794, 144, '', NULL, 'Raionul Dubăsari'),
(4795, 128, 'Sofia Region', NULL, 'Sofia Region'),
(4796, 236, 'Tandjile Region', NULL, 'Tandjile Region'),
(4797, 215, 'Нукунону', NULL, 'Nukunonu'),
(4798, 179, 'Bihor', NULL, 'Bihor'),
(4799, 124, '', NULL, 'Balzers'),
(4800, 174, 'Comerío Municipio', NULL, 'Comerío Municipio'),
(4801, 135, '', NULL, 'Sikasso Region'),
(4802, 128, 'Vakinankaratra Region', NULL, 'Vakinankaratra Region'),
(4803, 85, 'Ленстер', NULL, 'Leinster'),
(4804, 202, '', NULL, 'Isabel Province'),
(4805, 234, 'Krapinsko-Zagorska Županija', NULL, 'Krapinsko-Zagorska Županija'),
(4806, 138, 'Marrakesh-Safi', NULL, 'Marrakesh-Safi'),
(4807, 118, 'Крустпилсский край', NULL, 'Krustpils Novads'),
(4808, 173, 'Distrito de Évora', NULL, 'Distrito de Évora'),
(4809, 170, 'Provincia de Lima', NULL, 'Provincia de Lima'),
(4810, 132, 'Kriva Palanka', NULL, 'Kriva Palanka'),
(4811, 211, 'Iringa Region', NULL, 'Iringa Region'),
(4812, 246, 'Ярвамаа', NULL, 'Järvamaa'),
(4813, 201, 'Videm', NULL, 'Videm'),
(4814, 240, 'Kanton Glarus', NULL, 'Kanton Glarus'),
(4815, 196, '', NULL, 'Commune de Saint-Pierre'),
(4816, 22, 'Saint Thomas', NULL, 'Saint Thomas'),
(4817, 221, 'Анталья', NULL, 'Antalya'),
(4818, 49, 'Tỉnh Ninh Bình', NULL, 'Tỉnh Ninh Bình'),
(4819, 19, 'Панджшер', NULL, 'Panjshir'),
(4820, 39, 'Cankuzo Province', NULL, 'Cankuzo Province'),
(4821, 50, 'Province de la Nyanga', NULL, 'Province de la Nyanga'),
(4822, 49, 'Tỉnh Quảng Bình', NULL, 'Tỉnh Quảng Bình'),
(4823, 251, 'Saint Thomas', NULL, 'Saint Thomas'),
(4824, 44, 'Somogy megye', NULL, 'Somogy megye'),
(4825, 134, 'Селангор', NULL, 'Selangor'),
(4826, 118, 'Naukšēnu Novads', NULL, 'Naukšēnu Novads'),
(4827, 209, 'Changwat Yasothon', NULL, 'Changwat Yasothon'),
(4828, 144, 'Яловенский район', NULL, 'Ialoveni'),
(4829, 185, 'Minţaqat ‘Asīr', NULL, 'Minţaqat ‘Asīr'),
(4830, 163, 'Ra’s al Khaymah', NULL, 'Ra’s al Khaymah'),
(4831, 123, 'Panevėžys', NULL, 'Panevėžys'),
(4832, 109, '', NULL, 'Pec District'),
(4833, 201, 'Beltinci', NULL, 'Beltinci'),
(4834, 202, 'Malaita Province', NULL, 'Malaita Province'),
(4835, 201, 'Bistrica ob Sotli', NULL, 'Bistrica ob Sotli'),
(4836, 201, 'Dobje', NULL, 'Dobje'),
(4837, 199, 'Мухафаза Идлиб', NULL, 'Idlib Governorate'),
(4838, 118, 'Priekules Novads', NULL, 'Priekules Novads'),
(4839, 236, 'Moyen-Chari Region', NULL, 'Moyen-Chari Region'),
(4840, 45, 'Estado Falcón', NULL, 'Estado Falcón'),
(4841, 141, 'Estado de Zacatecas', NULL, 'Estado de Zacatecas'),
(4842, 179, 'Satu Mare', NULL, 'Satu Mare'),
(4843, 4, 'Kalbajar District', NULL, 'Kalbajar District'),
(4844, 57, 'Mamou Region', NULL, 'Mamou Region'),
(4845, 49, 'Tỉnh Nam Định', NULL, 'Tỉnh Nam Định'),
(4846, 49, 'Tỉnh Bình Thuận', NULL, 'Tỉnh Bình Thuận'),
(4847, 118, 'Auces Novads', NULL, 'Auces Novads'),
(4848, 203, 'Gobolka Woqooyi Galbeed', NULL, 'Gobolka Woqooyi Galbeed'),
(4849, 55, '', NULL, 'Guadeloupe'),
(4850, 51, 'Département de Nippes', NULL, 'Département de Nippes'),
(4851, 128, 'Atsimo-Andrefana Region', NULL, 'Atsimo-Andrefana Region'),
(4852, 96, 'Concelho de Santa Cruz', NULL, 'Concelho de Santa Cruz'),
(4853, 102, 'Chongqing Shi', NULL, 'Chongqing Shi'),
(4854, 89, 'Muḩāfaz̧at aḑ Ḑāli‘', NULL, 'Muḩāfaz̧at aḑ Ḑāli‘'),
(4855, 118, 'Rundāles Novads', NULL, 'Rundāles Novads'),
(4856, 211, 'Katavi Region', NULL, 'Katavi Region'),
(4857, 98, 'Tana River', NULL, 'Tana River'),
(4858, 237, 'Berane', NULL, 'Berane'),
(4859, 27, 'Литораль', NULL, 'Littoral'),
(4860, 10, 'George Hill', NULL, 'George Hill'),
(4861, 29, 'Oblast Kyustendil', NULL, 'Oblast Kyustendil'),
(4862, 204, 'Red Sea', NULL, 'Red Sea'),
(4863, 27, '', NULL, 'Plateau Department'),
(4864, 10, 'Stoney Ground', NULL, 'Stoney Ground'),
(4865, 86, '', NULL, 'Suðurland'),
(4866, 223, 'Sirdaryo', NULL, 'Sirdaryo'),
(4867, 7, 'Wilaya de Khenchela', NULL, 'Wilaya de Khenchela'),
(4868, 249, 'Ulsan', NULL, 'Ulsan'),
(4869, 44, 'Veszprém megye', NULL, 'Veszprém megye'),
(4870, 164, 'Muḩāfaz̧at ad Dākhilīyah', NULL, 'Muḩāfaz̧at ad Dākhilīyah'),
(4871, 81, 'Provinsi Jambi', NULL, 'Provinsi Jambi'),
(4872, 204, 'Blue Nile', NULL, 'Blue Nile'),
(4873, 204, 'West Darfur', NULL, 'West Darfur'),
(4874, 20, 'Black Point District', NULL, 'Black Point District'),
(4875, 45, 'Дельта-Амакуро', NULL, 'Delta Amacuro'),
(4876, 74, 'Muḩāfaz̧at al Fayyūm', NULL, 'Muḩāfaz̧at al Fayyūm'),
(4877, 204, 'West Kordofan State', NULL, 'West Kordofan State'),
(4878, 70, 'Province du Sud-Ubangi', NULL, 'Province du Sud-Ubangi'),
(4879, 51, 'Nord', NULL, 'Nord'),
(4880, 72, 'Saint George', NULL, 'Saint George'),
(4881, 209, 'Changwat Narathiwat', NULL, 'Changwat Narathiwat'),
(4882, 203, 'Gobolka Sool', NULL, 'Gobolka Sool'),
(4883, 72, 'Saint David', NULL, 'Saint David'),
(4884, 39, 'Bururi Province', NULL, 'Bururi Province'),
(4885, 216, '', NULL, 'Tongatapu'),
(4886, 86, 'Höfuðborgarsvæði', NULL, 'Höfuðborgarsvæði'),
(4887, 209, 'Changwat Nakhon Pathom', NULL, 'Changwat Nakhon Pathom'),
(4888, 179, 'Илфов', NULL, 'Ilfov'),
(4889, 87, 'Эстремадура', NULL, 'Extremadura'),
(4890, 119, 'Qacha’s Nek', NULL, 'Qacha’s Nek'),
(4891, 221, 'Karabük', NULL, 'Karabük'),
(4892, 20, 'North Abaco District', NULL, 'North Abaco District'),
(4893, 256, 'Punakha Dzongkhag', NULL, 'Punakha Dzongkhag'),
(4894, 154, 'Abia State', NULL, 'Abia State'),
(4895, 210, 'Тайбэй', NULL, 'Taipei'),
(4896, 92, 'West Bay', NULL, 'West Bay'),
(4897, 97, 'Baladīyat Umm Şalāl', NULL, 'Baladīyat Umm Şalāl'),
(4898, 81, 'Provinsi Sulawesi Selatan', NULL, 'Provinsi Sulawesi Selatan'),
(4899, 305, 'Howland Island', NULL, 'Howland Island'),
(4900, 154, 'Edo', NULL, 'Edo'),
(4901, 67, 'Merizo Municipality', NULL, 'Merizo Municipality'),
(4902, 233, 'Îles Sous-le-Vent', NULL, 'Îles Sous-le-Vent'),
(4903, 304, 'Îles Éparses de l\'océan Indien', NULL, 'Îles Éparses de l\'océan Indien'),
(4904, 190, 'Takamaka', NULL, 'Takamaka'),
(4905, 233, 'Îles du Vent', NULL, 'Îles du Vent'),
(4906, 151, 'Buada District', NULL, 'Buada District'),
(4907, 225, 'Alo', NULL, 'Alo'),
(4908, 305, 'Midway Islands', NULL, 'Midway Islands'),
(4909, 219, 'Татавин', NULL, 'Tataouine'),
(4910, 62, 'Sham Shui Po', NULL, 'Sham Shui Po'),
(4911, 67, 'Asan-Maina Municipality', NULL, 'Asan-Maina Municipality'),
(4912, 127, 'Кудимага', NULL, 'Guidimaka'),
(4913, 161, 'Finnmark Fylke', NULL, 'Finnmark Fylke'),
(4914, 236, 'Мандуль', NULL, 'Mandoul'),
(4915, 140, 'Taka Atoll', NULL, 'Taka Atoll'),
(4916, 6, 'Qarku i Fierit', NULL, 'Qarku i Fierit'),
(4917, 209, 'Changwat Chiang Rai', NULL, 'Changwat Chiang Rai'),
(4918, 129, 'Tsingoni', NULL, 'Tsingoni'),
(4919, 236, 'Wadi Fira Region', NULL, 'Wadi Fira Region'),
(4920, 19, 'Wilāyat-e Ghaznī', NULL, 'Wilāyat-e Ghaznī'),
(4921, 49, 'Tỉnh Thái Bình', NULL, 'Tỉnh Thái Bình'),
(4922, 104, 'Departamento de Cundinamarca', NULL, 'Departamento de Cundinamarca'),
(4923, 217, 'City of Port of Spain', NULL, 'City of Port of Spain'),
(4924, 138, 'Fès-Meknès', NULL, 'Fès-Meknès'),
(4925, 49, 'Tỉnh Tây Ninh', NULL, 'Tỉnh Tây Ninh'),
(4926, 237, 'Andrijevica', NULL, 'Andrijevica'),
(4927, 70, 'South Kivu Province', NULL, 'South Kivu Province'),
(4929, 165, 'Balochistan', NULL, 'Balochistan'),
(4930, 117, 'Khouèng Xékong', NULL, 'Khouèng Xékong'),
(4931, 33, 'Southern District', NULL, 'Southern District'),
(4932, 168, 'Gulf Province', NULL, 'Gulf Province'),
(4933, 134, 'Labuan', NULL, 'Labuan'),
(4934, 153, 'Агадес', NULL, 'Agadez'),
(4935, 89, 'Muḩāfaz̧at Şa‘dah', NULL, 'Muḩāfaz̧at Şa‘dah'),
(4936, 229, 'Южное Саво', NULL, 'Southern Savonia'),
(4937, 29, 'Oblast Ruse', NULL, 'Oblast Ruse'),
(4938, 78, 'Mashonaland West Province', NULL, 'Mashonaland West Province'),
(4939, 33, 'Kgalagadi District', NULL, 'Kgalagadi District'),
(4940, 168, 'New Ireland Province', NULL, 'New Ireland Province'),
(4941, 168, 'Enga Province', NULL, 'Enga Province'),
(4942, 226, 'Departamento de Florida', NULL, 'Departamento de Florida'),
(4943, 104, 'Departamento del Guainía', NULL, 'Departamento del Guainía'),
(4944, 179, 'București', NULL, 'București'),
(4945, 122, 'Sha‘bīyat al Buţnān', NULL, 'Sha‘bīyat al Buţnān'),
(4946, 179, 'Tulcea', NULL, 'Tulcea'),
(4947, 148, 'Mandalay Region', NULL, 'Mandalay Region'),
(4948, 124, 'Triesen', NULL, 'Triesen'),
(4949, 221, 'Gümüşhane', NULL, 'Gümüşhane'),
(4950, 7, 'Wilaya de Béchar', NULL, 'Wilaya de Béchar'),
(4951, 122, 'Sha‘bīyat al Wāḩāt', NULL, 'Sha‘bīyat al Wāḩāt'),
(4952, 209, 'Changwat Surin', NULL, 'Changwat Surin'),
(4953, 193, 'Saint Peter Basseterre', NULL, 'Saint Peter Basseterre'),
(4954, 235, 'Лобае', NULL, 'Lobaye'),
(4955, 38, 'Centre-Ouest', NULL, 'Centre-Ouest'),
(4956, 234, 'Bjelovarsko-Bilogorska Županija', NULL, 'Bjelovarsko-Bilogorska Županija'),
(4957, 16, 'Mendoza Province', NULL, 'Mendoza Province'),
(4958, 137, 'Il-Munxar', NULL, 'Il-Munxar'),
(4959, 14, 'Parish of Saint George', NULL, 'Parish of Saint George'),
(4960, 234, 'Karlovačka Županija', NULL, 'Karlovačka Županija'),
(4961, 193, 'Saint George Basseterre', NULL, 'Saint George Basseterre'),
(4962, 243, 'Provincia de Cotopaxi', NULL, 'Provincia de Cotopaxi'),
(4963, 201, 'Občina Tolmin', NULL, 'Občina Tolmin'),
(4964, 206, 'Нью-Мехико', NULL, 'New Mexico'),
(4965, 250, 'Восточно-Капская провинция', NULL, 'Eastern Cape'),
(4966, 157, 'Departamento de Jinotega', NULL, 'Departamento de Jinotega'),
(4967, 226, 'Departamento de Soriano', NULL, 'Departamento de Soriano'),
(4968, 39, 'Makamba Province', NULL, 'Makamba Province'),
(4969, 135, 'Bamako Region', NULL, 'Bamako Region'),
(4970, 104, 'Departamento del Valle del Cauca', NULL, 'Departamento del Valle del Cauca'),
(4971, 132, 'Kumanovo', NULL, 'Kumanovo'),
(4972, 94, 'Far North Region', NULL, 'Far North Region'),
(4973, 157, 'Departamento de Chontales', NULL, 'Departamento de Chontales'),
(4974, 98, 'Kiambu', NULL, 'Kiambu'),
(4975, 196, 'Miquelon-Langlade', NULL, 'Miquelon-Langlade'),
(4976, 125, 'Diekirch', NULL, 'Diekirch'),
(4977, 22, 'Saint Philip', NULL, 'Saint Philip'),
(4978, 49, 'Tỉnh Tuyên Quang', NULL, 'Tỉnh Tuyên Quang'),
(4979, 118, 'Kuldīgas Rajons', NULL, 'Kuldīgas Rajons'),
(4980, 226, 'Departamento de Río Negro', NULL, 'Departamento de Río Negro'),
(4981, 251, 'Saint Mary', NULL, 'Saint Mary'),
(4982, 34, 'São Paulo', NULL, 'São Paulo'),
(4983, 183, 'Serravalle', NULL, 'Serravalle'),
(4984, 174, 'Maricao Municipio', NULL, 'Maricao Municipio'),
(4985, 56, 'Departamento de Chiquimula', NULL, 'Departamento de Chiquimula'),
(4986, 120, 'Montserrado County', NULL, 'Montserrado County'),
(4987, 106, 'Région du Niari', NULL, 'Région du Niari'),
(4988, 16, 'Catamarca Province', NULL, 'Catamarca Province'),
(4989, 49, 'Tỉnh Lai Châu', NULL, 'Tỉnh Lai Châu'),
(4990, 183, 'Castello di Borgo Maggiore', NULL, 'Castello di Borgo Maggiore'),
(4991, 78, 'Bulawayo Province', NULL, 'Bulawayo Province'),
(4992, 20, 'Mayaguana District', NULL, 'Mayaguana District'),
(4993, 217, 'Tunapuna/Piarco', NULL, 'Tunapuna/Piarco'),
(4994, 185, 'Minţaqat ar Riyāḑ', NULL, 'Minţaqat ar Riyāḑ'),
(4995, 201, 'Občina Turnišče', NULL, 'Občina Turnišče'),
(4996, 209, 'Changwat Samut Sakhon', NULL, 'Changwat Samut Sakhon'),
(4997, 239, 'Coquimbo', NULL, 'Coquimbo'),
(4998, 178, 'Western Province', NULL, 'Western Province'),
(4999, 38, 'Восточная область', NULL, 'Est'),
(5000, 234, 'Požeško-Slavonska Županija', NULL, 'Požeško-Slavonska Županija'),
(5001, 38, 'Plateau-Central', NULL, 'Plateau-Central'),
(5002, 133, '', NULL, 'Central Region'),
(5003, 180, 'Departamento de La Unión', NULL, 'Departamento de La Unión'),
(5004, 174, 'San Juan', NULL, 'San Juan'),
(5005, 201, 'Kanal', NULL, 'Kanal'),
(5006, 102, 'Guizhou Sheng', NULL, 'Guizhou Sheng'),
(5007, 7, 'El Bayadh', NULL, 'El Bayadh'),
(5008, 169, 'Departamento Central', NULL, 'Departamento Central'),
(5009, 211, 'Simiyu Region', NULL, 'Simiyu Region'),
(5010, 169, 'Departamento de Canindeyú', NULL, 'Departamento de Canindeyú'),
(5011, 120, 'Grand Cape Mount County', NULL, 'Grand Cape Mount County'),
(5012, 132, 'Босилово', NULL, 'Bosilovo'),
(5013, 235, 'Préfecture de la Nana-Grébizi', NULL, 'Préfecture de la Nana-Grébizi'),
(5014, 10, 'Island Harbour', NULL, 'Island Harbour'),
(5015, 157, 'Departamento de Matagalpa', NULL, 'Departamento de Matagalpa'),
(5016, 49, 'Hau Giang', NULL, 'Hau Giang'),
(5017, 74, 'Muḩāfaz̧at al Jīzah', NULL, 'Muḩāfaz̧at al Jīzah'),
(5018, 300, 'Santon', NULL, 'Santon'),
(5019, 111, 'Sassandra-Marahoué', NULL, 'Sassandra-Marahoué'),
(5020, 36, 'Tutong District', NULL, 'Tutong District'),
(5021, 201, 'Птуй', NULL, 'Ptuj'),
(5022, 96, 'Concelho de São Salvador do Mundo', NULL, 'Concelho de São Salvador do Mundo'),
(5023, 223, 'Navoiy Province', NULL, 'Navoiy Province'),
(5024, 117, 'Luang Prabang Province', NULL, 'Luang Prabang Province'),
(5025, 49, 'Kon Tum', NULL, 'Kon Tum'),
(5026, 201, 'Sveta Ana', NULL, 'Sveta Ana'),
(5027, 27, 'Mono', NULL, 'Mono'),
(5028, 149, 'Kunene', NULL, 'Kunene'),
(5029, 149, 'Омусати', NULL, 'Omusati'),
(5030, 173, 'Distrito de Setúbal', NULL, 'Distrito de Setúbal'),
(5031, 209, 'Changwat Mae Hong Son', NULL, 'Changwat Mae Hong Son'),
(5032, 221, 'Hakkari', NULL, 'Hakkari'),
(5033, 127, 'Wilaya du Trarza', NULL, 'Wilaya du Trarza'),
(5034, 256, 'Bumthang Dzongkhag', NULL, 'Bumthang Dzongkhag'),
(5035, 164, 'Muḩāfaz̧at Z̧ufār', NULL, 'Muḩāfaz̧at Z̧ufār'),
(5036, 83, 'Muḩāfaz̧at Nīnawá', NULL, 'Muḩāfaz̧at Nīnawá'),
(5037, 201, 'Občina Moravče', NULL, 'Občina Moravče'),
(5038, 235, 'Баминги-Бангоран', NULL, 'Bamingui-Bangoran'),
(5039, 243, 'Provincia de Los Ríos', NULL, 'Provincia de Los Ríos'),
(5040, 201, 'Марибор', NULL, 'Maribor'),
(5041, 238, 'Karlovarský kraj', NULL, 'Karlovarský kraj'),
(5042, 238, 'Olomoucký kraj', NULL, 'Olomoucký kraj'),
(5043, 83, 'Muḩāfaz̧at Maysān', NULL, 'Muḩāfaz̧at Maysān'),
(5044, 241, 'Gotland', NULL, 'Gotland'),
(5045, 252, '', NULL, 'Jan Mayen'),
(5046, 241, 'Södermanlands län', NULL, 'Södermanlands län'),
(5047, 214, 'Область Саванн', NULL, 'Savanes'),
(5048, 201, 'Občina Šoštanj', NULL, 'Občina Šoštanj'),
(5049, 214, 'Плато', NULL, 'Plateaux'),
(5050, 154, 'Adamawa State', NULL, 'Adamawa State'),
(5051, 253, 'Saitama-ken', NULL, 'Saitama-ken'),
(5052, 129, 'Koungou', NULL, 'Koungou'),
(5053, 4, 'Зардобский Район', NULL, 'Zardab Rayon'),
(5054, 125, 'Luxembourg', NULL, 'Luxembourg'),
(5055, 19, 'Wilāyat-e Baghlān', NULL, 'Wilāyat-e Baghlān'),
(5056, 125, 'Grevenmacher', NULL, 'Grevenmacher'),
(5057, 209, 'Changwat Amnat Charoen', NULL, 'Changwat Amnat Charoen'),
(5058, 303, 'Тортевал', NULL, 'Torteval'),
(5059, 201, 'Občina Laško', NULL, 'Občina Laško'),
(5060, 19, 'Wilāyat-e Ghōr', NULL, 'Wilāyat-e Ghōr'),
(5061, 73, 'Provincia Espaillat', NULL, 'Provincia Espaillat'),
(5062, 129, 'Ouangani', NULL, 'Ouangani'),
(5063, 65, 'North Aegean', NULL, 'North Aegean'),
(5064, 118, 'Madona Municipality', NULL, 'Madona Municipality'),
(5065, 88, 'Молизе', NULL, 'Molise'),
(5066, 136, 'Shaviyani Atholhu', NULL, 'Shaviyani Atholhu'),
(5067, 174, 'Municipio de Jayuya', NULL, 'Municipio de Jayuya'),
(5068, 53, '', NULL, 'Lower River Division'),
(5069, 154, 'Bayelsa State', NULL, 'Bayelsa State'),
(5070, 169, 'Departamento de Paraguarí', NULL, 'Departamento de Paraguarí'),
(5071, 219, 'Gouvernorat de Kasserine', NULL, 'Gouvernorat de Kasserine'),
(5072, 219, 'Gouvernorat de Monastir', NULL, 'Gouvernorat de Monastir'),
(5073, 262, 'Ruweng', NULL, 'Ruweng'),
(5074, 137, 'L-Iklin', NULL, 'L-Iklin'),
(5075, 195, 'Микуд', NULL, 'Micoud'),
(5076, 173, 'Madeira', NULL, 'Madeira'),
(5077, 154, 'Gombe State', NULL, 'Gombe State'),
(5078, 137, 'L-Imdina', NULL, 'L-Imdina'),
(5079, 11, 'Южная Лунда', NULL, 'Lunda Sul'),
(5080, 78, 'Matabeleland South Province', NULL, 'Matabeleland South Province'),
(5081, 118, 'Mērsraga Novads', NULL, 'Mērsraga Novads'),
(5082, 237, 'Opština Žabljak', NULL, 'Opština Žabljak'),
(5083, 132, 'Kratovo', NULL, 'Kratovo'),
(5084, 239, 'Región de la Araucanía', NULL, 'Región de la Araucanía'),
(5085, 211, 'Mtwara Region', NULL, 'Mtwara Region'),
(5086, 201, 'Občina Šempeter-Vrtojba', NULL, 'Občina Šempeter-Vrtojba'),
(5087, 132, 'Opština Vraneštica', NULL, 'Opština Vraneštica'),
(5088, 226, 'Departamento de Colonia', NULL, 'Departamento de Colonia'),
(5089, 179, 'Judeţul Braşov', NULL, 'Judeţul Braşov'),
(5090, 181, 'Satupa‘itea', NULL, 'Satupa‘itea'),
(5091, 229, 'Уусимаа', NULL, 'Uusimaa'),
(5092, 132, 'Zelenikovo', NULL, 'Zelenikovo'),
(5093, 221, 'Niğde', NULL, 'Niğde'),
(5094, 132, 'Opština Želino', NULL, 'Opština Želino'),
(5095, 221, 'Nevşehir', NULL, 'Nevşehir'),
(5096, 122, 'Darnah', NULL, 'Darnah'),
(5097, 184, 'Принсипи', NULL, 'Príncipe'),
(5098, 201, 'Prebold', NULL, 'Prebold'),
(5099, 45, 'Estado Nueva Esparta', NULL, 'Estado Nueva Esparta'),
(5100, 180, 'Departamento de San Vicente', NULL, 'Departamento de San Vicente'),
(5101, 178, 'Kigali Province', NULL, 'Kigali Province'),
(5102, 228, 'Mimaropa', NULL, 'Mimaropa'),
(5103, 50, 'Province du Haut-Ogooué', NULL, 'Province du Haut-Ogooué'),
(5104, 173, 'Distrito de Aveiro', NULL, 'Distrito de Aveiro'),
(5105, 190, 'Grand Anse Mahe', NULL, 'Grand Anse Mahe'),
(5106, 209, 'Changwat Kalasin', NULL, 'Changwat Kalasin'),
(5107, 41, 'Sanma Province', NULL, 'Sanma Province'),
(5108, 219, 'Gouvernorat de Ben Arous', NULL, 'Gouvernorat de Ben Arous'),
(5109, 253, 'Oita Prefecture', NULL, 'Oita Prefecture'),
(5110, 56, 'Departamento de Izabal', NULL, 'Departamento de Izabal'),
(5111, 201, 'Občina Rače-Fram', NULL, 'Občina Rače-Fram'),
(5112, 4, 'Neftchala Rayon', NULL, 'Neftchala Rayon'),
(5113, 61, 'Departamento de Intibucá', NULL, 'Departamento de Intibucá'),
(5114, 138, 'Souss-Massa', NULL, 'Souss-Massa'),
(5115, 56, 'Departamento de Huehuetenango', NULL, 'Departamento de Huehuetenango'),
(5116, 201, 'Občina Kidričevo', NULL, 'Občina Kidričevo'),
(5117, 154, 'Imo State', NULL, 'Imo State'),
(5118, 133, 'Northern Region', NULL, 'Northern Region'),
(5119, 104, 'Departamento de Córdoba', NULL, 'Departamento de Córdoba'),
(5120, 173, 'Distrito de Castelo Branco', NULL, 'Distrito de Castelo Branco'),
(5121, 169, 'Departamento de Presidente Hayes', NULL, 'Departamento de Presidente Hayes'),
(5122, 4, 'Qubadli Rayon', NULL, 'Qubadli Rayon'),
(5123, 118, 'Dobeles Rajons', NULL, 'Dobeles Rajons'),
(5124, 50, 'Province de l’Ogooué-Lolo', NULL, 'Province de l’Ogooué-Lolo'),
(5125, 219, 'Gouvernorat de Sidi Bouzid', NULL, 'Gouvernorat de Sidi Bouzid'),
(5126, 221, 'Ankara', NULL, 'Ankara'),
(5127, 49, 'Tỉnh Trà Vinh', NULL, 'Tỉnh Trà Vinh'),
(5128, 221, 'Diyarbakır', NULL, 'Diyarbakır'),
(5129, 4, 'Mingacevir City', NULL, 'Mingacevir City'),
(5130, 73, 'Provincia de Puerto Plata', NULL, 'Provincia de Puerto Plata'),
(5131, 39, 'Rumonge Province', NULL, 'Rumonge Province'),
(5132, 152, '', NULL, 'Sudur Pashchimanchal'),
(5133, 98, 'Marsabit', NULL, 'Marsabit'),
(5134, 201, 'Občina Domžale', NULL, 'Občina Domžale'),
(5135, 174, 'Aibonito', NULL, 'Aibonito'),
(5136, 7, 'Wilaya de Biskra', NULL, 'Wilaya de Biskra'),
(5137, 120, 'Maryland County', NULL, 'Maryland County'),
(5138, 156, 'Оверайссел', NULL, 'Provincie Overijssel'),
(5139, 144, 'Raionul Căuşeni', NULL, 'Raionul Căuşeni'),
(5140, 4, 'Hajigabul Rayon', NULL, 'Hajigabul Rayon'),
(5141, 45, 'Estado Barinas', NULL, 'Estado Barinas'),
(5142, 104, 'Departamento de Caldas', NULL, 'Departamento de Caldas'),
(5143, 249, 'Busan', NULL, 'Busan'),
(5144, 30, 'Departamento de La Paz', NULL, 'Departamento de La Paz'),
(5145, 16, 'San Juan Province', NULL, 'San Juan Province'),
(5146, 207, '', NULL, 'Northern Province'),
(5147, 94, 'West Region', NULL, 'West Region'),
(5148, 21, 'Читтагонг', NULL, 'Chittagong'),
(5149, 135, 'Mopti Region', NULL, 'Mopti Region'),
(5150, 57, 'Kindia Region', NULL, 'Kindia Region'),
(5151, 49, 'Tỉnh Quảng Nam', NULL, 'Tỉnh Quảng Nam'),
(5152, 246, 'Пылвамаа', NULL, 'Põlvamaa'),
(5153, 241, 'Kalmar', NULL, 'Kalmar'),
(5154, 157, 'Departamento de Rivas', NULL, 'Departamento de Rivas'),
(5155, 234, 'Varaždinska Županija', NULL, 'Varaždinska Županija'),
(5156, 201, 'Občina Hoče-Slivnica', NULL, 'Občina Hoče-Slivnica'),
(5157, 128, 'Anosy Region', NULL, 'Anosy Region'),
(5158, 146, 'Arhangay Aymag', NULL, 'Arhangay Aymag'),
(5159, 179, 'Judeţul Iaşi', NULL, 'Judeţul Iaşi'),
(5160, 128, 'Androy Region', NULL, 'Androy Region'),
(5161, 249, 'Chungcheongbuk-do', NULL, 'Chungcheongbuk-do'),
(5162, 120, 'Grand Gedeh County', NULL, 'Grand Gedeh County'),
(5163, 191, 'Région de Sédhiou', NULL, 'Région de Sédhiou'),
(5164, 201, 'Podlehnik', NULL, 'Podlehnik'),
(5165, 234, 'Virovitičko-Podravska Županija', NULL, 'Virovitičko-Podravska Županija'),
(5166, 166, 'State of Ngchesar', NULL, 'State of Ngchesar'),
(5167, 83, 'Muḩāfaz̧at Bābil', NULL, 'Muḩāfaz̧at Bābil'),
(5168, 56, 'Departamento del Petén', NULL, 'Departamento del Petén'),
(5169, 201, 'Markovci', NULL, 'Markovci'),
(5170, 96, 'Concelho do Porto Novo', NULL, 'Concelho do Porto Novo'),
(5171, 86, 'Norðurland Eystra', NULL, 'Norðurland Eystra'),
(5172, 251, 'Clarendon', NULL, 'Clarendon'),
(5173, 201, 'Naklo', NULL, 'Naklo'),
(5174, 4, 'Qakh Rayon', NULL, 'Qakh Rayon'),
(5175, 66, 'Racha-Lechkhumi and Kvemo Svaneti', NULL, 'Racha-Lechkhumi and Kvemo Svaneti'),
(5176, 25, 'Stann Creek District', NULL, 'Stann Creek District'),
(5177, 29, 'Oblast Sliven', NULL, 'Oblast Sliven'),
(5178, 70, 'Province du Haut-Katanga', NULL, 'Province du Haut-Katanga'),
(5179, 104, 'Amazonas', NULL, 'Amazonas'),
(5180, 45, 'Estado Trujillo', NULL, 'Estado Trujillo'),
(5181, 72, 'Saint Luke', NULL, 'Saint Luke'),
(5182, 80, 'Пенджаб', NULL, 'State of Punjab'),
(5183, 4, 'Astara District', NULL, 'Astara District'),
(5184, 201, 'Občina Ormož', NULL, 'Občina Ormož'),
(5185, 7, 'Wilaya de Naama', NULL, 'Wilaya de Naama'),
(5186, 164, 'Musandam', NULL, 'Musandam'),
(5187, 201, 'Nova Gorica', NULL, 'Nova Gorica'),
(5188, 201, 'Murska Sobota', NULL, 'Murska Sobota'),
(5189, 20, 'Crooked Island and Long Cay District', NULL, 'Crooked Island and Long Cay District'),
(5190, 216, 'Vava‘u', NULL, 'Vava‘u'),
(5191, 201, 'Občina Luče', NULL, 'Občina Luče'),
(5192, 174, 'Vega Alta Municipio', NULL, 'Vega Alta Municipio'),
(5193, 201, 'Občina Razkrižje', NULL, 'Občina Razkrižje'),
(5194, 256, 'Lhuentse Dzongkhag', NULL, 'Lhuentse Dzongkhag'),
(5195, 245, 'Anseba Region', NULL, 'Anseba Region'),
(5196, 129, 'Bandraboua', NULL, 'Bandraboua'),
(5197, 25, 'Orange Walk District', NULL, 'Orange Walk District'),
(5198, 302, 'Saint Saviour', NULL, 'Saint Saviour'),
(5199, 117, 'Xaisomboun Province', NULL, 'Xaisomboun Province'),
(5200, 238, 'Plzeňský kraj', NULL, 'Plzeňský kraj'),
(5201, 118, 'Ilūkstes Novads', NULL, 'Ilūkstes Novads'),
(5202, 118, 'Vārkavas Novads', NULL, 'Vārkavas Novads'),
(5203, 61, 'Departamento de Valle', NULL, 'Departamento de Valle'),
(5204, 169, 'Departamento de San Pedro', NULL, 'Departamento de San Pedro'),
(5205, 256, 'Trongsa Dzongkhag', NULL, 'Trongsa Dzongkhag'),
(5206, 240, 'Невшатель', NULL, 'Neuchâtel'),
(5207, 65, 'Central Macedonia', NULL, 'Central Macedonia'),
(5208, 249, 'Daejeon', NULL, 'Daejeon'),
(5209, 236, 'Mayo-Kebbi West Region', NULL, 'Mayo-Kebbi West Region'),
(5210, 65, 'Eastern Macedonia and Thrace', NULL, 'Eastern Macedonia and Thrace'),
(5211, 126, 'Flacq District', NULL, 'Flacq District'),
(5212, 148, 'Taninthayi Region', NULL, 'Taninthayi Region'),
(5213, 129, 'Sada', NULL, 'Sada'),
(5214, 236, 'Kanem Region', NULL, 'Kanem Region'),
(5215, 140, 'Ujelang Atoll', NULL, 'Ujelang Atoll'),
(5216, 203, 'Gobolka Hiiraan', NULL, 'Gobolka Hiiraan'),
(5217, 186, 'Острова Тристан-да-Кунья', NULL, 'Tristan da Cunha'),
(5218, 63, 'Saint Mark', NULL, 'Saint Mark'),
(5219, 63, 'Saint John', NULL, 'Saint John'),
(5220, 141, 'Estado de Baja California', NULL, 'Estado de Baja California'),
(5221, 195, 'Soufrière', NULL, 'Soufrière'),
(5222, 118, 'Bauskas Novads', NULL, 'Bauskas Novads'),
(5223, 262, 'Jonglei', NULL, 'Jonglei'),
(5224, 173, 'Distrito de Viseu', NULL, 'Distrito de Viseu'),
(5225, 98, 'Bungoma', NULL, 'Bungoma'),
(5226, 229, 'Кюменлааксо', NULL, 'Kymenlaakso'),
(5227, 191, 'Диурбель', NULL, 'Diourbel'),
(5228, 219, 'Gouvernorat de Kébili', NULL, 'Gouvernorat de Kébili'),
(5229, 148, 'Kayin State', NULL, 'Kayin State'),
(5230, 7, 'El Tarf', NULL, 'El Tarf'),
(5231, 124, 'Schellenberg', NULL, 'Schellenberg'),
(5232, 124, 'Schaan', NULL, 'Schaan'),
(5233, 96, 'Concelho do Maio', NULL, 'Concelho do Maio'),
(5234, 201, 'Polzela', NULL, 'Polzela'),
(5235, 34, 'Santa Catarina', NULL, 'Santa Catarina'),
(5236, 180, 'Departamento de Usulután', NULL, 'Departamento de Usulután'),
(5237, 20, 'Cat Island', NULL, 'Cat Island'),
(5238, 80, 'Андаманские и Никобарские острова', NULL, 'Union Territory of Andaman and Nicobar Islands'),
(5239, 193, 'Saint Paul Charlestown', NULL, 'Saint Paul Charlestown'),
(5240, 201, 'Občina Žužemberk', NULL, 'Občina Žužemberk'),
(5241, 14, 'Parish of Saint Paul', NULL, 'Parish of Saint Paul'),
(5242, 49, 'Tỉnh Bình Dương', NULL, 'Tỉnh Bình Dương'),
(5243, 74, 'Muḩāfaz̧at Asyūţ', NULL, 'Muḩāfaz̧at Asyūţ'),
(5244, 99, '', NULL, 'Eparchía Lefkosías'),
(5245, 161, 'Sogn og Fjordane Fylke', NULL, 'Sogn og Fjordane Fylke'),
(5246, 185, 'Minţaqat al Bāḩah', NULL, 'Minţaqat al Bāḩah'),
(5247, 193, 'Saint Anne Sandy Point', NULL, 'Saint Anne Sandy Point'),
(5248, 98, 'Machakos', NULL, 'Machakos'),
(5249, 201, 'Илирска-Бистрица', NULL, 'Ilirska Bistrica'),
(5250, 61, 'Departamento de Santa Bárbara', NULL, 'Departamento de Santa Bárbara'),
(5251, 172, 'Любуское воеводство', NULL, 'Województwo Lubuskie'),
(5252, 84, 'Ostān-e Hamadān', NULL, 'Ostān-e Hamadān'),
(5253, 39, 'Rutana Province', NULL, 'Rutana Province'),
(5254, 201, 'Duplek', NULL, 'Duplek'),
(5255, 174, 'Lares Municipio', NULL, 'Lares Municipio'),
(5256, 223, 'Karakalpakstan', NULL, 'Karakalpakstan'),
(5257, 132, 'Opština Kočani', NULL, 'Opština Kočani'),
(5258, 44, 'Хайду-Бихар', NULL, 'Hajdú-Bihar'),
(5259, 73, 'Provincia de El Seibo', NULL, 'Provincia de El Seibo'),
(5260, 179, 'Judeţul Galaţi', NULL, 'Judeţul Galaţi'),
(5261, 140, 'Mejit Island', NULL, 'Mejit Island'),
(5262, 136, 'Laamu Atholhu', NULL, 'Laamu Atholhu'),
(5263, 138, 'Dakhla-Oued Ed-Dahab', NULL, 'Dakhla-Oued Ed-Dahab'),
(5264, 73, 'Provincia de Santo Domingo', NULL, 'Provincia de Santo Domingo'),
(5265, 118, 'Inčukalna Novads', NULL, 'Inčukalna Novads'),
(5266, 94, 'East Region', NULL, 'East Region'),
(5267, 190, 'Baie Lazare', NULL, 'Baie Lazare'),
(5268, 190, 'Anse Royale', NULL, 'Anse Royale'),
(5269, 202, 'Honiara', NULL, 'Honiara'),
(5270, 157, 'Departamento de Boaco', NULL, 'Departamento de Boaco'),
(5271, 132, 'Oslomej', NULL, 'Oslomej'),
(5272, 49, 'Tỉnh Sóc Trăng', NULL, 'Tỉnh Sóc Trăng'),
(5273, 104, 'Departamento de Nariño', NULL, 'Departamento de Nariño'),
(5274, 157, 'Costa Caribe Sur', NULL, 'Costa Caribe Sur'),
(5275, 232, '', NULL, 'Guyane'),
(5276, 4, 'Oghuz Rayon', NULL, 'Oghuz Rayon'),
(5277, 89, 'Muḩāfaz̧at Ta‘izz', NULL, 'Muḩāfaz̧at Ta‘izz'),
(5278, 235, 'Commune de Bangui', NULL, 'Commune de Bangui'),
(5279, 132, 'Opština Probištip', NULL, 'Opština Probištip'),
(5280, 132, 'Opština Rankovce', NULL, 'Opština Rankovce'),
(5281, 26, 'Bruxelles-Capitale', NULL, 'Bruxelles-Capitale'),
(5282, 217, 'Point Fortin', NULL, 'Point Fortin'),
(5283, 262, 'Tonj', NULL, 'Tonj'),
(5284, 41, 'Malampa Province', NULL, 'Malampa Province'),
(5285, 221, 'Rize', NULL, 'Rize'),
(5286, 109, 'Gjilan District', NULL, 'Gjilan District'),
(5287, 174, 'Cabo Rojo Municipio', NULL, 'Cabo Rojo Municipio'),
(5288, 104, 'Departamento del Cauca', NULL, 'Departamento del Cauca'),
(5289, 93, 'Kampong Thom', NULL, 'Kampong Thom'),
(5290, 170, 'Región de San Martín', NULL, 'Región de San Martín'),
(5291, 244, 'Provincia de Wele-Nzas', NULL, 'Provincia de Wele-Nzas'),
(5292, 173, 'Distrito de Coimbra', NULL, 'Distrito de Coimbra'),
(5293, 39, 'Bubanza Province', NULL, 'Bubanza Province'),
(5294, 93, 'Кампонгчнанг', NULL, 'Kampong Chhnang'),
(5295, 113, 'Provincia de Camagüey', NULL, 'Provincia de Camagüey'),
(5296, 88, '', NULL, 'Friuli Venezia Giulia'),
(5297, 300, 'Lezayre', NULL, 'Lezayre'),
(5298, 166, 'State of Sonsorol', NULL, 'State of Sonsorol'),
(5299, 166, 'State of Hatohobei', NULL, 'State of Hatohobei'),
(5300, 34, 'Rio de Janeiro', NULL, 'Rio de Janeiro'),
(5301, 57, 'Nzerekore Region', NULL, 'Nzerekore Region'),
(5302, 228, 'Восточные Висайи', NULL, 'Eastern Visayas'),
(5303, 49, 'Tỉnh Hà Nam', NULL, 'Tỉnh Hà Nam'),
(5304, 87, 'Балеарские Острова', NULL, 'Comunitat Autònoma de les Illes Balears'),
(5305, 81, 'Nanggroe Aceh Darussalam Province', NULL, 'Nanggroe Aceh Darussalam Province'),
(5306, 174, 'San Sebastián Municipio', NULL, 'San Sebastián Municipio'),
(5307, 234, 'Zadarska Županija', NULL, 'Zadarska Županija'),
(5308, 239, 'Región de Magallanes y de la Antártica Chilena', NULL, 'Región de Magallanes y de la Antártica Chilena'),
(5309, 98, 'Nairobi', NULL, 'Nairobi'),
(5310, 179, 'Judeţul Sălaj', NULL, 'Judeţul Sălaj'),
(5311, 88, 'Emilia-Romagna', NULL, 'Emilia-Romagna'),
(5312, 185, 'Makkah Province', NULL, 'Makkah Province'),
(5313, 146, 'Govĭ-Sumber', NULL, 'Govĭ-Sumber'),
(5314, 157, 'Departamento de Nueva Segovia', NULL, 'Departamento de Nueva Segovia'),
(5315, 169, 'Departamento de Caaguazú', NULL, 'Departamento de Caaguazú'),
(5316, 141, 'Estado de Oaxaca', NULL, 'Estado de Oaxaca'),
(5317, 180, 'Departamento de Ahuachapán', NULL, 'Departamento de Ahuachapán'),
(5318, 300, 'Rushen', NULL, 'Rushen'),
(5319, 237, 'Danilovgrad', NULL, 'Danilovgrad'),
(5320, 201, 'Občina Bled', NULL, 'Občina Bled'),
(5321, 157, 'Departamento de Managua', NULL, 'Departamento de Managua'),
(5322, 168, 'Central Province', NULL, 'Central Province'),
(5323, 62, 'Tai Po District', NULL, 'Tai Po District'),
(5324, 80, 'Уттар-Прадеш', NULL, 'Uttar Pradesh'),
(5325, 209, 'Changwat Phetchaburi', NULL, 'Changwat Phetchaburi'),
(5326, 141, 'Estado de Nuevo León', NULL, 'Estado de Nuevo León'),
(5327, 81, 'Provinsi Gorontalo', NULL, 'Provinsi Gorontalo'),
(5328, 25, 'Toledo District', NULL, 'Toledo District'),
(5329, 219, 'Мануба', NULL, 'Manouba'),
(5330, 204, 'White Nile', NULL, 'White Nile'),
(5331, 236, 'Mayo-Kebbi East Region', NULL, 'Mayo-Kebbi East Region'),
(5332, 70, 'Lomami', NULL, 'Lomami'),
(5333, 223, 'Жиззахская область', NULL, 'Jizzakh Province'),
(5334, 12, 'Encamp', NULL, 'Encamp'),
(5335, 241, 'Skåne län', NULL, 'Skåne län'),
(5336, 45, 'Estado Táchira', NULL, 'Estado Táchira'),
(5337, 201, 'Gornji Grad', NULL, 'Gornji Grad'),
(5338, 7, 'Wilaya de Boumerdes', NULL, 'Wilaya de Boumerdes'),
(5339, 201, 'Občina Mežica', NULL, 'Občina Mežica'),
(5340, 179, 'Gorj', NULL, 'Gorj'),
(5341, 238, 'Královéhradecký kraj', NULL, 'Královéhradecký kraj'),
(5342, 102, 'Fujian Sheng', NULL, 'Fujian Sheng'),
(5343, 118, 'Neretas Novads', NULL, 'Neretas Novads'),
(5344, 118, 'Viesītes Novads', NULL, 'Viesītes Novads'),
(5345, 231, 'Auvergne-Rhône-Alpes', NULL, 'Auvergne-Rhône-Alpes'),
(5346, 115, 'Aitutaki', NULL, 'Aitutaki'),
(5347, 4, 'Shirvan', NULL, 'Shirvan'),
(5348, 302, 'Saint Brelade', NULL, 'Saint Brelade'),
(5349, 62, 'Islands District', NULL, 'Islands District'),
(5350, 4, 'Lankaran Sahari', NULL, 'Lankaran Sahari'),
(5351, 73, 'Provincia de Baoruco', NULL, 'Provincia de Baoruco'),
(5352, 20, 'South Abaco District', NULL, 'South Abaco District'),
(5353, 256, 'Samdrup Jongkhar Dzongkhag', NULL, 'Samdrup Jongkhar Dzongkhag'),
(5354, 245, 'Northern Red Sea Region', NULL, 'Northern Red Sea Region'),
(5355, 67, 'Yigo Municipality', NULL, 'Yigo Municipality'),
(5356, 4, 'Xankandi Sahari', NULL, 'Xankandi Sahari'),
(5357, 256, 'Zhemgang Dzongkhag', NULL, 'Zhemgang Dzongkhag'),
(5358, 201, 'Občina Apače', NULL, 'Občina Apače'),
(5359, 151, 'Ewa District', NULL, 'Ewa District'),
(5360, 219, 'Gouvernorat de Tunis', NULL, 'Gouvernorat de Tunis'),
(5361, 98, 'Murang\'A', NULL, 'Murang\'A'),
(5362, 65, 'Central Greece', NULL, 'Central Greece'),
(5363, 250, '', NULL, 'Province of KwaZulu-Natal'),
(5364, 140, 'Rongelap Atoll', NULL, 'Rongelap Atoll'),
(5365, 201, 'Občina Poljčane', NULL, 'Občina Poljčane'),
(5366, 241, 'Västernorrlands län', NULL, 'Västernorrlands län'),
(5367, 141, 'Estado de Michoacán de Ocampo', NULL, 'Estado de Michoacán de Ocampo'),
(5368, 62, 'Kwun Tong', NULL, 'Kwun Tong'),
(5369, 217, 'San Fernando', NULL, 'San Fernando'),
(5370, 67, 'Mongmong-Toto-Maite Municipality', NULL, 'Mongmong-Toto-Maite Municipality'),
(5371, 120, 'Bomi County', NULL, 'Bomi County'),
(5372, 118, 'Valmiera District', NULL, 'Valmiera District'),
(5373, 137, 'Ħal Għaxaq', NULL, 'Ħal Għaxaq'),
(5374, 132, 'Opština Studeničani', NULL, 'Opština Studeničani'),
(5375, 137, 'Il-Gżira', NULL, 'Il-Gżira'),
(5376, 169, 'Departamento de Misiones', NULL, 'Departamento de Misiones'),
(5377, 195, 'Dennery', NULL, 'Dennery'),
(5378, 81, 'Provinsi Riau', NULL, 'Provinsi Riau'),
(5379, 39, 'Ngozi Province', NULL, 'Ngozi Province'),
(5380, 200, 'Zilina', NULL, 'Zilina'),
(5381, 154, 'Anambra State', NULL, 'Anambra State'),
(5382, 262, 'Aweil East', NULL, 'Aweil East'),
(5383, 70, 'Квилу', NULL, 'Kwilu'),
(5384, 104, 'Departamento de Santander', NULL, 'Departamento de Santander'),
(5385, 102, 'Sichuan Sheng', NULL, 'Sichuan Sheng'),
(5386, 179, 'Judeţul Botoşani', NULL, 'Judeţul Botoşani'),
(5387, 201, 'Tabor', NULL, 'Tabor'),
(5388, 81, 'Provinsi Kepulauan Riau', NULL, 'Provinsi Kepulauan Riau'),
(5389, 73, 'Provincia de Monte Cristi', NULL, 'Provincia de Monte Cristi'),
(5390, 49, 'Tỉnh Bắc Ninh', NULL, 'Tỉnh Bắc Ninh'),
(5391, 168, 'Manus Province', NULL, 'Manus Province'),
(5392, 80, 'Андхра-Прадеш', NULL, 'State of Andhra Pradesh'),
(5393, 137, 'Marsaxlokk', NULL, 'Marsaxlokk'),
(5394, 209, 'Changwat Si Sa Ket', NULL, 'Changwat Si Sa Ket'),
(5395, 240, 'Canton du Valais', NULL, 'Canton du Valais'),
(5396, 209, 'Changwat Satun', NULL, 'Changwat Satun'),
(5397, 121, 'Mohafazat Baalbek-Hermel', NULL, 'Mohafazat Baalbek-Hermel'),
(5398, 141, 'Estado de Campeche', NULL, 'Estado de Campeche'),
(5399, 137, 'In-Naxxar', NULL, 'In-Naxxar'),
(5400, 237, 'Pljevlja', NULL, 'Pljevlja'),
(5401, 201, 'Trebnje', NULL, 'Trebnje'),
(5402, 201, 'Словенске-Конице', NULL, 'Slovenske Konjice'),
(5403, 161, 'Buskerud fylke', NULL, 'Buskerud fylke'),
(5404, 207, 'Eastern Province', NULL, 'Eastern Province'),
(5405, 70, 'Province du Kongo Central', NULL, 'Province du Kongo Central'),
(5406, 174, 'Las Marías Municipio', NULL, 'Las Marías Municipio'),
(5407, 83, 'Muḩāfaz̧at al Başrah', NULL, 'Muḩāfaz̧at al Başrah'),
(5408, 246, 'Харьюмаа', NULL, 'Harjumaa'),
(5409, 54, 'Volta Region', NULL, 'Volta Region'),
(5410, 19, 'Фарьяб', NULL, 'Faryab Province'),
(5411, 118, 'Siguldas Novads', NULL, 'Siguldas Novads'),
(5412, 241, 'Uppsala', NULL, 'Uppsala'),
(5413, 251, 'Westmoreland', NULL, 'Westmoreland'),
(5414, 84, 'Ostān-e Kermānshāh', NULL, 'Ostān-e Kermānshāh'),
(5415, 49, 'Tỉnh Quảng Ngãi', NULL, 'Tỉnh Quảng Ngãi'),
(5416, 73, 'Provincia de La Romana', NULL, 'Provincia de La Romana'),
(5417, 49, 'Tỉnh Yên Bái', NULL, 'Tỉnh Yên Bái'),
(5418, 226, 'Departamento de Paysandú', NULL, 'Departamento de Paysandú'),
(5419, 4, 'Gobustan Rayon', NULL, 'Gobustan Rayon'),
(5420, 205, '', NULL, 'Distrikt Marowijne'),
(5421, 7, 'Wilaya de Tizi Ouzou', NULL, 'Wilaya de Tizi Ouzou'),
(5422, 93, 'Каеп', NULL, 'Kep'),
(5423, 53, 'Upper River', NULL, 'Upper River'),
(5424, 244, 'Provincia de Bioko Norte', NULL, 'Provincia de Bioko Norte'),
(5425, 209, 'Changwat Yala', NULL, 'Changwat Yala'),
(5426, 174, 'Barceloneta', NULL, 'Barceloneta'),
(5427, 251, 'Portland', NULL, 'Portland'),
(5428, 82, 'Muḩāfaz̧at al Balqā’', NULL, 'Muḩāfaz̧at al Balqā’'),
(5429, 205, 'Distrikt Commewijne', NULL, 'Distrikt Commewijne'),
(5430, 137, 'Il-Fgura', NULL, 'Il-Fgura'),
(5431, 134, 'Kelantan', NULL, 'Kelantan'),
(5432, 167, 'Panamá Oeste', NULL, 'Panamá Oeste'),
(5433, 203, 'Gobolka Jubbada Hoose', NULL, 'Gobolka Jubbada Hoose'),
(5434, 78, 'Harare Province', NULL, 'Harare Province'),
(5435, 153, 'Диффа', NULL, 'Diffa'),
(5436, 118, 'Priekuļi Municipality', NULL, 'Priekuļi Municipality'),
(5437, 104, 'Departamento de Sucre', NULL, 'Departamento de Sucre'),
(5438, 161, 'Telemark fylke', NULL, 'Telemark fylke'),
(5439, 180, 'Departamento de San Miguel', NULL, 'Departamento de San Miguel'),
(5440, 57, 'Boke Region', NULL, 'Boke Region'),
(5441, 201, 'Dobrna', NULL, 'Dobrna'),
(5442, 243, 'Provincia del Pastaza', NULL, 'Provincia del Pastaza'),
(5443, 179, 'Judeţul Sibiu', NULL, 'Judeţul Sibiu'),
(5444, 128, 'Menabe Region', NULL, 'Menabe Region'),
(5445, 179, 'Judeţul Bacău', NULL, 'Judeţul Bacău'),
(5446, 84, 'Ostān-e Chahār Maḩāl va Bakhtīārī', NULL, 'Ostān-e Chahār Maḩāl va Bakhtīārī'),
(5447, 28, 'Pembroke Parish', NULL, 'Pembroke Parish'),
(5448, 38, 'Southwest Region', NULL, 'Southwest Region'),
(5449, 228, 'Центральные Висайи', NULL, 'Central Visayas'),
(5450, 228, 'Zamboanga Peninsula', NULL, 'Zamboanga Peninsula'),
(5451, 243, 'Provincia del Carchi', NULL, 'Provincia del Carchi'),
(5452, 48, 'Ermera', NULL, 'Ermera'),
(5453, 132, 'Opština Pehčevo', NULL, 'Opština Pehčevo'),
(5454, 179, 'Judeţul Argeş', NULL, 'Judeţul Argeş'),
(5455, 117, 'Khouèng Oudômxai', NULL, 'Khouèng Oudômxai'),
(5456, 201, 'Радовлица', NULL, 'Radovljica'),
(5457, 146, 'Orhon Aymag', NULL, 'Orhon Aymag'),
(5458, 132, 'Opština Dojran', NULL, 'Opština Dojran'),
(5459, 253, 'Mie-ken', NULL, 'Mie-ken'),
(5460, 211, 'Pemba South Region', NULL, 'Pemba South Region'),
(5461, 201, 'Радле-об-Дравы', NULL, 'Radlje ob Dravi'),
(5462, 201, 'Občina Sveti Andraž v Slovenskih Goricah', NULL, 'Občina Sveti Andraž v Slovenskih Goricah'),
(5463, 29, 'Oblast Shumen', NULL, 'Oblast Shumen'),
(5464, 209, 'Changwat Phangnga', NULL, 'Changwat Phangnga'),
(5465, 74, 'Muḩāfaz̧at Sūhāj', NULL, 'Muḩāfaz̧at Sūhāj'),
(5466, 222, 'Eastern Region', NULL, 'Eastern Region'),
(5467, 164, 'Ash Sharqiyah South', NULL, 'Ash Sharqiyah South'),
(5468, 137, 'Ħaż-Żabbar', NULL, 'Ħaż-Żabbar'),
(5469, 164, 'Muḩāfaz̧at Masqaţ', NULL, 'Muḩāfaz̧at Masqaţ'),
(5470, 154, 'Borno State', NULL, 'Borno State'),
(5471, 256, 'Dagana Dzongkhag', NULL, 'Dagana Dzongkhag'),
(5472, 7, 'Wilaya de Aïn Temouchent', NULL, 'Wilaya de Aïn Temouchent'),
(5473, 70, 'Чопо', NULL, 'Tshopo'),
(5474, 174, 'Guayama Municipio', NULL, 'Guayama Municipio'),
(5475, 238, 'Liberecký kraj', NULL, 'Liberecký kraj'),
(5476, 154, 'Федеральная столичная территория', NULL, 'Federal Capital Territory'),
(5477, 129, 'Bandrele', NULL, 'Bandrele'),
(5478, 97, 'Baladīyat ash Shamāl', NULL, 'Baladīyat ash Shamāl'),
(5479, 67, 'Talofofo Municipality', NULL, 'Talofofo Municipality'),
(5480, 7, 'Adrar', NULL, 'Adrar'),
(5481, 302, 'Saint Mary', NULL, 'Saint Mary'),
(5482, 129, 'Chirongui', NULL, 'Chirongui'),
(5483, 115, 'Ma\'uke', NULL, 'Ma\'uke'),
(5484, 238, 'Ústecký kraj', NULL, 'Ústecký kraj'),
(5485, 140, 'Maloelap Atoll', NULL, 'Maloelap Atoll'),
(5486, 238, 'Zlínský kraj', NULL, 'Zlínský kraj'),
(5487, 67, 'Agana Heights Municipality', NULL, 'Agana Heights Municipality'),
(5488, 240, 'Kanton Nidwalden', NULL, 'Kanton Nidwalden'),
(5489, 30, 'Departamento de Cochabamba', NULL, 'Departamento de Cochabamba'),
(5490, 140, 'Namdrik Atoll', NULL, 'Namdrik Atoll'),
(5491, 4, 'Yevlakh Rayon', NULL, 'Yevlakh Rayon'),
(5492, 19, 'Bādghīs', NULL, 'Bādghīs'),
(5493, 129, 'M\'Tsangamouji', NULL, 'M\'Tsangamouji'),
(5494, 201, 'Občina Škocjan', NULL, 'Občina Škocjan'),
(5495, 118, 'Ciblas Novads', NULL, 'Ciblas Novads'),
(5496, 98, 'Garissa', NULL, 'Garissa');
INSERT INTO `region` (`region_id`, `country_id`, `title`, `title_short`, `title_lat`) VALUES
(5497, 89, 'Muḩāfaz̧at Ḩaḑramawt', NULL, 'Muḩāfaz̧at Ḩaḑramawt'),
(5498, 303, 'Forest', NULL, 'Forest'),
(5499, 190, 'Mont Fleuri', NULL, 'Mont Fleuri'),
(5500, 74, 'Muḩāfaz̧at Shamāl Sīnā’', NULL, 'Muḩāfaz̧at Shamāl Sīnā’'),
(5501, 67, 'Barrigada Municipality', NULL, 'Barrigada Municipality'),
(5502, 236, 'Сила', NULL, 'Sila'),
(5503, 136, 'Thaa Atholhu', NULL, 'Thaa Atholhu'),
(5504, 80, 'State of Uttarakhand', NULL, 'State of Uttarakhand'),
(5505, 240, 'Kanton Luzern', NULL, 'Kanton Luzern'),
(5506, 190, 'Inner Islands', NULL, 'Inner Islands'),
(5507, 132, 'Opština Karpoš', NULL, 'Opština Karpoš'),
(5508, 78, 'Midlands Province', NULL, 'Midlands Province'),
(5509, 229, 'Центральная Остроботния', NULL, 'Keski-Pohjanmaa'),
(5510, 262, 'Terekeka', NULL, 'Terekeka'),
(5511, 118, 'Smiltenes Novads', NULL, 'Smiltenes Novads'),
(5512, 201, 'Občina Črenšovci', NULL, 'Občina Črenšovci'),
(5513, 161, 'Hedmark fylke', NULL, 'Hedmark fylke'),
(5514, 186, '', NULL, 'Saint Helena'),
(5515, 122, 'Sha‘bīyat az Zāwiyah', NULL, 'Sha‘bīyat az Zāwiyah'),
(5516, 102, 'Tianjin Shi', NULL, 'Tianjin Shi'),
(5517, 16, 'Neuquén Province', NULL, 'Neuquén Province'),
(5518, 132, 'Sveti Nikole', NULL, 'Sveti Nikole'),
(5519, 54, 'Ashanti Region', NULL, 'Ashanti Region'),
(5520, 11, 'Kwanza Sul', NULL, 'Kwanza Sul'),
(5521, 118, 'Vecpiebalgas Novads', NULL, 'Vecpiebalgas Novads'),
(5522, 113, 'Provincia de Ciego de Ávila', NULL, 'Provincia de Ciego de Ávila'),
(5523, 93, 'Бантеаймеантьей', NULL, 'Banteay Meanchey'),
(5524, 135, 'Tombouctou Region', NULL, 'Tombouctou Region'),
(5525, 181, 'Туамасага', NULL, 'Tuamasaga'),
(5526, 52, 'Pomeroon-Supenaam Region', NULL, 'Pomeroon-Supenaam Region'),
(5527, 96, 'Concelho de Santa Catarina', NULL, 'Concelho de Santa Catarina'),
(5528, 262, 'Tambura', NULL, 'Tambura'),
(5529, 96, 'Sal Municipality', NULL, 'Sal Municipality'),
(5530, 120, 'River Gee County', NULL, 'River Gee County'),
(5531, 201, 'Občina Brežice', NULL, 'Občina Brežice'),
(5532, 203, 'Gobolka Galguduud', NULL, 'Gobolka Galguduud'),
(5533, 96, 'Concelho do Paul', NULL, 'Concelho do Paul'),
(5534, 209, 'Changwat Suphan Buri', NULL, 'Changwat Suphan Buri'),
(5535, 9, 'Rose Atoll', NULL, 'Rose Atoll'),
(5536, 58, 'Gabú', NULL, 'Gabú'),
(5537, 124, 'Маурен', NULL, 'Mauren'),
(5538, 239, 'Maule', NULL, 'Maule'),
(5539, 255, 'Vága Sýsla', NULL, 'Vága Sýsla'),
(5540, 201, 'Kostel', NULL, 'Kostel'),
(5541, 56, 'Departamento de Retalhuleu', NULL, 'Departamento de Retalhuleu'),
(5542, 146, 'Sühbaatar Aymag', NULL, 'Sühbaatar Aymag'),
(5543, 74, 'As Suways', NULL, 'As Suways'),
(5544, 234, 'Brodsko-Posavska Županija', NULL, 'Brodsko-Posavska Županija'),
(5545, 251, 'Кингстон', NULL, 'Kingston'),
(5546, 237, 'Opština Plav', NULL, 'Opština Plav'),
(5547, 226, 'Departamento de Tacuarembó', NULL, 'Departamento de Tacuarembó'),
(5548, 132, 'Kavadarci', NULL, 'Kavadarci'),
(5549, 104, 'Departamento del Vaupés', NULL, 'Departamento del Vaupés'),
(5550, 107, 'Rason', NULL, 'Rason'),
(5551, 138, 'Drâa-Tafilalet', NULL, 'Drâa-Tafilalet'),
(5552, 4, 'Barda Rayon', NULL, 'Barda Rayon'),
(5553, 238, 'Hlavní město Praha', NULL, 'Hlavní město Praha'),
(5554, 132, 'Opština Krivogaštani', NULL, 'Opština Krivogaštani'),
(5555, 201, 'Občina Majšperk', NULL, 'Občina Majšperk'),
(5556, 56, 'Departamento de Zacapa', NULL, 'Departamento de Zacapa'),
(5557, 209, 'Changwat Chai Nat', NULL, 'Changwat Chai Nat'),
(5558, 202, 'Choiseul', NULL, 'Choiseul'),
(5559, 209, 'Changwat Buri Ram', NULL, 'Changwat Buri Ram'),
(5560, 301, 'Ålands landsbygd', NULL, 'Ålands landsbygd'),
(5561, 125, 'Canton d\'Echternach', NULL, 'Canton d\'Echternach'),
(5562, 54, 'Upper East Region', NULL, 'Upper East Region'),
(5563, 240, 'Canton de Fribourg', NULL, 'Canton de Fribourg'),
(5564, 84, 'Kerman', NULL, 'Kerman'),
(5565, 190, 'Anse Boileau', NULL, 'Anse Boileau'),
(5566, 201, 'Občina Zavrč', NULL, 'Občina Zavrč'),
(5567, 49, 'Tỉnh Lạng Sơn', NULL, 'Tỉnh Lạng Sơn'),
(5568, 117, 'Vientiane Province', NULL, 'Vientiane Province'),
(5569, 22, 'Saint Joseph', NULL, 'Saint Joseph'),
(5570, 217, 'Penal/Debe', NULL, 'Penal/Debe'),
(5571, 205, 'Distrikt Coronie', NULL, 'Distrikt Coronie'),
(5572, 243, 'Provincia del Guayas', NULL, 'Provincia del Guayas'),
(5573, 123, 'Kaunas County', NULL, 'Kaunas County'),
(5574, 201, 'Občina Rogašovci', NULL, 'Občina Rogašovci'),
(5575, 118, 'Pārgaujas Novads', NULL, 'Pārgaujas Novads'),
(5576, 241, 'Kronoberg', NULL, 'Kronoberg'),
(5577, 244, 'Provincia de Litoral', NULL, 'Provincia de Litoral'),
(5578, 102, 'Zhejiang Sheng', NULL, 'Zhejiang Sheng'),
(5579, 253, 'Tokyo Prefecture', NULL, 'Tokyo Prefecture'),
(5580, 49, 'Tỉnh Kiến Giang', NULL, 'Tỉnh Kiến Giang'),
(5581, 251, 'Parish of Hanover', NULL, 'Parish of Hanover'),
(5582, 4, 'Goygol Rayon', NULL, 'Goygol Rayon'),
(5583, 187, '', NULL, 'Manzini District'),
(5584, 137, 'Il-Qrendi', NULL, 'Il-Qrendi'),
(5585, 174, 'Salinas Municipio', NULL, 'Salinas Municipio'),
(5586, 137, 'Victoria', NULL, 'Victoria'),
(5587, 102, 'Shanxi Sheng', NULL, 'Shanxi Sheng'),
(5588, 10, 'The Valley', NULL, 'The Valley'),
(5589, 49, 'Tỉnh Đồng Nai', NULL, 'Tỉnh Đồng Nai'),
(5590, 51, 'Département de l\'Artibonite', NULL, 'Département de l\'Artibonite'),
(5591, 49, 'Tỉnh Bạc Liêu', NULL, 'Tỉnh Bạc Liêu'),
(5592, 45, 'Estado Yaracuy', NULL, 'Estado Yaracuy'),
(5593, 300, 'Port Erin', NULL, 'Port Erin'),
(5594, 169, 'Departamento de Caazapá', NULL, 'Departamento de Caazapá'),
(5595, 180, 'Departamento de Chalatenango', NULL, 'Departamento de Chalatenango'),
(5596, 237, 'Opština Kolašin', NULL, 'Opština Kolašin'),
(5597, 209, 'Changwat Phatthalung', NULL, 'Changwat Phatthalung'),
(5598, 132, 'Македонска-Каменица', NULL, 'Makedonska Kamenica'),
(5599, 27, 'Département de l’Ouémé', NULL, 'Département de l’Ouémé'),
(5600, 231, 'Centre-Val de Loire', NULL, 'Centre-Val de Loire'),
(5601, 240, 'Kanton Solothurn', NULL, 'Kanton Solothurn'),
(5602, 137, 'Ix-Xgħajra', NULL, 'Ix-Xgħajra'),
(5603, 221, 'Bursa', NULL, 'Bursa'),
(5604, 29, 'Oblast Veliko Tarnovo', NULL, 'Oblast Veliko Tarnovo'),
(5605, 240, 'Kanton St. Gallen', NULL, 'Kanton St. Gallen'),
(5606, 204, 'River Nile', NULL, 'River Nile'),
(5607, 256, 'Tsirang Dzongkhag', NULL, 'Tsirang Dzongkhag'),
(5608, 79, 'Northern District', NULL, 'Northern District'),
(5609, 119, 'Цгутинг', NULL, 'Quthing'),
(5610, 29, 'Oblast Montana', NULL, 'Oblast Montana'),
(5611, 216, 'Eua', NULL, 'Eua'),
(5612, 132, 'Брвеница', NULL, 'Brvenica'),
(5613, 253, 'Kochi Prefecture', NULL, 'Kochi Prefecture'),
(5614, 20, 'North Andros District', NULL, 'North Andros District'),
(5615, 106, 'Cuvette-Ouest', NULL, 'Cuvette-Ouest'),
(5616, 238, 'Moravskoslezský kraj', NULL, 'Moravskoslezský kraj'),
(5617, 11, 'Namibe Province', NULL, 'Namibe Province'),
(5618, 122, 'Sha‘bīyat Mişrātah', NULL, 'Sha‘bīyat Mişrātah'),
(5619, 118, 'Līvānu Novads', NULL, 'Līvānu Novads'),
(5620, 201, 'Log-Dragomer', NULL, 'Log-Dragomer'),
(5621, 67, 'Mangilao Municipality', NULL, 'Mangilao Municipality'),
(5622, 191, 'Сен-Луи', NULL, 'Saint-Louis'),
(5623, 219, 'Gouvernorat de Tozeur', NULL, 'Gouvernorat de Tozeur'),
(5624, 70, 'Ituri', NULL, 'Ituri'),
(5625, 302, 'Trinity', NULL, 'Trinity'),
(5626, 132, 'Opstina Gjorce Petrov', NULL, 'Opstina Gjorce Petrov'),
(5627, 151, 'Baiti District', NULL, 'Baiti District'),
(5628, 126, 'Grand Port District', NULL, 'Grand Port District'),
(5629, 174, 'Yauco Municipio', NULL, 'Yauco Municipio'),
(5630, 4, 'Zangilan Rayon', NULL, 'Zangilan Rayon'),
(5631, 140, 'Bokak Atoll', NULL, 'Bokak Atoll'),
(5632, 118, 'Dagdas Novads', NULL, 'Dagdas Novads'),
(5633, 73, 'Provincia de San Pedro de Macorís', NULL, 'Provincia de San Pedro de Macorís'),
(5634, 65, 'Peloponnese', NULL, 'Peloponnese'),
(5635, 163, '‘Ajmān', NULL, '‘Ajmān'),
(5636, 303, 'Vale', NULL, 'Vale'),
(5637, 123, 'Vilniaus apskritis', NULL, 'Vilniaus apskritis'),
(5638, 239, 'Atacama', NULL, 'Atacama'),
(5639, 148, 'Shan State', NULL, 'Shan State'),
(5640, 195, 'Vieux-Fort', NULL, 'Vieux-Fort'),
(5641, 94, 'South-West Region', NULL, 'South-West Region'),
(5642, 229, 'Центральная Финляндия', NULL, 'Keski-Suomi'),
(5643, 137, 'Il-Ħamrun', NULL, 'Il-Ħamrun'),
(5644, 4, 'Kurdamir Rayon', NULL, 'Kurdamir Rayon'),
(5645, 219, 'Gouvernorat de Sfax', NULL, 'Gouvernorat de Sfax'),
(5646, 262, 'Central Upper Nile', NULL, 'Central Upper Nile'),
(5647, 29, 'Sofia-Grad', NULL, 'Sofia-Grad'),
(5648, 69, 'Зеландия', NULL, 'Region Sjælland'),
(5649, 82, 'Muḩāfaz̧at Mādabā', NULL, 'Muḩāfaz̧at Mādabā'),
(5650, 110, 'Provincia de San José', NULL, 'Provincia de San José'),
(5651, 195, 'Gros-Islet', NULL, 'Gros-Islet'),
(5652, 211, 'Zanzibar Urban/West Region', NULL, 'Zanzibar Urban/West Region'),
(5653, 33, 'Kgatleng District', NULL, 'Kgatleng District'),
(5654, 132, 'Vinica', NULL, 'Vinica'),
(5655, 120, 'Lofa County', NULL, 'Lofa County'),
(5656, 33, 'Central District', NULL, 'Central District'),
(5657, 54, 'Northern Region', NULL, 'Northern Region'),
(5658, 16, 'Santa Fe Province', NULL, 'Santa Fe Province'),
(5659, 118, 'Aizkraukles Rajons', NULL, 'Aizkraukles Rajons'),
(5660, 201, 'Trzin', NULL, 'Trzin'),
(5661, 73, 'Provincia de Dajabón', NULL, 'Provincia de Dajabón'),
(5662, 114, 'Muḩāfaz̧at al Jahrā’', NULL, 'Muḩāfaz̧at al Jahrā’'),
(5663, 7, 'Wilaya de Mascara', NULL, 'Wilaya de Mascara'),
(5664, 102, 'Hebei Sheng', NULL, 'Hebei Sheng'),
(5665, 80, 'State of Jammu and Kashmīr', NULL, 'State of Jammu and Kashmīr'),
(5666, 165, 'Федерально управляемые племенные территории', NULL, 'Federally Administered Tribal Areas'),
(5667, 181, 'Fa‘asaleleaga', NULL, 'Fa‘asaleleaga'),
(5668, 104, 'Distrito Capital de Bogotá', NULL, 'Distrito Capital de Bogotá'),
(5669, 127, 'Nouakchott Nord', NULL, 'Nouakchott Nord'),
(5670, 173, 'Distrito de Braga', NULL, 'Distrito de Braga'),
(5671, 38, 'Centre-Nord', NULL, 'Centre-Nord'),
(5672, 179, 'Judeţul Dâmboviţa', NULL, 'Judeţul Dâmboviţa'),
(5673, 240, 'Canton de Vaud', NULL, 'Canton de Vaud'),
(5674, 201, 'Zagorje ob Savi', NULL, 'Zagorje ob Savi'),
(5675, 14, 'Parish of Saint Mary', NULL, 'Parish of Saint Mary'),
(5676, 165, 'Punjab Province', NULL, 'Punjab Province'),
(5677, 99, 'Eparchía Lárnakas', NULL, 'Eparchía Lárnakas'),
(5678, 240, 'Kanton Thurgau', NULL, 'Kanton Thurgau'),
(5679, 237, 'Opština Plužine', NULL, 'Opština Plužine'),
(5680, 153, 'Zinder', NULL, 'Zinder'),
(5681, 146, 'Övörhangay Aymag', NULL, 'Övörhangay Aymag'),
(5682, 98, 'Taita Taveta', NULL, 'Taita Taveta'),
(5683, 237, 'Gusinje', NULL, 'Gusinje'),
(5684, 239, 'Región del Libertador General Bernardo O’Higgins', NULL, 'Región del Libertador General Bernardo O’Higgins'),
(5685, 73, 'Provincia Sánchez Ramírez', NULL, 'Provincia Sánchez Ramírez'),
(5686, 104, 'Departamento de La Guajira', NULL, 'Departamento de La Guajira'),
(5687, 201, 'Gorje', NULL, 'Gorje'),
(5688, 39, 'Province de Mwaro', NULL, 'Province de Mwaro'),
(5689, 98, 'Meru', NULL, 'Meru'),
(5690, 119, 'Берья', NULL, 'Berea'),
(5691, 138, 'Laâyoune-Sakia El Hamra', NULL, 'Laâyoune-Sakia El Hamra'),
(5692, 223, 'Samarqand Viloyati', NULL, 'Samarqand Viloyati'),
(5693, 4, 'Absheron Rayon', NULL, 'Absheron Rayon'),
(5694, 4, 'Lerik Rayon', NULL, 'Lerik Rayon'),
(5695, 25, 'Corozal District', NULL, 'Corozal District'),
(5696, 34, 'Sergipe', NULL, 'Sergipe'),
(5697, 209, 'Changwat Chachoengsao', NULL, 'Changwat Chachoengsao'),
(5698, 113, 'Provincia Mayabeque', NULL, 'Provincia Mayabeque'),
(5699, 190, 'Anse Etoile', NULL, 'Anse Etoile'),
(5700, 118, 'Sējas Novads', NULL, 'Sējas Novads'),
(5701, 56, 'Departamento de El Progreso', NULL, 'Departamento de El Progreso'),
(5702, 221, 'Ağrı', NULL, 'Ağrı'),
(5703, 118, 'Cēsu Novads', NULL, 'Cēsu Novads'),
(5704, 117, 'Champasak', NULL, 'Champasak'),
(5705, 221, 'Adıyaman', NULL, 'Adıyaman'),
(5706, 239, 'Región del Biobío', NULL, 'Región del Biobío'),
(5707, 134, 'Sabah', NULL, 'Sabah'),
(5708, 81, 'North Kalimantan', NULL, 'North Kalimantan'),
(5709, 104, 'Departamento del Meta', NULL, 'Departamento del Meta'),
(5710, 226, 'Departamento de Maldonado', NULL, 'Departamento de Maldonado'),
(5711, 183, 'Castello di Montegiardino', NULL, 'Castello di Montegiardino'),
(5712, 20, 'Inagua', NULL, 'Inagua'),
(5713, 235, 'Верхнее Мбому', NULL, 'Haut-Mbomou'),
(5714, 199, 'Мухафаза Хасеке', NULL, 'Al-Hasakah Governorate'),
(5715, 153, 'Tahoua', NULL, 'Tahoua'),
(5716, 128, 'Analamanga Region', NULL, 'Analamanga Region'),
(5717, 146, 'Hentiy Aymag', NULL, 'Hentiy Aymag'),
(5718, 201, 'Občina Škofja Loka', NULL, 'Občina Škofja Loka'),
(5719, 84, 'Ostān-e Tehrān', NULL, 'Ostān-e Tehrān'),
(5720, 93, 'Ŏtâr Méanchey', NULL, 'Ŏtâr Méanchey'),
(5721, 38, 'Sahel', NULL, 'Sahel'),
(5722, 166, 'State of Airai', NULL, 'State of Airai'),
(5723, 300, 'Michael', NULL, 'Michael'),
(5724, 96, 'Concelho da Boa Vista', NULL, 'Concelho da Boa Vista'),
(5725, 10, 'North Hill', NULL, 'North Hill'),
(5726, 144, 'Municipiul Bălţi', NULL, 'Municipiul Bălţi'),
(5727, 135, 'Koulikoro Region', NULL, 'Koulikoro Region'),
(5728, 185, 'Minţaqat Najrān', NULL, 'Minţaqat Najrān'),
(5729, 237, 'Котор', NULL, 'Kotor'),
(5730, 10, 'North Side', NULL, 'North Side'),
(5731, 137, 'Is-Siġġiewi', NULL, 'Is-Siġġiewi'),
(5732, 48, 'Айнару', NULL, 'Ainaro'),
(5733, 48, 'Айлеу', NULL, 'Aileu'),
(5734, 89, 'Amanat Al Asimah', NULL, 'Amanat Al Asimah'),
(5735, 7, 'Wilaya de Ouargla', NULL, 'Wilaya de Ouargla'),
(5736, 66, 'Samegrelo-Zemo Svanetis Mkhare', NULL, 'Samegrelo-Zemo Svanetis Mkhare'),
(5737, 201, 'Benedikt', NULL, 'Benedikt'),
(5738, 174, 'Culebra Municipio', NULL, 'Culebra Municipio'),
(5739, 201, 'Občina Križevci', NULL, 'Občina Križevci'),
(5740, 118, 'Baldones Novads', NULL, 'Baldones Novads'),
(5741, 74, 'Muḩāfaz̧at al Gharbīyah', NULL, 'Muḩāfaz̧at al Gharbīyah'),
(5742, 74, 'Beheira Governorate', NULL, 'Beheira Governorate'),
(5743, 98, 'Isiolo', NULL, 'Isiolo'),
(5744, 204, 'South Kordofan', NULL, 'South Kordofan'),
(5745, 201, 'Mestna Občina Novo mesto', NULL, 'Mestna Občina Novo mesto'),
(5746, 199, 'Мухафаза Дамаск', NULL, 'Muḩāfaz̧at Rīf Dimashq'),
(5747, 204, 'North Darfur', NULL, 'North Darfur'),
(5748, 149, 'Otjozondjupa', NULL, 'Otjozondjupa'),
(5749, 209, 'Changwat Nakhon Sawan', NULL, 'Changwat Nakhon Sawan'),
(5750, 204, 'North Kordofan', NULL, 'North Kordofan'),
(5751, 149, 'Kavango West Region', NULL, 'Kavango West Region'),
(5752, 129, 'Acoua', NULL, 'Acoua'),
(5753, 201, 'Muta', NULL, 'Muta'),
(5754, 29, 'Oblast Vratsa', NULL, 'Oblast Vratsa'),
(5755, 141, 'Estado de Veracruz-Llave', NULL, 'Estado de Veracruz-Llave'),
(5756, 67, 'Piti Municipality', NULL, 'Piti Municipality'),
(5757, 256, 'Паро', NULL, 'Paro Dzongkhag'),
(5758, 199, 'Мухафаза Деръа', NULL, 'Daraa Governorate'),
(5759, 245, 'Southern Red Sea Region', NULL, 'Southern Red Sea Region'),
(5760, 174, 'Gurabo Municipio', NULL, 'Gurabo Municipio'),
(5761, 256, 'Samtse Dzongkhag', NULL, 'Samtse Dzongkhag'),
(5762, 129, 'Chiconi', NULL, 'Chiconi'),
(5763, 164, 'Muḩāfaz̧at Shamāl ash Sharqīyah', NULL, 'Muḩāfaz̧at Shamāl ash Sharqīyah'),
(5764, 132, 'Opština Čučer-Sandevo', NULL, 'Opština Čučer-Sandevo'),
(5765, 7, 'Wilaya de Jijel', NULL, 'Wilaya de Jijel'),
(5766, 209, 'Changwat Prachin Buri', NULL, 'Changwat Prachin Buri'),
(5767, 127, 'Hodh ech Chargui', NULL, 'Hodh ech Chargui'),
(5768, 249, 'Jeju-do', NULL, 'Jeju-do'),
(5769, 118, 'Aglonas Novads', NULL, 'Aglonas Novads'),
(5770, 203, 'Gobolka Sanaag', NULL, 'Gobolka Sanaag'),
(5771, 6, 'Qarku i Dibrës', NULL, 'Qarku i Dibrës'),
(5772, 70, 'Lualaba', NULL, 'Lualaba'),
(5773, 11, 'Заире', NULL, 'Zaire'),
(5774, 256, 'Trashi Yangste', NULL, 'Trashi Yangste'),
(5775, 241, 'Västerbottens län', NULL, 'Västerbottens län'),
(5776, 249, 'Gyeongsangbuk-do', NULL, 'Gyeongsangbuk-do'),
(5777, 6, 'Qarku i Durrësit', NULL, 'Qarku i Durrësit'),
(5778, 151, 'Anetan District', NULL, 'Anetan District'),
(5779, 45, 'Estado Vargas', NULL, 'Estado Vargas'),
(5780, 32, '', NULL, 'Republika Srpska'),
(5781, 303, 'Сент-Андру', NULL, 'Saint Andrew'),
(5782, 168, 'Western Province', NULL, 'Western Province'),
(5783, 6, 'Qarku i Kukësit', NULL, 'Qarku i Kukësit'),
(5784, 201, 'Sveta Trojica v Slovenskih Goricah', NULL, 'Sveta Trojica v Slovenskih Goricah'),
(5785, 249, 'Gwangju', NULL, 'Gwangju'),
(5786, 118, 'Rugāju Novads', NULL, 'Rugāju Novads'),
(5787, 201, 'Občina Šentilj', NULL, 'Občina Šentilj'),
(5788, 93, 'Кампонгтям', NULL, 'Kampong Cham'),
(5789, 143, 'Província de Zambézia', NULL, 'Província de Zambézia'),
(5790, 174, 'Patillas Municipio', NULL, 'Patillas Municipio'),
(5791, 63, 'Saint George', NULL, 'Saint George'),
(5792, 82, 'Jerash', NULL, 'Jerash'),
(5793, 118, 'Ērgļu Novads', NULL, 'Ērgļu Novads'),
(5794, 201, 'Jezersko', NULL, 'Jezersko'),
(5795, 118, 'Krāslavas Rajons', NULL, 'Krāslavas Rajons'),
(5796, 198, 'South East Community Development Council', NULL, 'South East Community Development Council'),
(5797, 63, 'Carriacou and Petite Martinique', NULL, 'Carriacou and Petite Martinique'),
(5798, 198, 'South West Community Development Council', NULL, 'South West Community Development Council'),
(5799, 237, 'Ulcinj', NULL, 'Ulcinj'),
(5800, 104, 'Departamento del Magdalena', NULL, 'Departamento del Magdalena'),
(5801, 179, 'Judeţul Maramureş', NULL, 'Judeţul Maramureş'),
(5802, 19, 'Wilāyat-e Kāpīsā', NULL, 'Wilāyat-e Kāpīsā'),
(5803, 81, 'Provinsi Bali', NULL, 'Provinsi Bali'),
(5804, 209, 'Changwat Trat', NULL, 'Changwat Trat'),
(5805, 132, 'Opština Vrapčište', NULL, 'Opština Vrapčište'),
(5806, 262, 'Northern Upper Nile', NULL, 'Northern Upper Nile'),
(5807, 179, 'Judeţul Timiş', NULL, 'Judeţul Timiş'),
(5808, 185, 'Al Jawf', NULL, 'Al Jawf'),
(5809, 253, 'Nagasaki Prefecture', NULL, 'Nagasaki Prefecture'),
(5810, 206, 'Нью-Гэмпшир', NULL, 'New Hampshire'),
(5811, 154, 'Katsina State', NULL, 'Katsina State'),
(5812, 215, 'Fakaofo', NULL, 'Fakaofo'),
(5813, 146, 'Tsentral’nyy Aymak', NULL, 'Töv Aymag'),
(5814, 148, 'Kachin State', NULL, 'Kachin State'),
(5815, 121, 'Mohafazat Aakkâr', NULL, 'Mohafazat Aakkâr'),
(5816, 211, 'Lindi Region', NULL, 'Lindi Region'),
(5817, 193, 'Saint George Gingerland', NULL, 'Saint George Gingerland'),
(5818, 237, 'Podgorica', NULL, 'Podgorica'),
(5819, 146, 'Govĭ-Altay Aymag', NULL, 'Govĭ-Altay Aymag'),
(5820, 234, 'Koprivničko-Križevačka Županija', NULL, 'Koprivničko-Križevačka Županija'),
(5821, 201, 'Trbovlje', NULL, 'Trbovlje'),
(5822, 98, 'Kajiado', NULL, 'Kajiado'),
(5823, 45, 'Estado Sucre', NULL, 'Estado Sucre'),
(5824, 30, 'Departamento de Tarija', NULL, 'Departamento de Tarija'),
(5825, 154, 'Ogun State', NULL, 'Ogun State'),
(5826, 170, 'Амасонас', NULL, 'Amazonas'),
(5827, 143, 'Manica Province', NULL, 'Manica Province'),
(5828, 61, 'Departamento de Islas de la Bahía', NULL, 'Departamento de Islas de la Bahía'),
(5829, 170, 'Паско', NULL, 'Pasco'),
(5830, 4, 'Balakan Rayon', NULL, 'Balakan Rayon'),
(5831, 56, 'Departamento de Guatemala', NULL, 'Departamento de Guatemala'),
(5832, 122, 'Sha‘bīyat Ghāt', NULL, 'Sha‘bīyat Ghāt'),
(5833, 223, 'Кашкадаринская область', NULL, 'Qashqadaryo Province'),
(5834, 221, 'Elazığ', NULL, 'Elazığ'),
(5835, 218, '', NULL, 'Vaitupu'),
(5836, 82, 'Muḩāfaz̧at az Zarqā’', NULL, 'Muḩāfaz̧at az Zarqā’'),
(5837, 170, 'Ламбаеке', NULL, 'Lambayeque'),
(5838, 201, 'Občina Podčetrtek', NULL, 'Občina Podčetrtek'),
(5839, 191, 'Луга', NULL, 'Louga'),
(5840, 4, 'Gadabay Rayon', NULL, 'Gadabay Rayon'),
(5841, 4, 'Ganja City', NULL, 'Ganja City'),
(5842, 227, 'Северный округ', NULL, 'Northern Division'),
(5843, 251, 'Parish of Saint Ann', NULL, 'Parish of Saint Ann'),
(5844, 152, 'Pashchimanchal', NULL, 'Pashchimanchal'),
(5845, 22, 'Saint Andrew', NULL, 'Saint Andrew'),
(5846, 144, 'Криулянский район', NULL, 'Criuleni'),
(5847, 109, 'Ferizaj District', NULL, 'Ferizaj District'),
(5848, 22, 'Christ Church', NULL, 'Christ Church'),
(5849, 148, 'Mon State', NULL, 'Mon State'),
(5850, 163, 'Umm al Qaywayn', NULL, 'Umm al Qaywayn'),
(5851, 262, 'Gbudwe', NULL, 'Gbudwe'),
(5852, 16, 'La Pampa Province', NULL, 'La Pampa Province'),
(5853, 76, 'Northern Province', NULL, 'Northern Province'),
(5854, 93, 'Kampong Speu', NULL, 'Kampong Speu'),
(5855, 117, 'Khouèng Savannakhét', NULL, 'Khouèng Savannakhét'),
(5856, 222, 'Central Region', NULL, 'Central Region'),
(5857, 98, 'Nyeri', NULL, 'Nyeri'),
(5858, 181, 'A\'ana', NULL, 'A\'ana'),
(5859, 14, 'Барбуда', NULL, 'Barbuda'),
(5860, 45, 'Федеральный округ Венесуэлы', NULL, 'Distrito Capital'),
(5861, 201, 'Brezovica', NULL, 'Brezovica'),
(5862, 201, 'Borovnica', NULL, 'Borovnica'),
(5863, 135, 'Ségou Region', NULL, 'Ségou Region'),
(5864, 84, 'Бушир', NULL, 'Bushehr'),
(5865, 128, 'Vatovavy Fitovinany Region', NULL, 'Vatovavy Fitovinany Region'),
(5866, 83, 'Dihok', NULL, 'Dihok'),
(5867, 128, 'Ihorombe Region', NULL, 'Ihorombe Region'),
(5868, 11, 'Kuando Kubango', NULL, 'Kuando Kubango'),
(5869, 300, 'Maughold', NULL, 'Maughold'),
(5870, 81, 'Provinsi Maluku', NULL, 'Provinsi Maluku'),
(5871, 28, 'Hamilton', NULL, 'Hamilton'),
(5872, 137, 'Saint Lawrence', NULL, 'Saint Lawrence'),
(5873, 165, 'Khyber Pakhtunkhwa Province', NULL, 'Khyber Pakhtunkhwa Province'),
(5874, 39, 'Muyinga Province', NULL, 'Muyinga Province'),
(5875, 118, 'Engures Novads', NULL, 'Engures Novads'),
(5876, 134, 'Путраджайя', NULL, 'Putrajaya'),
(5877, 300, 'Patrick', NULL, 'Patrick'),
(5878, 45, 'Estado Mérida', NULL, 'Estado Mérida'),
(5879, 201, 'Občina Žetale', NULL, 'Občina Žetale'),
(5880, 49, 'Tỉnh Cà Mau', NULL, 'Tỉnh Cà Mau'),
(5881, 211, 'Geita Region', NULL, 'Geita Region'),
(5882, 10, 'The Quarter', NULL, 'The Quarter'),
(5883, 74, 'Kafr ash Shaykh', NULL, 'Kafr ash Shaykh'),
(5884, 49, 'Tỉnh Vĩnh Phúc', NULL, 'Tỉnh Vĩnh Phúc'),
(5885, 201, 'Bohinj', NULL, 'Bohinj'),
(5886, 211, 'Coast Region', NULL, 'Coast Region'),
(5887, 209, 'Changwat Phayao', NULL, 'Changwat Phayao'),
(5888, 96, 'Concelho da Ribeira Brava', NULL, 'Concelho da Ribeira Brava'),
(5889, 169, 'Departamento de Alto Paraguay', NULL, 'Departamento de Alto Paraguay'),
(5890, 72, 'Saint Peter', NULL, 'Saint Peter'),
(5891, 58, 'Cacheu Region', NULL, 'Cacheu Region'),
(5892, 166, 'State of Peleliu', NULL, 'State of Peleliu'),
(5893, 135, 'Kayes Region', NULL, 'Kayes Region'),
(5894, 203, 'Gobolka Bakool', NULL, 'Gobolka Bakool'),
(5895, 179, 'Harghita', NULL, 'Harghita'),
(5896, 204, 'East Darfur', NULL, 'East Darfur'),
(5897, 93, 'Прэахвихеа', NULL, 'Preah Vihear'),
(5898, 74, 'Red Sea Governorate', NULL, 'Red Sea Governorate'),
(5899, 256, 'Chhukha Dzongkhag', NULL, 'Chhukha Dzongkhag'),
(5900, 168, 'Jiwaka', NULL, 'Jiwaka'),
(5901, 76, 'Central Province', NULL, 'Central Province'),
(5902, 201, 'Grad', NULL, 'Grad'),
(5903, 231, 'Bourgogne-Franche-Comté', NULL, 'Bourgogne-Franche-Comté'),
(5904, 118, 'Rēzekne', NULL, 'Rēzekne'),
(5905, 110, 'Provincia de Heredia', NULL, 'Provincia de Heredia'),
(5906, 141, 'Estado de México', NULL, 'Estado de México'),
(5907, 200, 'Bratislava', NULL, 'Bratislava'),
(5908, 135, 'Gao Region', NULL, 'Gao Region'),
(5909, 115, 'Manihiki', NULL, 'Manihiki'),
(5910, 151, 'Meneng District', NULL, 'Meneng District'),
(5911, 61, 'Departamento de Comayagua', NULL, 'Departamento de Comayagua'),
(5912, 98, 'Samburu', NULL, 'Samburu'),
(5913, 62, 'Wan Chai', NULL, 'Wan Chai'),
(5914, 129, 'Kani-Kéli', NULL, 'Kani-Kéli'),
(5915, 62, 'Eastern', NULL, 'Eastern'),
(5916, 4, 'Zaqatala Rayon', NULL, 'Zaqatala Rayon'),
(5917, 127, 'Иншири', NULL, 'Inchiri'),
(5918, 201, 'Makole', NULL, 'Makole'),
(5919, 236, 'Borkou Region', NULL, 'Borkou Region'),
(5920, 70, 'Haut Uele', NULL, 'Haut Uele'),
(5921, 235, 'Уака', NULL, 'Ouaka'),
(5922, 32, 'Federation of Bosnia and Herzegovina', NULL, 'Federation of Bosnia and Herzegovina'),
(5923, 118, 'Viļakas Novads', NULL, 'Viļakas Novads'),
(5924, 201, 'Vitanje', NULL, 'Vitanje'),
(5925, 132, 'Гостивар', NULL, 'Gostivar'),
(5926, 138, 'Tanger-Tetouan-Al Hoceima', NULL, 'Tanger-Tetouan-Al Hoceima'),
(5927, 138, 'Oriental', NULL, 'Oriental'),
(5928, 70, 'Tanganika', NULL, 'Tanganika'),
(5929, 136, 'Raa Atholhu', NULL, 'Raa Atholhu'),
(5930, 30, 'Departamento de Santa Cruz', NULL, 'Departamento de Santa Cruz'),
(5931, 141, 'Estado de Quintana Roo', NULL, 'Estado de Quintana Roo'),
(5932, 34, 'Acre', NULL, 'Acre'),
(5933, 122, 'Sha‘bīyat Wādī ash Shāţi’', NULL, 'Sha‘bīyat Wādī ash Shāţi’'),
(5934, 69, 'North Denmark Region', NULL, 'North Denmark Region'),
(5935, 239, 'Región de Valparaíso', NULL, 'Región de Valparaíso'),
(5936, 153, 'Niamey', NULL, 'Niamey'),
(5937, 197, 'Центральная Сербия', NULL, 'Central Serbia'),
(5938, 78, 'Mashonaland East Province', NULL, 'Mashonaland East Province'),
(5939, 137, 'L-Imtarfa', NULL, 'L-Imtarfa'),
(5940, 121, 'Южный Ливан', NULL, 'Mohafazat Liban-Sud'),
(5941, 211, 'Tabora Region', NULL, 'Tabora Region'),
(5942, 44, 'Боршод-Абауй-Земплен', NULL, 'Borsod-Abauj Zemplen county'),
(5943, 105, '', NULL, 'Ndzuwani'),
(5944, 120, 'Gbarpolu County', NULL, 'Gbarpolu County'),
(5945, 81, 'Provinsi Kalimantan Selatan', NULL, 'Provinsi Kalimantan Selatan'),
(5946, 181, 'Палаули', NULL, 'Palauli'),
(5947, 161, 'Møre og Romsdal fylke', NULL, 'Møre og Romsdal fylke'),
(5948, 209, 'Changwat Surat Thani', NULL, 'Changwat Surat Thani'),
(5949, 114, 'Muḩāfaz̧at Mubārak al Kabīr', NULL, 'Muḩāfaz̧at Mubārak al Kabīr'),
(5950, 181, 'Gaga‘emauga', NULL, 'Gaga‘emauga'),
(5951, 201, 'Občina Braslovče', NULL, 'Občina Braslovče'),
(5952, 141, 'Estado de Hidalgo', NULL, 'Estado de Hidalgo'),
(5953, 141, 'Estado de Guerrero', NULL, 'Estado de Guerrero'),
(5954, 66, 'Ajaria', NULL, 'Ajaria'),
(5955, 209, 'Changwat Songkhla', NULL, 'Changwat Songkhla'),
(5956, 194, 'Parish of Saint David', NULL, 'Parish of Saint David'),
(5957, 174, 'Coamo Municipio', NULL, 'Coamo Municipio'),
(5958, 146, 'Uvs Aymag', NULL, 'Uvs Aymag'),
(5959, 148, 'Rakhine State', NULL, 'Rakhine State'),
(5960, 234, 'Dubrovačko-Neretvanska Županija', NULL, 'Dubrovačko-Neretvanska Županija'),
(5961, 201, 'Mirna', NULL, 'Mirna'),
(5962, 143, 'Sofala Province', NULL, 'Sofala Province'),
(5963, 113, 'Provincia Granma', NULL, 'Provincia Granma'),
(5964, 243, 'Provincia del Chimborazo', NULL, 'Provincia del Chimborazo'),
(5965, 201, 'Идрия', NULL, 'Idrija'),
(5966, 228, 'Negros Island Region', NULL, 'Negros Island Region'),
(5967, 132, 'Kisela Voda', NULL, 'Kisela Voda'),
(5968, 161, 'Vestfold fylke', NULL, 'Vestfold fylke'),
(5969, 45, 'Estado Lara', NULL, 'Estado Lara'),
(5970, 136, 'Kaafu Atoll', NULL, 'Kaafu Atoll'),
(5971, 174, 'Luquillo Municipio', NULL, 'Luquillo Municipio'),
(5972, 209, 'Changwat Bueng Kan', NULL, 'Changwat Bueng Kan'),
(5973, 202, 'Makira-Ulawa Province', NULL, 'Makira-Ulawa Province'),
(5974, 125, 'Canton d\'Esch-sur-Alzette', NULL, 'Canton d\'Esch-sur-Alzette'),
(5975, 221, 'Tokat', NULL, 'Tokat'),
(5976, 54, 'Upper West Region', NULL, 'Upper West Region'),
(5977, 157, 'Departamento de Carazo', NULL, 'Departamento de Carazo'),
(5978, 243, 'Provincia de Zamora-Chinchipe', NULL, 'Provincia de Zamora-Chinchipe'),
(5979, 87, 'Астурия', NULL, 'Principality of Asturias'),
(5980, 34, 'Paraná', NULL, 'Paraná'),
(5981, 157, 'North Atlantic Autonomous Region (RAAN)', NULL, 'North Atlantic Autonomous Region (RAAN)'),
(5982, 226, 'Departamento de Rivera', NULL, 'Departamento de Rivera'),
(5983, 205, 'Distrikt Para', NULL, 'Distrikt Para'),
(5984, 98, 'Embu', NULL, 'Embu'),
(5985, 217, 'Chaguanas', NULL, 'Chaguanas'),
(5986, 45, 'Estado Carabobo', NULL, 'Estado Carabobo'),
(5987, 134, 'Саравак', NULL, 'Sarawak'),
(5988, 152, 'Madhya Pashchimanchal', NULL, 'Madhya Pashchimanchal'),
(5989, 61, 'Departamento de Olancho', NULL, 'Departamento de Olancho'),
(5990, 59, '', NULL, 'Mecklenburg-Western Pomerania'),
(5991, 53, 'West Coast', NULL, 'West Coast'),
(5992, 174, 'Arecibo', NULL, 'Arecibo'),
(5993, 251, 'Saint Catherine', NULL, 'Saint Catherine'),
(5994, 106, 'Région de la Bouenza', NULL, 'Région de la Bouenza'),
(5995, 22, 'Saint George', NULL, 'Saint George'),
(5996, 89, 'Al Mahrah', NULL, 'Al Mahrah'),
(5997, 50, 'Province du Moyen-Ogooué', NULL, 'Province du Moyen-Ogooué'),
(5998, 134, 'Pahang', NULL, 'Pahang'),
(5999, 137, 'Dingli', NULL, 'Dingli'),
(6000, 123, 'Tauragė County', NULL, 'Tauragė County'),
(6001, 154, 'Sokoto State', NULL, 'Sokoto State'),
(6002, 109, 'Pristina District', NULL, 'Pristina District'),
(6003, 101, 'Line Islands', NULL, 'Line Islands'),
(6004, 174, 'Orocovis Municipio', NULL, 'Orocovis Municipio'),
(6005, 237, 'Opština Nikšić', NULL, 'Opština Nikšić'),
(6006, 209, 'Changwat Samut Prakan', NULL, 'Changwat Samut Prakan'),
(6007, 59, 'Thuringia', NULL, 'Thuringia'),
(6008, 178, 'Southern Province', NULL, 'Southern Province'),
(6009, 247, 'Southern Nations, Nationalities, and People\'s Region', NULL, 'Southern Nations, Nationalities, and People\'s Region'),
(6010, 146, 'Баян-хонгор', NULL, 'Bayanhongor Aymag'),
(6011, 211, 'Shinyanga Region', NULL, 'Shinyanga Region'),
(6012, 98, 'Migori', NULL, 'Migori'),
(6013, 201, 'Komen', NULL, 'Komen'),
(6014, 209, 'Changwat Ratchaburi', NULL, 'Changwat Ratchaburi'),
(6015, 19, 'Nangarhār', NULL, 'Nangarhār'),
(6016, 174, 'San Germán Municipio', NULL, 'San Germán Municipio'),
(6017, 45, 'Miranda', NULL, 'Miranda'),
(6018, 146, 'Bulgan', NULL, 'Bulgan'),
(6019, 137, 'Saint John', NULL, 'Saint John'),
(6020, 174, 'San Lorenzo Municipio', NULL, 'San Lorenzo Municipio'),
(6021, 111, 'Comoé', NULL, 'Comoé'),
(6022, 111, 'Denguélé', NULL, 'Denguélé'),
(6023, 226, 'Departamento de Artigas', NULL, 'Departamento de Artigas'),
(6024, 118, 'Tērvetes Novads', NULL, 'Tērvetes Novads'),
(6025, 96, 'Concelho de São Domingos', NULL, 'Concelho de São Domingos'),
(6026, 141, 'Estado de Yucatán', NULL, 'Estado de Yucatán'),
(6027, 12, 'Сан-Жулиа-де-Лория', NULL, 'Sant Julià de Lòria'),
(6028, 179, 'Judeţul Alba', NULL, 'Judeţul Alba'),
(6029, 118, 'Mārupes Novads', NULL, 'Mārupes Novads'),
(6030, 70, 'Kasai District', NULL, 'Kasai District'),
(6031, 199, 'Мухафаза Хомс', NULL, 'Homs Governorate'),
(6032, 34, 'Rondônia', NULL, 'Rondônia'),
(6033, 4, 'Shabran Rayon', NULL, 'Shabran Rayon'),
(6034, 7, 'Wilaya de Djelfa', NULL, 'Wilaya de Djelfa'),
(6035, 140, 'Ailinginae Atoll', NULL, 'Ailinginae Atoll'),
(6036, 220, 'Ашхабад', NULL, 'Ashgabat'),
(6037, 137, 'Iż-Żebbuġ', NULL, 'Iż-Żebbuġ'),
(6038, 143, 'Inhambane Province', NULL, 'Inhambane Province'),
(6039, 209, 'Changwat Krabi', NULL, 'Changwat Krabi'),
(6040, 140, 'Arno Atoll', NULL, 'Arno Atoll'),
(6041, 163, 'Dubai', NULL, 'Dubai'),
(6042, 243, 'Provincia de Manabí', NULL, 'Provincia de Manabí'),
(6043, 118, 'Ķeguma Novads', NULL, 'Ķeguma Novads'),
(6044, 113, 'Provincia de Villa Clara', NULL, 'Provincia de Villa Clara'),
(6045, 201, 'Občina Šmartno pri Litiji', NULL, 'Občina Šmartno pri Litiji'),
(6046, 201, 'Ljubno', NULL, 'Ljubno'),
(6047, 210, 'Гаосюн', NULL, 'Kaohsiung'),
(6048, 106, 'Pointe-Noire', NULL, 'Pointe-Noire'),
(6049, 84, 'Ostān-e Ardabīl', NULL, 'Ostān-e Ardabīl'),
(6050, 179, 'Covasna', NULL, 'Covasna'),
(6051, 200, 'Trnava', NULL, 'Trnava'),
(6052, 113, 'Provincia de Sancti Spíritus', NULL, 'Provincia de Sancti Spíritus'),
(6053, 92, 'North Side', NULL, 'North Side'),
(6054, 97, 'Al Wakrah', NULL, 'Al Wakrah'),
(6055, 118, 'Varakļānu Novads', NULL, 'Varakļānu Novads'),
(6056, 225, 'Sigave', NULL, 'Sigave'),
(6057, 201, 'Litija', NULL, 'Litija'),
(6058, 302, 'Saint John', NULL, 'Saint John'),
(6059, 246, 'Ляэнемаа', NULL, 'Läänemaa'),
(6060, 102, 'Heilongjiang Sheng', NULL, 'Heilongjiang Sheng'),
(6061, 190, 'Port Glaud', NULL, 'Port Glaud'),
(6062, 6, 'Qarku i Elbasanit', NULL, 'Qarku i Elbasanit'),
(6063, 209, 'Changwat Sa Kaeo', NULL, 'Changwat Sa Kaeo'),
(6064, 241, 'Värmlands län', NULL, 'Värmlands län'),
(6065, 190, 'Plaisance', NULL, 'Plaisance'),
(6066, 129, 'Pamandzi', NULL, 'Pamandzi'),
(6067, 201, 'Vojnik', NULL, 'Vojnik'),
(6068, 110, 'Provincia de Cartago', NULL, 'Provincia de Cartago'),
(6069, 73, 'Provincia de Santiago', NULL, 'Provincia de Santiago'),
(6070, 44, 'Heves megye', NULL, 'Heves megye'),
(6071, 7, 'Wilaya de Mostaganem', NULL, 'Wilaya de Mostaganem'),
(6072, 44, 'Baranya county', NULL, 'Baranya county'),
(6073, 6, 'Qarku i Shkodrës', NULL, 'Qarku i Shkodrës'),
(6074, 303, 'Кастел', NULL, 'Castel'),
(6075, 107, 'Pyongyang', NULL, 'Pyongyang'),
(6076, 89, 'Muḩāfaz̧at ‘Adan', NULL, 'Muḩāfaz̧at ‘Adan'),
(6077, 33, 'South East District', NULL, 'South East District'),
(6078, 209, 'Changwat Uthai Thani', NULL, 'Changwat Uthai Thani'),
(6079, 102, 'Yunnan Sheng', NULL, 'Yunnan Sheng'),
(6080, 78, 'Masvingo Province', NULL, 'Masvingo Province'),
(6081, 200, 'Presov', NULL, 'Presov'),
(6082, 211, 'Zanzibar Central/South Region', NULL, 'Zanzibar Central/South Region'),
(6083, 201, 'Bloke', NULL, 'Bloke'),
(6084, 87, 'Сеута', NULL, 'Ceuta'),
(6085, 114, 'Muḩāfaz̧at Ḩawallī', NULL, 'Muḩāfaz̧at Ḩawallī'),
(6086, 124, 'Vaduz', NULL, 'Vaduz'),
(6087, 154, 'Plateau State', NULL, 'Plateau State'),
(6088, 211, 'Singida Region', NULL, 'Singida Region'),
(6089, 114, 'Muḩāfaz̧at al Aḩmadī', NULL, 'Muḩāfaz̧at al Aḩmadī'),
(6090, 141, 'Чьяпас', NULL, 'Estado de Chiapas'),
(6091, 38, 'Boucle du Mouhoun Region', NULL, 'Boucle du Mouhoun Region'),
(6092, 170, 'Уанкавелика', NULL, 'Huancavelica'),
(6093, 38, 'Cascades Region', NULL, 'Cascades Region'),
(6094, 181, 'Атуа', NULL, 'Atua'),
(6095, 20, 'Exuma District', NULL, 'Exuma District'),
(6096, 38, 'Centre-Sud', NULL, 'Centre-Sud'),
(6097, 137, 'Il-Mosta', NULL, 'Il-Mosta'),
(6098, 98, 'Turkana', NULL, 'Turkana'),
(6099, 193, 'Saint James Windward', NULL, 'Saint James Windward'),
(6100, 99, 'Eparchía Kerýneias', NULL, 'Eparchía Kerýneias'),
(6101, 247, 'Harari Region', NULL, 'Harari Region'),
(6102, 73, 'Provincia de San Cristóbal', NULL, 'Provincia de San Cristóbal'),
(6103, 138, 'Casablanca-Settat', NULL, 'Casablanca-Settat'),
(6104, 253, 'Aichi-ken', NULL, 'Aichi-ken'),
(6105, 4, 'Bilasuvar Rayon', NULL, 'Bilasuvar Rayon'),
(6106, 237, 'Petnjica', NULL, 'Petnjica'),
(6107, 174, 'Lajas Municipio', NULL, 'Lajas Municipio'),
(6108, 49, 'Long An', NULL, 'Long An'),
(6109, 201, 'Храстник', NULL, 'Hrastnik'),
(6110, 34, 'Amapá', NULL, 'Amapá'),
(6111, 16, 'Jujuy Province', NULL, 'Jujuy Province'),
(6112, 205, 'Distrikt Sipaliwini', NULL, 'Distrikt Sipaliwini'),
(6113, 70, 'Kasaï-Central', NULL, 'Kasaï-Central'),
(6114, 127, 'Дахлет-Нуадибу', NULL, 'Dakhlet Nouadhibou'),
(6115, 4, 'Нахичевань', NULL, 'Nakhchivan Autonomous Republic'),
(6116, 16, 'Córdoba Province', NULL, 'Córdoba Province'),
(6117, 205, 'Distrikt Saramacca', NULL, 'Distrikt Saramacca'),
(6118, 54, 'Western Region', NULL, 'Western Region'),
(6119, 4, 'Masally District', NULL, 'Masally District'),
(6120, 190, 'Bel Air', NULL, 'Bel Air'),
(6121, 156, 'Фрисланд', NULL, 'Provincie Friesland'),
(6122, 93, 'Mondolkiri', NULL, 'Mondolkiri'),
(6123, 4, 'Laçın Rayonu', NULL, 'Laçın Rayonu'),
(6124, 118, 'Krimuldas Novads', NULL, 'Krimuldas Novads'),
(6125, 218, 'Nukufetau', NULL, 'Nukufetau'),
(6126, 49, 'Tỉnh Nghệ An', NULL, 'Tỉnh Nghệ An'),
(6127, 118, 'Даугавпилсский край', NULL, 'Daugavpils municipality'),
(6128, 174, 'Adjuntas', NULL, 'Adjuntas'),
(6129, 262, 'Aweil', NULL, 'Aweil'),
(6130, 49, 'Tỉnh Bình Định', NULL, 'Tỉnh Bình Định'),
(6131, 50, 'Province de la Ngounié', NULL, 'Province de la Ngounié'),
(6132, 16, 'Chaco Province', NULL, 'Chaco Province'),
(6133, 137, 'Attard', NULL, 'Attard'),
(6134, 262, 'Lol', NULL, 'Lol'),
(6135, 251, 'Saint James', NULL, 'Saint James'),
(6136, 7, 'Wilaya de Sétif', NULL, 'Wilaya de Sétif'),
(6137, 183, 'Кьезануова', NULL, 'Chiesanuova'),
(6138, 82, 'Amman Governorate', NULL, 'Amman Governorate'),
(6139, 4, 'Samukh Rayon', NULL, 'Samukh Rayon'),
(6140, 134, 'Perlis', NULL, 'Perlis'),
(6141, 161, 'Nord-Trøndelag Fylke', NULL, 'Nord-Trøndelag Fylke'),
(6142, 262, 'Gok', NULL, 'Gok'),
(6143, 134, 'Perak', NULL, 'Perak'),
(6144, 251, 'Saint Andrew', NULL, 'Saint Andrew'),
(6145, 132, 'Маврово и Ростуша', NULL, 'Opština Mavrovo i Rostuša'),
(6146, 173, 'Distrito de Beja', NULL, 'Distrito de Beja'),
(6147, 41, 'Penama Province', NULL, 'Penama Province'),
(6148, 183, 'Castello di Acquaviva', NULL, 'Castello di Acquaviva'),
(6149, 4, 'Siazan Rayon', NULL, 'Siazan Rayon'),
(6150, 262, 'Twic', NULL, 'Twic'),
(6151, 134, 'Negeri Sembilan', NULL, 'Negeri Sembilan'),
(6152, 253, 'Ishikawa-ken', NULL, 'Ishikawa-ken'),
(6153, 201, 'Kuzma', NULL, 'Kuzma'),
(6154, 217, 'Siparia', NULL, 'Siparia'),
(6155, 20, 'New Providence District', NULL, 'New Providence District'),
(6156, 262, 'Imatong', NULL, 'Imatong'),
(6157, 229, 'Кайнуу', NULL, 'Kainuu'),
(6158, 123, 'Telšių apskritis', NULL, 'Telšių apskritis'),
(6159, 101, 'Gilbert Islands', NULL, 'Gilbert Islands'),
(6160, 104, 'Departamento del Caquetá', NULL, 'Departamento del Caquetá'),
(6161, 174, 'Naranjito Municipio', NULL, 'Naranjito Municipio'),
(6162, 128, 'Itasy Region', NULL, 'Itasy Region'),
(6163, 38, 'High-Basins Region', NULL, 'High-Basins Region'),
(6164, 57, 'Faranah Region', NULL, 'Faranah Region'),
(6165, 128, 'Atsinanana Region', NULL, 'Atsinanana Region'),
(6166, 31, 'Синт-Эстатиус', NULL, 'Sint Eustatius'),
(6167, 300, 'Lonan', NULL, 'Lonan'),
(6168, 48, 'Мануфахи', NULL, 'Manufahi'),
(6169, 4, 'Goranboy Rayon', NULL, 'Goranboy Rayon'),
(6170, 49, 'Thành Phố Cần Thơ', NULL, 'Thành Phố Cần Thơ'),
(6171, 49, 'Thành Phố Hải Phòng', NULL, 'Thành Phố Hải Phòng'),
(6172, 300, 'Marown', NULL, 'Marown'),
(6173, 146, 'Dornod Aymag', NULL, 'Dornod Aymag'),
(6174, 188, 'Northern Islands Municipality', NULL, 'Northern Islands Municipality'),
(6175, 96, 'Concelho da Brava', NULL, 'Concelho da Brava'),
(6176, 48, 'Лаутен', NULL, 'Lautém'),
(6177, 137, 'Saint Julian\'s', NULL, 'Saint Julian\'s'),
(6178, 102, 'Guangxi Zhuangzu Zizhiqu', NULL, 'Guangxi Zhuangzu Zizhiqu'),
(6179, 98, 'Nandi', NULL, 'Nandi'),
(6180, 49, 'Tỉnh Tiền Giang', NULL, 'Tỉnh Tiền Giang'),
(6181, 29, 'Plovdiv', NULL, 'Plovdiv'),
(6182, 249, 'Chungcheongnam-do', NULL, 'Chungcheongnam-do'),
(6183, 10, 'East End', NULL, 'East End'),
(6184, 300, 'Onchan', NULL, 'Onchan'),
(6185, 234, 'Vukovarsko-Srijemska Županija', NULL, 'Vukovarsko-Srijemska Županija'),
(6186, 45, 'Estado Zulia', NULL, 'Estado Zulia'),
(6187, 144, 'Municipiul Bender', NULL, 'Municipiul Bender'),
(6188, 23, 'Мухаррак', NULL, 'Muharraq Governorate'),
(6189, 122, 'Эль-Куфра', NULL, 'Al Kufrah'),
(6190, 179, 'Judeţul Ialomiţa', NULL, 'Judeţul Ialomiţa'),
(6191, 191, 'Région de Kédougou', NULL, 'Région de Kédougou'),
(6192, 16, 'Salta Province', NULL, 'Salta Province'),
(6193, 146, 'Darhan-Uul Aymag', NULL, 'Darhan-Uul Aymag'),
(6194, 221, 'Şanlıurfa', NULL, 'Şanlıurfa'),
(6195, 233, 'Îles Tuamotu-Gambier', NULL, 'Îles Tuamotu-Gambier'),
(6196, 203, 'Gobolka Togdheer', NULL, 'Gobolka Togdheer'),
(6197, 168, 'Milne Bay Province', NULL, 'Milne Bay Province'),
(6198, 137, 'Tas-Sliema', NULL, 'Tas-Sliema'),
(6199, 203, 'Gobolka Bay', NULL, 'Gobolka Bay'),
(6200, 241, 'Jämtlands län', NULL, 'Jämtlands län'),
(6201, 96, 'Concelho de Santa Catarina do Fogo', NULL, 'Concelho de Santa Catarina do Fogo'),
(6202, 204, 'Al Qadarif State', NULL, 'Al Qadarif State'),
(6203, 72, 'Saint Paul', NULL, 'Saint Paul'),
(6204, 199, 'Мухафаза Халеб', NULL, 'Aleppo Governorate'),
(6205, 49, 'Tỉnh Bà Rịa-Vũng Tàu', NULL, 'Tỉnh Bà Rịa-Vũng Tàu'),
(6206, 20, 'Central Abaco District', NULL, 'Central Abaco District'),
(6207, 243, 'Provincia de Napo', NULL, 'Provincia de Napo'),
(6208, 148, 'Nay Pyi Taw', NULL, 'Nay Pyi Taw'),
(6209, 231, 'Île-de-France', NULL, 'Île-de-France'),
(6210, 34, 'Rio Grande do Norte', NULL, 'Rio Grande do Norte'),
(6211, 98, 'Kwale', NULL, 'Kwale'),
(6212, 83, 'Muḩāfaz̧at as Sulaymānīyah', NULL, 'Muḩāfaz̧at as Sulaymānīyah'),
(6213, 159, 'Уэст-Кост', NULL, 'West Coast'),
(6214, 201, 'Cankova', NULL, 'Cankova'),
(6215, 118, 'Vecumnieku Novads', NULL, 'Vecumnieku Novads'),
(6216, 174, 'Florida Municipio', NULL, 'Florida Municipio'),
(6217, 141, 'Estado de Morelos', NULL, 'Estado de Morelos'),
(6218, 173, 'Distrito de Santarém', NULL, 'Distrito de Santarém'),
(6219, 231, 'Grand-Est', NULL, 'Grand-Est'),
(6220, 104, 'Departamento de Risaralda', NULL, 'Departamento de Risaralda'),
(6221, 132, 'Opština Aračinovo', NULL, 'Opština Aračinovo'),
(6222, 211, 'Mwanza Region', NULL, 'Mwanza Region'),
(6223, 126, 'Rodrigues', NULL, 'Rodrigues'),
(6224, 132, 'Боговинье', NULL, 'Bogovinje'),
(6225, 119, 'Mohale’s Hoek District', NULL, 'Mohale’s Hoek District'),
(6226, 118, 'Salas Novads', NULL, 'Salas Novads'),
(6227, 256, 'Pemagatshel Dzongkhag', NULL, 'Pemagatshel Dzongkhag'),
(6228, 210, 'Fukien', NULL, 'Fukien'),
(6229, 94, 'North Region', NULL, 'North Region'),
(6230, 20, 'South Eleuthera', NULL, 'South Eleuthera'),
(6231, 126, 'Rivière du Rempart District', NULL, 'Rivière du Rempart District'),
(6232, 129, 'Dzaoudzi', NULL, 'Dzaoudzi'),
(6233, 246, 'Ляэне-Вирумаа', NULL, 'Lääne-Virumaa'),
(6234, 305, 'Palmyra Atoll', NULL, 'Palmyra Atoll'),
(6235, 16, 'Tucumán Province', NULL, 'Tucumán Province'),
(6236, 104, 'Departamento del Vichada', NULL, 'Departamento del Vichada'),
(6237, 151, 'Anibare District', NULL, 'Anibare District'),
(6238, 132, 'Gazi Baba', NULL, 'Gazi Baba'),
(6239, 303, 'Saint Martin', NULL, 'Saint Martin'),
(6240, 201, 'Копер', NULL, 'Koper'),
(6241, 201, 'Občina Straža', NULL, 'Občina Straža'),
(6242, 209, 'Chiang Mai Province', NULL, 'Chiang Mai Province'),
(6243, 201, 'Občina Sveti Tomaž', NULL, 'Občina Sveti Tomaž'),
(6244, 217, 'Mayaro', NULL, 'Mayaro'),
(6245, 243, 'Provincia de Francisco de Orellana', NULL, 'Provincia de Francisco de Orellana'),
(6246, 246, 'Вильяндимаа', NULL, 'Viljandimaa'),
(6247, 118, 'Valka Municipality', NULL, 'Valka Municipality'),
(6248, 174, 'Camuy Municipio', NULL, 'Camuy Municipio'),
(6249, 154, 'Ebonyi State', NULL, 'Ebonyi State'),
(6250, 141, 'Estado de Puebla', NULL, 'Estado de Puebla'),
(6251, 211, 'Manyara Region', NULL, 'Manyara Region'),
(6252, 33, 'North West District', NULL, 'North West District'),
(6253, 211, 'Ruvuma Region', NULL, 'Ruvuma Region'),
(6254, 198, 'North West Community Development Council', NULL, 'North West Community Development Council'),
(6255, 70, 'Bas Uele', NULL, 'Bas Uele'),
(6256, 242, 'Северо-Западная провинция', NULL, 'North Western Province'),
(6257, 87, 'Галиция', NULL, 'Galicia'),
(6258, 58, 'Oio Region', NULL, 'Oio Region'),
(6259, 229, 'Пирканмаа', NULL, 'Pirkanmaa'),
(6260, 137, 'L-Isla', NULL, 'L-Isla'),
(6261, 9, 'Western District', NULL, 'Western District'),
(6262, 74, 'Muḩāfaz̧at Maţrūḩ', NULL, 'Muḩāfaz̧at Maţrūḩ'),
(6263, 34, 'Ма́ту-Гро́су-ду-Сул', NULL, 'Mato Grosso do Sul'),
(6264, 169, 'Departamento de la Cordillera', NULL, 'Departamento de la Cordillera'),
(6265, 193, 'Saint Thomas Middle Island', NULL, 'Saint Thomas Middle Island'),
(6266, 132, 'Дебарца', NULL, 'Debarca'),
(6267, 193, 'Saint Paul Capesterre', NULL, 'Saint Paul Capesterre'),
(6268, 118, 'Nīcas Novads', NULL, 'Nīcas Novads'),
(6269, 146, 'Hovd', NULL, 'Hovd'),
(6270, 201, 'Občina Mirna Peč', NULL, 'Občina Mirna Peč'),
(6271, 240, 'Kanton Uri', NULL, 'Kanton Uri'),
(6272, 149, 'Zambezi Region', NULL, 'Zambezi Region'),
(6273, 98, 'Lamu', NULL, 'Lamu'),
(6274, 146, 'Selenge Aymag', NULL, 'Selenge Aymag'),
(6275, 234, 'Ličko-Senjska Županija', NULL, 'Ličko-Senjska Županija'),
(6276, 41, 'Tafea Province', NULL, 'Tafea Province'),
(6277, 136, 'Noonu Atoll', NULL, 'Noonu Atoll'),
(6278, 246, 'Йыгевамаа', NULL, 'Jõgevamaa'),
(6279, 118, 'Kokneses Novads', NULL, 'Kokneses Novads'),
(6280, 133, 'Southern Region', NULL, 'Southern Region'),
(6281, 7, 'Wilaya de Bouira', NULL, 'Wilaya de Bouira'),
(6282, 73, 'Provincia de San José de Ocoa', NULL, 'Provincia de San José de Ocoa'),
(6283, 17, '', NULL, 'Yerevan'),
(6284, 117, 'Attapu', NULL, 'Attapu'),
(6285, 81, 'Provinsi Maluku Utara', NULL, 'Provinsi Maluku Utara'),
(6286, 132, 'Makedonski Brod', NULL, 'Makedonski Brod'),
(6287, 50, 'Province de l’Ogooué-Maritime', NULL, 'Province de l’Ogooué-Maritime'),
(6288, 51, 'Centre', NULL, 'Centre'),
(6289, 84, 'Ostān-e Golestān', NULL, 'Ostān-e Golestān'),
(6290, 251, 'Trelawny', NULL, 'Trelawny'),
(6291, 253, 'Kagoshima-ken', NULL, 'Kagoshima-ken'),
(6292, 226, 'Departamento de Salto', NULL, 'Departamento de Salto'),
(6293, 141, 'Estado de Jalisco', NULL, 'Estado de Jalisco'),
(6294, 34, 'Alagoas', NULL, 'Alagoas'),
(6295, 159, 'Окленд', NULL, 'Auckland'),
(6296, 136, 'Dhaalu Atholhu', NULL, 'Dhaalu Atholhu'),
(6297, 262, 'Amadi State', NULL, 'Amadi State'),
(6298, 61, 'Departamento de Ocotepeque', NULL, 'Departamento de Ocotepeque'),
(6299, 201, 'Občina Sveti Jurij ob Ščavnici', NULL, 'Občina Sveti Jurij ob Ščavnici'),
(6300, 174, 'Manatí Municipio', NULL, 'Manatí Municipio'),
(6301, 219, 'Gouvernorat de Béja', NULL, 'Gouvernorat de Béja'),
(6302, 4, 'Shaki city', NULL, 'Shaki city'),
(6303, 217, 'Princes Town', NULL, 'Princes Town'),
(6304, 161, 'Troms Fylke', NULL, 'Troms Fylke'),
(6305, 89, 'Muḩāfaz̧at al Ḩudaydah', NULL, 'Muḩāfaz̧at al Ḩudaydah'),
(6306, 161, 'Nordland Fylke', NULL, 'Nordland Fylke'),
(6307, 174, 'Mayagüez Municipio', NULL, 'Mayagüez Municipio'),
(6308, 113, 'Provincia de Pinar del Río', NULL, 'Provincia de Pinar del Río'),
(6309, 174, 'Morovis Municipio', NULL, 'Morovis Municipio'),
(6310, 109, 'Mitrovica District', NULL, 'Mitrovica District'),
(6311, 7, 'Wilaya de Tipaza', NULL, 'Wilaya de Tipaza'),
(6312, 253, 'Tokushima-ken', NULL, 'Tokushima-ken'),
(6313, 87, 'Область Мадрид', NULL, 'Comunidad de Madrid'),
(6314, 146, 'Dzavhan Aymag', NULL, 'Dzavhan Aymag'),
(6315, 21, 'Dhaka Division', NULL, 'Dhaka Division'),
(6316, 118, 'Jūrmala', NULL, 'Jūrmala'),
(6317, 106, 'Lékoumou', NULL, 'Lékoumou'),
(6318, 89, 'Al Maḩwīt', NULL, 'Al Maḩwīt'),
(6319, 187, 'Lubombo District', NULL, 'Lubombo District'),
(6320, 7, 'Wilaya de Laghouat', NULL, 'Wilaya de Laghouat'),
(6321, 57, 'Labé Region', NULL, 'Labé Region'),
(6322, 93, 'Ратанакири', NULL, 'Ratanakiri'),
(6323, 28, 'Paget Parish', NULL, 'Paget Parish'),
(6324, 255, 'Eysturoyar sýsla', NULL, 'Eysturoyar sýsla'),
(6325, 98, 'Nyamira', NULL, 'Nyamira'),
(6326, 201, 'Občina Železniki', NULL, 'Občina Železniki'),
(6327, 174, 'Santa Isabel Municipio', NULL, 'Santa Isabel Municipio'),
(6328, 83, 'Muḩāfaz̧at Baghdād', NULL, 'Muḩāfaz̧at Baghdād'),
(6329, 86, 'Norðurland Vestra', NULL, 'Norðurland Vestra'),
(6330, 74, 'Ismailia Governorate', NULL, 'Ismailia Governorate'),
(6331, 201, 'Cerkvenjak', NULL, 'Cerkvenjak'),
(6332, 201, 'Постойна', NULL, 'Postojna'),
(6333, 36, 'Brunei and Muara District', NULL, 'Brunei and Muara District'),
(6334, 29, 'Oblast Stara Zagora', NULL, 'Oblast Stara Zagora'),
(6335, 70, 'Province du Nord-Ubangi', NULL, 'Province du Nord-Ubangi'),
(6336, 20, 'Central Andros District', NULL, 'Central Andros District'),
(6337, 4, 'Aghdam Rayon', NULL, 'Aghdam Rayon'),
(6338, 137, 'Iż-Żurrieq', NULL, 'Iż-Żurrieq'),
(6339, 140, 'Aur Atoll', NULL, 'Aur Atoll'),
(6340, 20, 'Mangrove Cay', NULL, 'Mangrove Cay'),
(6341, 160, '', NULL, 'Province des îles Loyauté'),
(6342, 239, 'Los Lagos', NULL, 'Los Lagos'),
(6343, 203, 'Gobolka Shabeellaha Hoose', NULL, 'Gobolka Shabeellaha Hoose'),
(6344, 58, 'Bafatá', NULL, 'Bafatá'),
(6345, 107, 'Hambuk', NULL, 'Hambuk'),
(6346, 179, 'Giurgiu', NULL, 'Giurgiu'),
(6347, 164, 'Muḩāfaz̧at al Buraymī', NULL, 'Muḩāfaz̧at al Buraymī'),
(6348, 302, 'Saint Clement', NULL, 'Saint Clement'),
(6349, 174, 'Guayanilla Municipio', NULL, 'Guayanilla Municipio'),
(6350, 252, 'Шпицберген', NULL, 'Svalbard'),
(6351, 132, 'Opština Čaška', NULL, 'Opština Čaška'),
(6352, 151, 'Uaboe District', NULL, 'Uaboe District'),
(6353, 253, 'Chiba-ken', NULL, 'Chiba-ken'),
(6354, 97, 'Baladīyat al Khawr wa adh Dhakhīrah', NULL, 'Baladīyat al Khawr wa adh Dhakhīrah'),
(6355, 304, 'Archipel des Kerguelen', NULL, 'Archipel des Kerguelen'),
(6356, 179, 'Judeţul Mureş', NULL, 'Judeţul Mureş'),
(6357, 201, 'Občina Radeče', NULL, 'Občina Radeče'),
(6358, 304, 'Îles Saint-Paul et Nouvelle-Amsterdam', NULL, 'Îles Saint-Paul et Nouvelle-Amsterdam'),
(6359, 7, 'Illizi', NULL, 'Illizi'),
(6360, 302, 'Saint Ouen', NULL, 'Saint Ouen'),
(6361, 118, 'Riebiņu Novads', NULL, 'Riebiņu Novads'),
(6362, 39, 'Ruyigi Province', NULL, 'Ruyigi Province'),
(6363, 201, 'Občina Krško', NULL, 'Občina Krško'),
(6364, 219, 'Gouvernorat de Siliana', NULL, 'Gouvernorat de Siliana'),
(6365, 89, 'Socotra', NULL, 'Socotra'),
(6366, 201, 'Kranj', NULL, 'Kranj'),
(6367, 201, 'Občina Kočevje', NULL, 'Občina Kočevje'),
(6368, 249, 'Gyeonggi-do', NULL, 'Gyeonggi-do'),
(6369, 126, 'Black River District', NULL, 'Black River District'),
(6370, 62, 'North', NULL, 'North'),
(6371, 138, 'Rabat-Salé-Kénitra', NULL, 'Rabat-Salé-Kénitra'),
(6372, 201, 'Občina Šentrupert', NULL, 'Občina Šentrupert'),
(6373, 132, 'Шуто-Оризари', NULL, 'Šuto Orizari'),
(6374, 201, 'Nazarje', NULL, 'Nazarje'),
(6375, 45, 'Estado Apure', NULL, 'Estado Apure'),
(6376, 4, 'Nakhchivan', NULL, 'Nakhchivan'),
(6377, 198, 'North East Community Development Region', NULL, 'North East Community Development Region'),
(6378, 110, 'Provincia de Puntarenas', NULL, 'Provincia de Puntarenas'),
(6379, 137, 'L-Imsida', NULL, 'L-Imsida'),
(6380, 113, 'Provincia de Guantánamo', NULL, 'Provincia de Guantánamo'),
(6381, 173, 'Distrito de Vila Real', NULL, 'Distrito de Vila Real'),
(6382, 96, 'Concelho do Tarrafal', NULL, 'Concelho do Tarrafal'),
(6383, 229, 'Канта-Хяме', NULL, 'Kanta-Häme'),
(6384, 211, 'Tanga Region', NULL, 'Tanga Region'),
(6385, 122, 'Jabal al Gharbi District', NULL, 'Jabal al Gharbi District'),
(6386, 84, 'Alborz', NULL, 'Alborz'),
(6387, 71, 'Дикиль', NULL, 'Dikhil'),
(6388, 27, 'Atlantique Department', NULL, 'Atlantique Department'),
(6389, 124, 'Gamprin', NULL, 'Gamprin'),
(6390, 179, 'Judeţul Bistriţa-Năsăud', NULL, 'Judeţul Bistriţa-Năsăud'),
(6391, 246, 'Сааремаа', NULL, 'Saaremaa'),
(6392, 247, 'Dire Dawa Region', NULL, 'Dire Dawa Region'),
(6393, 231, 'Corse', NULL, 'Corse'),
(6394, 19, 'Wilāyat-e Kābul', NULL, 'Wilāyat-e Kābul'),
(6395, 201, 'Občina Loška Dolina', NULL, 'Občina Loška Dolina'),
(6396, 98, 'Nyandarua', NULL, 'Nyandarua'),
(6397, 209, 'Changwat Samut Songkhram', NULL, 'Changwat Samut Songkhram'),
(6398, 228, 'Долина Кагаян', NULL, 'Cagayan Valley'),
(6399, 201, 'Občina Škofljica', NULL, 'Občina Škofljica'),
(6400, 98, 'Makueni', NULL, 'Makueni'),
(6401, 102, 'Ningxia Huizu Zizhiqu', NULL, 'Ningxia Huizu Zizhiqu'),
(6402, 49, 'Tỉnh Sơn La', NULL, 'Tỉnh Sơn La'),
(6403, 201, 'Občina Starše', NULL, 'Občina Starše'),
(6404, 54, 'Brong-Ahafo Region', NULL, 'Brong-Ahafo Region'),
(6405, 7, 'Wilaya de Mila', NULL, 'Wilaya de Mila'),
(6406, 93, 'Phnom Penh', NULL, 'Phnom Penh'),
(6407, 84, 'Ostān-e Khorāsān-e Jonūbī', NULL, 'Ostān-e Khorāsān-e Jonūbī'),
(6408, 218, 'Niutao', NULL, 'Niutao'),
(6409, 246, 'Ида-Вирумаа', NULL, 'Ida-Virumaa'),
(6410, 4, 'Lankaran Rayon', NULL, 'Lankaran Rayon'),
(6411, 190, 'Baie Sainte Anne', NULL, 'Baie Sainte Anne'),
(6412, 80, 'Сикким', NULL, 'Sikkim'),
(6413, 170, 'Ла-Либертад', NULL, 'La Libertad'),
(6414, 34, 'Bahia', NULL, 'Bahia'),
(6415, 104, 'Departamento de Tolima', NULL, 'Departamento de Tolima'),
(6416, 262, 'Eastern Lakes', NULL, 'Eastern Lakes'),
(6417, 83, 'Muḩāfaz̧at Arbīl', NULL, 'Muḩāfaz̧at Arbīl'),
(6418, 118, 'Salacgrīvas Novads', NULL, 'Salacgrīvas Novads'),
(6419, 104, 'Departamento del Chocó', NULL, 'Departamento del Chocó'),
(6420, 22, 'Saint James', NULL, 'Saint James'),
(6421, 152, 'Purwanchal', NULL, 'Purwanchal'),
(6422, 73, 'Provincia de Elías Piña', NULL, 'Provincia de Elías Piña'),
(6423, 56, 'Departamento de Chimaltenango', NULL, 'Departamento de Chimaltenango'),
(6424, 73, 'Provincia de Peravia', NULL, 'Provincia de Peravia'),
(6425, 44, 'Csongrád megye', NULL, 'Csongrád megye'),
(6426, 174, 'Moca Municipio', NULL, 'Moca Municipio'),
(6427, 102, 'Beijing Shi', NULL, 'Beijing Shi');
INSERT INTO `region` (`region_id`, `country_id`, `title`, `title_short`, `title_lat`) VALUES
(6428, 197, '', NULL, 'Autonomna Pokrajina Vojvodina'),
(6429, 4, 'Yevlakh City', NULL, 'Yevlakh City'),
(6430, 132, 'Opština Staro Nagoričane', NULL, 'Opština Staro Nagoričane'),
(6431, 76, 'North-Western Province', NULL, 'North-Western Province'),
(6432, 183, 'Castello di Fiorentino', NULL, 'Castello di Fiorentino'),
(6433, 253, 'Ямагата', NULL, 'Yamagata-ken'),
(6434, 4, 'Imishli Rayon', NULL, 'Imishli Rayon'),
(6435, 144, 'Raionul Călăraşi', NULL, 'Raionul Călăraşi'),
(6436, 177, 'Ханты-Мансийский Автономный округ - Югра Автономный округ', 'Ханты-Манси', 'Khanty-Mansiyskiy Avtonomnyy Okrug-Yugra'),
(6437, 73, 'Provincia María Trinidad Sánchez', NULL, 'Provincia María Trinidad Sánchez'),
(6438, 19, 'Sar-e Pul', NULL, 'Sar-e Pul'),
(6439, 143, 'Niassa Province', NULL, 'Niassa Province'),
(6440, 146, 'Bayan-Ölgiy Aymag', NULL, 'Bayan-Ölgiy Aymag'),
(6441, 209, 'Changwat Sakon Nakhon', NULL, 'Changwat Sakon Nakhon'),
(6442, 4, 'Khachmaz Rayon', NULL, 'Khachmaz Rayon'),
(6443, 300, 'Laxey', NULL, 'Laxey'),
(6444, 160, 'Province Sud', NULL, 'Province Sud'),
(6445, 118, 'Jelgavas Rajons', NULL, 'Jelgavas Rajons'),
(6446, 128, 'Amoron\'i Mania Region', NULL, 'Amoron\'i Mania Region'),
(6447, 166, 'State of Kayangel', NULL, 'State of Kayangel'),
(6448, 234, 'Sisačko-Moslavačka Županija', NULL, 'Sisačko-Moslavačka Županija'),
(6449, 188, 'Saipan Municipality', NULL, 'Saipan Municipality'),
(6450, 209, 'Changwat Ranong', NULL, 'Changwat Ranong'),
(6451, 201, 'Ribnica na Pohorju', NULL, 'Ribnica na Pohorju'),
(6452, 201, 'Brda', NULL, 'Brda'),
(6453, 10, 'Sandy Ground', NULL, 'Sandy Ground'),
(6454, 11, 'Província do Bié', NULL, 'Província do Bié'),
(6455, 118, 'Kandavas Novads', NULL, 'Kandavas Novads'),
(6456, 173, 'Distrito do Porto', NULL, 'Distrito do Porto'),
(6457, 166, 'State of Angaur', NULL, 'State of Angaur'),
(6458, 122, 'Surt', NULL, 'Surt'),
(6459, 48, 'Díli', NULL, 'Díli'),
(6460, 209, 'Changwat Phra Nakhon Si Ayutthaya', NULL, 'Changwat Phra Nakhon Si Ayutthaya'),
(6461, 104, 'Providencia y Santa Catalina, Departamento de Archipiélago de San Andrés', NULL, 'Providencia y Santa Catalina, Departamento de Archipiélago de San Andrés'),
(6462, 49, 'Tỉnh Hải Dương', NULL, 'Tỉnh Hải Dương'),
(6463, 153, 'Maradi', NULL, 'Maradi'),
(6464, 49, 'Tỉnh Hưng Yên', NULL, 'Tỉnh Hưng Yên'),
(6465, 132, 'Vasilevo', NULL, 'Vasilevo'),
(6466, 12, 'Ордино', NULL, 'Ordino'),
(6467, 209, 'Changwat Phetchabun', NULL, 'Changwat Phetchabun'),
(6468, 137, 'Tarxien', NULL, 'Tarxien'),
(6469, 10, 'The Farrington', NULL, 'The Farrington'),
(6470, 209, 'Changwat Pathum Thani', NULL, 'Changwat Pathum Thani'),
(6471, 201, 'Preddvor', NULL, 'Preddvor'),
(6472, 29, 'Oblast Smolyan', NULL, 'Oblast Smolyan'),
(6473, 201, 'Cerklje na Gorenjskem', NULL, 'Cerklje na Gorenjskem'),
(6474, 72, 'Saint Mark', NULL, 'Saint Mark'),
(6475, 132, 'Demir Kapija', NULL, 'Demir Kapija'),
(6476, 111, 'Zanzan', NULL, 'Zanzan'),
(6477, 12, 'Канильо', NULL, 'Canillo'),
(6478, 81, 'Provinsi Papua', NULL, 'Provinsi Papua'),
(6479, 161, 'Rogaland Fylke', NULL, 'Rogaland Fylke'),
(6480, 253, 'Симане', NULL, 'Shimane-ken'),
(6481, 132, 'Češinovo-Obleševo', NULL, 'Češinovo-Obleševo'),
(6482, 102, 'Guangdong Sheng', NULL, 'Guangdong Sheng'),
(6483, 107, 'Hwanghae-bukto', NULL, 'Hwanghae-bukto'),
(6484, 118, 'Garkalne Municipality', NULL, 'Garkalne Municipality'),
(6485, 81, 'Provinsi Lampung', NULL, 'Provinsi Lampung'),
(6486, 219, 'Gouvernorat de Jendouba', NULL, 'Gouvernorat de Jendouba'),
(6487, 87, 'Канарские Острова', NULL, 'Canary Islands'),
(6488, 148, 'Magway Region', NULL, 'Magway Region'),
(6489, 256, 'Haa Dzongkhag', NULL, 'Haa Dzongkhag'),
(6490, 86, 'Vestfirðir', NULL, 'Vestfirðir'),
(6491, 106, 'Commune de Brazzaville', NULL, 'Commune de Brazzaville'),
(6492, 126, 'Agalega Islands', NULL, 'Agalega Islands'),
(6493, 236, 'Logone Occidental Region', NULL, 'Logone Occidental Region'),
(6494, 122, 'Sha‘bīyat Nālūt', NULL, 'Sha‘bīyat Nālūt'),
(6495, 245, 'Maekel Region', NULL, 'Maekel Region'),
(6496, 4, 'Tartar Rayon', NULL, 'Tartar Rayon'),
(6497, 305, 'Бейкер', NULL, 'Baker Island'),
(6498, 201, 'Cirkulane', NULL, 'Cirkulane'),
(6499, 201, 'Kostanjevica na Krki', NULL, 'Kostanjevica na Krki'),
(6500, 126, 'Plaines Wilhems District', NULL, 'Plaines Wilhems District'),
(6501, 19, 'Badakhshān', NULL, 'Badakhshān'),
(6502, 174, 'Yabucoa Municipio', NULL, 'Yabucoa Municipio'),
(6503, 6, 'Qarku i Gjirokastrës', NULL, 'Qarku i Gjirokastrës'),
(6504, 118, 'Baltinavas Novads', NULL, 'Baltinavas Novads'),
(6505, 62, 'Kwai Tsing', NULL, 'Kwai Tsing'),
(6506, 6, 'Qarku i Lezhës', NULL, 'Qarku i Lezhës'),
(6507, 217, 'Tobago', NULL, 'Tobago'),
(6508, 167, 'Provincia de Coclé', NULL, 'Provincia de Coclé'),
(6509, 143, 'Gaza Province', NULL, 'Gaza Province'),
(6510, 65, 'South Aegean', NULL, 'South Aegean'),
(6511, 62, 'Сатхинь', NULL, 'Sha Tin'),
(6512, 107, 'P’yŏngan-namdo', NULL, 'P’yŏngan-namdo'),
(6513, 137, 'Il-Gudja', NULL, 'Il-Gudja'),
(6514, 98, 'Wajir', NULL, 'Wajir'),
(6515, 45, 'Estado Anzoátegui', NULL, 'Estado Anzoátegui'),
(6516, 16, 'Misiones Province', NULL, 'Misiones Province'),
(6517, 211, 'Arusha Region', NULL, 'Arusha Region'),
(6518, 132, 'Tetovo', NULL, 'Tetovo'),
(6519, 63, 'Saint Andrew', NULL, 'Saint Andrew'),
(6520, 154, 'Nasarawa State', NULL, 'Nasarawa State'),
(6521, 240, 'Kanton Appenzell Innerrhoden', NULL, 'Kanton Appenzell Innerrhoden'),
(6522, 132, 'Veles', NULL, 'Veles'),
(6523, 200, 'Kosice', NULL, 'Kosice'),
(6524, 104, 'Departamento del Guaviare', NULL, 'Departamento del Guaviare'),
(6525, 239, 'Antofagasta', NULL, 'Antofagasta'),
(6526, 88, 'Regione Autonoma Valle d\'Aosta', NULL, 'Regione Autonoma Valle d\'Aosta'),
(6527, 52, 'Potaro-Siparuni Region', NULL, 'Potaro-Siparuni Region'),
(6528, 201, 'Občina Sodražica', NULL, 'Občina Sodražica'),
(6529, 161, 'Akershus fylke', NULL, 'Akershus fylke'),
(6530, 53, 'Banjul', NULL, 'Banjul'),
(6531, 201, 'Cerknica', NULL, 'Cerknica'),
(6532, 81, 'Provinsi Kalimantan Barat', NULL, 'Provinsi Kalimantan Barat'),
(6533, 44, 'Pest megye', NULL, 'Pest megye'),
(6534, 262, 'Kapoeta', NULL, 'Kapoeta'),
(6535, 73, 'Provincia de Monte Plata', NULL, 'Provincia de Monte Plata'),
(6536, 226, 'Departamento de Canelones', NULL, 'Departamento de Canelones'),
(6537, 128, 'Diana Region', NULL, 'Diana Region'),
(6538, 122, 'Sha‘bīyat Wādī al Ḩayāt', NULL, 'Sha‘bīyat Wādī al Ḩayāt'),
(6539, 132, 'Аэродром', NULL, 'Aerodrom'),
(6540, 249, 'Gyeongsangnam-do', NULL, 'Gyeongsangnam-do'),
(6541, 194, 'Parish of Saint Patrick', NULL, 'Parish of Saint Patrick'),
(6542, 193, 'Saint Thomas Lowland', NULL, 'Saint Thomas Lowland'),
(6543, 98, 'Baringo', NULL, 'Baringo'),
(6544, 7, 'Wilaya de Tébessa', NULL, 'Wilaya de Tébessa'),
(6545, 201, 'Občina Žalec', NULL, 'Občina Žalec'),
(6546, 74, 'Muḩāfaz̧at Aswān', NULL, 'Muḩāfaz̧at Aswān'),
(6547, 121, 'Mohafazat Nabatîyé', NULL, 'Mohafazat Nabatîyé'),
(6548, 107, 'Ryanggang', NULL, 'Ryanggang'),
(6549, 300, 'Castletown', NULL, 'Castletown'),
(6550, 29, 'Pazardzhik', NULL, 'Pazardzhik'),
(6551, 157, 'Departamento de Granada', NULL, 'Departamento de Granada'),
(6552, 56, 'Departamento de Jalapa', NULL, 'Departamento de Jalapa'),
(6553, 54, 'Central Region', NULL, 'Central Region'),
(6554, 250, 'Province of Northern Cape', NULL, 'Province of Northern Cape'),
(6555, 144, 'Теленештский район', NULL, 'Teleneşti'),
(6556, 253, 'Яманаси', NULL, 'Yamanashi'),
(6557, 209, 'Changwat Chanthaburi', NULL, 'Changwat Chanthaburi'),
(6558, 16, 'Entre Ríos Province', NULL, 'Entre Ríos Province'),
(6559, 93, 'Pailin', NULL, 'Pailin'),
(6560, 11, 'Província do Uíge', NULL, 'Província do Uíge'),
(6561, 202, 'Rennell and Bellona', NULL, 'Rennell and Bellona'),
(6562, 218, 'Nukulaelae', NULL, 'Nukulaelae'),
(6563, 136, 'Gaafu Alifu Atholhu', NULL, 'Gaafu Alifu Atholhu'),
(6564, 132, 'Ohrid', NULL, 'Ohrid'),
(6565, 49, 'Tỉnh Ninh Thuận', NULL, 'Tỉnh Ninh Thuận'),
(6566, 56, 'Departamento de Escuintla', NULL, 'Departamento de Escuintla'),
(6567, 20, 'San Salvador District', NULL, 'San Salvador District'),
(6568, 4, 'Aghstafa Rayon', NULL, 'Aghstafa Rayon'),
(6569, 84, 'Qom', NULL, 'Qom'),
(6570, 125, 'Capellen', NULL, 'Capellen'),
(6571, 34, 'Ceará', NULL, 'Ceará'),
(6572, 190, 'Anse aux Pins', NULL, 'Anse aux Pins'),
(6573, 217, 'Couva-Tabaquite-Talparo', NULL, 'Couva-Tabaquite-Talparo'),
(6574, 141, 'Estado de San Luis Potosí', NULL, 'Estado de San Luis Potosí'),
(6575, 174, 'Arroyo', NULL, 'Arroyo'),
(6576, 104, 'Departamento del Huila', NULL, 'Departamento del Huila'),
(6577, 174, 'Maunabo Municipio', NULL, 'Maunabo Municipio'),
(6578, 174, 'Barranquitas', NULL, 'Barranquitas'),
(6579, 243, 'Provincia de Galápagos', NULL, 'Provincia de Galápagos'),
(6580, 113, 'La Habana', NULL, 'La Habana'),
(6581, 4, 'Sumqayit City', NULL, 'Sumqayit City'),
(6582, 262, 'Wau', NULL, 'Wau'),
(6583, 70, 'Province du Nord-Kivu', NULL, 'Province du Nord-Kivu'),
(6584, 132, 'Opština Sopište', NULL, 'Opština Sopište'),
(6585, 104, 'Departamento del Cesar', NULL, 'Departamento del Cesar'),
(6586, 217, 'San Juan/Laventille', NULL, 'San Juan/Laventille'),
(6587, 226, 'Departamento de Montevideo', NULL, 'Departamento de Montevideo'),
(6588, 16, 'Provincia de Buenos Aires', NULL, 'Provincia de Buenos Aires'),
(6589, 53, 'North Bank', NULL, 'North Bank'),
(6590, 253, 'Hiroshima-ken', NULL, 'Hiroshima-ken'),
(6591, 156, 'Дренте', NULL, 'Provincie Drenthe'),
(6592, 177, 'Санкт-Петербург Город', NULL, 'Sankt-Peterburg'),
(6593, 177, 'Севастополь Город', NULL, 'Gorod Sevastopol'),
(6594, 219, 'Gafsa Governorate', NULL, 'Gafsa Governorate'),
(6595, 137, 'Tal-Pietà', NULL, 'Tal-Pietà'),
(6596, 70, 'Kinshasa City', NULL, 'Kinshasa City'),
(6597, 31, '', NULL, 'Bonaire'),
(6598, 93, 'Свайриенг', NULL, 'Svay Rieng'),
(6599, 187, 'Shiselweni District', NULL, 'Shiselweni District'),
(6600, 118, 'Елгава', NULL, 'Jelgava'),
(6601, 228, 'Центральный Лусон', NULL, 'Central Luzon'),
(6602, 21, 'Sylhet Division', NULL, 'Sylhet Division'),
(6603, 201, 'Miren-Kostanjevica', NULL, 'Miren-Kostanjevica'),
(6604, 64, 'Кекката', NULL, 'Qeqqata'),
(6605, 29, 'Burgas', NULL, 'Burgas'),
(6606, 122, 'Al Marj', NULL, 'Al Marj'),
(6607, 241, 'Stockholm', NULL, 'Stockholm'),
(6608, 10, 'West End', NULL, 'West End'),
(6609, 241, 'Блекинге', NULL, 'Blekinge'),
(6610, 10, 'South Hill', NULL, 'South Hill'),
(6611, 132, 'Novo Selo', NULL, 'Novo Selo'),
(6612, 120, 'Grand Bassa County', NULL, 'Grand Bassa County'),
(6613, 202, 'Guadalcanal Province', NULL, 'Guadalcanal Province'),
(6614, 233, 'Îles Marquises', NULL, 'Îles Marquises'),
(6615, 89, 'Muḩāfaz̧at ‘Amrān', NULL, 'Muḩāfaz̧at ‘Amrān'),
(6616, 300, 'Ramsey', NULL, 'Ramsey'),
(6617, 166, 'State of Ngardmau', NULL, 'State of Ngardmau'),
(6618, 169, 'Departamento del Alto Paraná', NULL, 'Departamento del Alto Paraná'),
(6619, 21, 'Mymensingh Division', NULL, 'Mymensingh Division'),
(6620, 84, 'Ostān-e Īlām', NULL, 'Ostān-e Īlām'),
(6621, 117, 'Louangnamtha', NULL, 'Louangnamtha'),
(6622, 174, 'Dorado Municipio', NULL, 'Dorado Municipio'),
(6623, 168, 'West Sepik Province', NULL, 'West Sepik Province'),
(6624, 223, 'Fergana', NULL, 'Fergana'),
(6625, 12, 'Эскальдес-Энгордань', NULL, 'Escaldes-Engordany'),
(6626, 4, 'Aghjabadi Rayon', NULL, 'Aghjabadi Rayon'),
(6627, 236, 'Ouaddai Region', NULL, 'Ouaddai Region'),
(6628, 304, 'Archipel des Crozet', NULL, 'Archipel des Crozet'),
(6629, 137, 'Il-Belt Valletta', NULL, 'Il-Belt Valletta'),
(6630, 118, 'Skrīveru Novads', NULL, 'Skrīveru Novads'),
(6631, 204, 'Sennar', NULL, 'Sennar'),
(6632, 231, 'Occitanie', NULL, 'Occitanie'),
(6633, 136, 'Baa Atholhu', NULL, 'Baa Atholhu'),
(6634, 245, 'Debub Region', NULL, 'Debub Region'),
(6635, 161, 'Oslo County', NULL, 'Oslo County'),
(6636, 174, 'Hatillo Municipio', NULL, 'Hatillo Municipio'),
(6637, 61, 'Departamento de Yoro', NULL, 'Departamento de Yoro'),
(6638, 125, 'Redange', NULL, 'Redange'),
(6639, 4, 'Khojavend District', NULL, 'Khojavend District'),
(6640, 253, 'Saga-ken', NULL, 'Saga-ken'),
(6641, 201, 'Dobrepolje', NULL, 'Dobrepolje'),
(6642, 209, 'Changwat Udon Thani', NULL, 'Changwat Udon Thani'),
(6643, 209, 'Changwat Loei', NULL, 'Changwat Loei'),
(6644, 256, 'Wangdue Phodrang Dzongkhag', NULL, 'Wangdue Phodrang Dzongkhag'),
(6645, 98, 'Kisumu', NULL, 'Kisumu'),
(6646, 174, 'Vieques Municipality', NULL, 'Vieques Municipality'),
(6647, 62, 'Wong Tai Sin', NULL, 'Wong Tai Sin'),
(6648, 209, 'Changwat Khon Kaen', NULL, 'Changwat Khon Kaen'),
(6649, 141, 'Estado de Sinaloa', NULL, 'Estado de Sinaloa'),
(6650, 201, 'Občina Zreče', NULL, 'Občina Zreče'),
(6651, 201, 'Мислиня', NULL, 'Mislinja'),
(6652, 4, 'Şuşa Rayonu', NULL, 'Şuşa Rayonu'),
(6653, 201, 'Jesenice', NULL, 'Jesenice'),
(6654, 106, 'Sangha', NULL, 'Sangha'),
(6655, 132, 'Егуновце', NULL, 'Jegunovce'),
(6656, 118, 'Beverīnas Novads', NULL, 'Beverīnas Novads'),
(6657, 20, 'City of Freeport District', NULL, 'City of Freeport District'),
(6658, 4, 'Jalilabad', NULL, 'Jalilabad'),
(6659, 117, 'Salavan', NULL, 'Salavan'),
(6660, 229, 'Остроботния', NULL, 'Pohjanmaa'),
(6661, 174, 'Carolina Municipio', NULL, 'Carolina Municipio'),
(6662, 94, 'Centre Region', NULL, 'Centre Region'),
(6663, 89, 'Sanaa', NULL, 'Sanaa'),
(6664, 169, 'Departamento del Guairá', NULL, 'Departamento del Guairá'),
(6665, 52, 'Upper Demerara-Berbice Region', NULL, 'Upper Demerara-Berbice Region'),
(6666, 137, 'L-Imġarr', NULL, 'L-Imġarr'),
(6667, 195, 'Anse-la-Raye', NULL, 'Anse-la-Raye'),
(6668, 262, 'Western Lakes', NULL, 'Western Lakes'),
(6669, 185, 'Al-Qassim Province', NULL, 'Al-Qassim Province'),
(6670, 81, 'Provinsi Kalimantan Tengah', NULL, 'Provinsi Kalimantan Tengah'),
(6671, 124, 'Triesenberg', NULL, 'Triesenberg'),
(6672, 132, 'Resen', NULL, 'Resen'),
(6673, 201, 'Превалье', NULL, 'Prevalje'),
(6674, 167, 'Provincia de Herrera', NULL, 'Provincia de Herrera'),
(6675, 49, 'Tỉnh Hà Tĩnh', NULL, 'Tỉnh Hà Tĩnh'),
(6676, 9, 'Manu\'a District', NULL, 'Manu\'a District'),
(6677, 124, 'Planken', NULL, 'Planken'),
(6678, 167, 'Provincia de Los Santos', NULL, 'Provincia de Los Santos'),
(6679, 49, 'Tỉnh Bắc Giang', NULL, 'Tỉnh Bắc Giang'),
(6680, 194, 'Parish of Saint George', NULL, 'Parish of Saint George'),
(6681, 181, 'Аига-и-ле-Таи', NULL, 'Aiga-i-le-Tai'),
(6682, 179, 'Dolj', NULL, 'Dolj'),
(6683, 49, 'Thành Phố Đà Nẵng', NULL, 'Thành Phố Đà Nẵng'),
(6684, 247, 'Afar Region', NULL, 'Afar Region'),
(6685, 194, 'Parish of Saint Andrew', NULL, 'Parish of Saint Andrew'),
(6686, 180, 'Departamento de San Salvador', NULL, 'Departamento de San Salvador'),
(6687, 201, 'Občina Tržič', NULL, 'Občina Tržič'),
(6688, 148, 'Bago Region', NULL, 'Bago Region'),
(6689, 159, 'Hawke\'s Bay', NULL, 'Hawke\'s Bay'),
(6690, 247, 'Oromiya Region', NULL, 'Oromiya Region'),
(6691, 137, 'Pembroke', NULL, 'Pembroke'),
(6692, 4, 'Dashkasan Rayon', NULL, 'Dashkasan Rayon'),
(6693, 190, 'Cascade', NULL, 'Cascade'),
(6694, 140, 'Kili Island', NULL, 'Kili Island'),
(6695, 218, 'Nui', NULL, 'Nui'),
(6696, 218, 'Nanumea', NULL, 'Nanumea'),
(6697, 127, 'Бракна', NULL, 'Brakna'),
(6698, 179, 'Constanța', NULL, 'Constanța'),
(6699, 83, 'Muhafazat Wasit', NULL, 'Muhafazat Wasit'),
(6700, 113, 'Artemisa', NULL, 'Artemisa'),
(6701, 49, 'Tỉnh Quảng Trị', NULL, 'Tỉnh Quảng Trị'),
(6702, 136, 'Gaafu Dhaalu Atholhu', NULL, 'Gaafu Dhaalu Atholhu'),
(6703, 262, 'Akobo', NULL, 'Akobo'),
(6704, 19, 'Farāh', NULL, 'Farāh'),
(6705, 175, '', NULL, 'Réunion'),
(6706, 201, 'Občina Gorišnica', NULL, 'Občina Gorišnica'),
(6707, 136, 'Faafu Atholhu', NULL, 'Faafu Atholhu'),
(6708, 201, 'Gornja Radgona', NULL, 'Gornja Radgona'),
(6709, 174, 'Aguadilla', NULL, 'Aguadilla'),
(6710, 132, 'Petrovec', NULL, 'Petrovec'),
(6711, 190, 'Les Mamelles', NULL, 'Les Mamelles'),
(6712, 137, 'Il-Birgu', NULL, 'Il-Birgu'),
(6713, 22, 'Saint John', NULL, 'Saint John'),
(6714, 152, 'Madhyamanchal', NULL, 'Madhyamanchal'),
(6715, 11, 'Cunene Province', NULL, 'Cunene Province'),
(6716, 221, 'Сакарья', NULL, 'Sakarya'),
(6717, 109, 'Gjakova District', NULL, 'Gjakova District'),
(6718, 137, 'Il-Fontana', NULL, 'Il-Fontana'),
(6719, 104, 'Departamento de Casanare', NULL, 'Departamento de Casanare'),
(6720, 262, 'Jubek', NULL, 'Jubek'),
(6721, 199, 'Мухафаза Латакия', NULL, 'Latakia Governorate'),
(6722, 201, 'Словенска-Бистрица', NULL, 'Slovenska Bistrica'),
(6723, 208, '', NULL, 'Dushanbe'),
(6724, 201, 'Slovenj Gradec', NULL, 'Slovenj Gradec'),
(6725, 228, 'Calabarzon', NULL, 'Calabarzon'),
(6726, 49, 'Gia Lai', NULL, 'Gia Lai'),
(6727, 74, 'Muḩāfaz̧at al Wādī al Jadīd', NULL, 'Muḩāfaz̧at al Wādī al Jadīd'),
(6728, 49, 'Tỉnh Phú Thọ', NULL, 'Tỉnh Phú Thọ'),
(6729, 34, 'Rio Grande do Sul', NULL, 'Rio Grande do Sul'),
(6730, 156, 'Зеландия', NULL, 'Provincie Zeeland'),
(6732, 209, 'Changwat Roi Et', NULL, 'Changwat Roi Et'),
(6733, 180, 'Departamento de La Libertad', NULL, 'Departamento de La Libertad'),
(6734, 228, 'Bicol', NULL, 'Bicol'),
(6735, 34, 'Roraima', NULL, 'Roraima'),
(6736, 29, 'Pernik', NULL, 'Pernik'),
(6737, 179, 'Hunedoara', NULL, 'Hunedoara'),
(6738, 48, 'Баукау', NULL, 'Baucau'),
(6739, 23, 'Northern Governorate', NULL, 'Northern Governorate'),
(6740, 19, 'Wilāyat-e Takhār', NULL, 'Wilāyat-e Takhār'),
(6741, 111, 'Lacs', NULL, 'Lacs'),
(6742, 88, 'Sardegna', NULL, 'Sardegna'),
(6743, 74, 'Muḩāfaz̧at al Minūfīyah', NULL, 'Muḩāfaz̧at al Minūfīyah'),
(6744, 27, 'Донга', NULL, 'Donga'),
(6745, 118, 'Babītes Novads', NULL, 'Babītes Novads'),
(6746, 86, 'Austurland', NULL, 'Austurland'),
(6747, 237, 'Херцег-Нови', NULL, 'Herceg Novi'),
(6748, 157, 'Departamento de Madriz', NULL, 'Departamento de Madriz'),
(6749, 137, 'Ix-Xagħra', NULL, 'Ix-Xagħra'),
(6750, 164, 'Al Batinah South Governorate', NULL, 'Al Batinah South Governorate'),
(6751, 201, 'Občina Veržej', NULL, 'Občina Veržej'),
(6752, 202, 'Central Province', NULL, 'Central Province'),
(6753, 159, 'Nelson', NULL, 'Nelson'),
(6754, 98, 'Laikipia', NULL, 'Laikipia'),
(6755, 132, 'Novaci', NULL, 'Novaci'),
(6756, 118, 'Рига', NULL, 'Rīga'),
(6757, 107, 'Hamnam', NULL, 'Hamnam'),
(6758, 147, 'Parish of Saint Georges', NULL, 'Parish of Saint Georges'),
(6759, 201, 'Логатец', NULL, 'Logatec'),
(6760, 126, 'Каргадос-Карахос', NULL, 'Cargados Carajos'),
(6761, 127, 'Тагант', NULL, 'Tagant'),
(6762, 86, 'Vesturland', NULL, 'Vesturland'),
(6763, 174, 'Vega Baja Municipio', NULL, 'Vega Baja Municipio'),
(6764, 118, 'Jaunjelgavas Novads', NULL, 'Jaunjelgavas Novads'),
(6765, 154, 'Bauchi State', NULL, 'Bauchi State'),
(6766, 110, 'Provincia de Guanacaste', NULL, 'Provincia de Guanacaste'),
(6767, 216, 'Острова Ниуас', NULL, 'Niuas'),
(6768, 211, 'Kilimanjaro Region', NULL, 'Kilimanjaro Region'),
(6769, 159, 'Taranaki', NULL, 'Taranaki'),
(6770, 106, 'Région du Kouilou', NULL, 'Région du Kouilou'),
(6771, 174, 'Hormigueros Municipio', NULL, 'Hormigueros Municipio'),
(6772, 81, 'Центральный Сулавеси', NULL, 'Sulawesi Tengah'),
(6773, 140, 'Likiep Atoll', NULL, 'Likiep Atoll'),
(6774, 302, 'Saint Lawrence', NULL, 'Saint Lawrence'),
(6775, 209, 'Changwat Maha Sarakham', NULL, 'Changwat Maha Sarakham'),
(6776, 161, 'Oppland fylke', NULL, 'Oppland fylke'),
(6777, 132, 'Opština Delčevo', NULL, 'Opština Delčevo'),
(6778, 140, 'Mili Atoll', NULL, 'Mili Atoll'),
(6779, 115, 'Rakahanga', NULL, 'Rakahanga'),
(6780, 152, 'Pāthekā', NULL, 'Pāthekā'),
(6781, 145, '', NULL, 'Commune de Monaco'),
(6782, 70, 'Haut-Lomani', NULL, 'Haut-Lomani'),
(6783, 67, 'Agat Municipality', NULL, 'Agat Municipality'),
(6784, 76, 'Western Province', NULL, 'Western Province'),
(6785, 65, 'Epirus', NULL, 'Epirus'),
(6786, 140, 'Ujae Atoll', NULL, 'Ujae Atoll'),
(6787, 236, 'Tibesti Region', NULL, 'Tibesti Region'),
(6788, 203, 'Gobolka Mudug', NULL, 'Gobolka Mudug'),
(6789, 157, 'Departamento de León', NULL, 'Departamento de León'),
(6790, 167, 'Provincia de Chiriquí', NULL, 'Provincia de Chiriquí'),
(6791, 67, 'Inarajan Municipality', NULL, 'Inarajan Municipality'),
(6792, 109, 'Prizren District', NULL, 'Prizren District'),
(6793, 102, 'Anhui Sheng', NULL, 'Anhui Sheng'),
(6794, 98, 'West Pokot', NULL, 'West Pokot'),
(6795, 169, 'Departamento de Ñeembucú', NULL, 'Departamento de Ñeembucú'),
(6796, 7, 'Wilaya de Ghardaïa', NULL, 'Wilaya de Ghardaïa'),
(6797, 44, 'Дьёр-Мошон-Шопрон', NULL, 'Győr-Moson-Sopron megye'),
(6798, 117, 'Vientiane Prefecture', NULL, 'Vientiane Prefecture'),
(6799, 201, 'Občina Žirovnica', NULL, 'Občina Žirovnica'),
(6800, 221, 'Коджаэли', NULL, 'Kocaeli'),
(6801, 52, 'Upper Takutu-Upper Essequibo Region', NULL, 'Upper Takutu-Upper Essequibo Region'),
(6802, 207, 'Southern Province', NULL, 'Southern Province'),
(6803, 201, 'Komenda', NULL, 'Komenda'),
(6804, 262, 'Northern Liech', NULL, 'Northern Liech'),
(6805, 74, 'Muḩāfaz̧at Būr Sa‘īd', NULL, 'Muḩāfaz̧at Būr Sa‘īd'),
(6806, 56, 'Departamento de Santa Rosa', NULL, 'Departamento de Santa Rosa'),
(6807, 211, 'Zanzibar North Region', NULL, 'Zanzibar North Region'),
(6808, 249, 'Incheon', NULL, 'Incheon'),
(6809, 221, 'Kars', NULL, 'Kars'),
(6810, 226, 'Departamento de Flores', NULL, 'Departamento de Flores'),
(6811, 120, 'Grand Kru County', NULL, 'Grand Kru County'),
(6812, 181, 'Va‘a-o-Fonoti', NULL, 'Va‘a-o-Fonoti'),
(6813, 120, 'Margibi County', NULL, 'Margibi County'),
(6814, 168, 'Southern Highlands Province', NULL, 'Southern Highlands Province'),
(6815, 120, 'River Cess County', NULL, 'River Cess County'),
(6816, 209, 'Changwat Tak', NULL, 'Changwat Tak'),
(6817, 102, 'Hainan Sheng', NULL, 'Hainan Sheng'),
(6818, 104, 'Departamento de Boyacá', NULL, 'Departamento de Boyacá'),
(6819, 7, 'Wilaya de Batna', NULL, 'Wilaya de Batna'),
(6820, 185, 'Minţaqat al Ḩudūd ash Shamālīyah', NULL, 'Minţaqat al Ḩudūd ash Shamālīyah'),
(6821, 132, 'Butel', NULL, 'Butel'),
(6822, 240, 'Kanton Zug', NULL, 'Kanton Zug'),
(6823, 300, 'Arbory', NULL, 'Arbory'),
(6824, 193, 'Saint Mary Cayon', NULL, 'Saint Mary Cayon'),
(6825, 180, 'Departamento de Santa Ana', NULL, 'Departamento de Santa Ana'),
(6826, 247, 'Benishangul-Gumuz Region', NULL, 'Benishangul-Gumuz Region'),
(6827, 300, 'Braddan', NULL, 'Braddan'),
(6828, 118, 'Grobiņas Novads', NULL, 'Grobiņas Novads'),
(6829, 201, 'Občina Šmarje pri Jelšah', NULL, 'Občina Šmarje pri Jelšah'),
(6830, 241, 'Östergötlands län', NULL, 'Östergötlands län'),
(6831, 140, 'Jabat Island', NULL, 'Jabat Island'),
(6832, 132, 'Opština Kičevo', NULL, 'Opština Kičevo'),
(6833, 136, 'Meemu Atholhu', NULL, 'Meemu Atholhu'),
(6834, 27, 'Zou Department', NULL, 'Zou Department'),
(6835, 249, 'Seoul', NULL, 'Seoul'),
(6836, 118, 'Pļaviņu Novads', NULL, 'Pļaviņu Novads'),
(6837, 39, 'Bujumbura Rural Province', NULL, 'Bujumbura Rural Province'),
(6838, 47, 'Saint Croix Island', NULL, 'Saint Croix Island'),
(6839, 121, 'Beyrouth', NULL, 'Beyrouth'),
(6840, 209, 'Changwat Chaiyaphum', NULL, 'Changwat Chaiyaphum'),
(6841, 76, 'Lusaka Province', NULL, 'Lusaka Province'),
(6842, 246, 'Хийумаа', NULL, 'Hiiumaa'),
(6843, 59, 'Saxony', NULL, 'Saxony'),
(6844, 132, 'Opština Lipkovo', NULL, 'Opština Lipkovo'),
(6845, 4, 'Aghdash Rayon', NULL, 'Aghdash Rayon'),
(6846, 22, 'Saint Michael', NULL, 'Saint Michael'),
(6847, 100, '', NULL, 'Osh City'),
(6848, 205, 'Distrikt Paramaribo', NULL, 'Distrikt Paramaribo'),
(6849, 49, 'Tỉnh Vĩnh Long', NULL, 'Tỉnh Vĩnh Long'),
(6850, 221, 'Afyonkarahisar', NULL, 'Afyonkarahisar'),
(6851, 98, 'Мандера', NULL, 'Mandera'),
(6852, 167, 'Ngöbe-Buglé', NULL, 'Ngöbe-Buglé'),
(6853, 262, 'Fashoda', NULL, 'Fashoda'),
(6854, 183, 'Castello di San Marino Città', NULL, 'Castello di San Marino Città'),
(6855, 44, 'Сабольч-Сатмар-Берег', NULL, 'Szabolcs-Szatmár-Bereg'),
(6856, 137, 'Bormla', NULL, 'Bormla'),
(6857, 123, 'Alytus County', NULL, 'Alytus County'),
(6858, 211, 'Mbeya Region', NULL, 'Mbeya Region'),
(6859, 123, 'Marijampolė County', NULL, 'Marijampolė County'),
(6860, 102, 'Henan Sheng', NULL, 'Henan Sheng'),
(6861, 243, 'Provincia de Esmeraldas', NULL, 'Provincia de Esmeraldas'),
(6862, 201, 'Gornji Petrovci', NULL, 'Gornji Petrovci'),
(6863, 144, 'Municipiul Chişinău', NULL, 'Municipiul Chişinău'),
(6864, 203, 'Gobolka Jubbada Dhexe', NULL, 'Gobolka Jubbada Dhexe'),
(6865, 61, 'Departamento de El Paraíso', NULL, 'Departamento de El Paraíso'),
(6866, 205, 'Distrikt Brokopondo', NULL, 'Distrikt Brokopondo'),
(6867, 20, 'Harbour Island District', NULL, 'Harbour Island District'),
(6868, 179, 'Judeţul Buzău', NULL, 'Judeţul Buzău'),
(6869, 118, 'Durbes Novads', NULL, 'Durbes Novads'),
(6870, 50, 'Эстуэр', NULL, 'Estuaire'),
(6871, 16, 'San Luis Province', NULL, 'San Luis Province'),
(6872, 44, 'Nógrád megye', NULL, 'Nógrád megye'),
(6873, 28, 'Sandys Parish', NULL, 'Sandys Parish'),
(6874, 219, 'Gouvernorat de Gabès', NULL, 'Gouvernorat de Gabès'),
(6875, 93, 'Stung Treng', NULL, 'Stung Treng'),
(6876, 4, 'Qazakh Rayon', NULL, 'Qazakh Rayon'),
(6877, 30, 'Departamento de Potosí', NULL, 'Departamento de Potosí'),
(6878, 48, 'Ликиса', NULL, 'Liquiçá'),
(6879, 49, 'Tỉnh Đồng Tháp', NULL, 'Tỉnh Đồng Tháp'),
(6880, 118, 'Jaunpils Novads', NULL, 'Jaunpils Novads'),
(6881, 137, 'Saint Lucia', NULL, 'Saint Lucia'),
(6882, 201, 'Hajdina', NULL, 'Hajdina'),
(6883, 73, 'Provincia de La Altagracia', NULL, 'Provincia de La Altagracia'),
(6884, 234, 'Zagreb County', NULL, 'Zagreb County'),
(6885, 239, 'Región de Arica y Parinacota', NULL, 'Región de Arica y Parinacota'),
(6886, 243, 'Provincia de Bolívar', NULL, 'Provincia de Bolívar'),
(6887, 96, 'Concelho do São Filipe', NULL, 'Concelho do São Filipe'),
(6888, 27, 'Collines Department', NULL, 'Collines Department'),
(6889, 300, 'Port St Mary', NULL, 'Port St Mary'),
(6890, 66, 'Кахетия', NULL, 'Kakheti'),
(6891, 27, 'Kouffo Department', NULL, 'Kouffo Department'),
(6892, 169, 'Departamento del Amambay', NULL, 'Departamento del Amambay'),
(6893, 66, 'Имеретия', NULL, 'Imereti'),
(6894, 137, 'Is-Swieqi', NULL, 'Is-Swieqi'),
(6895, 132, 'Opština Konče', NULL, 'Opština Konče'),
(6896, 237, 'Budva', NULL, 'Budva'),
(6897, 118, 'Iecavas Novads', NULL, 'Iecavas Novads'),
(6898, 81, 'Kepulauan Bangka Belitung', NULL, 'Kepulauan Bangka Belitung'),
(6899, 132, 'Зрновци', NULL, 'Zrnovci'),
(6900, 96, 'São Lourenço dos Órgãos', NULL, 'São Lourenço dos Órgãos'),
(6901, 66, 'Самцхе-Джавахети', NULL, 'Samtskhe-Javakheti'),
(6902, 56, 'Departamento de Baja Verapaz', NULL, 'Departamento de Baja Verapaz'),
(6903, 132, 'Карбинци', NULL, 'Karbinci'),
(6904, 253, 'Kyoto Prefecture', NULL, 'Kyoto Prefecture'),
(6905, 54, 'Greater Accra Region', NULL, 'Greater Accra Region'),
(6906, 222, 'Northern Region', NULL, 'Northern Region'),
(6907, 209, 'Changwat Lamphun', NULL, 'Changwat Lamphun'),
(6908, 209, 'Changwat Lampang', NULL, 'Changwat Lampang'),
(6909, 209, 'Changwat Nakhon Si Thammarat', NULL, 'Changwat Nakhon Si Thammarat'),
(6910, 242, 'Southern Province', NULL, 'Southern Province'),
(6911, 174, 'Toa Baja Municipio', NULL, 'Toa Baja Municipio'),
(6912, 84, 'Ostān-e Kordestān', NULL, 'Ostān-e Kordestān'),
(6913, 140, 'Bikar Atoll', NULL, 'Bikar Atoll'),
(6914, 231, 'Nouvelle-Aquitaine', NULL, 'Nouvelle-Aquitaine'),
(6915, 238, 'Kraj Vysočina', NULL, 'Kraj Vysočina'),
(6916, 132, 'Mogila', NULL, 'Mogila'),
(6917, 216, 'Ha‘apai', NULL, 'Ha‘apai'),
(6918, 104, 'Quindío Department', NULL, 'Quindío Department'),
(6919, 87, 'Область Валенсия', NULL, 'Comunitat Valenciana'),
(6920, 140, 'Enewetak Atoll', NULL, 'Enewetak Atoll'),
(6921, 119, 'Мокотлонг', NULL, 'Mokhotlong'),
(6922, 173, 'Azores', NULL, 'Azores'),
(6923, 102, 'Liaoning Sheng', NULL, 'Liaoning Sheng'),
(6924, 305, 'Атолл Уэйк', NULL, 'Wake Island'),
(6925, 140, 'Kwajalein Atoll', NULL, 'Kwajalein Atoll'),
(6926, 92, 'George Town', NULL, 'George Town'),
(6927, 125, 'Vianden', NULL, 'Vianden'),
(6928, 67, 'Umatac Municipality', NULL, 'Umatac Municipality'),
(6929, 221, 'Düzce', NULL, 'Düzce'),
(6930, 49, 'Tỉnh Cao Bằng', NULL, 'Tỉnh Cao Bằng'),
(6931, 115, 'Penrhyn', NULL, 'Penrhyn'),
(6932, 209, 'Bangkok', NULL, 'Bangkok'),
(6933, 65, 'Crete', NULL, 'Crete'),
(6934, 127, 'Горголь', NULL, 'Gorgol'),
(6935, 242, 'Eastern Province', NULL, 'Eastern Province'),
(6936, 302, 'Saint Helier', NULL, 'Saint Helier'),
(6937, 201, 'Občina Renče-Vogrsko', NULL, 'Občina Renče-Vogrsko'),
(6938, 19, 'Wilāyat-e Laghmān', NULL, 'Wilāyat-e Laghmān'),
(6939, 214, 'Центральная область', NULL, 'Centrale'),
(6940, 203, 'Gobolka Nugaal', NULL, 'Gobolka Nugaal'),
(6941, 201, 'Kamnik', NULL, 'Kamnik'),
(6942, 62, 'Тхюньмунь', NULL, 'Tuen Mun'),
(6943, 102, 'Jiangsu Sheng', NULL, 'Jiangsu Sheng'),
(6944, 141, 'Estado de Tlaxcala', NULL, 'Estado de Tlaxcala'),
(6945, 142, 'State of Pohnpei', NULL, 'State of Pohnpei'),
(6946, 141, 'Estado de Baja California Sur', NULL, 'Estado de Baja California Sur'),
(6947, 235, 'Préfecture de la Nana-Mambéré', NULL, 'Préfecture de la Nana-Mambéré'),
(6948, 141, 'Estado de Guanajuato', NULL, 'Estado de Guanajuato'),
(6949, 117, 'Khouèng Bokèo', NULL, 'Khouèng Bokèo'),
(6950, 201, 'Mestna Občina Ljubljana', NULL, 'Mestna Občina Ljubljana'),
(6951, 19, 'Wilāyat-e Zābul', NULL, 'Wilāyat-e Zābul'),
(6952, 246, 'Тартумаа', NULL, 'Tartumaa'),
(6953, 33, 'Ghanzi District', NULL, 'Ghanzi District'),
(6954, 16, 'Santiago del Estero Province', NULL, 'Santiago del Estero Province'),
(6955, 174, 'Peñuelas Municipio', NULL, 'Peñuelas Municipio'),
(6956, 81, 'Provinsi Papua Barat', NULL, 'Provinsi Papua Barat'),
(6957, 9, 'Swains Island', NULL, 'Swains Island'),
(6958, 71, 'Djibouti Region', NULL, 'Djibouti Region'),
(6959, 170, 'Departamento de Moquegua', NULL, 'Departamento de Moquegua'),
(6960, 161, 'Sør-Trøndelag Fylke', NULL, 'Sør-Trøndelag Fylke'),
(6961, 56, 'Departamento de Sacatepéquez', NULL, 'Departamento de Sacatepéquez'),
(6962, 209, 'Changwat Sing Buri', NULL, 'Changwat Sing Buri'),
(6963, 14, 'Parish of Saint John', NULL, 'Parish of Saint John'),
(6964, 128, 'Bongolava Region', NULL, 'Bongolava Region'),
(6965, 234, 'Istarska Županija', NULL, 'Istarska Županija'),
(6966, 247, 'Gambela Region', NULL, 'Gambela Region'),
(6967, 137, 'In-Nadur', NULL, 'In-Nadur'),
(6968, 208, 'Районы республиканского подчинения', NULL, 'Districts of Republican Subordination'),
(6969, 122, 'Sha‘bīyat Banghāzī', NULL, 'Sha‘bīyat Banghāzī'),
(6970, 19, 'Wilāyat-e Jowzjān', NULL, 'Wilāyat-e Jowzjān'),
(6971, 237, 'Opština Šavnik', NULL, 'Opština Šavnik'),
(6972, 201, 'Občina Loški Potok', NULL, 'Občina Loški Potok'),
(6973, 99, 'Eparchía Ammochóstou', NULL, 'Eparchía Ammochóstou'),
(6974, 178, 'Northern Province', NULL, 'Northern Province'),
(6975, 228, 'Soccsksargen', NULL, 'Soccsksargen'),
(6976, 201, 'Občina Velike Lašče', NULL, 'Občina Velike Lašče'),
(6977, 98, 'Elegeyo-Marakwet', NULL, 'Elegeyo-Marakwet'),
(6978, 201, 'Občina Šmarješke Toplice', NULL, 'Občina Šmarješke Toplice'),
(6979, 201, 'Sveti Jurij v Slovenskih Goricah', NULL, 'Sveti Jurij v Slovenskih Goricah'),
(6980, 98, 'Kilifi', NULL, 'Kilifi'),
(6981, 205, 'Distrikt Wanica', NULL, 'Distrikt Wanica'),
(6982, 73, 'Samaná Province', NULL, 'Samaná Province'),
(6983, 136, 'Lhaviyani Atholhu', NULL, 'Lhaviyani Atholhu'),
(6984, 132, 'Grad Skopje', NULL, 'Grad Skopje'),
(6985, 161, 'Vest-Agder Fylke', NULL, 'Vest-Agder Fylke'),
(6986, 223, 'Surxondaryo Viloyati', NULL, 'Surxondaryo Viloyati'),
(6987, 250, 'Province of North West', NULL, 'Province of North West'),
(6988, 202, 'Temotu Province', NULL, 'Temotu Province'),
(6989, 81, 'West Nusa Tenggara', NULL, 'West Nusa Tenggara'),
(6990, 106, 'Plateaux', NULL, 'Plateaux'),
(6991, 301, 'Ålands skärgård', NULL, 'Ålands skärgård'),
(6992, 70, 'Sankuru', NULL, 'Sankuru'),
(6993, 76, 'Luapula Province', NULL, 'Luapula Province'),
(6994, 74, 'Muḩāfaz̧at Qinā', NULL, 'Muḩāfaz̧at Qinā'),
(6995, 134, 'Terengganu', NULL, 'Terengganu'),
(6996, 117, 'Xaignabouli', NULL, 'Xaignabouli'),
(6997, 207, 'Western Area', NULL, 'Western Area'),
(6998, 88, 'Lombardia', NULL, 'Lombardia'),
(6999, 217, 'Diego Martin', NULL, 'Diego Martin'),
(7000, 190, 'Roche Caiman', NULL, 'Roche Caiman'),
(7001, 49, 'Tỉnh Bến Tre', NULL, 'Tỉnh Bến Tre'),
(7002, 118, 'Rūjienas Novads', NULL, 'Rūjienas Novads'),
(7003, 82, 'Al Karak', NULL, 'Al Karak'),
(7004, 93, 'Кандаль', NULL, 'Kandal'),
(7005, 174, 'Bayamón Municipio', NULL, 'Bayamón Municipio'),
(7006, 201, 'Kobilje', NULL, 'Kobilje'),
(7007, 174, 'Naguabo Municipio', NULL, 'Naguabo Municipio'),
(7008, 137, 'Ħal Għargħur', NULL, 'Ħal Għargħur'),
(7009, 4, 'Fizuli Rayon', NULL, 'Fizuli Rayon'),
(7010, 137, 'L-Għasri', NULL, 'L-Għasri'),
(7011, 177, 'Саха /Якутия/ Республика', 'Якутия', 'Respublika Sakha (Yakutiya)'),
(7012, 177, 'Москва Город', NULL, 'Moskva'),
(7013, 28, 'Smith’s Parish', NULL, 'Smith’s Parish'),
(7014, 49, 'Tỉnh Thái Nguyên', NULL, 'Tỉnh Thái Nguyên'),
(7015, 137, 'Il-Qala', NULL, 'Il-Qala'),
(7016, 84, 'Ostān-e Lorestān', NULL, 'Ostān-e Lorestān'),
(7017, 31, 'Saba', NULL, 'Saba'),
(7018, 49, 'Tỉnh Hà Giang', NULL, 'Tỉnh Hà Giang'),
(7019, 28, 'Saint George’s Parish', NULL, 'Saint George’s Parish'),
(7020, 118, 'Skrundas Novads', NULL, 'Skrundas Novads'),
(7021, 201, 'Občina Sežana', NULL, 'Občina Sežana'),
(7022, 11, 'Luanda Province', NULL, 'Luanda Province'),
(7023, 48, 'Манатуту', NULL, 'Manatuto'),
(7024, 201, 'Lovrenc na Pohorju', NULL, 'Lovrenc na Pohorju'),
(7025, 157, 'Departamento de Río San Juan', NULL, 'Departamento de Río San Juan'),
(7026, 228, 'Davao', NULL, 'Davao'),
(7027, 49, 'Tỉnh Thừa Thiên-Huế', NULL, 'Tỉnh Thừa Thiên-Huế'),
(7028, 191, 'Région de Kaffrine', NULL, 'Région de Kaffrine'),
(7029, 165, 'Gilgit-Baltistan', NULL, 'Gilgit-Baltistan'),
(7030, 209, 'Changwat Phrae', NULL, 'Changwat Phrae'),
(7031, 74, 'Cairo Governorate', NULL, 'Cairo Governorate'),
(7032, 180, 'Departamento de Cuscatlán', NULL, 'Departamento de Cuscatlán'),
(7033, 137, 'Sannat', NULL, 'Sannat'),
(7034, 154, 'Oyo State', NULL, 'Oyo State'),
(7035, 73, 'Provincia de Independencia', NULL, 'Provincia de Independencia'),
(7036, 166, 'State of Ngeremlengui', NULL, 'State of Ngeremlengui'),
(7037, 118, 'Olaines Novads', NULL, 'Olaines Novads'),
(7038, 51, 'Département de l\'Ouest', NULL, 'Département de l\'Ouest'),
(7039, 199, 'Мухафаза Хама', NULL, 'Hama Governorate'),
(7040, 169, 'Asunción', NULL, 'Asunción'),
(7041, 236, 'Chari-Baguirmi Region', NULL, 'Chari-Baguirmi Region'),
(7042, 84, 'East Azerbaijan', NULL, 'East Azerbaijan'),
(7043, 49, 'Tỉnh Đắk Lắk', NULL, 'Tỉnh Đắk Lắk'),
(7044, 52, 'Essequibo Islands-West Demerara Region', NULL, 'Essequibo Islands-West Demerara Region'),
(7045, 132, 'Градско', NULL, 'Gradsko'),
(7046, 243, 'Provincia de Morona-Santiago', NULL, 'Provincia de Morona-Santiago'),
(7047, 93, 'Prey Veng', NULL, 'Prey Veng'),
(7048, 52, 'East Berbice-Corentyne Region', NULL, 'East Berbice-Corentyne Region'),
(7049, 66, 'Абхазия', NULL, 'Abkhazia'),
(7050, 201, 'Dobrovnik', NULL, 'Dobrovnik'),
(7051, 204, 'Kassala State', NULL, 'Kassala State'),
(7052, 168, 'Hela', NULL, 'Hela'),
(7053, 127, 'Тирис-Земмур', NULL, 'Tiris Zemmour'),
(7054, 83, 'Muḩāfaz̧at Kirkūk', NULL, 'Muḩāfaz̧at Kirkūk'),
(7055, 98, 'Siaya', NULL, 'Siaya'),
(7056, 201, 'Mozirje', NULL, 'Mozirje'),
(7057, 147, 'Parish of Saint Peter', NULL, 'Parish of Saint Peter'),
(7058, 174, 'Trujillo Alto Municipio', NULL, 'Trujillo Alto Municipio'),
(7059, 201, 'Metlika', NULL, 'Metlika'),
(7060, 173, 'Гуарда', NULL, 'Distrito da Guarda'),
(7061, 201, 'Hodos', NULL, 'Hodos'),
(7062, 243, 'Provincia de Loja', NULL, 'Provincia de Loja'),
(7063, 86, 'Suðurnes', NULL, 'Suðurnes'),
(7064, 102, 'Gansu Sheng', NULL, 'Gansu Sheng'),
(7065, 221, 'Zonguldak', NULL, 'Zonguldak'),
(7066, 209, 'Changwat Ang Thong', NULL, 'Changwat Ang Thong'),
(7067, 151, 'Ярен', NULL, 'Yaren'),
(7068, 174, 'Guaynabo Municipio', NULL, 'Guaynabo Municipio'),
(7069, 209, 'Changwat Kamphaeng Phet', NULL, 'Changwat Kamphaeng Phet'),
(7070, 67, 'Sinajana Municipality', NULL, 'Sinajana Municipality'),
(7071, 200, 'Nitra', NULL, 'Nitra'),
(7072, 154, 'Delta State', NULL, 'Delta State'),
(7073, 141, 'Чиуауа', NULL, 'Estado de Chihuahua'),
(7074, 179, 'Judeţul Neamţ', NULL, 'Judeţul Neamţ'),
(7075, 122, 'Murzuq', NULL, 'Murzuq'),
(7076, 61, 'Departamento de Colón', NULL, 'Departamento de Colón'),
(7077, 256, 'Trashigang Dzongkhag', NULL, 'Trashigang Dzongkhag'),
(7078, 302, 'Saint Peter', NULL, 'Saint Peter'),
(7079, 209, 'Changwat Lop Buri', NULL, 'Changwat Lop Buri'),
(7080, 125, 'Mersch', NULL, 'Mersch'),
(7081, 20, 'Spanish Wells District', NULL, 'Spanish Wells District'),
(7082, 227, '', NULL, 'Rotuma'),
(7083, 98, 'Kisii', NULL, 'Kisii'),
(7084, 209, 'Changwat Nong Bua Lamphu', NULL, 'Changwat Nong Bua Lamphu'),
(7085, 159, 'Марлборо', NULL, 'Marlborough'),
(7086, 73, 'Provincia de Santiago Rodríguez', NULL, 'Provincia de Santiago Rodríguez'),
(7087, 236, 'Barh el Gazel', NULL, 'Barh el Gazel'),
(7088, 159, 'Бей-оф-Пленти', NULL, 'Bay of Plenty'),
(7089, 102, 'Tibet Autonomous Region', NULL, 'Tibet Autonomous Region'),
(7090, 211, 'Morogoro Region', NULL, 'Morogoro Region'),
(7091, 6, 'Qarku i Vlorës', NULL, 'Qarku i Vlorës'),
(7092, 226, 'Departamento de Treinta y Tres', NULL, 'Departamento de Treinta y Tres'),
(7093, 117, 'Houaphan', NULL, 'Houaphan'),
(7094, 201, 'Odranci', NULL, 'Odranci'),
(7095, 63, 'Saint Patrick', NULL, 'Saint Patrick'),
(7096, 174, 'Canóvanas Municipio', NULL, 'Canóvanas Municipio'),
(7097, 82, 'Ajloun', NULL, 'Ajloun'),
(7098, 251, 'Manchester', NULL, 'Manchester'),
(7099, 242, 'Province of Uva', NULL, 'Province of Uva'),
(7100, 201, 'Občina Solčava', NULL, 'Občina Solčava'),
(7101, 211, 'Kagera Region', NULL, 'Kagera Region'),
(7102, 154, 'Kogi State', NULL, 'Kogi State'),
(7103, 229, '', NULL, 'Etelä-Karjala'),
(7104, 49, 'Tỉnh Phú Yên', NULL, 'Tỉnh Phú Yên'),
(7105, 154, 'Taraba State', NULL, 'Taraba State'),
(7106, 181, 'Ваисигано', NULL, 'Vaisigano'),
(7107, 45, 'Estado Guárico', NULL, 'Estado Guárico'),
(7108, 7, 'Wilaya de Bejaïa', NULL, 'Wilaya de Bejaïa'),
(7109, 203, 'Gobolka Gedo', NULL, 'Gobolka Gedo'),
(7110, 114, 'Muḩāfaz̧at al Farwānīyah', NULL, 'Muḩāfaz̧at al Farwānīyah'),
(7111, 19, 'Wardak', NULL, 'Wardak'),
(7112, 201, 'Občina Ajdovščina', NULL, 'Občina Ajdovščina'),
(7113, 128, 'Sava Region', NULL, 'Sava Region'),
(7114, 124, 'Ruggell', NULL, 'Ruggell'),
(7115, 137, 'Il-Marsa', NULL, 'Il-Marsa'),
(7116, 128, 'Betsiboka Region', NULL, 'Betsiboka Region'),
(7117, 255, 'Suðuroyar sýsla', NULL, 'Suðuroyar sýsla'),
(7118, 185, 'Minţaqat Tabūk', NULL, 'Minţaqat Tabūk'),
(7119, 51, 'Grandans', NULL, 'Grandans'),
(7120, 201, 'Občina Divača', NULL, 'Občina Divača'),
(7121, 99, 'Eparchía Lemesoú', NULL, 'Eparchía Lemesoú'),
(7122, 4, 'Qabala Rayon', NULL, 'Qabala Rayon'),
(7123, 148, 'Ayeyawady Region', NULL, 'Ayeyawady Region'),
(7124, 201, 'Osilnica', NULL, 'Osilnica'),
(7125, 201, 'Velenje', NULL, 'Velenje'),
(7126, 236, 'Ennedi-Est', NULL, 'Ennedi-Est'),
(7127, 163, 'Abū Z̧aby', NULL, 'Abū Z̧aby'),
(7128, 4, 'Saatly Rayon', NULL, 'Saatly Rayon'),
(7129, 107, 'P’yŏngan-bukto', NULL, 'P’yŏngan-bukto'),
(7130, 303, 'Saint Sampson', NULL, 'Saint Sampson'),
(7131, 80, 'State of Odisha', NULL, 'State of Odisha'),
(7132, 65, 'Western Macedonia', NULL, 'Western Macedonia'),
(7133, 32, 'Округ Брчко', NULL, 'Brčko'),
(7134, 157, 'Departamento de Estelí', NULL, 'Departamento de Estelí'),
(7135, 118, 'Ikšķiles Novads', NULL, 'Ikšķiles Novads'),
(7136, 4, 'Baku City', NULL, 'Baku City'),
(7137, 250, 'Western Cape', NULL, 'Western Cape'),
(7138, 80, 'Чандигарх', NULL, 'Union Territory of Chandigarh'),
(7139, 218, 'Nanumanga', NULL, 'Nanumanga'),
(7140, 16, 'Chubut Province', NULL, 'Chubut Province'),
(7141, 190, 'Beau Vallon', NULL, 'Beau Vallon'),
(7142, 240, 'Kanton Graubünden', NULL, 'Kanton Graubünden'),
(7143, 84, 'Ostān-e Khorāsān-e Shomālī', NULL, 'Ostān-e Khorāsān-e Shomālī'),
(7144, 98, 'Kericho', NULL, 'Kericho'),
(7145, 19, 'Daykundi Province', NULL, 'Daykundi Province'),
(7146, 73, 'Provincia de La Vega', NULL, 'Provincia de La Vega'),
(7147, 134, 'Melaka', NULL, 'Melaka'),
(7148, 118, 'Лиепая', NULL, 'Liepāja'),
(7149, 58, 'Tombali', NULL, 'Tombali'),
(7150, 132, 'Prilep', NULL, 'Prilep'),
(7151, 137, 'Бальцан', NULL, 'Balzan'),
(7152, 4, 'Shaki Rayon', NULL, 'Shaki Rayon'),
(7153, 132, 'Opština Radoviš', NULL, 'Opština Radoviš'),
(7154, 113, 'Municipio Especial Isla de la Juventud', NULL, 'Municipio Especial Isla de la Juventud'),
(7155, 7, 'Wilaya de Saïda', NULL, 'Wilaya de Saïda'),
(7156, 201, 'Radenci', NULL, 'Radenci'),
(7157, 4, 'Shamkir Rayon', NULL, 'Shamkir Rayon'),
(7158, 244, 'Provincia de Centro Sur', NULL, 'Provincia de Centro Sur'),
(7159, 61, 'Departamento de Atlántida', NULL, 'Departamento de Atlántida'),
(7160, 183, 'Castello di Faetano', NULL, 'Castello di Faetano'),
(7161, 154, 'Niger State', NULL, 'Niger State'),
(7162, 132, 'Struga', NULL, 'Struga'),
(7163, 226, 'Departamento de Lavalleja', NULL, 'Departamento de Lavalleja'),
(7164, 177, 'Чувашская Республика - Чувашия', 'Чувашия', 'Chuvashskaya Respublika'),
(7165, 177, 'Байконур Город', NULL, 'Baikonur'),
(7166, 201, 'Ig', NULL, 'Ig'),
(7167, 4, 'Qusar Rayon', NULL, 'Qusar Rayon'),
(7168, 21, 'Rājshāhi Division', NULL, 'Rājshāhi Division'),
(7169, 180, 'Departamento de Morazán', NULL, 'Departamento de Morazán'),
(7170, 21, 'Khulna Division', NULL, 'Khulna Division'),
(7171, 118, 'Brocēnu Novads', NULL, 'Brocēnu Novads'),
(7172, 118, 'Rojas Novads', NULL, 'Rojas Novads'),
(7173, 144, 'Unitatea Teritorială din Stînga Nistrului', NULL, 'Unitatea Teritorială din Stînga Nistrului'),
(7174, 201, 'Cerkno', NULL, 'Cerkno'),
(7175, 26, 'Wallonia', NULL, 'Wallonia'),
(7176, 84, 'Yazd', NULL, 'Yazd'),
(7177, 118, 'Ozolnieku Novads', NULL, 'Ozolnieku Novads'),
(7178, 201, 'Občina Kobarid', NULL, 'Občina Kobarid'),
(7179, 96, 'Concelho de São Miguel', NULL, 'Concelho de São Miguel'),
(7180, 201, 'Destrnik', NULL, 'Destrnik'),
(7181, 241, 'Jönköpings län', NULL, 'Jönköpings län'),
(7182, 89, 'Muḩāfaz̧at Laḩij', NULL, 'Muḩāfaz̧at Laḩij'),
(7183, 237, 'Биело-Поле', NULL, 'Bijelo Polje'),
(7184, 149, 'Hardap', NULL, 'Hardap'),
(7185, 96, 'Concelho de Ribeira Grande de Santiago', NULL, 'Concelho de Ribeira Grande de Santiago'),
(7186, 137, 'Ix-Xewkija', NULL, 'Ix-Xewkija'),
(7187, 168, 'Madang Province', NULL, 'Madang Province'),
(7188, 84, 'Ostān-e Āz̄arbāyjān-e Gharbī', NULL, 'Ostān-e Āz̄arbāyjān-e Gharbī'),
(7189, 107, 'Kangwŏn-do', NULL, 'Kangwŏn-do'),
(7190, 56, 'Departamento de Alta Verapaz', NULL, 'Departamento de Alta Verapaz'),
(7191, 74, 'Muḩāfaz̧at al Uqşur', NULL, 'Muḩāfaz̧at al Uqşur'),
(7192, 240, 'Kanton Schwyz', NULL, 'Kanton Schwyz'),
(7193, 201, 'Velika Polana', NULL, 'Velika Polana'),
(7194, 204, 'Central Darfur State', NULL, 'Central Darfur State'),
(7195, 201, 'Лендава', NULL, 'Lendava'),
(7196, 203, 'Gobolka Awdal', NULL, 'Gobolka Awdal'),
(7197, 61, 'Departamento de La Paz', NULL, 'Departamento de La Paz'),
(7198, 201, 'Dol pri Ljubljani', NULL, 'Dol pri Ljubljani'),
(7199, 174, 'Fajardo Municipio', NULL, 'Fajardo Municipio'),
(7200, 70, 'Чуапа', NULL, 'Tshuapa'),
(7201, 79, 'Хайфский округ', NULL, 'Haifa'),
(7202, 20, 'East Grand Bahama District', NULL, 'East Grand Bahama District'),
(7203, 163, 'Ash Shāriqah', NULL, 'Ash Shāriqah'),
(7204, 201, 'Občina Ravne na Koroškem', NULL, 'Občina Ravne na Koroškem'),
(7205, 44, 'Tolna megye', NULL, 'Tolna megye'),
(7206, 76, 'Muchinga Province', NULL, 'Muchinga Province'),
(7207, 19, 'Velāyat-e Nūrestān', NULL, 'Velāyat-e Nūrestān'),
(7208, 140, 'Ebon Atoll', NULL, 'Ebon Atoll'),
(7209, 20, 'Moore’s Island District', NULL, 'Moore’s Island District'),
(7210, 201, 'Vuzenica', NULL, 'Vuzenica'),
(7211, 7, 'Wilaya de Aïn Defla', NULL, 'Wilaya de Aïn Defla'),
(7212, 20, 'North Eleuthera District', NULL, 'North Eleuthera District'),
(7213, 305, 'Навасса', NULL, 'Navassa Island'),
(7214, 143, 'Maputo Province', NULL, 'Maputo Province'),
(7215, 201, 'Lukovica', NULL, 'Lukovica'),
(7216, 126, 'Savanne District', NULL, 'Savanne District'),
(7217, 238, 'Среднечешский край', NULL, 'Středočeský kraj'),
(7218, 250, 'Free State', NULL, 'Free State'),
(7219, 129, 'Dembeni', NULL, 'Dembeni'),
(7220, 140, 'Majuro Atoll', NULL, 'Majuro Atoll'),
(7221, 7, 'Wilaya de Skikda', NULL, 'Wilaya de Skikda'),
(7222, 81, 'Daerah Istimewa Yogyakarta', NULL, 'Daerah Istimewa Yogyakarta'),
(7223, 201, 'Občina Ivančna Gorica', NULL, 'Občina Ivančna Gorica'),
(7224, 115, 'Pukapuka', NULL, 'Pukapuka'),
(7225, 67, 'Chalan Pago-Ordot Municipality', NULL, 'Chalan Pago-Ordot Municipality'),
(7226, 242, 'Western Province', NULL, 'Western Province'),
(7227, 7, 'Wilaya de M’Sila', NULL, 'Wilaya de M’Sila'),
(7228, 201, 'Občina Ruše', NULL, 'Občina Ruše'),
(7229, 4, 'Sabirabad Rayon', NULL, 'Sabirabad Rayon'),
(7230, 229, 'Южная Остроботния', NULL, 'Etelä-Pohjanmaa'),
(7231, 253, 'Nara-ken', NULL, 'Nara-ken'),
(7232, 117, 'Bolikhamxai', NULL, 'Bolikhamxai'),
(7233, 113, 'Provincia de Santiago de Cuba', NULL, 'Provincia de Santiago de Cuba'),
(7234, 91, 'Kostanayskaya Oblast’', NULL, 'Qostanay Oblysy'),
(7235, 174, 'Cayey Municipio', NULL, 'Cayey Municipio'),
(7236, 102, 'Xinjiang Uygur Zizhiqu', NULL, 'Xinjiang Uygur Zizhiqu'),
(7237, 262, 'Fangak', NULL, 'Fangak'),
(7238, 73, 'Provincia Duarte', NULL, 'Provincia Duarte'),
(7239, 16, 'Santa Cruz Province', NULL, 'Santa Cruz Province'),
(7240, 122, 'Sha‘bīyat al Jafārah', NULL, 'Sha‘bīyat al Jafārah'),
(7241, 49, 'An Giang', NULL, 'An Giang'),
(7242, 168, 'East Sepik Province', NULL, 'East Sepik Province'),
(7243, 81, 'Provinsi Sulawesi Barat', NULL, 'Provinsi Sulawesi Barat'),
(7244, 93, 'Поусат', NULL, 'Pursat'),
(7245, 142, 'State of Chuuk', NULL, 'State of Chuuk'),
(7246, 168, 'Chimbu Province', NULL, 'Chimbu Province'),
(7247, 249, 'Sejong-si', NULL, 'Sejong-si'),
(7248, 247, 'Ādīs Ābeba Āstedader', NULL, 'Ādīs Ābeba Āstedader'),
(7249, 99, 'Eparchía Páfou', NULL, 'Eparchía Páfou'),
(7250, 127, 'Nouakchott Sud', NULL, 'Nouakchott Sud'),
(7251, 83, 'Muhafazat Salah ad Din', NULL, 'Muhafazat Salah ad Din'),
(7252, 124, 'Эшен', NULL, 'Eschen'),
(7253, 255, 'Sandoyar Sýsla', NULL, 'Sandoyar Sýsla'),
(7254, 300, 'Bride', NULL, 'Bride'),
(7255, 201, 'Pivka', NULL, 'Pivka'),
(7256, 300, 'Douglas', NULL, 'Douglas'),
(7257, 255, 'Norðoyar sýsla', NULL, 'Norðoyar sýsla'),
(7258, 122, 'Sha‘bīyat al Jabal al Akhḑar', NULL, 'Sha‘bīyat al Jabal al Akhḑar'),
(7259, 85, 'Манстер', NULL, 'Munster'),
(7260, 19, 'Wilāyat-e Paktīkā', NULL, 'Wilāyat-e Paktīkā'),
(7261, 102, 'Hunan Sheng', NULL, 'Hunan Sheng'),
(7262, 118, 'Lubānas Novads', NULL, 'Lubānas Novads'),
(7263, 140, 'Jemo Island', NULL, 'Jemo Island'),
(7264, 199, 'Мухафаза Эль-Кунейтра', NULL, 'Quneitra Governorate'),
(7265, 61, 'Departamento de Choluteca', NULL, 'Departamento de Choluteca'),
(7266, 119, 'Leribe District', NULL, 'Leribe District'),
(7267, 138, 'Guelmim-Oued Noun', NULL, 'Guelmim-Oued Noun'),
(7268, 190, 'Bel Ombre', NULL, 'Bel Ombre'),
(7269, 218, 'Funafuti', NULL, 'Funafuti'),
(7270, 141, 'Estado de Tamaulipas', NULL, 'Estado de Tamaulipas'),
(7271, 136, 'Haa Dhaalu Atholhu', NULL, 'Haa Dhaalu Atholhu'),
(7272, 93, 'Kratie', NULL, 'Kratie'),
(7273, 136, 'Haa Alifu Atholhu', NULL, 'Haa Alifu Atholhu'),
(7274, 167, 'Provincia de Bocas del Toro', NULL, 'Provincia de Bocas del Toro'),
(7275, 132, 'Negotino', NULL, 'Negotino'),
(7276, 201, 'Kozje', NULL, 'Kozje'),
(7277, 174, 'Aguada', NULL, 'Aguada'),
(7278, 117, 'Xiangkhouang', NULL, 'Xiangkhouang'),
(7279, 168, 'Bougainville', NULL, 'Bougainville'),
(7280, 51, 'Sud', NULL, 'Sud'),
(7281, 132, 'Валандово', NULL, 'Valandovo'),
(7282, 148, 'Kayah State', NULL, 'Kayah State'),
(7283, 7, 'Wilaya de Blida', NULL, 'Wilaya de Blida'),
(7284, 88, 'Лацио', NULL, 'Lazio'),
(7285, 20, 'Berry Islands District', NULL, 'Berry Islands District'),
(7286, 153, 'Dosso Region', NULL, 'Dosso Region'),
(7287, 89, 'Al Bayda Governorate', NULL, 'Al Bayda Governorate'),
(7288, 137, 'L-Għarb', NULL, 'L-Għarb'),
(7289, 134, 'Pulau Pinang', NULL, 'Pulau Pinang'),
(7290, 118, 'Raunas Novads', NULL, 'Raunas Novads'),
(7291, 118, 'Strenču Novads', NULL, 'Strenču Novads'),
(7292, 132, 'Strumica', NULL, 'Strumica'),
(7293, 143, 'Cidade de Maputo', NULL, 'Cidade de Maputo'),
(7294, 300, 'German', NULL, 'German'),
(7295, 88, 'Больцано', NULL, 'Trentino-Alto Adige'),
(7296, 118, 'Vaiņodes Novads', NULL, 'Vaiņodes Novads'),
(7297, 93, 'Siem Reap', NULL, 'Siem Reap'),
(7298, 187, 'Hhohho District', NULL, 'Hhohho District'),
(7299, 160, 'Province Nord', NULL, 'Province Nord'),
(7300, 83, 'Diyālá', NULL, 'Diyālá'),
(7301, 234, 'Splitsko-Dalmatinska Županija', NULL, 'Splitsko-Dalmatinska Županija'),
(7302, 209, 'Changwat Prachuap Khiri Khan', NULL, 'Changwat Prachuap Khiri Khan'),
(7303, 98, 'Kakamega', NULL, 'Kakamega'),
(7304, 202, 'Western Province', NULL, 'Western Province'),
(7305, 243, 'Provincia del Cañar', NULL, 'Provincia del Cañar'),
(7306, 7, 'Annaba', NULL, 'Annaba'),
(7307, 137, 'Saint Paul’s Bay', NULL, 'Saint Paul’s Bay'),
(7308, 48, 'Bobonaro', NULL, 'Bobonaro'),
(7309, 23, 'Southern Governorate', NULL, 'Southern Governorate'),
(7310, 29, 'Oblast Kardzhali', NULL, 'Oblast Kardzhali'),
(7311, 111, 'Montagnes', NULL, 'Montagnes'),
(7312, 132, 'Богданци', NULL, 'Bogdanci'),
(7313, 157, 'Departamento de Masaya', NULL, 'Departamento de Masaya'),
(7314, 81, 'Provinsi Jawa Tengah', NULL, 'Provinsi Jawa Tengah'),
(7315, 203, 'Gobolka Bari', NULL, 'Gobolka Bari'),
(7316, 185, 'Jizan Region', NULL, 'Jizan Region'),
(7317, 59, 'Free and Hanseatic City of Hamburg', NULL, 'Free and Hanseatic City of Hamburg'),
(7318, 203, 'Gobolka Banaadir', NULL, 'Gobolka Banaadir'),
(7319, 149, 'Erongo', NULL, 'Erongo'),
(7320, 141, 'Ciudad de México', NULL, 'Ciudad de México'),
(7321, 223, 'Xorazm Viloyati', NULL, 'Xorazm Viloyati'),
(7322, 164, 'Muḩāfaz̧at al Wusţá', NULL, 'Muḩāfaz̧at al Wusţá'),
(7323, 236, 'Salamat Region', NULL, 'Salamat Region'),
(7324, 201, 'Medvode', NULL, 'Medvode'),
(7325, 173, 'Distrito de Leiria', NULL, 'Distrito de Leiria'),
(7326, 137, 'Iż-Żejtun', NULL, 'Iż-Żejtun'),
(7327, 7, 'Wilaya de Chlef', NULL, 'Wilaya de Chlef'),
(7328, 154, 'Ondo State', NULL, 'Ondo State'),
(7329, 83, 'An Najaf', NULL, 'An Najaf'),
(7330, 118, 'Lielvārdes Novads', NULL, 'Lielvārdes Novads'),
(7331, 140, 'Bikini Atoll', NULL, 'Bikini Atoll'),
(7332, 58, 'Biombo', NULL, 'Biombo'),
(7333, 67, 'Santa Rita Municipality', NULL, 'Santa Rita Municipality'),
(7334, 94, 'North-West Region', NULL, 'North-West Region'),
(7335, 159, 'Tasman', NULL, 'Tasman'),
(7336, 129, 'Bouéni', NULL, 'Bouéni'),
(7337, 236, 'Lac Region', NULL, 'Lac Region'),
(7338, 174, 'Humacao Municipio', NULL, 'Humacao Municipio'),
(7339, 92, 'Sister Island', NULL, 'Sister Island'),
(7340, 154, 'Jigawa State', NULL, 'Jigawa State'),
(7341, 256, 'Thimphu Dzongkhag', NULL, 'Thimphu Dzongkhag'),
(7342, 118, 'Ogres novads', NULL, 'Ogres novads'),
(7343, 102, 'Jilin Sheng', NULL, 'Jilin Sheng'),
(7344, 151, 'Boe District', NULL, 'Boe District'),
(7345, 236, 'Région de la Ville de N’Djaména', NULL, 'Région de la Ville de N’Djaména'),
(7346, 156, 'Флеволанд', NULL, 'Provincie Flevoland'),
(7347, 70, 'Province du Maniema', NULL, 'Province du Maniema'),
(7348, 118, 'Ludzas Rajons', NULL, 'Ludzas Rajons'),
(7349, 201, 'Изола', NULL, 'Izola'),
(7350, 140, 'Wotho Atoll', NULL, 'Wotho Atoll'),
(7351, 83, 'Muḩāfaz̧at al Anbār', NULL, 'Muḩāfaz̧at al Anbār'),
(7352, 174, 'Caguas Municipio', NULL, 'Caguas Municipio'),
(7353, 24, 'Витебская Oбласть', NULL, 'Vitebsk Oblast'),
(7354, 45, 'Estado Amazonas', NULL, 'Estado Amazonas'),
(7355, 169, 'Departamento de Itapúa', NULL, 'Departamento de Itapúa'),
(7356, 118, 'Balvu Novads', NULL, 'Balvu Novads'),
(7357, 11, 'Lunda Norte Province', NULL, 'Lunda Norte Province'),
(7358, 58, 'Quinara', NULL, 'Quinara'),
(7359, 240, 'Kanton Aargau', NULL, 'Kanton Aargau'),
(7360, 154, 'Osun State', NULL, 'Osun State');
INSERT INTO `region` (`region_id`, `country_id`, `title`, `title_short`, `title_lat`) VALUES
(7361, 105, 'Mohéli', NULL, 'Mohéli'),
(7362, 154, 'Rivers State', NULL, 'Rivers State'),
(7363, 154, 'Yobe State', NULL, 'Yobe State'),
(7364, 96, 'Concelho da Ribeira Grande', NULL, 'Concelho da Ribeira Grande'),
(7365, 168, 'Eastern Highlands Province', NULL, 'Eastern Highlands Province'),
(7366, 199, '', NULL, 'Damascus Governorate'),
(7367, 79, 'Jerusalem', NULL, 'Jerusalem'),
(7368, 27, 'Borgou Department', NULL, 'Borgou Department'),
(7369, 209, 'Changwat Sukhothai', NULL, 'Changwat Sukhothai'),
(7370, 208, 'Viloyati Mukhtori Kŭhistoni Badakhshon', NULL, 'Viloyati Mukhtori Kŭhistoni Badakhshon'),
(7371, 104, 'Departamento de Bolívar', NULL, 'Departamento de Bolívar'),
(7372, 79, 'Tel Aviv District', NULL, 'Tel Aviv District'),
(7373, 127, 'Nouakchott Ouest', NULL, 'Nouakchott Ouest'),
(7374, 118, 'Alsungas Novads', NULL, 'Alsungas Novads'),
(7375, 174, 'Ciales Municipio', NULL, 'Ciales Municipio'),
(7376, 201, 'Hrpelje-Kozina', NULL, 'Hrpelje-Kozina'),
(7377, 143, 'Тете', NULL, 'Tete'),
(7378, 208, 'Viloyati Khatlon', NULL, 'Viloyati Khatlon'),
(7379, 98, 'Uasin Gishu', NULL, 'Uasin Gishu'),
(7380, 56, 'Departamento de Quetzaltenango', NULL, 'Departamento de Quetzaltenango'),
(7381, 154, 'Akwa Ibom State', NULL, 'Akwa Ibom State'),
(7382, 228, 'Кордильерский административный регион', NULL, 'Cordillera Administrative Region'),
(7383, 76, 'Southern Province', NULL, 'Southern Province'),
(7384, 65, 'Western Greece', NULL, 'Western Greece'),
(7385, 190, 'Grand Anse Praslin', NULL, 'Grand Anse Praslin'),
(7386, 250, 'Гаутенг', NULL, 'Gauteng'),
(7387, 56, 'Departamento de Jutiapa', NULL, 'Departamento de Jutiapa'),
(7388, 136, 'Gnaviyani Atholhu', NULL, 'Gnaviyani Atholhu'),
(7389, 29, 'Oblast Dobrich', NULL, 'Oblast Dobrich'),
(7390, 173, 'Distrito de Faro', NULL, 'Distrito de Faro'),
(7391, 190, 'Glacis', NULL, 'Glacis'),
(7392, 144, 'Тараклийский район', NULL, 'Taraclia'),
(7393, 140, 'Lib Island', NULL, 'Lib Island'),
(7394, 174, 'Las Piedras Municipio', NULL, 'Las Piedras Municipio'),
(7395, 244, 'Provincia de Annobón', NULL, 'Provincia de Annobón'),
(7396, 47, 'Saint John Island', NULL, 'Saint John Island'),
(7397, 83, 'Muḩāfaz̧at Karbalā’', NULL, 'Muḩāfaz̧at Karbalā’'),
(7398, 201, 'Občina Rogaška Slatina', NULL, 'Občina Rogaška Slatina'),
(7399, 237, 'Bar', NULL, 'Bar'),
(7400, 61, 'Departamento de Francisco Morazán', NULL, 'Departamento de Francisco Morazán'),
(7401, 135, 'Kidal Region', NULL, 'Kidal Region'),
(7402, 84, 'Isfahan', NULL, 'Isfahan'),
(7403, 21, 'Rangpur Division', NULL, 'Rangpur Division'),
(7404, 118, 'Limbažu Rajons', NULL, 'Limbažu Rajons'),
(7405, 236, 'Guera Region', NULL, 'Guera Region'),
(7406, 102, 'Qinghai Sheng', NULL, 'Qinghai Sheng'),
(7407, 235, 'Préfecture de la Sangha-Mbaéré', NULL, 'Préfecture de la Sangha-Mbaéré'),
(7408, 262, 'Gogrial', NULL, 'Gogrial'),
(7409, 4, 'Ujar Rayon', NULL, 'Ujar Rayon'),
(7410, 205, 'Distrikt Nickerie', NULL, 'Distrikt Nickerie'),
(7411, 201, 'Občina Črnomelj', NULL, 'Občina Črnomelj'),
(7412, 4, 'Tovuz Rayon', NULL, 'Tovuz Rayon'),
(7413, 249, 'Gangwon-do', NULL, 'Gangwon-do'),
(7414, 190, 'Au Cap', NULL, 'Au Cap'),
(7415, 84, 'Ostān-e Semnān', NULL, 'Ostān-e Semnān'),
(7416, 93, 'Кохконг', NULL, 'Koh Kong'),
(7417, 118, 'Burtnieku Novads', NULL, 'Burtnieku Novads'),
(7418, 240, 'Canton de Berne', NULL, 'Canton de Berne'),
(7419, 25, 'Belize District', NULL, 'Belize District'),
(7420, 7, 'Wilaya de Médéa', NULL, 'Wilaya de Médéa'),
(7421, 41, 'Shefa Province', NULL, 'Shefa Province'),
(7422, 11, 'Bengo Province', NULL, 'Bengo Province'),
(7423, 253, 'Toyama-ken', NULL, 'Toyama-ken'),
(7424, 118, 'Amatas Novads', NULL, 'Amatas Novads'),
(7425, 123, 'Šiauliai County', NULL, 'Šiauliai County'),
(7426, 132, 'Opština Štip', NULL, 'Opština Štip'),
(7427, 170, 'Callao', NULL, 'Callao'),
(7428, 113, 'Las Tunas', NULL, 'Las Tunas'),
(7429, 123, 'Utena County', NULL, 'Utena County'),
(7430, 262, 'Maridi', NULL, 'Maridi'),
(7431, 53, 'Central River', NULL, 'Central River'),
(7432, 247, 'Tigray Region', NULL, 'Tigray Region'),
(7433, 146, 'Ömnögovi Province', NULL, 'Ömnögovi Province'),
(7434, 117, 'Khouèng Phôngsali', NULL, 'Khouèng Phôngsali'),
(7435, 174, 'Río Grande Municipio', NULL, 'Río Grande Municipio'),
(7436, 201, 'Oplotnica', NULL, 'Oplotnica'),
(7437, 57, 'Kankan Region', NULL, 'Kankan Region'),
(7438, 174, 'Sabana Grande Municipio', NULL, 'Sabana Grande Municipio'),
(7439, 211, 'Rukwa Region', NULL, 'Rukwa Region'),
(7440, 98, 'Nakuru', NULL, 'Nakuru'),
(7441, 83, 'Muḩāfaz̧at Dhī Qār', NULL, 'Muḩāfaz̧at Dhī Qār'),
(7442, 191, 'Région de Thiès', NULL, 'Région de Thiès'),
(7443, 137, 'Safi', NULL, 'Safi'),
(7444, 7, 'Oum el Bouaghi', NULL, 'Oum el Bouaghi'),
(7445, 209, 'Phuket Province', NULL, 'Phuket Province'),
(7446, 209, 'Changwat Rayong', NULL, 'Changwat Rayong'),
(7447, 49, 'Tỉnh Bắc Kạn', NULL, 'Tỉnh Bắc Kạn'),
(7448, 48, 'Кова-Лима', NULL, 'Cova Lima'),
(7449, 96, 'Concelho dos Mosteiros', NULL, 'Concelho dos Mosteiros'),
(7450, 104, 'Departamento de Antioquia', NULL, 'Departamento de Antioquia'),
(7451, 239, 'Región de Los Ríos', NULL, 'Región de Los Ríos'),
(7452, 52, 'Cuyuni-Mazaruni Region', NULL, 'Cuyuni-Mazaruni Region'),
(7453, 111, 'Gôh-Djiboua', NULL, 'Gôh-Djiboua'),
(7454, 209, 'Changwat Phitsanulok', NULL, 'Changwat Phitsanulok'),
(7455, 199, 'Мухафаза Дейр-эз-Зор', NULL, 'Deir ez-Zor Governorate'),
(7456, 211, 'Pemba North Region', NULL, 'Pemba North Region'),
(7457, 119, 'Таба-Цека', NULL, 'Thaba-Tseka'),
(7458, 221, 'Хатай', NULL, 'Hatay'),
(7459, 29, 'Oblast Silistra', NULL, 'Oblast Silistra'),
(7460, 256, 'Mongar Dzongkhag', NULL, 'Mongar Dzongkhag'),
(7461, 118, 'Ķekavas Novads', NULL, 'Ķekavas Novads'),
(7462, 52, 'Barima-Waini Region', NULL, 'Barima-Waini Region'),
(7463, 111, 'Woroba', NULL, 'Woroba'),
(7464, 30, 'Departamento de Oruro', NULL, 'Departamento de Oruro'),
(7465, 201, 'Piran', NULL, 'Piran'),
(7466, 111, 'Abidjan', NULL, 'Abidjan'),
(7467, 81, 'Daerah Khusus Ibukota Jakarta', NULL, 'Daerah Khusus Ibukota Jakarta'),
(7468, 137, 'Ħaż-Żebbuġ', NULL, 'Ħaż-Żebbuġ'),
(7469, 51, 'Département du Nord-Est', NULL, 'Département du Nord-Est'),
(7470, 29, 'Oblast Yambol', NULL, 'Oblast Yambol'),
(7471, 27, 'Atakora Department', NULL, 'Atakora Department'),
(7472, 74, 'Muḩāfaz̧at ad Daqahlīyah', NULL, 'Muḩāfaz̧at ad Daqahlīyah'),
(7473, 74, 'Muḩāfaz̧at al Qalyūbīyah', NULL, 'Muḩāfaz̧at al Qalyūbīyah'),
(7474, 174, 'Toa Alta Municipio', NULL, 'Toa Alta Municipio'),
(7475, 98, 'Tharaka - Nithi', NULL, 'Tharaka - Nithi'),
(7476, 62, 'Yuen Long District', NULL, 'Yuen Long District'),
(7477, 256, 'Sarpang Dzongkhag', NULL, 'Sarpang Dzongkhag'),
(7478, 204, 'Al Jazirah State', NULL, 'Al Jazirah State'),
(7479, 201, 'Ljutomer', NULL, 'Ljutomer'),
(7480, 111, 'Savanes', NULL, 'Savanes'),
(7481, 111, 'Vallée du Bandama', NULL, 'Vallée du Bandama'),
(7482, 179, 'Олт', NULL, 'Olt'),
(7483, 4, 'Goychay Rayon', NULL, 'Goychay Rayon'),
(7484, 231, 'Hauts-de-France', NULL, 'Hauts-de-France'),
(7485, 44, 'Ваш', NULL, 'Vas megye'),
(7486, 140, 'Erikub Atoll', NULL, 'Erikub Atoll'),
(7487, 102, 'Shandong Sheng', NULL, 'Shandong Sheng'),
(7488, 82, 'Muḩāfaz̧at Ma‘ān', NULL, 'Muḩāfaz̧at Ma‘ān'),
(7489, 136, 'Southern Ari Atoll', NULL, 'Southern Ari Atoll'),
(7490, 154, 'Kaduna State', NULL, 'Kaduna State'),
(7491, 302, 'Grouville', NULL, 'Grouville'),
(7492, 92, 'Bodden Town', NULL, 'Bodden Town'),
(7493, 305, 'Jarvis Island', NULL, 'Jarvis Island'),
(7494, 65, 'Attica', NULL, 'Attica'),
(7495, 190, 'Saint Louis', NULL, 'Saint Louis'),
(7496, 6, 'Qarku i Beratit', NULL, 'Qarku i Beratit'),
(7497, 199, 'Мухафаза Тартус', NULL, 'Tartus Governorate'),
(7498, 4, 'Beylagan Rayon', NULL, 'Beylagan Rayon'),
(7499, 121, 'Mohafazat Mont-Liban', NULL, 'Mohafazat Mont-Liban'),
(7500, 110, 'Provincia de Alajuela', NULL, 'Provincia de Alajuela'),
(7501, 6, 'Qarku i Korçës', NULL, 'Qarku i Korçës'),
(7502, 168, 'Western Highlands Province', NULL, 'Western Highlands Province'),
(7503, 303, 'Saint Peter Port', NULL, 'Saint Peter Port'),
(7504, 243, 'Provincia de Imbabura', NULL, 'Provincia de Imbabura'),
(7505, 243, 'Provincia de Sucumbíos', NULL, 'Provincia de Sucumbíos'),
(7506, 89, 'Muḩāfaz̧at Dhamār', NULL, 'Muḩāfaz̧at Dhamār'),
(7507, 120, 'Bong County', NULL, 'Bong County'),
(7508, 120, 'Sinoe County', NULL, 'Sinoe County');

--
-- Дамп данных таблицы `region_timezone`
--

INSERT INTO `region_timezone` (`country_id`, `region_title`, `timezone`) VALUES
(177, 'Алтайский край', 'Asia/Omsk'),
(177, 'Амурская область', 'Asia/Yakutsk'),
(177, 'Астраханская область', 'Europe/Volgograd'),
(177, 'Волгоградская область', 'Europe/Volgograd'),
(177, 'Еврейская автономная область', 'Asia/Vladivostok'),
(177, 'Забайкальский край', 'Asia/Yakutsk'),
(177, 'Иркутская область', 'Asia/Irkutsk'),
(177, 'Калининградская область', 'Europe/Kaliningrad'),
(177, 'Камчатский край', 'Asia/Kamchatka'),
(177, 'Кемеровская область', 'Asia/Novokuznetsk'),
(177, 'Кировская область', 'Europe/Volgograd'),
(177, 'Красноярский край', 'Asia/Krasnoyarsk'),
(177, 'Курганская область', 'Asia/Yekaterinburg'),
(177, 'Магаданская область', 'Asia/Magadan'),
(177, 'Новосибирская область', 'Asia/Novosibirsk'),
(177, 'Омская область', 'Asia/Omsk'),
(177, 'Оренбургская область', 'Asia/Yekaterinburg'),
(177, 'Пермский край', 'Asia/Yekaterinburg'),
(177, 'Приморский край', 'Asia/Vladivostok'),
(177, 'Республика Алтай', 'Asia/Omsk'),
(177, 'Республика Башкортостан', 'Asia/Yekaterinburg'),
(177, 'Республика Бурятия', 'Asia/Irkutsk'),
(177, 'Республика Крым', 'Europe/Simferopol'),
(177, 'Республика Саха (Якутия)', 'Asia/Vladivostok'),
(177, 'Республика Тыва', 'Asia/Krasnoyarsk'),
(177, 'Республика Хакасия', 'Asia/Krasnoyarsk'),
(177, 'Самарская область', 'Europe/Samara'),
(177, 'Саратовская область', 'Europe/Volgograd'),
(177, 'Сахалинская область', 'Asia/Sakhalin'),
(177, 'Свердловская область', 'Asia/Yekaterinburg'),
(177, 'Томская область', 'Asia/Novosibirsk'),
(177, 'Тюменская область', 'Asia/Yekaterinburg'),
(177, 'Удмуртская Республика', 'Europe/Samara'),
(177, 'Хабаровский край', 'Asia/Vladivostok'),
(177, 'ХМАО', 'Asia/Yekaterinburg'),
(177, 'Челябинская область', 'Asia/Yekaterinburg'),
(177, 'Чукотский автономный округ', 'Asia/Anadyr'),
(177, 'Якутская область', 'Asia/Yakutsk'),
(177, 'ЯНАО', 'Asia/Yekaterinburg');

--
-- Дамп данных таблицы `user`
--

INSERT INTO `user` (`user_id`, `partner_id`, `login`, `password`, `access_token`, `name`, `avatar`, `is_deleted`) VALUES
(1, 0, 'pdk_admin', '827ccb0eea8a706c4c34a16891f84e7b', '1-b666170990fd6dd7ccb0d49ed4fae308c8c3ebcdad9e805816c8b90711f490f0', 'Администратор', NULL, 0);

--
-- Дамп данных таблицы `site_config`
--

INSERT INTO `site_config` (`siteConfigId`, `loginLenMin`, `loginLenMax`, `loginTriesMax`, `loginTriesLock`, `pswLenMin`, `pswLenMax`, `rememberMeTime`, `pswLenMinForCashRegisterCfg`, `pswLenMaxForCashRegisterCfg`, `cashRegisterCfgAlg`, `checkApiRequestHashOff`, `redirectForMain`, `supportEmail`, `supportEmailNoReply`, `supportTel`, `sunIsLast`, `distanceRound`, `prefillingTicket`, `boardingStatementForm`, `noPlaceAge`, `buyTicketCntMax`, `jData`, `jLog`, `debug`, `user_id`, `addTime`) VALUES
(1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2018-06-25 12:19:28');

--
-- Дамп данных таблицы `TerminalGroup`
--

INSERT INTO `TerminalGroup` (`Id`, `Name`, `Description`, `VendorName`, `ModelName`, `BaseOSName`, `Type`, `TemplateConfig`, `IsDeleted`, `EditUserId`, `EditDateTime`, `Version`) VALUES
(1, 'Группа по-умолчанию', 'Настройки по умолчанию для терминалов с фискальным накопителем', 'SunMi', 'MSPOS-K', 'Android 6.x', 1, '[{\"Name\": \"LifeTime\", \"Type\": \"0\", \"Title\": \"Время жизни конфига off-line(сек)\", \"Params\": {\"Max\": \"4320000\", \"Min\": \"3600\"}, \"Default\": \"38600\", \"Required\": 1, \"Description\": \"Время жизни конфига off-line(сек)\", \"RegularMask\": \"[0-9]{2,6}\"}, {\"Name\": \"PrintRepeat\", \"Type\": \"0\", \"Title\": \"Количество повторных попыток печати при сбое\", \"Params\": {\"Max\": \"9\", \"Min\": \"1\"}, \"Default\": \"3\", \"Required\": 1, \"Description\": \"Количество повторных попыток печати при сбое\", \"RegularMask\": \"[0-9]{1}\"}, {\"Name\": \"TiketMinimumTimeRefund\", \"Type\": \"0\", \"Title\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"Params\": {\"Max\": \"999\", \"Min\": \"30\"}, \"Default\": \"180\", \"Required\": 1, \"Description\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.ServerAddress\", \"Type\": \"2\", \"Title\": \"dns-имя сервера SDBP\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"http://api.proxy.xz-lab.ru\", \"Required\": 1, \"Description\": \"dns-имя сервера SDBP\", \"RegularMask\": \"\"}, {\"Name\": \"API.ServerPort\", \"Type\": \"0\", \"Title\": \"порт сервера SDBP\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"80\", \"Required\": 1, \"Description\": \"порт сервера SDBP\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"API.Timeout\", \"Type\": \"0\", \"Title\": \"Максимальное время ответа сервера(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 1, \"Description\": \"Максимальное время ответа сервера(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.TimeRefresh\", \"Type\": \"0\", \"Title\": \"Интервал времени для обновления данных из SDBP(сек)\", \"Params\": {\"Max\": \"9999\", \"Min\": \"600\"}, \"Default\": \"600\", \"Required\": 1, \"Description\": \"Интервал времени для обновления данных из SDBP(сек)\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"API.TimeHeartbeat\", \"Type\": \"0\", \"Title\": \"Интервал проверки доступности SDBP(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"60\", \"Required\": 1, \"Description\": \"Интервал проверки доступности SDBP(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.SendFlag\", \"Type\": \"3\", \"Title\": \"Флаг отправки статистики на сервер\", \"Params\": {}, \"Default\": \"true\", \"Required\": 1, \"Description\": \"Флаг отправки статистики на сервер\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"Stat.ServerAddress\", \"Type\": \"2\", \"Title\": \"Адрес сервера для приема статистики\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"http://log.es.xz-lab.ru\", \"Required\": 1, \"Description\": \"Адрес сервера для приема статистики\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.ServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера для приема статистики\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"80\", \"Required\": 1, \"Description\": \"Порт сервера для приема статистики\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"Stat.ServerType\", \"Type\": \"5\", \"Title\": \"Тип сервера статистики\", \"Params\": {\"ReferenceName\": \"terminal-stat-server\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"Тип сервера статистики\", \"RegularMask\": \"(?:0|1)\"}, {\"Name\": \"Stat.SendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность отправки статистики(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"60\", \"Required\": 1, \"Description\": \"Периодичность отправки статистики(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.GeneralSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных general\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"60\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных general\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.FiscalSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных fiscal\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"180\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных fiscal\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.OperationsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных operations\", \"Params\": {\"Max\": \"9999\", \"Min\": \"60\"}, \"Default\": \"3600\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных operations\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"Stat.UserActionSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных user-action\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных user-action\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"FN.Enable\", \"Type\": \"3\", \"Title\": \"Фискальный накопитель используется\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Фискальный накопитель используется\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.RegNumber\", \"Type\": \"2\", \"Title\": \"Регистрационный номер ККТ\", \"Params\": {\"Max\": \"16\", \"Min\": \"16\"}, \"Default\": \"\", \"Required\": 1, \"Description\": \"Регистрационный номер ККТ\", \"RegularMask\": \"[0-9]{16}\"}, {\"Name\": \"FN.TaxCode\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения.\", \"Params\": {\"Field1\": \"FNTaxCode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"Common\", \"Required\": 0, \"Description\": \"Код системы налогообложения.\", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"FN.OperatingMode\", \"Type\": \"6\", \"Title\": \"Код режима работы\", \"Params\": {\"Field1\": \"FNOperatingMode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"Default\", \"Required\": 0, \"Description\": \"Код режима работы\", \"RegularMask\": \"\"}, {\"Name\": \"FN.IsGambling\", \"Type\": \"6\", \"Title\": \"Признак проведения азартных игр\", \"Params\": {\"Field1\": \"FNIsGambling\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения азартных игр\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.IsLottery\", \"Type\": \"6\", \"Title\": \"Признак проведения лотереи\", \"Params\": {\"Field1\": \"FNIsLottery\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения лотереи\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.PaymentAgent\", \"Type\": \"2\", \"Title\": \"Код платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Код платёжного агента\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServer\", \"Type\": \"2\", \"Title\": \"Адрес сервера ОФД для подключения\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"f1test.taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сервера ОФД для подключения\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServerPort\", \"Type\": \"6\", \"Title\": \"Порт сервера ОФД для подключения\", \"Params\": {\"Field1\": \"OFDServerPort\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7778\", \"Required\": 0, \"Description\": \"Порт сервера ОФД для подключения\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"FN.OFDName\", \"Type\": \"6\", \"Title\": \"Полное наименование оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ТАКСКОМ\", \"Required\": 0, \"Description\": \"Полное наименование оператора фискальных данных\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDINN\", \"Type\": \"6\", \"Title\": \"ИНН оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDINN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7704211201\", \"Required\": 0, \"Description\": \"ИНН оператора фискальных данных\", \"RegularMask\": \"[0-9]{10}\"}, {\"Name\": \"FN.TerminalOwnerName\", \"Type\": \"6\", \"Title\": \"Имя организации (пользователя)\", \"Params\": {\"Field1\": \"Name\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ООО ПОДКОНТРОЛЕМ\", \"Required\": 0, \"Description\": \"Имя организации (пользователя)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerAddress\", \"Type\": \"6\", \"Title\": \"Адрес организации(адрес расчётов)\", \"Params\": {\"Field1\": \"Address\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"127055, г. Москва, ул. Образцова, д. 14, 3 этаж, пом. II, комн. 2 \", \"Required\": 0, \"Description\": \"Адрес организации(адрес расчётов)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerPhysicalAddress\", \"Type\": \"2\", \"Title\": \"Физическое место расчётов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"разъездная торговля\", \"Required\": 0, \"Description\": \"Физическое место расчётов\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerEmail\", \"Type\": \"6\", \"Title\": \"Email отправителя\", \"Params\": {\"Field1\": \"Email\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"<EMAIL>\", \"Required\": 0, \"Description\": \"Email отправителя\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDReceiptCheckURI\", \"Type\": \"6\", \"Title\": \"Адрес сайта для проверки фискального признака\", \"Params\": {\"Field1\": \"OFDReceiptCheckURI\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сайта для проверки фискального признака\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FnsServerAddress\", \"Type\": \"6\", \"Title\": \"Адрес сайта налогового органа\", \"Params\": {\"Field1\": \"FnsServerAddress\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"nalog.ru\", \"Required\": 0, \"Description\": \"Адрес сайта налогового органа\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Name\", \"Type\": \"6\", \"Title\": \"Наименование сопровождающей организации\", \"Params\": {\"Field1\": \"SupportName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Наименование сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Phone\", \"Type\": \"6\", \"Title\": \"Контактный телефон\", \"Params\": {\"Field1\": \"SupportPhone\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Контактный телефон\", \"RegularMask\": \"\"}, {\"Name\": \"Support.EMail\", \"Type\": \"6\", \"Title\": \"Электронный адрес сопровождающей организации\", \"Params\": {\"Field1\": \"SupportEmail\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Электронный адрес сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityzenshipId\", \"Type\": \"0\", \"Title\": \"ID гражданства\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"170\", \"Required\": 1, \"Description\": \"ID гражданства\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"Default.DocumentId\", \"Type\": \"0\", \"Title\": \"ID идентификационного документа\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"ID идентификационного документа\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"FN.Cashier\", \"Type\": \"6\", \"Title\": \"ФИО Кассира\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"API.AgentId\", \"Type\": \"6\", \"Title\": \"Идентификатор пользователя \", \"Params\": {\"Field1\": \"TerminalUserId\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"API.Sign\", \"Type\": \"6\", \"Title\": \"Секретный ключ пользователя\", \"Params\": {\"Field1\": \"APISecretKey\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaxBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Максимум билетов для покупки\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"10\", \"Required\": 0, \"Description\": \"Максимальное количество билетов для покупки в городе за раз\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOFullName\", \"Type\": \"6\", \"Title\": \"ФИО кассира полное\", \"Params\": {\"Field1\": \"UserFIO\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира полное\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOShortName\", \"Type\": \"6\", \"Title\": \"ФИО кассира короткое\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира короткое\", \"RegularMask\": \"\"}, {\"Name\": \"FN.INN\", \"Type\": \"6\", \"Title\": \"ИНН владельца терминала\", \"Params\": {\"Field1\": \"INN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ИНН владельца терминала\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"Field1\": \"FNTaxCodeDefault\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseDocumentRecognition\", \"Type\": \"3\", \"Title\": \"Подключить распознавание документов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 1, \"Description\": \"Подключить распознавание документов\", \"RegularMask\": \"\"}, {\"Name\": \"YTanker.Enable\", \"Type\": \"3\", \"Title\": \"Включить Яндекс заправки\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить Яндекс заправки\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityStartTicketCount\", \"Type\": \"0\", \"Title\": \"Кол-во билетов в стартовом тарифе\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Кол-во билетов в стартовом тарифе\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityEnableSwipeAndLongTap\", \"Type\": \"3\", \"Title\": \"Включить свайп и долгое нажатие при продаже билетов \", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить свайп и долгое нажатие при продаже билетов \", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"5\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"ReferenceName\": \"FNTaxCode\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"UI.IntercityEnableCurrentRideSelection\", \"Type\": \"3\", \"Title\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"EMV.AcquiringMode\", \"Type\": \"0\", \"Title\": \"Режим работы эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Login\", \"Type\": \"2\", \"Title\": \"Логин для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Логин для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Password\", \"Type\": \"2\", \"Title\": \"Пароль для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.ServerUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервера процессинга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера процессинга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.TerminalId\", \"Type\": \"2\", \"Title\": \"Идентификатор терминала в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор терминала в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CurrencyId\", \"Type\": \"0\", \"Title\": \"Валюта по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Валюта по умолчанию\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTaskDays\", \"Type\": \"0\", \"Title\": \"Глубина разнарядки\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Глубина разнарядки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CheckTicketURL\", \"Type\": \"2\", \"Title\": \"Базовый урл для генерации ссылки проверки билета\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"http://test.com\", \"Required\": 0, \"Description\": \"Базовый урл для генерации ссылки проверки билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.WorkMode\", \"Type\": \"5\", \"Title\": \"Режим работы с ФН\", \"Params\": {\"ReferenceName\": \"FNMode\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы с ФН\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaySkipTicketSelection\", \"Type\": \"3\", \"Title\": \"Ускоренный режим продажи билетов в городе\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Если по данной оплате всего один тариф, то при включении флага экран выбора билетов будет пропущен, будет продан 1 билет\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityChangeByUserDepth\", \"Type\": \"0\", \"Title\": \"Количество рейсов для отображения при выборе текущей остановки\", \"Params\": {\"Max\": \"\", \"Min\": \"3\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Количество рейсов для отображения при выборе текущей остановки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AutoWithdrawalCash\", \"Type\": \"3\", \"Title\": \"Авто изъятие наличных\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Авто изъятие наличных при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"Default.IntercityActualRideOffset\", \"Type\": \"0\", \"Title\": \"Дельта актуализации рейсов в межгороде\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"За сколько минут от текущей даты можно показать рейсы для водителя\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.ServiceUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервиса\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Базовый урл сервиса для работы с онлайн процессингом транспортной карты\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.Base64PKCS12\", \"Type\": \"2\", \"Title\": \"Сертификат и приватный ключ в Base64\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Сертификат и приватный ключ в Base64\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.PassPKCS12\", \"Type\": \"2\", \"Title\": \"Пароль от сертификата\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль от сертификата\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityDisableMainScreenMenu\", \"Type\": \"3\", \"Title\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnMainScreen\", \"Type\": \"3\", \"Title\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityTicketNumberPolitics\", \"Type\": \"0\", \"Title\": \"Использовать упрощенную генерацию номера билета\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Использовать упрощенную генерацию номера билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AgentTypeDefault\", \"Type\": \"2\", \"Title\": \"Параметр агента по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Если данный параметр задан, то поле агент в чеке всегда будет принимать данное значение\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.ContractorTelNum\", \"Type\": \"2\", \"Title\": \"Телефон поставщика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.CommissionAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон комиссионера\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.PaymentAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierName\", \"Type\": \"2\", \"Title\": \"Название перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Навание перевозчика, если не задано, используется имя владельца ККТ\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityMainScreenPaymentPanelStyle\", \"Type\": \"0\", \"Title\": \"Стиль отображения кнопок вариантов оплаты\", \"Params\": {\"Max\": \"10\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Стиль отображения кнопок вариантов оплаты\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CloudFiscalOperatorBindingId\", \"Type\": \"2\", \"Title\": \"Идентификатор ОФД для облака\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор ОФД для облака\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityTempHideFiscalOperationsForDriver\", \"Type\": \"3\", \"Title\": \"ВРЕМЕННОЕ поле\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"ВРЕМЕННОЕ отключение закрытия смены и кассовых операций у водителя\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierInn\", \"Type\": \"2\", \"Title\": \"ИНН перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"ИНН перевозчика\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.AgentName\", \"Type\": \"2\", \"Title\": \"Наименование  агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Наименование агента\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionHoldDays\", \"Type\": \"0\", \"Title\": \"Время хранения транзакций в днях.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Время хранения транзакций в днях.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMinBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Минимальное количество билетов для продажи\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Минимальное количество билетов для продажи\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityRouteUpdateTime\", \"Type\": \"0\", \"Title\": \"Интервал обновления остановок в сек.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"15\", \"Required\": 0, \"Description\": \"Интервал обновления остановок в сек.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalStatusBarExpandEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки  статус бара\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки статус бара\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalKeyHomeEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки home\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки home\", \"RegularMask\": \"\"}, {\"Name\": \"Default.EnableLastReceiptCopyPrinting\", \"Type\": \"3\", \"Title\": \"Возможность печати копии последнего билета\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Возможность печати копии последнего билета\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnTicketSelectionScreen\", \"Type\": \"3\", \"Title\": \"Включить поле на экране выбора количества билетов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить поле на экране выбора количества билетов\", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseExtendTicketReport\", \"Type\": \"3\", \"Title\": \"Печать расширенного отчета о проданных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать расширенного отчета о проданных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CancelOutgoWhenShiftClosed\", \"Type\": \"3\", \"Title\": \"Автосброс занарядки при закрытии смены\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Автосброс занарядки при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityPrintSegmentDates\", \"Type\": \"3\", \"Title\": \"Печать даты отправления / прибытия в пригородных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать даты отправления / прибытия в пригородных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PrintExtOpenShift\", \"Type\": \"3\", \"Title\": \"Печатать расширенную информацию после открытия смены\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Печатать расширенную информацию после открытия смены\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.PaymentDialogType\", \"Type\": \"0\", \"Title\": \"Тип UI для экв. ядра\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип UI для экв. ядра\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityEnablePassengerData\", \"Type\": \"3\", \"Title\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FFDVersionCode\", \"Type\": \"2\", \"Title\": \"Код версии ФФД, строкой\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Код версии ФФД (2 или не задан для 1.05, 3 для 1.1)\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.AccessCode\", \"Type\": \"2\", \"Title\": \"Пин-код для авторизации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пин-код для авторизации\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.TransactionsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных transactions\", \"Params\": {\"Max\": \"\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 0, \"Description\": \"Периодичность сбора и отправки пакета данных transactions\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalSettingsPwd\", \"Type\": \"2\", \"Title\": \"Пароль для входа в настройки AQSI\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для входа в настройки AQSI\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTimeSyncMode\", \"Type\": \"0\", \"Title\": \"Тип синхронизации времени на терминале и сервере\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип синхронизации времени на терминале и сервере\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityPaymentScreenMode\", \"Type\": \"0\", \"Title\": \"Битовая маска, отвечающая за спец-режимы на экране платежа в режиме город\", \"Params\": {\"Max\": \"255\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Битовая маска, отвечающая за спец-режимы на экране платежа в режиме город\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityAutoCloseSellScreenDelay\", \"Type\": \"0\", \"Title\": \"Интервал авто закрытия экрана (сек.) продажи в ускоренном режиме\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"2\", \"Required\": 0, \"Description\": \"Интервал авто закрытия экрана продажи в ускоренном режиме (сек.)\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PersonalTaskAutoSelect\", \"Type\": \"3\", \"Title\": \"Использовать авто выбор рейса\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Использовать авто выбор рейса\", \"RegularMask\": \"\"}, {\"Name\": \"Default.ReservedField1\", \"Type\": \"3\", \"Title\": \"Зарезервированное поле для временных значений\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Зарезервированное поле для временных значений\", \"RegularMask\": \"\"}, {\"Name\": \"FN.PrintCloudQR\", \"Type\": \"3\", \"Title\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionSendInterval\", \"Type\": \"0\", \"Title\": \"Интервал посылки транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"480\", \"Required\": 0, \"Description\": \"Интервал посылки транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionCollectInterval\", \"Type\": \"0\", \"Title\": \"Интервал сбора транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"300\", \"Required\": 0, \"Description\": \"Интервал сбора транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.WorkMode\", \"Type\": \"0\", \"Title\": \"Режим работы валидатора\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы валидатора\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerList\", \"Type\": \"2\", \"Title\": \"Список IP адресов для серверов валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Список IP адресов для серверов валидации\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.EnableKrsServer\", \"Type\": \"3\", \"Title\": \"Индикация включения сервера для валидации на устройстве\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Индикация включения сервера для валидации на устройстве\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.CfgCrc\", \"Type\": \"0\", \"Title\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.FwVersion\", \"Type\": \"2\", \"Title\": \"Ожидаемая версия прошивки устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Ожидаемая версия прошивки устройства\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.ChildDevices\", \"Type\": \"2\", \"Title\": \"Конфигурация дочерних терминалов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Конфигурация дочерних терминалов\", \"RegularMask\": \"\"}, {\"Name\": \"APS.Devices\", \"Type\": \"2\", \"Title\": \"Список настроек устройств контроля пассажиропотока\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Список настроек устройств контроля пассажиропотока\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Address\", \"Type\": \"2\", \"Title\": \"Адрес сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Port\", \"Type\": \"0\", \"Title\": \"Порт сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.User\", \"Type\": \"2\", \"Title\": \"Идентификатор пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Pwd\", \"Type\": \"2\", \"Title\": \"Пароль пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.AppVer\", \"Type\": \"2\", \"Title\": \"Версия ПО актуальная для устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Версия ПО актуальная для устройства\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.ApsDataSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора информации о пассажиропотоке\", \"Params\": {\"Max\": \"\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 0, \"Description\": \"Периодичность сбора информации о пассажиропотоке\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера валидации\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.CfgJson\", \"Type\": \"2\", \"Title\": \"Персонализованная конфигурация для транспортной карты\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Персонализованная конфигурация для транспортной карты\", \"RegularMask\": \"\"}, {\"Name\": \"SV.TID\", \"Type\": \"2\", \"Title\": \"Терминал смартвисты для региона\", \"Params\": {\"Max\": \"12\", \"Min\": \"4\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Терминал смартвисты для региона\", \"RegularMask\": \"\"}, {\"Name\": \"SV.Region\", \"Type\": \"2\", \"Title\": \"Регион к которому относится терминал SV\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Регион к которому относится терминал SV\", \"RegularMask\": \"\"}]', 0, NULL, '2021-11-11 14:14:01', 5);
INSERT INTO `TerminalGroup` (`Id`, `Name`, `Description`, `VendorName`, `ModelName`, `BaseOSName`, `Type`, `TemplateConfig`, `IsDeleted`, `EditUserId`, `EditDateTime`, `Version`) VALUES
(2, 'Тест newpos 8210', '  test group for newpos 8210              ', 'newpos', 'NEWPOS', 'OS', 1, '[{\"Name\": \"LifeTime\", \"Type\": \"0\", \"Title\": \"Время жизни конфига off-line(сек)\", \"Params\": {\"Max\": \"4320000\", \"Min\": \"3600\"}, \"Default\": \"38600\", \"Required\": 1, \"Description\": \"Время жизни конфига off-line(сек)\", \"RegularMask\": \"[0-9]{2,6}\"}, {\"Name\": \"PrintRepeat\", \"Type\": \"0\", \"Title\": \"Количество повторных попыток печати при сбое\", \"Params\": {\"Max\": \"9\", \"Min\": \"1\"}, \"Default\": \"3\", \"Required\": 1, \"Description\": \"Количество повторных попыток печати при сбое\", \"RegularMask\": \"[0-9]{1}\"}, {\"Name\": \"TiketMinimumTimeRefund\", \"Type\": \"0\", \"Title\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"Params\": {\"Max\": \"999\", \"Min\": \"30\"}, \"Default\": \"180\", \"Required\": 1, \"Description\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.ServerAddress\", \"Type\": \"2\", \"Title\": \"dns-имя сервера SDBP\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"https://am.XX.tkp2.sbertroika.tech\", \"Required\": 1, \"Description\": \"dns-имя сервера SDBP\", \"RegularMask\": \"\"}, {\"Name\": \"API.ServerPort\", \"Type\": \"0\", \"Title\": \"порт сервера SDBP\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"443\", \"Required\": 1, \"Description\": \"порт сервера SDBP\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"API.Timeout\", \"Type\": \"0\", \"Title\": \"Максимальное время ответа сервера(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 1, \"Description\": \"Максимальное время ответа сервера(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.TimeRefresh\", \"Type\": \"0\", \"Title\": \"Интервал времени для обновления данных из SDBP(сек)\", \"Params\": {\"Max\": \"9999\", \"Min\": \"600\"}, \"Default\": \"600\", \"Required\": 1, \"Description\": \"Интервал времени для обновления данных из SDBP(сек)\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"API.TimeHeartbeat\", \"Type\": \"0\", \"Title\": \"Интервал проверки доступности SDBP(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"60\", \"Required\": 1, \"Description\": \"Интервал проверки доступности SDBP(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.SendFlag\", \"Type\": \"3\", \"Title\": \"Флаг отправки статистики на сервер\", \"Params\": {}, \"Default\": \"true\", \"Required\": 1, \"Description\": \"Флаг отправки статистики на сервер\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"Stat.ServerAddress\", \"Type\": \"2\", \"Title\": \"Адрес сервера для приема статистики\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"http://log.ru\", \"Required\": 1, \"Description\": \"Адрес сервера для приема статистики\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.ServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера для приема статистики\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"80\", \"Required\": 1, \"Description\": \"Порт сервера для приема статистики\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"Stat.ServerType\", \"Type\": \"5\", \"Title\": \"Тип сервера статистики\", \"Params\": {\"ReferenceName\": \"terminal-stat-server\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"Тип сервера статистики\", \"RegularMask\": \"(?:0|1)\"}, {\"Name\": \"Stat.SendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность отправки статистики(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"310\", \"Required\": 1, \"Description\": \"Периодичность отправки статистики(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.GeneralSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных general\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"330\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных general\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.FiscalSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных fiscal\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"350\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных fiscal\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.OperationsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных operations\", \"Params\": {\"Max\": \"9999\", \"Min\": \"60\"}, \"Default\": \"3600\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных operations\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"Stat.UserActionSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных user-action\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"320\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных user-action\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"FN.Enable\", \"Type\": \"3\", \"Title\": \"Фискальный накопитель используется\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Фискальный накопитель используется\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.RegNumber\", \"Type\": \"2\", \"Title\": \"Регистрационный номер ККТ\", \"Params\": {\"Max\": \"16\", \"Min\": \"16\"}, \"Default\": \"\", \"Required\": 1, \"Description\": \"Регистрационный номер ККТ\", \"RegularMask\": \"[0-9]{16}\"}, {\"Name\": \"FN.TaxCode\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения.\", \"Params\": {\"Field1\": \"FNTaxCode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Код системы налогообложения.\", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"FN.OperatingMode\", \"Type\": \"6\", \"Title\": \"Код режима работы\", \"Params\": {\"Field1\": \"FNOperatingMode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"Default\", \"Required\": 0, \"Description\": \"Код режима работы\", \"RegularMask\": \"\"}, {\"Name\": \"FN.IsGambling\", \"Type\": \"6\", \"Title\": \"Признак проведения азартных игр\", \"Params\": {\"Field1\": \"FNIsGambling\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения азартных игр\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.IsLottery\", \"Type\": \"6\", \"Title\": \"Признак проведения лотереи\", \"Params\": {\"Field1\": \"FNIsLottery\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения лотереи\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.PaymentAgent\", \"Type\": \"2\", \"Title\": \"Код платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Код платёжного агента\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServer\", \"Type\": \"2\", \"Title\": \"Адрес сервера ОФД для подключения\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"f1test.taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сервера ОФД для подключения\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServerPort\", \"Type\": \"6\", \"Title\": \"Порт сервера ОФД для подключения\", \"Params\": {\"Field1\": \"OFDServerPort\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7778\", \"Required\": 0, \"Description\": \"Порт сервера ОФД для подключения\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"FN.OFDName\", \"Type\": \"6\", \"Title\": \"Полное наименование оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ТАКСКОМ\", \"Required\": 0, \"Description\": \"Полное наименование оператора фискальных данных\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDINN\", \"Type\": \"6\", \"Title\": \"ИНН оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDINN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7704211201\", \"Required\": 0, \"Description\": \"ИНН оператора фискальных данных\", \"RegularMask\": \"[0-9]{10}\"}, {\"Name\": \"FN.TerminalOwnerName\", \"Type\": \"6\", \"Title\": \"Имя организации (пользователя)\", \"Params\": {\"Field1\": \"Name\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ООО ПОДКОНТРОЛЕМ\", \"Required\": 0, \"Description\": \"Имя организации (пользователя)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerAddress\", \"Type\": \"6\", \"Title\": \"Адрес организации(адрес расчётов)\", \"Params\": {\"Field1\": \"Address\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"127055, г. Москва, ул. Образцова, д. 14, 3 этаж, пом. II, комн. 2 \", \"Required\": 0, \"Description\": \"Адрес организации(адрес расчётов)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerPhysicalAddress\", \"Type\": \"2\", \"Title\": \"Физическое место расчётов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"разъездная торговля\", \"Required\": 0, \"Description\": \"Физическое место расчётов\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerEmail\", \"Type\": \"6\", \"Title\": \"Email отправителя\", \"Params\": {\"Field1\": \"Email\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"<EMAIL>\", \"Required\": 0, \"Description\": \"Email отправителя\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDReceiptCheckURI\", \"Type\": \"6\", \"Title\": \"Адрес сайта для проверки фискального признака\", \"Params\": {\"Field1\": \"OFDReceiptCheckURI\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сайта для проверки фискального признака\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FnsServerAddress\", \"Type\": \"6\", \"Title\": \"Адрес сайта налогового органа\", \"Params\": {\"Field1\": \"FnsServerAddress\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"nalog.ru\", \"Required\": 0, \"Description\": \"Адрес сайта налогового органа\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Name\", \"Type\": \"6\", \"Title\": \"Наименование сопровождающей организации\", \"Params\": {\"Field1\": \"SupportName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Наименование сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Phone\", \"Type\": \"6\", \"Title\": \"Контактный телефон\", \"Params\": {\"Field1\": \"SupportPhone\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Контактный телефон\", \"RegularMask\": \"\"}, {\"Name\": \"Support.EMail\", \"Type\": \"6\", \"Title\": \"Электронный адрес сопровождающей организации\", \"Params\": {\"Field1\": \"SupportEmail\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Электронный адрес сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityzenshipId\", \"Type\": \"0\", \"Title\": \"ID гражданства\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"170\", \"Required\": 1, \"Description\": \"ID гражданства\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"Default.DocumentId\", \"Type\": \"0\", \"Title\": \"ID идентификационного документа\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"ID идентификационного документа\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"FN.Cashier\", \"Type\": \"6\", \"Title\": \"ФИО Кассира\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"API.AgentId\", \"Type\": \"6\", \"Title\": \"Идентификатор пользователя \", \"Params\": {\"Field1\": \"TerminalUserId\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"API.Sign\", \"Type\": \"6\", \"Title\": \"Секретный ключ пользователя\", \"Params\": {\"Field1\": \"APISecretKey\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaxBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Максимум билетов для покупки\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"10\", \"Required\": 0, \"Description\": \"Максимальное количество билетов для покупки в городе за раз\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOFullName\", \"Type\": \"6\", \"Title\": \"ФИО кассира полное\", \"Params\": {\"Field1\": \"UserFIO\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира полное\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOShortName\", \"Type\": \"6\", \"Title\": \"ФИО кассира короткое\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира короткое\", \"RegularMask\": \"\"}, {\"Name\": \"FN.INN\", \"Type\": \"6\", \"Title\": \"ИНН владельца терминала\", \"Params\": {\"Field1\": \"INN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ИНН владельца терминала\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"Field1\": \"FNTaxCodeDefault\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseDocumentRecognition\", \"Type\": \"3\", \"Title\": \"Подключить распознавание документов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 1, \"Description\": \"Подключить распознавание документов\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityStartTicketCount\", \"Type\": \"0\", \"Title\": \"Кол-во билетов в стартовом тарифе\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Кол-во билетов в стартовом тарифе\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityEnableSwipeAndLongTap\", \"Type\": \"3\", \"Title\": \"Включить свайп и долгое нажатие при продаже билетов \", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить свайп и долгое нажатие при продаже билетов \", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"5\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"ReferenceName\": \"FNTaxCode\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"UI.IntercityEnableCurrentRideSelection\", \"Type\": \"3\", \"Title\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"EMV.AcquiringMode\", \"Type\": \"0\", \"Title\": \"Режим работы эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"52\", \"Required\": 0, \"Description\": \"Режим работы эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Login\", \"Type\": \"2\", \"Title\": \"Логин для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Логин для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Password\", \"Type\": \"2\", \"Title\": \"Пароль для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.ServerUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервера процессинга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера процессинга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.TerminalId\", \"Type\": \"2\", \"Title\": \"Идентификатор терминала в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор терминала в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CurrencyId\", \"Type\": \"0\", \"Title\": \"Валюта по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Валюта по умолчанию\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTaskDays\", \"Type\": \"0\", \"Title\": \"Глубина разнарядки\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Глубина разнарядки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CheckTicketURL\", \"Type\": \"6\", \"Title\": \"Базовый урл для генерации ссылки проверки билета\", \"Params\": {\"Field1\": \"CheckTicketURL\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"http://test.com\", \"Required\": 0, \"Description\": \"Базовый урл для генерации ссылки проверки билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.WorkMode\", \"Type\": \"5\", \"Title\": \"Режим работы с ФН\", \"Params\": {\"ReferenceName\": \"FNMode\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы с ФН\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaySkipTicketSelection\", \"Type\": \"3\", \"Title\": \"Ускоренный режим продажи билетов в городе\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Если по данной оплате всего один тариф, то при включении флага экран выбора билетов будет пропущен, будет продан 1 билет\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityChangeByUserDepth\", \"Type\": \"0\", \"Title\": \"Количество рейсов для отображения при выборе текущей остановки\", \"Params\": {\"Max\": \"\", \"Min\": \"3\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Количество рейсов для отображения при выборе текущей остановки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AutoWithdrawalCash\", \"Type\": \"3\", \"Title\": \"Авто изъятие наличных\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Авто изъятие наличных при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.ServiceUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервиса\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Базовый урл сервиса для работы с онлайн процессингом транспортной карты\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.Base64PKCS12\", \"Type\": \"2\", \"Title\": \"Сертификат и приватный ключ в Base64\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Сертификат и приватный ключ в Base64\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.PassPKCS12\", \"Type\": \"2\", \"Title\": \"Пароль от сертификата\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль от сертификата\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityDisableMainScreenMenu\", \"Type\": \"3\", \"Title\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnMainScreen\", \"Type\": \"3\", \"Title\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityTicketNumberPolitics\", \"Type\": \"0\", \"Title\": \"Использовать упрощенную генерацию номера билета\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Использовать упрощенную генерацию номера билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AgentTypeDefault\", \"Type\": \"2\", \"Title\": \"Параметр агента по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Если данный параметр задан, то поле агент в чеке всегда будет принимать данное значение\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.ContractorTelNum\", \"Type\": \"2\", \"Title\": \"Телефон поставщика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.CommissionAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон комиссионера\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.PaymentAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierName\", \"Type\": \"2\", \"Title\": \"Название перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Навание перевозчика, если не задано, используется имя владельца ККТ\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityMainScreenPaymentPanelStyle\", \"Type\": \"0\", \"Title\": \"Стиль отображения кнопок вариантов оплаты\", \"Params\": {\"Max\": \"10\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Стиль отображения кнопок вариантов оплаты\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CloudFiscalOperatorBindingId\", \"Type\": \"2\", \"Title\": \"Идентификатор ОФД для облака\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" 99\", \"Required\": 0, \"Description\": \"Идентификатор ОФД для облака\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityTempHideFiscalOperationsForDriver\", \"Type\": \"3\", \"Title\": \"ВРЕМЕННОЕ поле\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"ВРЕМЕННОЕ отключение закрытия смены и кассовых операций у водителя\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierInn\", \"Type\": \"2\", \"Title\": \"ИНН перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"ИНН перевозчика\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.AgentName\", \"Type\": \"2\", \"Title\": \"Наименование  агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Наименование агента\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionHoldDays\", \"Type\": \"0\", \"Title\": \"Время хранения транзакций в днях.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Время хранения транзакций в днях.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMinBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Минимальное количество билетов для продажи\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Минимальное количество билетов для продажи\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityRouteUpdateTime\", \"Type\": \"0\", \"Title\": \"Интервал обновления остановок в сек.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"15\", \"Required\": 0, \"Description\": \"Интервал обновления остановок в сек.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalStatusBarExpandEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки  статус бара\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки статус бара\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalKeyHomeEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки home\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки home\", \"RegularMask\": \"\"}, {\"Name\": \"Default.EnableLastReceiptCopyPrinting\", \"Type\": \"3\", \"Title\": \"Возможность печати копии последнего билета\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Возможность печати копии последнего билета\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnTicketSelectionScreen\", \"Type\": \"3\", \"Title\": \"Включить поле на экране выбора количества билетов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить поле на экране выбора количества билетов\", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseExtendTicketReport\", \"Type\": \"3\", \"Title\": \"Печать расширенного отчета о проданных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать расширенного отчета о проданных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CancelOutgoWhenShiftClosed\", \"Type\": \"3\", \"Title\": \"Автосброс занарядки при закрытии смены\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Автосброс занарядки при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityPrintSegmentDates\", \"Type\": \"3\", \"Title\": \"Печать даты отправления / прибытия в пригородных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать даты отправления / прибытия в пригородных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PrintExtOpenShift\", \"Type\": \"3\", \"Title\": \"Печатать расширенную информацию после открытия смены\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Печатать расширенную информацию после открытия смены\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.PaymentDialogType\", \"Type\": \"0\", \"Title\": \"Тип UI для экв. ядра\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип UI для экв. ядра\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityEnablePassengerData\", \"Type\": \"3\", \"Title\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FFDVersionCode\", \"Type\": \"2\", \"Title\": \"Код версии ФФД, строкой\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Код версии ФФД (2 или не задан для 1.05, 3 для 1.1)\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.AccessCode\", \"Type\": \"2\", \"Title\": \"Пин-код для авторизации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пин-код для авторизации\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.TransactionsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных transactions\", \"Params\": {\"Max\": \"\", \"Min\": \"60\"}, \"Default\": \"320\", \"Required\": 0, \"Description\": \"Периодичность сбора и отправки пакета данных transactions\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalSettingsPwd\", \"Type\": \"2\", \"Title\": \"Пароль для входа в настройки AQSI\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для входа в настройки AQSI\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTimeSyncMode\", \"Type\": \"0\", \"Title\": \"Тип синхронизации времени на терминале и сервере\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип синхронизации времени на терминале и сервере\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityAutoCloseSellScreenEnable\", \"Type\": \"3\", \"Title\": \"Включение режима ускоренной работы экрана продажи\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение режима ускоренной работы экрана продажи (ТЕСТОВЫЙ ФУНКЦИОНАЛ)\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityAutoCloseSellScreenDelay\", \"Type\": \"0\", \"Title\": \"Интервал авто закрытия экрана (сек.) продажи в ускоренном режиме\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"2\", \"Required\": 0, \"Description\": \"Интервал авто закрытия экрана продажи в ускоренном режиме (сек.)\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PersonalTaskAutoSelect\", \"Type\": \"3\", \"Title\": \"Использовать авто выбор рейса\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Использовать авто выбор рейса\", \"RegularMask\": \"\"}, {\"Name\": \"Default.ReservedField1\", \"Type\": \"3\", \"Title\": \"Зарезервированное поле для временных значений\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Зарезервированное поле для временных значений\", \"RegularMask\": \"\"}, {\"Name\": \"FN.PrintCloudQR\", \"Type\": \"3\", \"Title\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionSendInterval\", \"Type\": \"0\", \"Title\": \"Интервал посылки транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"480\", \"Required\": 0, \"Description\": \"Интервал посылки транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionCollectInterval\", \"Type\": \"0\", \"Title\": \"Интервал сбора транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"300\", \"Required\": 0, \"Description\": \"Интервал сбора транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.WorkMode\", \"Type\": \"0\", \"Title\": \"Режим работы валидатора\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы валидатора\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerList\", \"Type\": \"2\", \"Title\": \"Список IP адресов для серверов валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Список IP адресов для серверов валидации\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.EnableKrsServer\", \"Type\": \"3\", \"Title\": \"Индикация включения сервера для валидации на устройстве\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Индикация включения сервера для валидации на устройстве\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.CfgCrc\", \"Type\": \"0\", \"Title\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.FwVersion\", \"Type\": \"2\", \"Title\": \"Ожидаемая версия прошивки устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Ожидаемая версия прошивки устройства\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.ChildDevices\", \"Type\": \"2\", \"Title\": \"Конфигурация дочерних терминалов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Конфигурация дочерних терминалов\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Address\", \"Type\": \"2\", \"Title\": \"Адрес сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Port\", \"Type\": \"0\", \"Title\": \"Порт сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.User\", \"Type\": \"2\", \"Title\": \"Идентификатор пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Pwd\", \"Type\": \"2\", \"Title\": \"Пароль пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.AppVer\", \"Type\": \"2\", \"Title\": \"Версия ПО актуальная для устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Версия ПО актуальная для устройства\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера валидации\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.CfgJson\", \"Type\": \"2\", \"Title\": \"Персонализованная конфигурация для транспортной карты\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Персонализованная конфигурация для транспортной карты\", \"RegularMask\": \"\"}]', 0, NULL, '2022-01-23 10:30:15', 12);
INSERT INTO `TerminalGroup` (`Id`, `Name`, `Description`, `VendorName`, `ModelName`, `BaseOSName`, `Type`, `TemplateConfig`, `IsDeleted`, `EditUserId`, `EditDateTime`, `Version`) VALUES
(3, 'Aqsi 5f', '  test group for Aqsi    ', 'Aqsi', 'Aqsi 5f', 'OS Android 7.0', 1, '[{\"Name\": \"LifeTime\", \"Type\": \"0\", \"Title\": \"Время жизни конфига off-line(сек)\", \"Params\": {\"Max\": \"4320000\", \"Min\": \"3600\"}, \"Default\": \"38600\", \"Required\": 1, \"Description\": \"Время жизни конфига off-line(сек)\", \"RegularMask\": \"[0-9]{2,6}\"}, {\"Name\": \"PrintRepeat\", \"Type\": \"0\", \"Title\": \"Количество повторных попыток печати при сбое\", \"Params\": {\"Max\": \"9\", \"Min\": \"1\"}, \"Default\": \"3\", \"Required\": 1, \"Description\": \"Количество повторных попыток печати при сбое\", \"RegularMask\": \"[0-9]{1}\"}, {\"Name\": \"TiketMinimumTimeRefund\", \"Type\": \"0\", \"Title\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"Params\": {\"Max\": \"999\", \"Min\": \"30\"}, \"Default\": \"180\", \"Required\": 1, \"Description\": \"Минимальное время на возврат билета(cек до начала рейса)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.ServerAddress\", \"Type\": \"2\", \"Title\": \"dns-имя сервера SDBP\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"https://am.XX.tkp2.sbertroika.tech\", \"Required\": 1, \"Description\": \"dns-имя сервера SDBP\", \"RegularMask\": \"\"}, {\"Name\": \"API.ServerPort\", \"Type\": \"0\", \"Title\": \"порт сервера SDBP\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"443\", \"Required\": 1, \"Description\": \"порт сервера SDBP\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"API.Timeout\", \"Type\": \"0\", \"Title\": \"Максимальное время ответа сервера(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 1, \"Description\": \"Максимальное время ответа сервера(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"API.TimeRefresh\", \"Type\": \"0\", \"Title\": \"Интервал времени для обновления данных из SDBP(сек)\", \"Params\": {\"Max\": \"9999\", \"Min\": \"600\"}, \"Default\": \"600\", \"Required\": 1, \"Description\": \"Интервал времени для обновления данных из SDBP(сек)\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"API.TimeHeartbeat\", \"Type\": \"0\", \"Title\": \"Интервал проверки доступности SDBP(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"60\", \"Required\": 1, \"Description\": \"Интервал проверки доступности SDBP(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.SendFlag\", \"Type\": \"3\", \"Title\": \"Флаг отправки статистики на сервер\", \"Params\": {}, \"Default\": \"true\", \"Required\": 1, \"Description\": \"Флаг отправки статистики на сервер\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"Stat.ServerAddress\", \"Type\": \"2\", \"Title\": \"Адрес сервера для приема статистики\", \"Params\": {\"Max\": \"128\", \"Min\": \"1\"}, \"Default\": \"http://log.ru\", \"Required\": 1, \"Description\": \"Адрес сервера для приема статистики\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.ServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера для приема статистики\", \"Params\": {\"Max\": \"65534\", \"Min\": \"80\"}, \"Default\": \"80\", \"Required\": 1, \"Description\": \"Порт сервера для приема статистики\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"Stat.ServerType\", \"Type\": \"5\", \"Title\": \"Тип сервера статистики\", \"Params\": {\"ReferenceName\": \"terminal-stat-server\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"Тип сервера статистики\", \"RegularMask\": \"(?:0|1)\"}, {\"Name\": \"Stat.SendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность отправки статистики(сек)\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"300\", \"Required\": 1, \"Description\": \"Периодичность отправки статистики(сек)\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.GeneralSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных general\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"300\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных general\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.FiscalSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных fiscal\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"300\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных fiscal\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"Stat.OperationsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных operations\", \"Params\": {\"Max\": \"9999\", \"Min\": \"60\"}, \"Default\": \"3600\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных operations\", \"RegularMask\": \"[0-9]{2,4}\"}, {\"Name\": \"Stat.UserActionSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных user-action\", \"Params\": {\"Max\": \"999\", \"Min\": \"60\"}, \"Default\": \"300\", \"Required\": 1, \"Description\": \"Периодичность сбора и отправки пакета данных user-action\", \"RegularMask\": \"[0-9]{2,3}\"}, {\"Name\": \"FN.Enable\", \"Type\": \"3\", \"Title\": \"Фискальный накопитель используется\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Фискальный накопитель используется\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.RegNumber\", \"Type\": \"2\", \"Title\": \"Регистрационный номер ККТ\", \"Params\": {\"Max\": \"16\", \"Min\": \"16\"}, \"Default\": \"\", \"Required\": 1, \"Description\": \"Регистрационный номер ККТ\", \"RegularMask\": \"[0-9]{16}\"}, {\"Name\": \"FN.TaxCode\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения.\", \"Params\": {\"Field1\": \"FNTaxCode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Код системы налогообложения.\", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"FN.OperatingMode\", \"Type\": \"6\", \"Title\": \"Код режима работы\", \"Params\": {\"Field1\": \"FNOperatingMode\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"Default\", \"Required\": 0, \"Description\": \"Код режима работы\", \"RegularMask\": \"\"}, {\"Name\": \"FN.IsGambling\", \"Type\": \"6\", \"Title\": \"Признак проведения азартных игр\", \"Params\": {\"Field1\": \"FNIsGambling\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения азартных игр\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.IsLottery\", \"Type\": \"6\", \"Title\": \"Признак проведения лотереи\", \"Params\": {\"Field1\": \"FNIsLottery\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Признак проведения лотереи\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"FN.PaymentAgent\", \"Type\": \"2\", \"Title\": \"Код платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Код платёжного агента\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServer\", \"Type\": \"2\", \"Title\": \"Адрес сервера ОФД для подключения\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"f1test.taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сервера ОФД для подключения\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDServerPort\", \"Type\": \"6\", \"Title\": \"Порт сервера ОФД для подключения\", \"Params\": {\"Field1\": \"OFDServerPort\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7778\", \"Required\": 0, \"Description\": \"Порт сервера ОФД для подключения\", \"RegularMask\": \"[0-9]{2,5}\"}, {\"Name\": \"FN.OFDName\", \"Type\": \"6\", \"Title\": \"Полное наименование оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ТАКСКОМ\", \"Required\": 0, \"Description\": \"Полное наименование оператора фискальных данных\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDINN\", \"Type\": \"6\", \"Title\": \"ИНН оператора фискальных данных\", \"Params\": {\"Field1\": \"OFDINN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"7704211201\", \"Required\": 0, \"Description\": \"ИНН оператора фискальных данных\", \"RegularMask\": \"[0-9]{10}\"}, {\"Name\": \"FN.TerminalOwnerName\", \"Type\": \"6\", \"Title\": \"Имя организации (пользователя)\", \"Params\": {\"Field1\": \"Name\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"ООО ПОДКОНТРОЛЕМ\", \"Required\": 0, \"Description\": \"Имя организации (пользователя)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerAddress\", \"Type\": \"6\", \"Title\": \"Адрес организации(адрес расчётов)\", \"Params\": {\"Field1\": \"Address\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"127055, г. Москва, ул. Образцова, д. 14, 3 этаж, пом. II, комн. 2 \", \"Required\": 0, \"Description\": \"Адрес организации(адрес расчётов)\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerPhysicalAddress\", \"Type\": \"2\", \"Title\": \"Физическое место расчётов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"разъездная торговля\", \"Required\": 0, \"Description\": \"Физическое место расчётов\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TerminalOwnerEmail\", \"Type\": \"6\", \"Title\": \"Email отправителя\", \"Params\": {\"Field1\": \"Email\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"<EMAIL>\", \"Required\": 0, \"Description\": \"Email отправителя\", \"RegularMask\": \"\"}, {\"Name\": \"FN.OFDReceiptCheckURI\", \"Type\": \"6\", \"Title\": \"Адрес сайта для проверки фискального признака\", \"Params\": {\"Field1\": \"OFDReceiptCheckURI\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"taxcom.ru\", \"Required\": 0, \"Description\": \"Адрес сайта для проверки фискального признака\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FnsServerAddress\", \"Type\": \"6\", \"Title\": \"Адрес сайта налогового органа\", \"Params\": {\"Field1\": \"FnsServerAddress\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"nalog.ru\", \"Required\": 0, \"Description\": \"Адрес сайта налогового органа\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Name\", \"Type\": \"6\", \"Title\": \"Наименование сопровождающей организации\", \"Params\": {\"Field1\": \"SupportName\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Наименование сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Support.Phone\", \"Type\": \"6\", \"Title\": \"Контактный телефон\", \"Params\": {\"Field1\": \"SupportPhone\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Контактный телефон\", \"RegularMask\": \"\"}, {\"Name\": \"Support.EMail\", \"Type\": \"6\", \"Title\": \"Электронный адрес сопровождающей организации\", \"Params\": {\"Field1\": \"SupportEmail\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Электронный адрес сопровождающей организации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityzenshipId\", \"Type\": \"0\", \"Title\": \"ID гражданства\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"170\", \"Required\": 1, \"Description\": \"ID гражданства\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"Default.DocumentId\", \"Type\": \"0\", \"Title\": \"ID идентификационного документа\", \"Params\": {\"Max\": \"9999999999\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 1, \"Description\": \"ID идентификационного документа\", \"RegularMask\": \"[0-9]{1,10}\"}, {\"Name\": \"FN.Cashier\", \"Type\": \"6\", \"Title\": \"ФИО Кассира\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"API.AgentId\", \"Type\": \"6\", \"Title\": \"Идентификатор пользователя \", \"Params\": {\"Field1\": \"TerminalUserId\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"API.Sign\", \"Type\": \"6\", \"Title\": \"Секретный ключ пользователя\", \"Params\": {\"Field1\": \"APISecretKey\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Расчетный параметр. Зависит от авторизованного пользователя.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaxBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Максимум билетов для покупки\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"10\", \"Required\": 0, \"Description\": \"Максимальное количество билетов для покупки в городе за раз\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOFullName\", \"Type\": \"6\", \"Title\": \"ФИО кассира полное\", \"Params\": {\"Field1\": \"UserFIO\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира полное\", \"RegularMask\": \"\"}, {\"Name\": \"CashierFIOShortName\", \"Type\": \"6\", \"Title\": \"ФИО кассира короткое\", \"Params\": {\"Field1\": \"UserFIOShort\", \"TableName\": \"TerminalUser\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ФИО кассира короткое\", \"RegularMask\": \"\"}, {\"Name\": \"FN.INN\", \"Type\": \"6\", \"Title\": \"ИНН владельца терминала\", \"Params\": {\"Field1\": \"INN\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"ИНН владельца терминала\", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"6\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"Field1\": \"FNTaxCodeDefault\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseDocumentRecognition\", \"Type\": \"3\", \"Title\": \"Подключить распознавание документов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 1, \"Description\": \"Подключить распознавание документов\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityStartTicketCount\", \"Type\": \"0\", \"Title\": \"Кол-во билетов в стартовом тарифе\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Кол-во билетов в стартовом тарифе\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityEnableSwipeAndLongTap\", \"Type\": \"3\", \"Title\": \"Включить свайп и долгое нажатие при продаже билетов \", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить свайп и долгое нажатие при продаже билетов \", \"RegularMask\": \"\"}, {\"Name\": \"FN.TaxCodeDefault\", \"Type\": \"5\", \"Title\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"Params\": {\"ReferenceName\": \"FNTaxCode\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Код системы налогообложения применяемый в терминале по умолчанию \", \"RegularMask\": \"(?:Common|Simplified|SimplifiedWithExpense|ENVD|CommonAgricultural|Patent)\"}, {\"Name\": \"UI.IntercityEnableCurrentRideSelection\", \"Type\": \"3\", \"Title\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг, отвечающий за возможность выбора текущего рейса в межгороде\", \"RegularMask\": \"(?:true|false)\"}, {\"Name\": \"EMV.AcquiringMode\", \"Type\": \"0\", \"Title\": \"Режим работы эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"52\", \"Required\": 0, \"Description\": \"Режим работы эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Login\", \"Type\": \"2\", \"Title\": \"Логин для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Логин для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.Password\", \"Type\": \"2\", \"Title\": \"Пароль для авторизации в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для авторизации в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.ServerUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервера процессинга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера процессинга\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.TerminalId\", \"Type\": \"2\", \"Title\": \"Идентификатор терминала в системе эквайринга\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор терминала в системе эквайринга\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CurrencyId\", \"Type\": \"0\", \"Title\": \"Валюта по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Валюта по умолчанию\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTaskDays\", \"Type\": \"0\", \"Title\": \"Глубина разнарядки\", \"Params\": {\"Max\": \"\", \"Min\": \"1\"}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Глубина разнарядки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CheckTicketURL\", \"Type\": \"6\", \"Title\": \"Базовый урл для генерации ссылки проверки билета\", \"Params\": {\"Field1\": \"CheckTicketURL\", \"TableName\": \"TerminalOrganizationList\"}, \"Default\": \"http://test.com\", \"Required\": 0, \"Description\": \"Базовый урл для генерации ссылки проверки билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.WorkMode\", \"Type\": \"5\", \"Title\": \"Режим работы с ФН\", \"Params\": {\"ReferenceName\": \"FNMode\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы с ФН\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMaySkipTicketSelection\", \"Type\": \"3\", \"Title\": \"Ускоренный режим продажи билетов в городе\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Если по данной оплате всего один тариф, то при включении флага экран выбора билетов будет пропущен, будет продан 1 билет\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityChangeByUserDepth\", \"Type\": \"0\", \"Title\": \"Количество рейсов для отображения при выборе текущей остановки\", \"Params\": {\"Max\": \"\", \"Min\": \"3\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Количество рейсов для отображения при выборе текущей остановки\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AutoWithdrawalCash\", \"Type\": \"3\", \"Title\": \"Авто изъятие наличных\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Авто изъятие наличных при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.ServiceUrl\", \"Type\": \"2\", \"Title\": \"Адрес сервиса\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Базовый урл сервиса для работы с онлайн процессингом транспортной карты\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.Base64PKCS12\", \"Type\": \"2\", \"Title\": \"Сертификат и приватный ключ в Base64\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Сертификат и приватный ключ в Base64\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.PassPKCS12\", \"Type\": \"2\", \"Title\": \"Пароль от сертификата\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль от сертификата\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityDisableMainScreenMenu\", \"Type\": \"3\", \"Title\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Скрыть кнопку перехода в настройки на главном экране в режиме город\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnMainScreen\", \"Type\": \"3\", \"Title\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение поля для чтения бесконтактных карт на главном экране\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityTicketNumberPolitics\", \"Type\": \"0\", \"Title\": \"Использовать упрощенную генерацию номера билета\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Использовать упрощенную генерацию номера билета\", \"RegularMask\": \"\"}, {\"Name\": \"FN.AgentTypeDefault\", \"Type\": \"2\", \"Title\": \"Параметр агента по умолчанию\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Если данный параметр задан, то поле агент в чеке всегда будет принимать данное значение\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.ContractorTelNum\", \"Type\": \"2\", \"Title\": \"Телефон поставщика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.CommissionAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон комиссионера\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.PaymentAgentTelNum\", \"Type\": \"2\", \"Title\": \"Телефон платёжного агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierName\", \"Type\": \"2\", \"Title\": \"Название перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Навание перевозчика, если не задано, используется имя владельца ККТ\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityMainScreenPaymentPanelStyle\", \"Type\": \"0\", \"Title\": \"Стиль отображения кнопок вариантов оплаты\", \"Params\": {\"Max\": \"10\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Стиль отображения кнопок вариантов оплаты\", \"RegularMask\": \"\"}, {\"Name\": \"FN.CloudFiscalOperatorBindingId\", \"Type\": \"2\", \"Title\": \"Идентификатор ОФД для облака\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" 99\", \"Required\": 0, \"Description\": \"Идентификатор ОФД для облака\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityTempHideFiscalOperationsForDriver\", \"Type\": \"3\", \"Title\": \"ВРЕМЕННОЕ поле\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"ВРЕМЕННОЕ отключение закрытия смены и кассовых операций у водителя\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityDefaultCarrierInn\", \"Type\": \"2\", \"Title\": \"ИНН перевозчика\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"ИНН перевозчика\", \"RegularMask\": \"\"}, {\"Name\": \"FN.Agent.AgentName\", \"Type\": \"2\", \"Title\": \"Наименование  агента\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Наименование агента\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionHoldDays\", \"Type\": \"0\", \"Title\": \"Время хранения транзакций в днях.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Время хранения транзакций в днях.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityMinBuyTicketCount\", \"Type\": \"0\", \"Title\": \"Минимальное количество билетов для продажи\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Минимальное количество билетов для продажи\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityRouteUpdateTime\", \"Type\": \"0\", \"Title\": \"Интервал обновления остановок в сек.\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"15\", \"Required\": 0, \"Description\": \"Интервал обновления остановок в сек.\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalStatusBarExpandEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки  статус бара\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки статус бара\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalKeyHomeEnabled\", \"Type\": \"3\", \"Title\": \"Состояние блокировки home\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Состояние блокировки home\", \"RegularMask\": \"\"}, {\"Name\": \"Default.EnableLastReceiptCopyPrinting\", \"Type\": \"3\", \"Title\": \"Возможность печати копии последнего билета\", \"Params\": {}, \"Default\": \"1\", \"Required\": 0, \"Description\": \"Возможность печати копии последнего билета\", \"RegularMask\": \"\"}, {\"Name\": \"NFC.EnableFieldOnTicketSelectionScreen\", \"Type\": \"3\", \"Title\": \"Включить поле на экране выбора количества билетов\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включить поле на экране выбора количества билетов\", \"RegularMask\": \"\"}, {\"Name\": \"Default.UseExtendTicketReport\", \"Type\": \"3\", \"Title\": \"Печать расширенного отчета о проданных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать расширенного отчета о проданных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CancelOutgoWhenShiftClosed\", \"Type\": \"3\", \"Title\": \"Автосброс занарядки при закрытии смены\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Автосброс занарядки при закрытии смены\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityPrintSegmentDates\", \"Type\": \"3\", \"Title\": \"Печать даты отправления / прибытия в пригородных билетах\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Печать даты отправления / прибытия в пригородных билетах\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PrintExtOpenShift\", \"Type\": \"3\", \"Title\": \"Печатать расширенную информацию после открытия смены\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Печатать расширенную информацию после открытия смены\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.PaymentDialogType\", \"Type\": \"0\", \"Title\": \"Тип UI для экв. ядра\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип UI для экв. ядра\", \"RegularMask\": \"\"}, {\"Name\": \"Default.CityEnablePassengerData\", \"Type\": \"3\", \"Title\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение эксп. функции ввода данных пассажира в пригороде\", \"RegularMask\": \"\"}, {\"Name\": \"FN.FFDVersionCode\", \"Type\": \"2\", \"Title\": \"Код версии ФФД, строкой\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"3\", \"Required\": 0, \"Description\": \"Код версии ФФД (2 или не задан для 1.05, 3 для 1.1)\", \"RegularMask\": \"\"}, {\"Name\": \"EMV.AccessCode\", \"Type\": \"2\", \"Title\": \"Пин-код для авторизации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пин-код для авторизации\", \"RegularMask\": \"\"}, {\"Name\": \"Stat.TransactionsSendPeriod\", \"Type\": \"0\", \"Title\": \"Периодичность сбора и отправки пакета данных transactions\", \"Params\": {\"Max\": \"\", \"Min\": \"60\"}, \"Default\": \"120\", \"Required\": 0, \"Description\": \"Периодичность сбора и отправки пакета данных transactions\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalSettingsPwd\", \"Type\": \"2\", \"Title\": \"Пароль для входа в настройки AQSI\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль для входа в настройки AQSI\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TerminalTimeSyncMode\", \"Type\": \"0\", \"Title\": \"Тип синхронизации времени на терминале и сервере\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Тип синхронизации времени на терминале и сервере\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityAutoCloseSellScreenEnable\", \"Type\": \"3\", \"Title\": \"Включение режима ускоренной работы экрана продажи\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Включение режима ускоренной работы экрана продажи (ТЕСТОВЫЙ ФУНКЦИОНАЛ)\", \"RegularMask\": \"\"}, {\"Name\": \"UI.CityAutoCloseSellScreenDelay\", \"Type\": \"0\", \"Title\": \"Интервал авто закрытия экрана (сек.) продажи в ускоренном режиме\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"2\", \"Required\": 0, \"Description\": \"Интервал авто закрытия экрана продажи в ускоренном режиме (сек.)\", \"RegularMask\": \"\"}, {\"Name\": \"Default.PersonalTaskAutoSelect\", \"Type\": \"3\", \"Title\": \"Использовать авто выбор рейса\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Использовать авто выбор рейса\", \"RegularMask\": \"\"}, {\"Name\": \"Default.ReservedField1\", \"Type\": \"3\", \"Title\": \"Зарезервированное поле для временных значений\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Зарезервированное поле для временных значений\", \"RegularMask\": \"\"}, {\"Name\": \"FN.PrintCloudQR\", \"Type\": \"3\", \"Title\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"Params\": {}, \"Default\": \"true\", \"Required\": 0, \"Description\": \"Флаг отвечающий за печать QR в  режиме облачной фискализации\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionSendInterval\", \"Type\": \"0\", \"Title\": \"Интервал посылки транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"480\", \"Required\": 0, \"Description\": \"Интервал посылки транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"Default.TransactionCollectInterval\", \"Type\": \"0\", \"Title\": \"Интервал сбора транзакций\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"300\", \"Required\": 0, \"Description\": \"Интервал сбора транзакций\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.WorkMode\", \"Type\": \"0\", \"Title\": \"Режим работы валидатора\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Режим работы валидатора\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerList\", \"Type\": \"2\", \"Title\": \"Список IP адресов для серверов валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Список IP адресов для серверов валидации\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.EnableKrsServer\", \"Type\": \"3\", \"Title\": \"Индикация включения сервера для валидации на устройстве\", \"Params\": {}, \"Default\": \"false\", \"Required\": 0, \"Description\": \"Индикация включения сервера для валидации на устройстве\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.CfgCrc\", \"Type\": \"0\", \"Title\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Ожидаемая контрольная сумма конфигурации устройства\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.FwVersion\", \"Type\": \"2\", \"Title\": \"Ожидаемая версия прошивки устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Ожидаемая версия прошивки устройства\", \"RegularMask\": \"\"}, {\"Name\": \"VTK.ChildDevices\", \"Type\": \"2\", \"Title\": \"Конфигурация дочерних терминалов\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Конфигурация дочерних терминалов\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Address\", \"Type\": \"2\", \"Title\": \"Адрес сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Адрес сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Port\", \"Type\": \"0\", \"Title\": \"Порт сервера обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.User\", \"Type\": \"2\", \"Title\": \"Идентификатор пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Идентификатор пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.Pwd\", \"Type\": \"2\", \"Title\": \"Пароль пользователя для входа на сервер обновлений\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Пароль пользователя для входа на сервер обновлений\", \"RegularMask\": \"\"}, {\"Name\": \"TMS.AppVer\", \"Type\": \"2\", \"Title\": \"Версия ПО актуальная для устройства\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Версия ПО актуальная для устройства\", \"RegularMask\": \"\"}, {\"Name\": \"KRS.KrsServerPort\", \"Type\": \"0\", \"Title\": \"Порт сервера валидации\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \"0\", \"Required\": 0, \"Description\": \"Порт сервера валидации\", \"RegularMask\": \"\"}, {\"Name\": \"TransportCard.CfgJson\", \"Type\": \"2\", \"Title\": \"Персонализованная конфигурация для транспортной карты\", \"Params\": {\"Max\": \"\", \"Min\": \"\"}, \"Default\": \" \", \"Required\": 0, \"Description\": \"Персонализованная конфигурация для транспортной карты\", \"RegularMask\": \"\"}]', 0, NULL, '2022-01-23 10:30:09', 3);

ALTER TABLE `TerminalGroup` AUTO_INCREMENT=4;

INSERT INTO `city` (`city_id`, `city_type_id`, `country_id`, `region`, `district`, `title`, `title_genitive`, `title_accusative`, `title_prepositional`, `title_lat`, `title_genitive_lat`, `title_accusative_lat`, `title_prepositional_lat`, `lng`, `lat`, `city_title_search`, `is_verified`, `code`, `region_id`) VALUES
(1, 1, 177, '', '', 'Москва', NULL, NULL, NULL, 'moskva', NULL, NULL, NULL, '37.622513', '55.75322', 'Москва', 1, NULL, NULL),
(2, 1, 177, 'Московская область', '', 'Бронницы', NULL, NULL, NULL, 'bronnicy', NULL, NULL, NULL, '38.264243', '55.42565', 'Бронницы', 1, NULL, NULL),
(3, 1, 177, 'Московская область', '', 'Воскресенск', NULL, NULL, NULL, 'voskresensk', NULL, NULL, NULL, '38.673362', '55.322978', 'Воскресенск', 1, NULL, NULL),
(4, 1, 177, 'Московская область', '', 'Егорьевск', NULL, NULL, NULL, 'egorevsk', NULL, NULL, NULL, '39.035842', '55.383055', 'Егорьевск', 1, NULL, NULL);

ALTER TABLE `city` AUTO_INCREMENT=5;

INSERT INTO `station` (`station_id`, `city_id`, `title`, `description`, `title_genitive`, `title_accusative`, `title_prepositional`, `title_lat`, `title_genitive_lat`, `title_accusative_lat`, `title_prepositional_lat`, `lng`, `lat`, `address`, `phone`, `site`, `okato`, `is_verified`, `code`, `hash`, `region_id`) VALUES
(1, 1, 'Холмогорская улица', NULL, NULL, NULL, NULL, 'holmogorskaya-ulica', NULL, NULL, NULL, '37.72086365323308', '55.87864288787607', '', NULL, NULL, NULL, 1, NULL, NULL, NULL),
(2, 1, 'ВДНХ', NULL, NULL, NULL, NULL, 'vdnh', NULL, NULL, NULL, '37.6415554155073', '55.82123687529568', '', NULL, NULL, NULL, 1, NULL, NULL, NULL),
(3, 1, 'м. Проспект Мира', NULL, NULL, NULL, NULL, 'm-prospekt-mira', NULL, NULL, NULL, '37.63363893044377', '55.78157502403599', '', NULL, NULL, NULL, 1, NULL, NULL, NULL),
(4, 2, 'АС Бронницы', NULL, NULL, NULL, NULL, 'as-bronnicy', NULL, NULL, NULL, '38.263607007805284', '55.42477308808494', '', NULL, NULL, NULL, 1, NULL, NULL, NULL),
(5, 3, 'Автовокзал Воскресенск', NULL, NULL, NULL, NULL, 'avtovokzal-voskresensk', NULL, NULL, NULL, '38.68490086318203', '55.32526911249417', '', NULL, NULL, NULL, 1, NULL, NULL, NULL),
(6, 4, 'АВ Егорьевск', NULL, NULL, NULL, NULL, 'av-egorevsk', NULL, NULL, NULL, '39.00986748789978', '55.39081771931744', '', NULL, NULL, NULL, 1, NULL, NULL, NULL);

ALTER TABLE `station` AUTO_INCREMENT=7;

INSERT INTO `xref_city_partner` (`id`, `partner_id`, `city_id`, `outer_id`, `outer_title`, `source`, `datetime_merge`, `is_start`, `is_end`, `as_active`, `is_deleted`) VALUES
(1, 1, 1, '1', 'Москва', '{\"city_id\":\"1\",\"city_title\":\"Москва\",\"region_title\":\"\",\"district_title\":\"\",\"country_id\":\"177\",\"lat\":\"55.75322\",\"lng\":\"37.622513\",\"country_title\":\"Россия\",\"country_iso3166\":\"RU\"}', '2021-11-11 18:48:41', 1, 1, 1, 0),
(2, 1, 2, '2', 'Бронницы', '{\"city_id\":\"2\",\"city_title\":\"Бронницы\",\"region_title\":\"Московская область\",\"district_title\":\"\",\"country_id\":\"177\",\"lat\":\"55.42565\",\"lng\":\"38.264243\",\"country_title\":\"Россия\",\"country_iso3166\":\"RU\"}', NULL, 1, 1, 1, 0),
(3, 1, 3, '3', 'Воскресенск', '{\"city_id\":\"3\",\"city_title\":\"Воскресенск\",\"region_title\":\"Московская область\",\"district_title\":\"\",\"country_id\":\"177\",\"lat\":\"55.322978\",\"lng\":\"38.673362\",\"country_title\":\"Россия\",\"country_iso3166\":\"RU\"}', NULL, 1, 1, 1, 0),
(4, 1, 4, '4', 'Егорьевск', '{\"city_id\":\"4\",\"city_title\":\"Егорьевск\",\"region_title\":\"Московская область\",\"district_title\":\"\",\"country_id\":\"177\",\"lat\":\"55.383055\",\"lng\":\"39.035842\",\"country_title\":\"Россия\",\"country_iso3166\":\"RU\"}', NULL, 0, 1, 1, 0);

ALTER TABLE `xref_city_partner` AUTO_INCREMENT=5;

INSERT INTO `xref_station_partner` (`id`, `partner_id`, `outer_city_id`, `outer_station_id`, `outer_station_title`, `station_id`) VALUES
(1, 1, '1', '1', 'Холмогорская улица', 1),
(2, 1, '1', '2', 'ВДНХ', 2),
(3, 1, '1', '3', 'м. Проспект Мира', 3),
(4, 1, '2', '4', 'АС Бронницы', 4),
(5, 1, '3', '5', 'Автовокзал Воскресенск', 5),
(6, 1, '4', '6', 'АВ Егорьевск', 6);

ALTER TABLE `xref_station_partner` AUTO_INCREMENT=7;
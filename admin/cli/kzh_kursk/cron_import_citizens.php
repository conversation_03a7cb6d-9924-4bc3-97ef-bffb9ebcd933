<?php

use App\Core\Infrastructure\Logger\ConsoleLogger;
use core\app;
use lib\Avn\KZh\CitizensImportService;
use lib\Avn\KZh\FtpService;
use lib\Avn\KZh\Repository\KZhConnectionDb;
use lib\Avn\KZh\Repository\KZhWriteoffBindingDb;
use lib\Avn\SocialCategoryDb;
use lib\AvnTypes\EMVAbonementListDb;
use Psr\Log\LogLevel;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Starting citizens import...');

// TODO
$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new DateTime($app->args->time_limit);

    if ($time_limit <= (new DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$ftpTimeout = !empty($app->args->ftp_timeout) ? (int)$app->args->ftp_timeout : 30;

// TODO
$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

$logger = new ConsoleLogger();
if (!$vvv) {
    $logger->setLevelVisibility(LogLevel::DEBUG, false);
}

if ($dry_run) {
    $app->log('Dry run not implemented', 'FAILURE');
    exit;
//    $app->log('Dry run: no actual changes will be made', 'WARNING');
}


if (empty($app->args->writeoffs_schedule_id)) {
    $app->log('No writeoffs_schedule_id parameter is set', 'FAILURE');
    exit;
}
if (empty($app->args->default_writeoff_id)) {
    $app->log('No default_writeoff_id parameter is set', 'FAILURE');
    exit;
}

$writeoffsScheduleId = (int)$app->args->writeoffs_schedule_id;

$defaultWriteoffId = (int)$app->args->default_writeoff_id;

$escortWriteoffId = (int)$app->args->escort_writeoff_id;
if (empty($escortWriteoffId)) {
    $app->log('No escort_writeoff_id parameter is set', 'WARNING');
    $escortWriteoffId = $defaultWriteoffId;
}

try {
    $connectionsDb = new KZhConnectionDb();
    $emissionImportModel = $app->emission_import_model;
    $socialCategoryDb = new SocialCategoryDb();
    $kZhWriteoffBindingDb = new KZhWriteoffBindingDb();
    $abonementListDb = new EMVAbonementListDb();

    $connections = $connectionsDb->getActiveConnections();

    foreach ($connections as $connection) {
        /** @var FtpService $ftpService */
        $ftpService = $app->logWithTimer('connecting to ftp', function () use ($connection, $ftpTimeout) {
            return new FtpService(
                $connection->host,
                $connection->port,
                $connection->ssl,
                $connection->login,
                $connection->password,
                $connection->path,
                $ftpTimeout
            );
        });

        $importService = new CitizensImportService(
            $connection,
            $ftpService,
            $emissionImportModel,
            $socialCategoryDb,
            $kZhWriteoffBindingDb,
            $abonementListDb,
            $defaultWriteoffId,
            $writeoffsScheduleId,
            $escortWriteoffId,
            $logger,
            true
        );

        $app->logWithTimer('importing citizens', function () use ($importService) {
            $importService->import();
        });
    }
} catch (Exception $e) {
    $app->log('Error during citizens import. ' . $e->getMessage(), 'FAILURE');
}

if ($dry_run) {
    $app->log('Dry run: no actual changes were made', 'WARNING');
}

$app->log('Import completed.');

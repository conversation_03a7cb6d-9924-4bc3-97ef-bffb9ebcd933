<?php

use App\Core\Infrastructure\Logger\ConsoleLogger;
use core\app;
use lib\Avn\KZh\FtpService;
use lib\Avn\KZh\PrivilegesImportService;
use lib\Avn\KZh\Repository\KZhConnectionDb;
use lib\Avn\ReferenceLib;
use lib\Avn\SocialCategoryDb;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Starting privileges import...');

$ftpTimeout = !empty($app->args->ftp_timeout) ? (int)$app->args->ftp_timeout : 30;

$dry_run = !empty($app->args->dry_run);

if ($dry_run) {
    $app->log('Dry run not implemented', 'FAILURE');
    exit;
//    $app->log('Dry run: no actual changes will be made', 'WARNING');
}

$logger = new ConsoleLogger();

try {
    $connectionsDb = new KZhConnectionDb();
    $referenceLib = new ReferenceLib();
    $socialCategoryDb = new SocialCategoryDb();

    $connections = $connectionsDb->getActiveConnections();

    foreach ($connections as $connection) {
        /** @var FtpService $ftpService */
        $ftpService = $app->logWithTimer('connecting to ftp', function () use ($connection, $ftpTimeout) {
            return new FtpService(
                $connection->host,
                $connection->port,
                $connection->ssl,
                $connection->login,
                $connection->password,
                $connection->path,
                $ftpTimeout
            );
        });

        $importService = new PrivilegesImportService(
            $connection,
            $ftpService,
            $referenceLib,
            $socialCategoryDb,
            $logger
        );

        $app->logWithTimer('importing privileges', function () use ($importService) {
            return $importService->import();
        });
    }
} catch (Exception $e) {
    $app->log('Error during privileges import. ' . $e->getMessage(), 'FAILURE');
}

if ($dry_run) {
    $app->log('Dry run: no actual changes were made', 'WARNING');
}

$app->log('Import completed.');

<?php

use App\Core\Infrastructure\Logger\ConsoleLogger;
use core\app;
use lib\Avn\KZh\FtpService;
use lib\Avn\KZh\Repository\KZhConnectionDb;
use lib\Avn\KZh\UidsImportService;
use lib\AvnTypes\EMVAbonementListDb;
use Psr\Log\LogLevel;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/../..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Starting uids import...');

// TODO
$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new DateTime($app->args->time_limit);

    if ($time_limit <= (new DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$ftpTimeout = !empty($app->args->ftp_timeout) ? (int)$app->args->ftp_timeout : 30;

if (empty($app->args->writeoffs_id)) {
    $app->log('No writeoffs_id parameter is set', 'FAILURE');
    exit;
}
if (empty($app->args->writeoffs_schedule_id)) {
    $app->log('No writeoffs_schedule_id parameter is set', 'FAILURE');
    exit;
}

$writeoffsId = (int)$app->args->writeoffs_id;
$writeoffsScheduleId = (int)$app->args->writeoffs_schedule_id;

// TODO
$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

$logger = new ConsoleLogger();
if (!$vvv) {
    $logger->setLevelVisibility(LogLevel::DEBUG, false);
}

if ($dry_run) {
    $app->log('Dry run not implemented', 'FAILURE');
    exit;

//    $app->log('Dry run: no actual changes will be made', 'WARNING');
}

try {
    $connectionsDb = new KZhConnectionDb();
    $emissionImportModel = $app->emission_import_model;
    $abonementListDb = new EMVAbonementListDb();

    $connections = $connectionsDb->getActiveConnections();

    foreach ($connections as $connection) {
        /** @var FtpService $ftpService */
        $ftpService = $app->logWithTimer('connecting to ftp', function () use ($connection, $ftpTimeout) {
            return new FtpService(
                $connection->host,
                $connection->port,
                $connection->ssl,
                $connection->login,
                $connection->password,
                $connection->path,
                $ftpTimeout
            );
        });

        $importService = new UidsImportService(
            $connection,
            $ftpService,
            $emissionImportModel,
            $abonementListDb,
            $writeoffsId,
            $writeoffsScheduleId,
            $logger
        );

        $app->logWithTimer('importing uids', function () use ($importService) {
            return $importService->import();
        });
    }
} catch (Exception $e) {
    $app->log('Error during uids import. ' . $e->getMessage(), 'FAILURE');
}

if ($dry_run) {
    $app->log('Dry run: no actual changes were made', 'WARNING');
}

$app->log('Import completed.');

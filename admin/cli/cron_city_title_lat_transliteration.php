<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=admin.unitiki.int', 'FAILURE');
    die();
}

$city_list = $app->city_model->get_city_list_without_title_lat();

foreach ($city_list as $city) {
    $city->title_lat = \helper\common::translit($city->title, false, false, true);
}

$same_city_title_lat_list = $app->city_model->get_same_city_title_lat_list($city_list);
$i = 0;
foreach ($city_list as $city) {
    if (in_array($city->title_lat, $same_city_title_lat_list)) {
        $app->log('city_id #' . $city->city_id . ' ' . $city->title_lat . ' has not unique title!');
        continue;
    }

    $app->db->update_ignore('city', [
        'title_lat' => $city->title_lat,
    ], 'city_id = ' . (int) $city->city_id);

    $same_city_title_lat_list[] = $city->title_lat;
    $i++;
}

$app->log('Количество городов с обновленным title_lat: ' . $i);

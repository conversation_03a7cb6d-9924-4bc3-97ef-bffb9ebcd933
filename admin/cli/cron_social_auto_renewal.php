<?php

use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\EMV\EMVWriteOffsTemplateDB;
use lib\Avn\EMV\EMVWriteOffsTemplateScheduleDB;
use lib\Avn\Mifare\ABTCardList;
use lib\Avn\Mifare\ABTCardListDb;
use lib\AvnTypes\EMVWriteOffsTemplate;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var \core\app $app */

$app->log('Starting social abonements renewal...');

$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new \DateTime($app->args->time_limit);

    if ($time_limit <= (new \DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$limit = !empty($app->args->limit) ? (int)$app->args->limit : 5000;

$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

if (!$vvv) {
    $app->setLogLevelVisibility('NOTE', false);
}

try {
    if (!$app->options_model->getOptionValue('social_templates_auto_renewal') && !$dry_run) {
        throw new Exception('Option “social_templates_auto_renewal” is disabled');
    }


    $emvWriteOffsTemplateDb = new EMVWriteOffsTemplateDB();
    $abtCardListDb = new ABTCardListDb();
    $abnLib = new EMVAbonementLib();
    $emvWriteOffsTemplateScheduleDbLib = new EMVWriteOffsTemplateScheduleDB();

    /** @var EMVWriteOffsTemplate[] $renewableTemplates */
    $renewableTemplates = $app->logWithTimer('fetching renewable templates', function () use ($emvWriteOffsTemplateDb) {
        return $emvWriteOffsTemplateDb->getRenewableTemplates();
    });

    $renewableTemplatesIds = array_map(fn ($template) => $template->Id, $renewableTemplates);
    $schedules = $emvWriteOffsTemplateScheduleDbLib->getScheduleByTemplate($renewableTemplatesIds);

    $actualSchedules = array_filter($schedules, function ($schedule) {
        $now = new DateTime();
        $sellDateTimeStart = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['sell_date_time_start']);
        $sellDateTimeEnd = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['sell_date_time_end']);
        $validTimeEnd = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['valid_time_end']);

        if (
            $sellDateTimeStart <= $now &&
            ($sellDateTimeEnd >= $now || is_null($sellDateTimeEnd)) &&
            ($validTimeEnd >= $now || is_null($validTimeEnd))
        ) {
            return true;
        }
        return false;
    });

    $templateScheduleArr = [];
    foreach ($actualSchedules as $schedule) {
        $templateScheduleArr[$schedule['template_id']][] = $schedule['id'];
    }

    if (empty($renewableTemplates) || empty($actualSchedules)) {
        throw new Exception('No renewable templates found');
    }
    $app->log('Found ' . count($renewableTemplates) . ' templates', 'INFO');

    /** @var ABTCardList[] $cardsToRenew */
    $cardsToRenew = $app->logWithTimer(
        'fetching cards to renew',
        function () use ($abtCardListDb, $renewableTemplates) {
            return $abtCardListDb->GetSocialCardsToRenew($renewableTemplates);
        }
    );
    if (empty($cardsToRenew)) {
        throw new Exception('No cards to renew found');
    }
    $app->log('Found ' . count($cardsToRenew) . ' cards to renew', 'INFO');


    $renewedCount = 0;

    $app->logWithTimer(
        'renewing cards',
        function () use (
            $app,
            $renewableTemplates,
            $cardsToRenew,
            $abtCardListDb,
            $abnLib,
            $limit,
            $time_limit,
            $dry_run,
            $templateScheduleArr,
            &$renewedCount
        ) {
            foreach ($cardsToRenew as $card) {
                if ($time_limit !== null && new DateTime() > $time_limit) {
                    $app->log('Time limit reached, stopping', 'WARNING');
                    break;
                }

                if ($limit !== null && $renewedCount > $limit) {
                    $app->log('Cards limit reached, stopping', 'WARNING');
                    break;
                }


                $templates = array_filter($renewableTemplates, function ($template) use ($card) {
                    $templateCategories = $template->getAllowedSocialCategories();
                    if ($templateCategories === null) {
                        return true;
                    }

                    $cardCategories = $card->getSocialCategoriesAvailable();
                    if ($cardCategories === null) {
                        return true;
                    }

                    if (array_intersect($templateCategories, $cardCategories)) {
                        return true;
                    }

                    return false;
                });

                if (count($templates) > 1) {
                    $app->log(
                        'Card ' . ($card->PAN ?? $card->UID) . ' has more than one template available to renew (ids ' . implode(
                            ', ',
                            array_column(
                                $templates,
                                'Id'
                            )
                        ) . '), skipping',
                        'WARNING'
                    );
                    continue;
                }

                if (
                    (count($templates) === 1 && count(
                        $templateScheduleArr[$templates[array_key_first($templates)]->Id]
                    ) > 1)
                ) {
                    $app->log(
                        'Card ' . ($card->PAN ?? $card->UID) . ' has more than one schedule available to renew (ids '
                        . implode(', ', $templateScheduleArr[$templates[array_key_first($templates)]->Id])
                        . '), skipping',
                        'WARNING'
                    );
                    continue;
                }

                if (count($templates) === 0) {
                    $app->log(
                        'Card ' . ($card->PAN ?? $card->UID) . ' has no templates available to renew, skipping',
                        'WARNING'
                    );
                    continue;
                }

                /** @var EMVWriteOffsTemplate $template */
                $template = reset($templates);

                if ($dry_run) {
                    $app->log(
                        'dry_run: renewing card ' . ($card->PAN ?? $card->UID) . ' with template ' . $template->Id,
                        'NOTE'
                    );
                    continue;
                }


                try {
                    $abonementId = $app->logWithTimer(
                        'renewing card ' . ($card->PAN ?? $card->UID) . ' with template ' . $template->Id,
                        function () use ($abnLib, $card, $template, $abtCardListDb, $templateScheduleArr) {
                            $abnLib->ClearError();

                            $abonement = $abnLib->Set(
                                0,
                                $template->Id,
                                $card->PANHash,
                                null,
                                null,
                                $card->PAN,
                                $card->UID,
                                $card->UIDHash,
                                null,
                                null,
                                $templateScheduleArr[$template->Id][0]
                            );

                            if ($abnLib->IsError()) {
                                throw new Exception(
                                    $abnLib->GetLastError(),
                                    ConstApiError::Code(ConstApiError::BadParams)
                                );
                            }

                            return $abonement->AbonementId;
                        },
                        'NOTE'
                    );
                    $app->log('New abonement id: ' . $abonementId, 'NOTE');

                    $renewedCount++;
                } catch (Exception $e) {
                    $app->log(
                        'Error while renewing card ' . ($card->PAN ?? $card->UID) . ' with template ' . $template->Id . ': ' . $e->getMessage(),
                        'FAILURE'
                    );
                }
            }
        }
    );
} catch (Exception $e) {
    $app->log('Error during social abonements renewal. ' . $e->getMessage(), 'FAILURE');
}


$app->log('Social abonements renewal completed.');

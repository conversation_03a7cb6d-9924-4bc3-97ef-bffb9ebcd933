<?php

/**
 * Created by PhpStorm.
 * User: 1
 * Date: 23.03.2019
 * Time: 20:23
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var \core\app $app */

$app->log('Start process csv files...');

$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new \DateTime($app->args->time_limit);
    if (!($time_limit instanceof \DateTime) || $time_limit <= (new \DateTime())) {
        $time_limit = null;
    }
}

try {
    $app->emission_import_model->parse_csv_files($time_limit);
} catch (Exception $e) {
    $app->log("Error during parsing csv files. " . $e->getMessage(), 'FAILURE');
}



$app->log('Process csv files completed.');

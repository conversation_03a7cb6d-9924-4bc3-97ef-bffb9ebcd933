<?php

use App\Core\Infrastructure\Logger\ConsoleLogger;
use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\EMV\EMVWriteOffsTemplateDB;
use lib\Avn\EMV\EMVWriteOffsTemplateScheduleDB;
use lib\Avn\Mifare\ABTCardList;
use lib\Avn\Mifare\ABTCardListDb;
use lib\AvnTypes\EMVWriteOffsTemplate;
use lib\Constants\ConstApiError;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var \core\app $app */

$app->log('Starting social abonements creating...');

$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new \DateTime($app->args->time_limit);

    if (!($time_limit instanceof \DateTime) || $time_limit <= (new \DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$limit = !empty($app->args->limit) ? (int)$app->args->limit : null;

$pageSize = !empty($app->args->page_size) ? (int)$app->args->page_size : 500;

$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

if (!$vvv) {
    $app->setLogLevelVisibility('NOTE', false);
}

$templateId = !empty($app->args->template) ? (int)$app->args->template : null;


try {
    $logger = new ConsoleLogger();
    $logger->setLevelVisibility('debug', $vvv);

    $emvWriteOffsTemplateDb = new EMVWriteOffsTemplateDB();
    $abtCardListDb = new ABTCardListDb($logger);
    $abnLib = new EMVAbonementLib();
    $emvWriteOffsTemplateScheduleDbLib = new EMVWriteOffsTemplateScheduleDB();

    if (empty($templateId)) {
        throw new Exception('No “template” parameter is set');
    }

    /** @var ?EMVWriteOffsTemplate $template */
    $template = $emvWriteOffsTemplateDb->Get($templateId);

    if (empty($template)) {
        throw new Exception('Template not found');
    }

    $app->log('Found template “' . $template->Name . '”', 'INFO');

    $schedules = $emvWriteOffsTemplateScheduleDbLib->getScheduleByTemplate([$template->Id]);
    $actualSchedules = array_filter($schedules, function ($schedule) {
        $now = new DateTime();
        $sellDateTimeStart = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['sell_date_time_start']);
        $sellDateTimeEnd = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['sell_date_time_end']);
        $validTimeEnd = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['valid_time_end']);

        if (
            $sellDateTimeStart <= $now &&
            ($sellDateTimeEnd >= $now || is_null($sellDateTimeEnd)) &&
            ($validTimeEnd >= $now || is_null($validTimeEnd))
        ) {
            return true;
        }

        return false;
    });
    if (count($actualSchedules) == 0) {
        throw new Exception('Actual schedules has not been found');
    }
    if (count($actualSchedules) > 1) {
        throw new Exception('2 actual schedules has been found');
    }

    $offset = 0;
    $page = 0;

    while (true) {
        $cardsReturnedInChunk = 0;

        /** @var ABTCardList[] $cards */
        $cards = $app->logWithTimer(
            'fetching cards',
            function () use ($abtCardListDb, $template, $actualSchedules, $pageSize, $offset, &$cardsReturnedInChunk) {
                return $abtCardListDb->getSocialCardsToCreateAbonements(
                    $template,
                    array_values($actualSchedules)[0],
                    $pageSize,
                    $offset,
                    $cardsReturnedInChunk
                );
            }
        );

        if ($cardsReturnedInChunk === 0) {
            $app->log('No cards returned, stopping', 'WARNING');
            break;
        }

        $app->log('Found ' . count($cards) . ' cards on page ' . $page, 'INFO');

        if (empty($cards)) {
            $page++;
            $offset = $offset + $pageSize;

            continue;
        }

        $actualTemplateSchedule = array_values($actualSchedules)[0]['id'];

        $createdCount = 0;

        $app->logWithTimer(
            'creating cards',
            function () use (
                $app,
                $cards,
                $abtCardListDb,
                $abnLib,
                $template,
                $limit,
                $time_limit,
                $dry_run,
                $actualTemplateSchedule,
                &$createdCount
            ) {
                foreach ($cards as $card) {
                    if ($time_limit !== null && new DateTime() > $time_limit) {
                        $app->log('Time limit reached, stopping', 'WARNING');
                        break;
                    }

                    if ($limit !== null && $createdCount > $limit) {
                        $app->log('Cards limit reached, stopping', 'WARNING');
                        break;
                    }


                    if ($dry_run) {
                        $app->log(
                            'dry_run: creating abonement for card ' . $card->PAN ?? $card->UID,
                            'NOTE'
                        );
                        continue;
                    }


                    try {
                        $abonementId = $app->logWithTimer(
                            'creating abonement for card ' . $card->PAN ?? $card->UID . ' with template ' . $template->Id,
                            function () use ($abnLib, $card, $template, $abtCardListDb, $actualTemplateSchedule) {
                                $abnLib->ClearError();

                                $abonement = $abnLib->Set(
                                    0,
                                    $template->Id,
                                    $card->PANHash,
                                    null,
                                    null,
                                    $card->PAN,
                                    $card->UID,
                                    $card->UIDHash,
                                    null,
                                    null,
                                    $actualTemplateSchedule
                                );

                                if ($abnLib->IsError()) {
                                    throw new Exception(
                                        $abnLib->GetLastError(),
                                        ConstApiError::Code(ConstApiError::BadParams)
                                    );
                                }

                                if ($template->Type == EMVAbonementLib::TYPE_WALLET) {
                                    return $abonement->WalletId;
                                }

                                return $abonement->AbonementId;
                            },
                            'NOTE'
                        );
                        $app->log('New abonement id: ' . $abonementId, 'NOTE');

                        $createdCount++;
                    } catch (Exception $e) {
                        $app->log(
                            'Error while creating abonement for card ' . $card->PAN ?? $card->UID . ': ' . $e->getMessage(),
                            'FAILURE'
                        );
                    }
                }
            }
        );

        $page++;
        $offset = $offset + $pageSize;
    }
} catch (Exception $e) {
    $app->log('Error during social abonements creating. ' . $e->getMessage(), 'FAILURE');
}


$app->log('Social abonements creating completed.');

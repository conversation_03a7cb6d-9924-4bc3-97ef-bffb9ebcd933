<?php

use core\app;
use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\EMV\EMVStopList;
use lib\Avn\EMV\EMVStopListDb;
use lib\Avn\PanHashAlgorithmDb;
use lib\Modules\Options\Options;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Starting stoplist recalculating...');

$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new DateTime($app->args->time_limit);

    if ($time_limit <= (new DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$limit = !empty($app->args->limit) ? (int)$app->args->limit : 5000;

$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

if (!$vvv) {
    $app->setLogLevelVisibility('NOTE', false);
}

if ($dry_run) {
    $app->log('Dry run: no actual changes will be made', 'WARNING');
}

try {
    $now = new DateTime();

    $abnLib = new EMVAbonementLib();
    $options = new Options();
    $emvStopListDb = new EMVStopListDB();


    $app->log('Removing expired abonements...', 'INFO');
    $abonementsWalletsWhiteList = $app->logWithTimer('fetching white list', function () use ($emvStopListDb) {
        return $emvStopListDb->GetActiveAbonementList();
    });

    foreach ($abonementsWalletsWhiteList as $abonementWalletRecord) {
        if ($time_limit !== null && new DateTime() > $time_limit) {
            $app->log('Time limit reached, stopping', 'WARNING');
            break;
        }

        $abonementWalletRecord = (new EMVStopList())->Set($abonementWalletRecord);

        if (in_array($abonementWalletRecord->Type, EMVStopListDb::TYPES_WHITE_WALLET)) {
            $wallet = $abnLib->GetWallet($abonementWalletRecord->AbonementId);

            if (
                empty($wallet)
                || $wallet->IsActive == 0
                || ((float)$wallet->Balance) < ((float)$wallet->LowLimit)
                || (!empty($wallet->ValidTimeStart) && ((new DateTime($wallet->ValidTimeStart)) > $now))
            ) {
                if (!$dry_run) {
                    $emvStopListDb->UpdateRemoveAbonementRecord($abonementWalletRecord->Id, 5);
                }

                $app->log('Removed wallet record ' . $abonementWalletRecord->Id, 'NOTE');
            }
        } else {
            $abonement = $abnLib->GetAbonement($abonementWalletRecord->AbonementId);

            if (
                empty($abonement)
                || (!empty($abonement->ValidTimeStart) && ((new DateTime($abonement->ValidTimeStart)) > $now))
            ) {
                if (!$dry_run) {
                    $emvStopListDb->UpdateRemoveAbonementRecord($abonementWalletRecord->Id);
                }

                $app->log('Removed abonement record ' . $abonementWalletRecord->Id . ' (ValidTimeStart > now)', 'NOTE');
            } else {
                if ($abonement->IsActive == 0) {
                    if (!$dry_run) {
                        $emvStopListDb->UpdateRemoveAbonementRecord($abonementWalletRecord->Id, $abonement->BlockedReason);
                    }

                    $app->log('Removed abonement record ' . $abonementWalletRecord->Id . ' (IsActive = 0)', 'NOTE');
                }
            }
        }
    }

    unset($abonementsWalletsWhiteList);

    if ($time_limit !== null && new DateTime() > $time_limit) {
        throw new Exception('Time limit reached, stopping');
    }


    $app->log('Adding new abonements...', 'INFO');
    $algorithmIds = (new PanHashAlgorithmDb())->getIdsForWhiteList();
    $activeAbonementList = $algorithmIds
        ? $abnLib->GetActiveAbonementListWithCustomHashes($algorithmIds, 64)
        : $abnLib->GetActiveAbonementList();

    $abonementsSellTimesByIdForFind = [];
    $balanceByAbonementId = [];
    foreach ($activeAbonementList as $abonement) {
        if (isset($abonement->UID)) {
            $idForFind = strtolower($abonement->UID);
        } elseif (isset($abonement->PANHash)) {
            $idForFind = strtolower($abonement->PANHash);
        } else {
            $idForFind = strtolower($abonement->getCustomPANHash());
        }
        $abonementPeriod = $abonement->ValidTimeStart . '_' . $abonement->ValidTimeEnd;
        $counters = $abnLib->getAbonementCounters($abonement->AbonementId);

        if (!isset($abonementsSellTimesByIdForFind[$idForFind])) {
            $abonementsSellTimesByIdForFind[$idForFind] = [];
        }
        if (!isset($abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod])) {
            $abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod] = [];
        }

        $abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod][$abonement->AbonementId] = DateTime::createFromFormat('Y-m-d H:i:s', $abonement->SellDateTime);

        $balanceByAbonementId[$abonement->AbonementId] = $emvStopListDb->MakeAbonementTripCounters($counters);
    }

    foreach ($activeAbonementList as $abonement) {
        if ($time_limit !== null && new DateTime() > $time_limit) {
            $app->log('Time limit reached, stopping', 'WARNING');
            break;
        }

        $app->log('Checking abonement ' . $abonement->AbonementId . '... ', 'NOTE');

        if (!empty($abonement->ValidTimeStart) && ((new DateTime($abonement->ValidTimeStart)) > $now)) {
            $app->log('Abonement ' . $abonement->AbonementId . ' is not valid yet, skipping', 'NOTE');

            continue;
        }

        if (!empty($abonement->ValidTimeEnd)) {
            if (new DateTime($abonement->ValidTimeEnd) < $now && $abonement->IsActive == 1) {
                if (!$dry_run) {
                    $abnLib->BlockAbonementWallet(
                        $abonement,
                        EMVAbonementLib::BLOCK_REASON_UNKNOWN,
                        EMVAbonementLib::BLOCK_STATUS_EXPIRED
                    );
                }

                $app->log('Abonement ' . $abonement->AbonementId . ' is expired, blocked it', 'NOTE');

                continue;
            }
        }


        $type = null;
        $idForFind = null;
        $abonementPeriod = $abonement->ValidTimeStart . '_' . $abonement->ValidTimeEnd;
        $app->log('Checking abonement ' . $abonement->AbonementId . ' for period ' . $abonementPeriod, 'NOTE');

        $type = $abonement->getStopListType();
        $idForFind = $abonement->getMainCredentials();

        $app->log('Checking abonement ' . $abonement->AbonementId . ' by id ' . $idForFind, 'NOTE');

        $balance = $balanceByAbonementId[$abonement->AbonementId];
        $app->log('Abonement ' . $abonement->AbonementId . ' balance: ' . $balance, 'NOTE');

        if (count($abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod]) > 1) {
            asort($abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod]);
            $abonementsIds = array_keys($abonementsSellTimesByIdForFind[$idForFind][$abonementPeriod]);

            $app->log('AbonementsIds in period and id [0] ' . $abonementsIds[0], 'NOTE');
            $app->log('AbonementsIds in period and id [1] ' . $abonementsIds[1], 'NOTE');

            if ($abonementsIds[0] === (int)$abonement->AbonementId) {
                $app->log('Abonement ' . $abonement->AbonementId . ' is bought first for its period for card ' . $idForFind, 'NOTE');

                // это первый купленный абонемент, если баланс 0, то удаляется
                if ($balance === '#1$0') {
                    $app->log('Abonement ' . $abonement->AbonementId . ' counter is used up, blocking...' . $idForFind, 'NOTE');

                    if (!$dry_run) {
                        $abnLib->BlockAbonementWallet(
                            $abonement,
                            EMVAbonementLib::BLOCK_REASON_UNKNOWN,
                            EMVAbonementLib::BLOCK_STATUS_USED
                        );
                    }
                }
            } elseif ($abonementsIds[1] === (int)$abonement->AbonementId) {
                $app->log('Abonement ' . $abonement->AbonementId . ' is bought second for its period for card ' . $idForFind, 'NOTE');

                // это второй купленный абонемент, если баланс первого ноль, то второй добавляется в стоп лист
                if ($balanceByAbonementId[$abonementsIds[0]] !== '#1$0') {
                    $app->log('Abonement ' . $abonement->AbonementId . ' is bought after ' . $abonementsIds[0] . ' which is not used up yet, skipping', 'NOTE');
                    continue;
                }
            } else {
                $app->log('Abonement ' . $abonement->AbonementId . ' is not bought first nor second for its period for card ' . $idForFind . ', skipping', 'NOTE');
                continue;
            }
        }

        $old = $emvStopListDb->FindMultiplyWithArrays([
            'PANHash' => $idForFind,
            'IsActual' => 1,
            'Type' => [
                EMVStopListDb::TYPE_BLACK_PANHASH,
                EMVStopListDb::TYPE_WHITE_ABONEMENT,
                EMVStopListDb::TYPE_WHITE_ABONEMENT_SOCIAL,
                EMVStopListDb::TYPE_WHITE_WALLET,
                EMVStopListDb::TYPE_WHITE_WALLET_UID,
                EMVStopListDb::TYPE_BLACK_UID,
                EMVStopListDb::TYPE_WHITE_ABONEMENT_UID,
                EMVStopListDb::TYPE_WHITE_ABONEMENT_GOST_PANHASH,
            ]
        ]);
        $emvStopListDb->ClearError();

        if (!empty($old) && count($old) > 0) {
            $record = (new EMVStopList())->Set($old[0]);
            $app->log('Existing record with id ' . $record->Id . ' found', 'NOTE');

            if (
                $record->Type == EMVStopListDb::TYPE_BLACK_PANHASH
                || $record->Type == EMVStopListDb::TYPE_BLACK_UID
                || $record->IsBlocked > 0
                || $record->Balance !== $balance
            ) {
                if (!$dry_run) {
                    $emvStopListDb->InsertStopListRecord(
                        $idForFind,
                        0,
                        $type,
                        $abonement->AbonementId,
                        date('Y-m-d H:i:s'),
                        $abonement->WriteOffsId,
                        $balance,
                        $abonement->ValidTimeMaximum
                    );
                }

                $app->log('New record inserted, PANHash = ' . $idForFind, 'NOTE');
            }
        } else {
            if (!$dry_run) {
                $emvStopListDb->InsertStopListRecord(
                    $idForFind,
                    0,
                    $type,
                    $abonement->AbonementId,
                    date('Y-m-d H:i:s'),
                    $abonement->WriteOffsId,
                    $balance,
                    $abonement->ValidTimeMaximum
                );
            }

            $app->log('New record inserted, PANHash = ' . $idForFind, 'NOTE');
        }
    }

    unset($activeAbonementList);

    if ($time_limit !== null && new DateTime() > $time_limit) {
        throw new Exception('Time limit reached, stopping');
    }


    $app->log('Adding new wallets...', 'INFO');
    $activeWalletList = $abnLib->GetActiveWalletList();

    foreach ($activeWalletList as $wallet) {
        if ($time_limit !== null && new DateTime() > $time_limit) {
            $app->log('Time limit reached, stopping', 'WARNING');
            break;
        }

        $app->log('Checking wallet ' . $wallet->WalletId . '...', 'NOTE');

        if ((float)$wallet->Balance < (float)$wallet->LowLimit) {
            $app->log('Wallet ' . $wallet->WalletId . ' balance is less than LowLimit, skipping', 'NOTE');
            continue;
        }

        if (!empty($wallet->ValidTimeStart) && (new DateTime($wallet->ValidTimeStart)) > $now) {
            $app->log('Wallet ' . $wallet->WalletId . ' is not valid yet, skipping', 'NOTE');
            continue;
        }


        $type = null;
        $idForFind = null;

        if (isset($wallet->UID)) {
            $type = EMVStopListDb::TYPE_WHITE_WALLET_UID;
            $idForFind = strtolower($wallet->UID);
        } else {
            $type = EMVStopListDb::TYPE_WHITE_WALLET;
            $idForFind = strtolower($wallet->PANHash);
        }


        // поиск только по картам с кошельками
        // так как транс карты и банк карты хранятся по-разному (хэш и открытый уид), искать можно среди всех
        $old = $emvStopListDb->FindMultiplyWithArrays([
            'PANHash' => $idForFind,
            'IsActual' => 1,
            'Type' => [
                EMVStopListDb::TYPE_BLACK_PANHASH,
                EMVStopListDb::TYPE_WHITE_ABONEMENT,
                EMVStopListDb::TYPE_WHITE_ABONEMENT_SOCIAL,
                EMVStopListDb::TYPE_WHITE_WALLET,
                EMVStopListDb::TYPE_WHITE_WALLET_UID,
                EMVStopListDb::TYPE_BLACK_UID,
                EMVStopListDb::TYPE_WHITE_ABONEMENT_UID,
            ]
        ]);
        $emvStopListDb->ClearError();

        if (!empty($old) && count($old) > 0) {
            $record = (new EMVStopList())->Set($old[0]);
            $app->log('Existing record with id ' . $record->Id . ' found', 'NOTE');

            if (
                $record->Type == EMVStopListDb::TYPE_BLACK_PANHASH
                || $record->Type == EMVStopListDb::TYPE_BLACK_UID
                || $record->IsBlocked > 0
                || $record->Balance !== $wallet->Balance
            ) {
                if (!$dry_run) {
                    $emvStopListDb->InsertStopListRecord(
                        $idForFind,
                        0,
                        $type,
                        $wallet->WalletId,
                        date('Y-m-d H:i:s'),
                        $wallet->WriteOffsId,
                        $wallet->Balance,
                        $wallet->ValidTimeEnd
                    );
                }

                $app->log('New record inserted, PANHash = ' . $idForFind, 'NOTE');
            }
        } else {
            if (!$dry_run) {
                $emvStopListDb->InsertStopListRecord(
                    $idForFind,
                    0,
                    $type,
                    $wallet->WalletId,
                    date('Y-m-d H:i:s'),
                    $wallet->WriteOffsId,
                    $wallet->Balance,
                    $wallet->ValidTimeEnd
                );
            }

            $app->log('New record inserted, PANHash = ' . $idForFind, 'NOTE');
        }

        if (!$dry_run) {
            $app->log('Setting sl_last_time_update...', 'NOTE');
            $options->set_option('sl_last_time_update', date('Y-m-d H:i:s'));
        }
    }

    unset($activeWalletList);
} catch (Exception $e) {
    $app->log('Error during stoplist recalculating. ' . $e->getMessage(), 'FAILURE');
}

if ($dry_run) {
    $app->log('Dry run: no actual changes were made', 'WARNING');
}

$app->log('Stoplist recalculating completed.');

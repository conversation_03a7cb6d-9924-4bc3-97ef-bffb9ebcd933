<?php

use App\StopList\Console\Command\RecalculateCommand;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */


// ВРЕМЕННЫЙ КОСТЫЛЬ ДЛЯ УДАЛЕНИЯ ДУБЛИКАТОВ =======================================

$app->log('Start');

$ch = 10000;
$table = "EMVStopList";

$AbonementIdsRaw = $app->db->query('SELECT PANHash , COUNT(PANHash) AS ca
FROM EMVStopList
WHERE IsActual=1
GROUP BY PANHash
HAVING ca > 1')->result();

$PANHashs = array_map(function ($item) {
    return $item->PANHash;
}, $AbonementIdsRaw);

$count = 0;
$i = 0;
$total = 0;
//$app->db->transaction_start();
foreach ($PANHashs as $PANHash) {
    $i++;
    $ids = $app->db->query("SELECT Id FROM $table WHERE PANHash = '$PANHash'
    ORDER BY Id DESC")->result();
    unset($ids[0]);
    $total += count($ids);
    $app->log("Iteration - $i  Update PANHash=$PANHash rows deleted - " . count($ids));
    if (count($ids) > 0) {
        $id = array_map(function ($item) {
            return $item->Id;
        }, $ids);

        $chunkId = array_chunk($id, $ch);
        $count++;
        foreach ($chunkId as $item) {
            $item = implode(',', $item);
            $app->db->query("DELETE FROM $table WHERE Id IN ($item)");
            if ($count > 100) {
                //$app->db->transaction_commit();
                $count = 0;
            }
        }

    }
}

$app->log("Delete TOTAL " . $total);

// =============================================================================

(new RecalculateCommand($app))->execute();

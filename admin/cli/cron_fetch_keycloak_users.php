<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

use lib\Modules\ExternalUserLib\Keycloak\KeycloakClient;

/**
 * @var $app core\app
 */
$app->log('Starting Keycloak synchronizing users...');

if (empty($app->args->http_host)) {
    $app->log('No parameter http_host. Example --http_host=api.local.unitiki.int', 'FAILURE');
    die();
}

// Используем новую централизованную систему конфигурации
$keycloakConfig = \lib\Cfg\Cfg::$admin['keycloak'];
if ($keycloakConfig['is_enabled'] === true) {
    try {
        // init keycloak client
        $keycloakClient = new KeycloakClient(
            $keycloakConfig['base_url'],
            $keycloakConfig['realm'],
            $keycloakConfig['client_id'],
            $keycloakConfig['client_secret']
        );

        if ($keycloakClient->serviceIsAvailable() === true) {
            // try auth in keycloak
            $keycloakClient->authentication($keycloakClient::CLIENT_CREDENTIALS_GRANT_TYPE_VALUE);

            // get info about oauth2 client
            $client = $keycloakClient->getMyClient();

            // get total count users
            $total = $keycloakClient->getUsersCount();

            if ($total > 0) {
                // get users
                $keycloakUsers = $keycloakClient->getUsers(['max' => $total]);

                $keycloakUsers = array_map(static function ($user) use ($keycloakClient, $client): array {
                    // get list all associate roles include (not) composite roles
                    $roles = $keycloakClient->getMyCompositeRoles($user['id'], $client['id']);

                    /**
                     * Skipping composite roles, because they are holding not composite associate roles.
                     * E.g: composite role holding list not-composite roles like `tickets_manage`, `pay_view`, e.t.c
                     */
                    $filterableRoles = array_values(array_filter($roles, fn ($role) => $role['composite'] === false));
                    $user['roles'] = array_map(fn ($role) => $role['name'], $filterableRoles);

                    return $user;
                }, $keycloakUsers);

                $filterableKeycloakUsers = array_values(array_filter($keycloakUsers, fn ($user) => empty($user['roles']) === false));

                if ($filterableKeycloakUsers === []) {
                    $app->log('Nothing synchronizing', 'FAILURE');
                    die();
                }

                $keycloakUsernames = implode(', ', array_map(fn ($keycloakUser) => $app->db->quoteValue($keycloakUser['username']), $filterableKeycloakUsers));

                // Mark as is_deleted early saved keycloak users, which deleted now in keycloak
                $app->db->update(
                    'admin_user',
                    ['is_deleted' => true],
                    sprintf('`is_deleted` = 0 AND `is_keycloak` = 1 AND `login` NOT IN (%s);', $keycloakUsernames)
                );

                foreach ($filterableKeycloakUsers as $keycloakUser) {
                    $name = isset($keycloakUser['firstName']) && isset($keycloakUser['lastName'])
                        ? sprintf('%s %s', $keycloakUser['firstName'], $keycloakUser['lastName'])
                        : $keycloakUser['username'];

                    // Check user already exist ?
                    $user = $app->user_admin_model->get_item_by_params([
                        'login' => $keycloakUser['username'],
                    ]);

                    if (!$user) {
                        $userId = $app->user_admin_model->add([
                            'login' => $keycloakUser['username'],
                            'is_keycloak' => true,
                            'is_deleted' => !$keycloakUser['enabled'],
                            'name' => $name,
                            'password' => ''
                        ]);
                    } else {
                        $userId = $user->user_id;
                        $app->user_admin_model->edit($userId, [
                            'is_deleted' => !$keycloakUser['enabled'],
                            'name' => $name,
                            'is_keycloak' => true
                        ]);
                    }
                    $app->user_admin_model->set_roles_user($userId, $keycloakUser['roles']);
                }
                $app->log('List keycloak users was successfully fetched', 'SUCCESS');
            } else {
                $app->log('Nothing synchronizing', 'FAILURE');
                die();
            }
        } else {
            $app->log('Service not available. Please check connection', 'FAILURE');
            die();
        }
    } catch (Exception $exception) {
        $app->log($exception->getMessage(), 'FAILURE');
        die();
    }
}

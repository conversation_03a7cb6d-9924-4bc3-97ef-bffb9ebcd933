<?php

declare(strict_types=1);

use core\app;
use lib\Modules\ABTCardList\EmissionImportProcessor;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
/**
 * @var app $app
 */
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=admin.unitiki.int', 'FAILURE');
    die();
}

if (!isset($app->args->path)) {
    $app->log('Не указан путь для импорта. Например --path=/pdk/sdbp.local/path_to_file.csv', 'FAILURE');
    die();
}

/** @var string $file */
$file = $app->args->path;

try {
    $service = new EmissionImportProcessor();
    $app->logWithTimer('process', fn () => $service->process($file));
} catch (Exception $exception) {
    $app->log($exception->getMessage(), 'FAILURE');
    die;
}

$app->log('Successfully imported', 'SUCCESS');
die;

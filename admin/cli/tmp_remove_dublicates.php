<?php

use App\Card\Console\Command\ImportCommand;
use core\app;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Start');

$ch = $app->args->ch ?? 1000;
$table = $app->args->table ?? "EMVStopList";

$AbonementIdsRaw = $app->db->query('SELECT PANHash , COUNT(PANHash) AS ca
FROM EMVStopList
WHERE IsActual=1
GROUP BY PANHash
HAVING ca > 1')->result();

$PANHashs = array_map(function ($item) {
    return $item->PANHash;
}, $AbonementIdsRaw);

$count = 0;
$i = 0;
$total = 0;
//$app->db->transaction_start();
foreach ($PANHashs as $PANHash) {
    $i++;
    $ids = $app->db->query("SELECT Id FROM $table WHERE PANHash = '$PANHash'
    ORDER BY Id DESC")->result();
    unset($ids[0]);
    $total += count($ids);
    $app->log("Iteration - $i  Update PANHash=$PANHash rows deleted - " . count($ids));
    if (count($ids) > 0) {
        $id = array_map(function ($item) {
            return $item->Id;
        }, $ids);

        $chunkId = array_chunk($id, $ch);
        $count++;
        foreach ($chunkId as $item) {
            $item = implode(',', $item);
            $app->db->query("DELETE FROM $table WHERE Id IN ($item)");
            if ($count > 100) {
                //$app->db->transaction_commit();
                $count = 0;
            }
        }

    }
}

$app->log("Delete TOTAL " . $total);

/*

$AbonementIdsRaw = $app->db->query('SELECT AbonementId FROM EMVAbonementList WHERE UID IN (
     SELECT DISTINCT card_uid FROM golden_crown_transactions
)')->result();

$AbonementIds = array_map(function ($item) {
    return $item->AbonementId;
}, $AbonementIdsRaw);

$count = 0;
$i = 0;
//$app->db->transaction_start();
foreach ($AbonementIds as $abonementId) {
    $i++;
    $ids = $app->db->query("SELECT Id FROM $table WHERE AbonementId = $abonementId
    ORDER BY Id DESC")->result();
    unset($ids[0]);
    $app->log("Iteration - $i  Update AbonementId=$abonementId rows deleted - " . count($ids));
    if (count($ids) > 0) {
        $id = array_map(function ($item) {
            return $item->Id;
        }, $ids);

        $chunkId = array_chunk($id, $ch);
        $count++;
        foreach ($chunkId as $item) {
            $item = implode(',', $item);
            $app->db->query("DELETE FROM $table WHERE Id IN ($item)");
            if ($count > 100) {
                //$app->db->transaction_commit();
                $count = 0;
            }
        }

    }
}*/

//$app->db->transaction_commit();

<?php

use core\app;
use lib\Avn\EMV\EMVAbonementLib;
use lib\Avn\EMV\EMVTransferAbonementCountersTravelsLib;
use lib\Avn\EMV\EMVWriteOffsTemplateDB;
use lib\Avn\Mifare\ABTCardListDb;
use lib\Constants\ConstApiError;
use lib\Rtl\DrvDB;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

/** @var app $app */

$app->log('Starting abonements creating...');

$time_limit = null;
if (!empty($app->args->time_limit)) {
    $time_limit = new DateTime($app->args->time_limit);

    if (!($time_limit instanceof DateTime) || $time_limit <= (new DateTime())) {
        $app->log('Invalid “time_limit” parameter is set, using no limit', 'WARNING');
        $time_limit = null;
    }
}

$limit = !empty($app->args->limit) ? (int)$app->args->limit : 50000;

$dry_run = !empty($app->args->dry_run);

$vvv = !empty($app->args->vvv);

if (!$vvv) {
    $app->setLogLevelVisibility('NOTE', false);
}

try {
    $emvWriteOffsTemplateDb = new EMVWriteOffsTemplateDB();
    $abtCardListDb = new ABTCardListDb();
    $abnLib = new EMVAbonementLib();
    $abnTransferLib = new EMVTransferAbonementCountersTravelsLib($abnLib);


    $cardsToImport = $app->logWithTimer(
        'fetching cards',
        function () {
            $db = new DrvDB();
            $db->query = 'SELECT * FROM __import_poehali WHERE status = "Активен" AND code_social = 0';
            $db->Send();
            return $db->FetchAll();
        }
    );
    $app->log('Found ' . count($cardsToImport) . ' cards to import', 'INFO');

    $createdCount = 0;

    $app->logWithTimer(
        'creating abonements',
        function () use (
            $app,
            $abnLib,
            &$abnTransferLib,
            $cardsToImport,
            $limit,
            $time_limit,
            $dry_run,
            &$createdCount
        ) {
            foreach ($cardsToImport as $card) {
                $card = (object)$card;

                if ($time_limit !== null && new DateTime() > $time_limit) {
                    $app->log('Time limit reached, stopping', 'WARNING');
                    break;
                }

                if ($limit !== null && $createdCount > $limit) {
                    $app->log('Cards limit reached, stopping', 'WARNING');
                    break;
                }


                try {
                    $panhash = hash('sha256', $card->pan_card);

                    $template = null;
                    $startDate = null;
                    $endDate = null;

                    if (
                        in_array($card->type, [
                        'На 3 дня. Саратов',
                        'На 30 дней. Саратов',
                        'На 7 дней. Саратов',
                        'На 10 дней. Саратов',
                        'На 1 день. Саратов',
                        'На 20 дней. Саратов',
                        ])
                    ) {
                        $template = 18;

                        $startDate = (new DateTimeImmutable())->setTime(0, 0, 0);
                        $days = (int)explode(' ', $card->type)[1] + 1;
                        $endDate = (new DateTimeImmutable())->add(new DateInterval('P' . $days . 'D'))->setTime(0, 0, 0);
                    }

                    if ($card->type === 'На 5 поездок. Саратов') {
                        $template = 17;
                        $startDate = new DateTimeImmutable('2022-11-01 00:00:00');
                        $endDate = new DateTimeImmutable('2022-12-01 00:00:00');
                    }
                    if ($card->type === 'На 10 поездок. Саратов') {
                        $template = 11;
                        $startDate = new DateTimeImmutable('2022-11-01 00:00:00');
                        $endDate = new DateTimeImmutable('2022-12-01 00:00:00');
                    }
                    if ($card->type === 'На 20 поездок. Саратов') {
                        $template = 12;
                        $startDate = new DateTimeImmutable('2022-11-01 00:00:00');
                        $endDate = new DateTimeImmutable('2022-12-01 00:00:00');
                    }


                    if ($template === null) {
                        $app->log('Not found template for type “' . $card->type . '”, skipping', 'WARNING');
                        continue;
                    }


                    $duplicate = $abnLib->getDuplicateRow(
                        EMVAbonementLib::TYPE_ABONEMENT,
                        null,
                        null,
                        $card->pan_card,
                        $panhash,
                        $startDate ? $startDate->format('Y-m-d H:i:s') : null,
                        $endDate ? $endDate->format('Y-m-d H:i:s') : null
                    );

                    if ($duplicate !== false) {
                        $app->log('Duplicate found for card ' . $card->pan_card . ', skipping', 'WARNING');
                        continue;
                    }


                    if ($dry_run) {
                        $app->log(
                            'dry_run: creating abonement for card ' . $card->pan_card,
                            'NOTE'
                        );
                        continue;
                    }


                    $abonementId = $app->logWithTimer(
                        'creating abonement for card ' . $card->pan_card . ' with template ' . $template,
                        function () use ($abnLib, $card, $panhash, $template, $startDate, $endDate) {
                            $abnLib->ClearError();

                            $abonement = $abnLib->Set(
                                0,
                                $template,
                                $panhash,
                                null,
                                null,
                                $card->pan_card,
                                null,
                                null
                            );

                            if ($abnLib->IsError()) {
                                throw new Exception(
                                    $abnLib->GetLastError(),
                                    ConstApiError::Code(ConstApiError::BadParams)
                                );
                            }

                            if ($abonement->IsActive == 0) {
                                $abnLib->UpdateAbonement($abonement, [
                                    'IsActive' => 1,
                                ]);
                            }

                            if ($startDate !== null && $endDate !== null) {
                                $abnLib->UpdateAbonement($abonement, [
                                    'ValidTimeStart' => $startDate->format('Y-m-d H:i:s'),
                                    'ValidTimeDays' => $endDate->diff($startDate)->days,
                                    'ValidTimeEnd' => $endDate->format('Y-m-d H:i:s'),
                                    'ValidTimeMaximum' => $endDate->format('Y-m-d H:i:s'),
                                ]);
                            }

                            return $abonement->AbonementId;
                        },
                        'NOTE'
                    );

                    // Отключено для Саратова
                    //$transferAbonement = $abnLib->getAbonementById($abonementId);
                    //$abnTransferLib->transfer($transferAbonement);

                    $app->log('New abonement id: ' . $abonementId, 'NOTE');

                    $createdCount++;
                } catch (Exception $e) {
                    $app->log(
                        'Error while creating abonement for card ' . $card->pan_card . ': ' . $e->getMessage(),
                        'FAILURE'
                    );
                }
            }
        }
    );
} catch (Exception $e) {
    $app->log('Error during abonements creating. ' . $e->getMessage(), 'FAILURE');
}


$app->log('Abonements creating completed.');

<?php

use lib\Avn\EMV\EMVWriteOffsTemplateDB;
use lib\Avn\EMV\EMVWriteOffsTemplateScheduleDB;



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=--http_host=api.gds.int', 'FAILURE');
    die();
}

$app->log('Starting add schedule in writeOffTemplate...');

try {
    $writeOffsClient = new EMVWriteOffsTemplateDB();
    $writeOffsScheduleClient = new EMVWriteOffsTemplateScheduleDB();

    $templateList = $writeOffsClient->getActualTemplates();
    $writeOffsScheduleList = $writeOffsScheduleClient->GetAllRecords();

    $nextSchedule = null;
    $now = new DateTime();
    $afterTwoDays = (new DateTime())->modify('+ 2 days');
    foreach ($writeOffsScheduleList as $schedule) {
        $saleDateTimeStart = DateTime::createFromFormat('Y-m-d H:i:s', $schedule['sell_date_time_start']);

        if ($saleDateTimeStart > $now && $saleDateTimeStart < $afterTwoDays) {
            $nextSchedule = $schedule;
        }
    }

    if (!is_null($nextSchedule)) {
        foreach ($templateList as $template) {
            if ($template->IsAddSchedule == 1) {
                $scheduleTemplates = $app->emv_model->getTemplateSchedule($template->Id);
                if (!in_array($nextSchedule['id'], $scheduleTemplates)) {
                    $app->emv_model->addScheduleTemplate(['template_id' => $template->Id, 'schedule_id' => $nextSchedule['id']]);
                }
            }
        }
    }
} catch (Exception $e) {
    $app->log('Error add schedule in writeOffTemplate. ' . $e->getMessage(), 'FAILURE');
}

$app->log('Add schedule in writeOffTemplate completed.');

<?php

/**
 * @var $app core\app
 */



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=admin.unitiki.int', 'FAILURE');
    die();
}

$station_list = $app->station_model->get_station_list_without_title_lat();

foreach ($station_list as $station) {
    $station->title_lat = \helper\common::translit($station->title, false, false, true);
    $station->title_lat = ucfirst(strtolower($station->title_lat));
}

$same_station_title_lat_list = $app->station_model->get_same_station_title_lat_list($station_list);

$station_title_lat_by_city = [];
foreach ($same_station_title_lat_list as $station_id => $item) {
    $same_title_lat = ucfirst(strtolower($item->title_lat));
    $station_title_lat_by_city[$item->city_id][$same_title_lat] = true;
}

$i = 0;
foreach ($station_list as $station) {
    if (isset($station_title_lat_by_city[$station->city_id][$station->title_lat])) {
        $app->log('city_id #' . $station->city_id . ', station_id #' . $station->station_id . ' ' . $station->title_lat . ' has not unique title!');
        continue;
    }

    if (empty($station->title_lat)) {
        $app->log(PHP_EOL . 'city_id #' . $station->city_id . ', station_id #' . $station->station_id . ' ' . $station->title_lat . ' station title lat is empty!' . PHP_EOL);
        continue;
    }

    $app->db->update_ignore('station', [
        'title_lat' => $station->title_lat,
    ], 'station_id = ' . (int) $station->station_id);

    // добавляем в массив город - станцию, чтобы потом проверять на совпадение одинаковых станций в пределах города
    $station_title_lat_by_city[$station->city_id][$station->title_lat] = true;
    $i++;
}

$app->log('Количество станций с обновленным title_lat: ' . $i);

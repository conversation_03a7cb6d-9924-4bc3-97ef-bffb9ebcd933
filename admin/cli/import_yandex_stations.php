<?php



$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');
if (!isset($app->args->http_host)) {
    $app->log('Не указан параметр http_host. Например --http_host=admin.unitiki.int', 'FAILURE');
    die();
}

if (!isset($app->args->file)) {
    $app->log('Не указан параметр file. Например /pdk/yandex_stations.json', 'FAILURE');
    die();
}

$filename = $app->args->file;

if (!file_exists($filename)) {
    $app->log('Файл ' . $filename . ' не найден', 'FAILURE');
    die();
}

$yandexStations = \json_decode((string)file_get_contents($filename));
if (empty($yandexStations)) {
    $app->log('Не удалось загрузить станции из файла ' . $filename, 'FAILURE');
    die();
}

$all_stations_count = 0;
foreach ($yandexStations->countries as $country) {
    foreach ($country->regions as $region) {
        foreach ($region->settlements as $city) {
            $all_stations_count += count($city->stations);
        }
    }
}

echo 'Найдено ' . $all_stations_count . ' станций' . PHP_EOL;

$current_station_count = 0;
foreach ($yandexStations->countries as $country) {
    $countryTitle = $country->title;
    $countryCode = $country->codes->yandex_code ?? null;
    foreach ($country->regions as $region) {
        $regionTitle = $region->title;
        $regionCode = $region->codes->yandex_code ?? null;
        foreach ($region->settlements as $city) {
            $cityTitle = $city->title;
            $cityCode = $city->codes->yandex_code ?? null;
            foreach ($city->stations as $station) {
                $stationTitle = $station->title;
                $stationCode = $station->codes->yandex_code;
                $stationEsrCode = $station->codes->esr_code ?? null;
                $stationDirection = $station->direction;
                $stationType = $station->station_type;
                $stationTransportType = $station->transport_type;
                $stationLongitude = $station->longitude;
                $stationLatitude = $station->latitude;
                $stationSummary = $countryTitle . " " . $countryCode . ";" . $regionTitle . ";" . $regionCode . ";" . $cityTitle . ";" . $cityCode . ";" . $stationTitle . ";" . $stationCode;
                $app->db->insert("YandexStation", [
                    "CountryTitle" => $countryTitle,
                    "CountryCode" => $countryCode,
                    "RegionTitle" => $regionTitle,
                    "RegionCode" => $regionCode,
                    "CityTitle" => $cityTitle,
                    "CityCode" => $cityCode,
                    "StationDirection" => $stationDirection,
                    "StationCode" => $stationCode,
                    "StationEsrCode" => $stationEsrCode,
                    "StationType" => $stationType,
                    "StationTitle" => $stationTitle,
                    "StationLongitude" => $stationLongitude,
                    "StationLatitude" => $stationLatitude,
                    "StationTransportType" => $stationTransportType,
                    "StationSummary" => $stationSummary
                ]);
                $current_station_count++;
                echo "\rОбработано " . ((int)($current_station_count / $all_stations_count * 100.0)) . "% (" . $current_station_count . " из " . $all_stations_count . ")";
            }
        }
    }
}

echo PHP_EOL;

<?php

$config->database = (object) array(
    'mysql' => (object) array(
       'host' => 'db54dev-node1.tkp2.prod',
        'user' => 'gds-user',
        'password' => 'ZB4AhOD@2QmoXRX6GLr03',
        'database' => 'sdbp54.prod',
    ),
    'debug' => true,
);

$config->url_crm = 'http://admin.sdbp.54dev.tkp2.prod';
$config->hash_ignore = true;

//логирование активностей
$config->stat_search_log = false;                            //логирование попытки поиска

//разбиение для вставки в sql лог внешних апи
$config->outer_api_log_sql_chunk_size = 1;
$config->email_ticket_refund_dev = false;


$config->unitiki_api = new stdClass();
$config->unitiki_api->url = 'http://api.sdbp.54dev.tkp2.prod';
$config->unitiki_api->agent_id = 1;
$config->unitiki_api->secret_key = '';
$config->unitiki_api->has_log = false; // Логирование запросов к API в файл api_log.txt

$config->telegram_fail_send=true;//отпрaвлять или нет уведомления о неудачах в телегу
$config->telegram_token = '*********************************************';
$config->telegram_fail_chat_id = "-1001393055234";
$config->telegram_fiscalization_error_chat_id="-1001743283039";
$config->telegram_buyfail_chat_id="-1001743283039";
$config->telegram_balance_chat_id="-1001743283039";

require_once($_SERVER['DOCUMENT_ROOT'] . '/config/partners/develop.php');

//сколько транзакций отдавать в НСИ за один запрос при синхронизации
$config->max_tt_request_count=250;

$config->default_agent_id = 6;

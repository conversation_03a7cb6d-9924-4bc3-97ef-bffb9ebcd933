# cron_emission_import.php - Card Emission Import

**File Path:** `admin/cli/cron_emission_import.php`  
**Type:** Card management and emission processing cron script  
**Status:** Active - Important for card lifecycle management  

## Purpose and Logic Description

The `cron_emission_import.php` script handles the import and processing of card emission data from external sources. This script is responsible for managing the lifecycle of cards in the system, including new card issuance, card updates, and integration with external emission systems. It processes emission data to ensure cards are properly registered and available for use in the transportation payment system.

## Schedule/Frequency

**Execution Interval:** Every 10 minutes (`*/10 * * * *`)  
**Priority:** Medium-High - Important for card availability and customer service  
**Business Impact:** Delays in card emission affect customer onboarding and card replacement  

## Inputs/Outputs

### Required Parameters
- `--http_host` - Admin host (e.g., `admin.sdbp.54dev.tkp2.prod`)

### Input Sources
- External emission system APIs
- Card manufacturer data feeds
- Manual emission requests from admin interface
- Card replacement and renewal requests
- Bulk card issuance files

### Output Destinations
- **Primary Output:** Card database tables with new emission records
- **Log Output:** `/tkp2/sdbp54devprod/log/cron_emission_import.log`
- **Integration Output:** Updates to card management systems
- **Notification Output:** Alerts for successful/failed emissions

## Processing Logic

### Data Flow
1. **Source Data Retrieval:** Fetches emission data from external systems
2. **Data Validation:** Validates card data format and integrity
3. **Duplicate Detection:** Prevents duplicate card registrations
4. **Card Registration:** Creates new card records in the system
5. **Status Updates:** Updates card status and availability
6. **Integration Sync:** Synchronizes with related systems

### Card Types Handled
- EMV smart cards
- Contactless payment cards
- Transportation abonement cards
- Special purpose cards (student, senior, etc.)
- Replacement and renewal cards

## Dependencies

### External Systems
- Card emission and manufacturing systems
- External card issuer APIs
- Card personalization services
- Inventory management systems

### Internal Dependencies
- Card database tables (ABTCardList, EMVCardList, etc.)
- Admin interface for manual operations
- Logging infrastructure
- Card validation libraries
- Emission workflow management

### Related Scripts
- `admin/cli/import_emission_go.php` - Alternative emission import utility
- `api/cli/api_action.php` - May trigger card-related actions
- Card management and validation scripts

## Performance Characteristics

### Processing Volume
- Handles batch imports of card emission data
- Processes individual card requests
- Manages high-volume card issuance periods

### Resource Usage
- Database write-intensive operations
- Network communication with external systems
- Memory usage for batch processing
- File I/O for emission data files

### Scalability Considerations
- 10-minute interval allows for reasonable processing time
- Batch processing capabilities for efficiency
- Error handling for partial failures
- Queue management for high-volume periods

## Error Handling and Recovery

### Common Error Scenarios
1. **External System Unavailability:** Emission system downtime
2. **Data Format Issues:** Invalid card data from external sources
3. **Duplicate Card Errors:** Attempting to register existing cards
4. **Database Constraints:** Violation of card data integrity rules
5. **Network Connectivity Issues:** Communication failures with external systems

### Recovery Mechanisms
- Retry logic for temporary failures
- Error queuing for manual intervention
- Partial processing capability
- Data validation and sanitization
- Transaction rollback for failed operations

## Monitoring and Alerting

### Key Metrics to Monitor
- Number of cards processed per execution
- Success/failure rates for card imports
- Processing time per batch
- External system response times
- Error patterns and frequencies

### Alert Conditions
- High failure rates in card import
- External system connectivity issues
- Processing time exceeding normal thresholds
- Database constraint violations
- Unusual patterns in emission requests

## Security Considerations

### Data Protection
- Card data contains sensitive personal information
- PCI DSS compliance requirements
- Encryption for card data transmission and storage
- Access controls for emission data
- Audit trails for all card operations

### System Security
- Secure communication with external emission systems
- Authentication and authorization for API access
- Data integrity validation
- Secure handling of card credentials

## Business Impact

### Customer Experience
- Timely card availability for new customers
- Quick processing of card replacements
- Reduced wait times for card activation
- Improved customer service response times

### Operational Efficiency
- Automated card lifecycle management
- Reduced manual intervention requirements
- Streamlined emission processes
- Better inventory management

## Integration Points

### Upstream Systems
- Card manufacturing and personalization systems
- Customer registration systems
- Card ordering and inventory management
- External emission service providers

### Downstream Systems
- Card activation and validation services
- Customer account management
- Transaction processing systems
- Card status tracking and reporting

## Maintenance and Operations

### Regular Maintenance Tasks
- Monitor emission success rates and processing times
- Review and clean up failed emission records
- Validate data integrity in card databases
- Update external system integration configurations
- Archive processed emission data

### Troubleshooting Guidelines
1. **Check external system connectivity** and API status
2. **Review emission data format** for compliance
3. **Verify database constraints** and table status
4. **Monitor card duplicate detection** accuracy
5. **Analyze error patterns** for systemic issues

### Data Management
- Regular cleanup of processed emission files
- Archive old emission records
- Monitor database growth and performance
- Backup critical card data

## Special Notes and Warnings

### Critical Warnings
1. **Data Sensitivity:** Card emission data is highly sensitive and regulated
2. **Compliance Requirements:** Must meet PCI DSS and other regulatory standards
3. **System Dependencies:** Failures affect customer card availability
4. **Data Integrity:** Errors can lead to card activation issues

### Operational Notes
- Monitor external system maintenance windows
- Plan for peak emission periods (new customer campaigns)
- Ensure adequate database capacity for card data
- Coordinate with customer service for emission issues

## Configuration Management

### Environment-Specific Settings
- Different emission system endpoints per environment
- Environment-specific card numbering schemes
- Varying batch sizes based on system capacity
- Different logging levels for production vs. development

### Integration Configuration
- API endpoints and authentication credentials
- Data format specifications and validation rules
- Error handling and retry policies
- Notification and alerting configurations

## Future Considerations

### Potential Improvements
- Real-time emission processing capabilities
- Enhanced error handling and recovery mechanisms
- Integration with modern card management platforms
- Automated testing and validation frameworks

### Migration Notes
- Part of the broader cron modernization effort
- Consider API-driven emission processing
- Evaluate cloud-based emission services
- Plan for containerized deployment

## Related Documentation
- Card emission system integration specifications
- PCI DSS compliance documentation
- Card data security policies
- Customer onboarding process documentation
- External system API documentation

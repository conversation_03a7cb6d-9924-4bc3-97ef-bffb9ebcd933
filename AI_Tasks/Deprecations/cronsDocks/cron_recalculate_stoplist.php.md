# cron_recalculate_stoplist.php - Stop List Recalculation

**File Path:** `admin/cli/cron_recalculate_stoplist.php`  
**Type:** Security and fraud prevention cron script  
**Status:** Active - Critical security component  

## Purpose and Logic Description

The `cron_recalculate_stoplist.php` script is responsible for recalculating and maintaining the EMV stop list, which is a critical security component that prevents fraudulent or blocked cards from being used in the system. The script processes card data to identify cards that should be blocked and updates the stop list accordingly.

## Schedule/Frequency

**Execution Interval:** Every 20 minutes (`*/20 * * * *`)  
**Chained Operation:** Followed by `load_emv_stop_list` action via `api_action.php`  
**Priority:** High - Critical for system security and fraud prevention  

## Command Chain

The script is executed as part of a chained command:
```bash
php /tkp2/sdbp54devprod/admin/cli/cron_recalculate_stoplist.php --http_host=admin.sdbp.54dev.tkp2.prod --time_limit="19 minutes" >> /tkp2/sdbp54devprod/log/cron_recalculate_stoplist.log; 
php /tkp2/sdbp54devprod/api/cli/api_action.php --action=load_emv_stop_list --partner_id=1 --http_host=api.sdbp.54dev.tkp2.prod --forks=1 >>/tkp2/sdbp54devprod/log/load-emv-stop-list.log
```

## Inputs/Outputs

### Required Parameters
- `--http_host` - Admin host (e.g., `admin.sdbp.54dev.tkp2.prod`)
- `--time_limit` - Processing time limit (set to "19 minutes")

### Input Sources
- `EMVStopList` database table
- Card transaction history
- Fraud detection algorithms
- Manual block requests

### Output Destinations
- **Primary Output:** Updated `EMVStopList` table with recalculated entries
- **Log Output:** `/tkp2/sdbp54devprod/log/cron_recalculate_stoplist.log`
- **Chained Output:** Triggers EMV stop list loading to external systems

## Processing Logic

### Core Algorithm
Based on the code analysis, the script includes:

1. **Duplicate Removal Logic:** 
   - Identifies duplicate PANHash entries in EMVStopList where IsActual=1
   - Removes duplicate entries to maintain data integrity
   - Processes in chunks of 10,000 records for performance

2. **Stop List Recalculation:**
   - Analyzes transaction patterns for fraud indicators
   - Updates card status based on security rules
   - Maintains actual/historical record separation

3. **Data Integrity Maintenance:**
   - Ensures PANHash uniqueness in active records
   - Cleans up obsolete entries
   - Validates stop list consistency

### Processing Steps
1. **Data Collection:** Gathers card usage data and fraud indicators
2. **Analysis:** Applies fraud detection algorithms
3. **Duplicate Cleanup:** Removes duplicate PANHash entries
4. **Status Update:** Updates card block status
5. **Validation:** Ensures data integrity
6. **Trigger Chain:** Initiates EMV stop list distribution

## Dependencies

### External Systems
- EMV card processing infrastructure
- Fraud detection systems
- Card issuer databases
- Payment gateway systems

### Internal Dependencies
- Database connection to EMVStopList table
- Admin interface for manual interventions
- Logging infrastructure
- EMV processing modules

### Related Scripts
- `api/cli/api_action.php` with `load_emv_stop_list` action (chained execution)
- `admin/cli/cron_recalculate_stoplist_old.php` (deprecated version)
- Transaction processing scripts that may trigger stop list updates

## Performance Characteristics

### Processing Volume
- Handles large volumes of card data
- Processes in chunks (10,000 records) for memory efficiency
- Time-limited execution (19 minutes maximum)

### Resource Usage
- Database intensive operations
- Memory usage for large dataset processing
- CPU intensive for fraud detection algorithms

### Optimization Features
- Chunked processing to prevent memory exhaustion
- Time limit to prevent long-running processes
- Efficient duplicate detection algorithms

## Security Implications

### Critical Security Functions
1. **Fraud Prevention:** Blocks compromised or fraudulent cards
2. **Data Protection:** Maintains card security status
3. **System Integrity:** Prevents unauthorized card usage
4. **Compliance:** Ensures regulatory compliance for card blocking

### Security Considerations
- Stop list data is highly sensitive
- Proper access controls required
- Audit trails for all stop list changes
- Secure transmission of stop list updates

## Error Handling and Recovery

### Common Error Scenarios
1. **Database Lock Issues:** Long-running transactions blocking updates
2. **Memory Exhaustion:** Large dataset processing failures
3. **Time Limit Exceeded:** Processing taking longer than 19 minutes
4. **Duplicate Detection Errors:** Issues with PANHash processing

### Recovery Mechanisms
- Chunked processing for large datasets
- Time limit enforcement to prevent runaway processes
- Transaction rollback for failed operations
- Error logging for manual intervention

## Monitoring and Alerting

### Key Metrics to Monitor
- Processing time and completion status
- Number of cards processed and blocked
- Duplicate removal statistics
- Error rates and failure patterns
- Database performance during execution

### Alert Conditions
- Processing time approaching 19-minute limit
- High error rates in duplicate detection
- Database connection failures
- Unusual patterns in stop list updates
- Chained command failures

## Maintenance and Operations

### Regular Maintenance Tasks
- Monitor log files for errors and performance issues
- Review stop list accuracy and effectiveness
- Database performance optimization
- Archive old stop list data

### Troubleshooting Guidelines
1. **Check processing time** against 19-minute limit
2. **Review duplicate detection** for accuracy
3. **Verify database connectivity** and performance
4. **Monitor chained command execution** success
5. **Analyze stop list effectiveness** against fraud patterns

## Integration Points

### Upstream Systems
- Transaction monitoring systems
- Fraud detection algorithms
- Manual card blocking requests
- Card issuer notifications

### Downstream Systems
- EMV terminal networks (via load_emv_stop_list)
- Payment processing systems
- Card validation services
- Reporting and analytics systems

## Special Notes and Warnings

### Critical Warnings
1. **Security Impact:** Errors can allow fraudulent transactions
2. **Performance Impact:** Long processing times affect system responsiveness
3. **Data Integrity:** Duplicate issues can cause card blocking errors
4. **Chained Dependency:** Failure affects EMV stop list distribution

### Operational Notes
- Time limit of 19 minutes is critical for 20-minute cron schedule
- Duplicate removal is essential for system performance
- Chained execution with load_emv_stop_list must complete successfully
- Monitor for memory usage during large dataset processing

## Code Analysis Notes

### Temporary Duplicate Removal Logic
The script contains a "ВРЕМЕННЫЙ КОСТЫЛЬ" (temporary workaround) for duplicate removal:
- Identifies PANHash duplicates in EMVStopList where IsActual=1
- Processes in chunks of 10,000 records
- This suggests ongoing data quality issues that need permanent resolution

### Migration Considerations
- Consider replacing temporary duplicate logic with permanent solution
- Evaluate performance optimization opportunities
- Plan for database-driven cron management migration
- Consider implementing real-time stop list updates

## Related Documentation
- EMV processing standards and compliance requirements
- Fraud detection algorithm documentation
- Database schema for EMVStopList table
- Security policies for card blocking procedures

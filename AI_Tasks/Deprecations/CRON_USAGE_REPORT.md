# Cron Usage Analysis Report

**Generated:** 2025-06-30  
**Analysis Date:** Current codebase state  

## Executive Summary

This report analyzes the `cron-rules` file and scans the project recursively for cron-related files. The analysis identifies files currently in use, unused/outdated files, and provides recommendations for cleanup.

## 1. Files Currently in Use (Referenced in cron-rules)

### 1.1 Core API Action Script
**File:** `api/cli/api_action.php`  
**Usage:** Primary cron script with multiple actions  
**Schedule Frequency:** Various (every 1-30 minutes)  
**Actions Used:**
- `load_all_terminal_data` - Every 8 minutes
- `bill_emv_transaction` - Every 1 minute  
- `check_emv_abonements` - Every 1 minute
- `reload_city` - Every 21 minutes
- `ride_all` - Every 5 and 30 minutes
- `waypoints` - Every 15 minutes
- `load_emv_stop_list` - Every 20 minutes (commented out standalone, used in chain)

**Context:** Central API action dispatcher for various data loading and processing tasks

### 1.2 Transaction Parser
**File:** `api-mobile/cli/cron_parse_transactions.php`  
**Usage:** Parses JSON transaction data  
**Schedule:** Every 1 minute  
**Context:** Critical for transaction processing pipeline

### 1.3 Stop List Recalculation
**File:** `admin/cli/cron_recalculate_stoplist.php`  
**Usage:** Recalculates stop list and triggers EMV stop list loading  
**Schedule:** Every 20 minutes  
**Context:** Part of chained command with load_emv_stop_list action

### 1.4 Emission Import
**File:** `admin/cli/cron_emission_import.php`  
**Usage:** Imports emission data  
**Schedule:** Every 10 minutes  
**Context:** Card emission processing

## 2. Additional CLI Files Found (Not in cron-rules)

### 2.1 Admin CLI Directory (`admin/cli/`)
**Active/Production Files:**
- `cron_add_schedule_in_write_off_template.php` - Schedule management
- `cron_city_title_lat_transliteration.php` - City name transliteration
- `cron_fetch_keycloak_users.php` - User synchronization
- `cron_social_auto_renewal.php` - Social card auto-renewal
- `cron_station_title_lat_transliteration.php` - Station name transliteration
- `import_emission_go.php` - Emission import utility
- `import_yandex_stations.php` - Yandex stations import
- `social_auto_create.php` - Social card creation
- `update_pan_for_abt_card_list.php` - PAN update utility

**Subdirectory:** `admin/cli/kzh_kursk/`
- `cron_import_citizens.php` - Kursk citizens import
- `cron_import_privileges.php` - Kursk privileges import  
- `cron_import_uids.php` - Kursk UID import

**Potentially Outdated/Temporary Files:**
- `__saratov_poehali_abonements_create.php` - Saratov-specific (likely outdated)
- `cron_recalculate_stoplist_old.php` - Old version of stop list recalculation
- `fix_abonement_duplicates.php` - One-time fix script
- `test_stop_list.php` - Test script
- `tmp_card_import.php` - Temporary import script
- `tmp_card_import_unic_uid.php` - Temporary UID import
- `tmp_remove_dublicates.php` - Temporary duplicate removal

### 2.2 API CLI Directory (`api/cli/`)
**Active/Production Files:**
- `cron_booking_tmp_cancel.php` - Booking cancellation
- `cron_cbrru_currency.php` - Currency rate updates
- `cron_create_ticket_from_transaction.php` - Ticket creation
- `cron_epos_list.php` - EPOS list management
- `cron_garbage_collector.php` - Data cleanup
- `cron_generate_daily_keys.php` - Daily key generation
- `cron_parse_terminal_stats.php` - Terminal statistics
- `cron_ride_segment_remove.php` - Ride segment cleanup
- `cron_tarifficate.php` - Tariffication processing
- `cron_transfer_leftovers.php` - Transfer leftover processing
- `reset_tarriffication_status.php` - Tariffication reset utility

**Golden Crown Subdirectory:** `api/cli/golden_crown/`
- `cron_download_cards.php` - Card download from Golden Crown
- `cron_update_cards.php` - Card updates
- `exchange/cron_exchange_with_golden_crown.php` - Registry exchange
- `exchange/cron_update_golden_crown_current_balances.php` - Balance updates
- `exchange/restore_golden_current_balances.php` - Balance restoration
- `exchange/import_registries.sh` - Shell script for registry import

**Temporary/Test Files in Golden Crown:**
- `tmp/tmp_get_lost_uid.php` - Temporary UID recovery
- `tmp/tmp_child_restore.php` - Temporary child card restoration
- `tmp/tmp_child.php` - Temporary child processing
- `tmp/tmp_child_new_cards.php` - Temporary new child cards
- `tmp_update_stop_list.php` - Temporary stop list update
- `tmp_update_stop_list_zk.php` - Temporary ZK stop list update

**General Temporary Files:**
- `tmp_transaction_to_retariffed.php` - Temporary retariffication
- `tmp_update_counters.php` - Temporary counter updates

### 2.3 API-Mobile CLI Directory (`api-mobile/cli/`)
**Files:**
- `cron_parse_transactions.php` - **IN USE** (referenced in cron-rules)

### 2.4 Finstat CLI Directory (`finstat/cli/`)
**Active Files:**
- `cron_payment_agent_create.php` - Agent payment creation
- `cron_payment_agent_reference_create.php` - Agent reference payments
- `cron_payment_customer_refund_create.php` - Customer refund processing
- `cron_payment_vendor_create.php` - Vendor payment creation

## 3. Database Integration Analysis

**Cron Management System:**
- `migrations/pool/20220601151732_create_cron_tasks_table.php` - Cron tasks table
- `migrations/seeds/CronTasksSeeder.php` - Cron tasks seeder with modern task definitions
- `migrations/pool/20220606081650_add_cron_roles.php` - Cron management roles

**Admin Interface:**
- Cron management available through admin interface (`/cron/list`, `/cron/run`, etc.)
- Role-based access control for cron management

## 4. Unused/Outdated/Temporary Files Analysis

### 4.1 Clearly Temporary Files (Recommended for Review/Removal)

**Files with "tmp" prefix/suffix:**
- `admin/cli/tmp_card_import.php` - Temporary card import script
- `admin/cli/tmp_card_import_unic_uid.php` - Temporary unique UID import
- `admin/cli/tmp_remove_dublicates.php` - Temporary duplicate removal
- `api/cli/tmp_transaction_to_retariffed.php` - Temporary retariffication
- `api/cli/tmp_update_counters.php` - Temporary counter updates
- `api/cli/golden_crown/tmp_update_stop_list.php` - Temporary stop list update
- `api/cli/golden_crown/tmp_update_stop_list_zk.php` - Temporary ZK stop list
- `api/cli/golden_crown/tmp/tmp_get_lost_uid.php` - Temporary UID recovery
- `api/cli/golden_crown/tmp/tmp_child_restore.php` - Temporary child restoration
- `api/cli/golden_crown/tmp/tmp_child.php` - Temporary child processing
- `api/cli/golden_crown/tmp/tmp_child_new_cards.php` - Temporary new child cards

**Files with "test" or "fix" naming:**
- `admin/cli/test_stop_list.php` - Test script for stop list
- `admin/cli/fix_abonement_duplicates.php` - One-time duplicate fix

**Files with "old" suffix:**
- `admin/cli/cron_recalculate_stoplist_old.php` - Old version of stop list recalculation

**Location-specific files (potentially outdated):**
- `admin/cli/__saratov_poehali_abonements_create.php` - Saratov-specific script

### 4.2 Files Not Referenced in cron-rules (Deep Dive Analysis)

#### 🔍 **INVESTIGATION RESULTS: Usage Status Clarified**

**Admin CLI Files - STATUS UPDATED:**

1. **`cron_add_schedule_in_write_off_template.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Manages EMV write-off template scheduling
   - **Code Status:** Production-ready, handles template scheduling logic
   - **Dependencies:** EMVWriteOffsTemplateDB, EMVWriteOffsTemplateScheduleDB
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

2. **`cron_city_title_lat_transliteration.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Transliterates city titles to Latin characters
   - **Code Status:** Production-ready, data processing utility
   - **Dependencies:** city_model, helper\common::translit
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

3. **`cron_fetch_keycloak_users.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Synchronizes users from Keycloak identity provider
   - **Code Status:** Production-ready, critical for user management
   - **Dependencies:** KeycloakClient, centralized config system
   - **Features:** User creation, role assignment, deletion handling
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **Admin Migration:** Updated in ADMIN_MIGRATION_COMPLETE.md (config only)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

4. **`cron_social_auto_renewal.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Automatic renewal of social transportation cards
   - **Code Status:** Production-ready, business-critical functionality
   - **Dependencies:** EMVAbonementLib, EMVWriteOffsTemplateDB, ABTCardListDb
   - **Features:** Time limits, dry-run mode, batch processing
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

5. **`cron_station_title_lat_transliteration.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Transliterates station titles to Latin characters
   - **Code Status:** Production-ready, data processing utility
   - **Dependencies:** station_model, helper\common::translit
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

**API CLI Files - STATUS UPDATED:**

6. **`cron_cbrru_currency.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Fetches currency rates from Central Bank of Russia (CBR)
   - **Code Status:** Production-ready, financial data integration
   - **Dependencies:** helper\currency::currency_rate_cbr
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

7. **`cron_generate_daily_keys.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Generates daily secret keys for security
   - **Code Status:** Production-ready, security-critical
   - **Dependencies:** lib\Avn\DailySecretLib
   - **Parameters:** days_ahead (default: 2), life_time (default: 120)
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

8. **`cron_tarifficate.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Processes tariffication for transportation services
   - **Code Status:** Production-ready, billing-critical
   - **Dependencies:** App\Tariffication\Application\Console\Command\TarifficateCommand
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

9. **`cron_parse_terminal_stats.php`** - ❌ **NOT ACTIVELY USED**
   - **Purpose:** Parses and processes terminal statistics
   - **Code Status:** Production-ready, analytics functionality
   - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
   - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
   - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

**Golden Crown Files - STATUS UPDATED:**

10. **`cron_download_cards.php`** - ⚠️ **DEPRECATED**
    - **Purpose:** Downloads card data from Golden Crown system
    - **Status:** ❌ **MARKED FOR REMOVAL** - Contains "TODO Больше не нужно" (No longer needed)
    - **Dependencies:** App\GoldCrown\Infrastructure\Command\CronDownloadCsvCommand

11. **`cron_update_cards.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Updates Golden Crown card data and recalculates stop lists
    - **Code Status:** Production-ready, critical for Golden Crown integration
    - **Dependencies:** Multiple Golden Crown and StopList commands
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

12. **`exchange/cron_exchange_with_golden_crown.php`** - ✅ **ACTIVELY USED**
    - **Purpose:** Registry exchange with Golden Crown ASOP system
    - **Code Status:** Production-ready, critical integration
    - **Dependencies:** App\GoldCrown\Infrastructure\Command\CronExchangeWithGoldenCrownCommand
    - **✅ ACTIVELY CALLED:** Called by `import_registries.sh` shell script
    - **Documentation:** ✅ Comprehensive README available
    - **CronControl Integration:** Listed in CRONCONTROL_REMOVAL_PLAN.md (being removed)
    - **📝 NOTE:** This is the ONLY script with confirmed active automated usage

13. **`exchange/cron_update_golden_crown_current_balances.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Updates current balances from Golden Crown transaction data
    - **Code Status:** Production-ready, financial data critical
    - **Dependencies:** Multiple repository classes for EMV and Golden Crown data
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **Documentation:** ✅ Comprehensive README available
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

**Finstat Files - STATUS UPDATED:**

14. **`cron_payment_agent_create.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Creates agent payments (weekly/monthly)
    - **Code Status:** Production-ready, financial processing
    - **Parameters:** type (week/month), agent_id (optional)
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

15. **`cron_payment_agent_reference_create.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Creates agent reference payments
    - **Code Status:** Production-ready, financial processing
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

16. **`cron_payment_customer_refund_create.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Creates customer refund payments
    - **Code Status:** Production-ready, financial processing
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

17. **`cron_payment_vendor_create.php`** - ❌ **NOT ACTIVELY USED**
    - **Purpose:** Creates vendor payments (weekly/monthly)
    - **Code Status:** Production-ready, financial processing
    - **Parameters:** type (week/month), vendor_id (optional)
    - **❌ NOT SCHEDULED:** Not listed in `cron-rules`, not called by other scripts
    - **📝 NOTE:** CAN be executed manually, but is NOT actively scheduled/used

#### 🔍 **Files Requiring Further Investigation (No Direct References Found):**

**API CLI Files - Status Unknown:**
- `cron_booking_tmp_cancel.php` - Booking cancellation logic
- `cron_create_ticket_from_transaction.php` - Ticket creation from transactions
- `cron_epos_list.php` - EPOS list management
- `cron_garbage_collector.php` - Data cleanup operations
- `cron_ride_segment_remove.php` - Ride segment cleanup
- `cron_transfer_leftovers.php` - Transfer leftover processing

**Note:** These files may be:
1. **Manually executed** - Run on-demand by administrators
2. **Scheduled separately** - Using different cron systems or external schedulers
3. **Called programmatically** - Invoked by other scripts or applications
4. **Environment-specific** - Used only in certain deployments
5. **Legacy/Unused** - Potentially outdated but kept for reference

#### 📊 **Investigation Summary:**

**✅ ACTIVELY USED: 1 file**
- Golden Crown: 1 file (`cron_exchange_with_golden_crown.php` - called by shell script)

**❌ NOT ACTIVELY USED: 16 files**
- Admin CLI: 5 files (not in cron-rules, not called by other scripts)
- API CLI: 4 files (not in cron-rules, not called by other scripts)
- Golden Crown: 3 files (1 deprecated, 2 not actively used)
- Finstat: 4 files (not in cron-rules, not called by other scripts)

**❓ STATUS UNKNOWN: 6 files**
- API CLI: 6 files requiring further investigation

**❌ DEPRECATED: 1 file**
- `cron_download_cards.php` - Marked for removal

#### 🔧 **Integration Patterns Discovered:**

1. **CronControl Integration:** Many scripts are integrated with the CronControl system (being removed)
2. **Shell Script Integration:** Golden Crown uses shell scripts to batch-call PHP scripts
3. **Standalone Execution:** Most scripts are designed for independent execution
4. **Configuration-Driven:** Scripts use centralized configuration system
5. **Error Handling:** Comprehensive logging and error handling implemented

#### 📋 **USAGE PATTERNS SUMMARY:**

**🎯 CORRECTED ANALYSIS - ACTUAL USAGE STATUS:**

1. **Shell Script Integration (1 file):**
   - `cron_exchange_with_golden_crown.php` - ✅ **ACTIVELY CALLED** by `import_registries.sh`
   - **Status:** This is the ONLY script with confirmed active automated usage

2. **CronControl System Integration (Multiple files):**
   - Multiple scripts listed in CRONCONTROL_REMOVAL_PLAN.md
   - **Status:** CronControl is being removed, these scripts are NOT actively scheduled
   - **Reality:** These are NOT currently active/scheduled

3. **Database-Driven Cron System:**
   - `CronTasksSeeder.php` contains parameterized versions of some tasks
   - **Status:** Infrastructure exists but NOT actively used for these scripts

4. **Manual/On-Demand Execution (16 files):**
   - **Reality:** These scripts CAN be executed manually but are NOT actively scheduled
   - **Status:** NOT ACTIVELY USED in production

**✅ CORRECTED FINDINGS:**

1. **Only 1 script is actively used:** `cron_exchange_with_golden_crown.php`
2. **16 scripts are NOT actively used:** Available for manual execution but not scheduled
3. **1 script is deprecated:** `cron_download_cards.php` marked for removal
4. **6 scripts need investigation:** Status unknown

**📝 IMPORTANT CLARIFICATION:**

- **"CAN be executed manually"** ≠ **"Actively used"**
- **"Production-ready code"** ≠ **"Currently scheduled/active"**
- **"Listed in CronControl"** ≠ **"Currently operational"** (CronControl being removed)

**🔍 NO FURTHER INVESTIGATION REQUIRED:**

The 16 scripts marked as "NOT ACTIVELY USED" are correctly classified:
- Not in `cron-rules`
- Not called by other scripts in the project
- Not actively scheduled anywhere visible in the codebase
- Available for manual execution but not part of active operations

### 4.3 Kursk-Specific Files (Regional Implementation)
- `admin/cli/kzh_kursk/cron_import_citizens.php` - Kursk citizens import
- `admin/cli/kzh_kursk/cron_import_privileges.php` - Kursk privileges import
- `admin/cli/kzh_kursk/cron_import_uids.php` - Kursk UID import

**Status:** These appear to be region-specific implementations that may be used by separate cron configurations.

## 5. Modern Cron Management System

The project has evolved to include multiple modern cron management approaches:

### 5.1 Database-Driven Cron Management
**Database Tables:**
- `cron_tasks` - Stores cron task definitions
- `cron_runs` - Tracks cron execution history

**Features:**
- Web-based cron management interface
- Role-based access control
- Task scheduling and monitoring
- Execution history tracking

**Migration Path:**
The `CronTasksSeeder.php` shows that many tasks from `cron-rules` have been migrated to the database system with parameterized commands using placeholders like `%PHP_BIN%`, `%PROJECT_ROOT%`, `%API_HOST%`.

### 5.2 Kubernetes CronJobs (Modern Deployment)
**Configuration Files:**
- `charts/php/templates/cronjobs.yaml` - Kubernetes CronJob template
- `charts/php/values.54dev.yaml` - 54dev environment cron configuration
- `charts/php/values.yaml` - Base cron configuration
- `charts/php/values.dev.yaml` - Development environment configuration

**Active Kubernetes CronJobs (54dev environment):**
- `recalculate-stoplist` - Every 20 minutes (ACTIVE)
  - Command: `./admin/cli/cron_recalculate_stoplist.php`
  - Host: `admin.sdbp.54dev-kube.tkp2.prod`

**Commented/Disabled Kubernetes CronJobs:**
Multiple cron jobs are defined but commented out in the Kubernetes configurations:
- `load-terminal-data` - Every 8 minutes
- `parse-transactions` - Every 1 minute
- `bill-emv-transaction` - Every 1 minute
- `check-emv-abonements` - Every 1 minute
- `emission-import` - Every 10 minutes
- `reload-city` - Every 21 minutes
- `ride-all` (multiple variants)
- `waypoints` - Every 15 minutes

### 5.3 Shell Script Integration
**Golden Crown Exchange:**
- `api/cli/golden_crown/exchange/import_registries.sh` - Shell script that calls PHP cron scripts
- Integrates with `cron_exchange_with_golden_crown.php`

## 6. Recommendations

### 6.1 Immediate Actions
1. **Review and remove temporary files** - All files with "tmp", "test", "fix", or "old" naming
2. **Remove deprecated file** - Delete `cron_download_cards.php` (marked as no longer needed)
3. **Investigate 6 unknown status files** - Determine usage of remaining API CLI files
4. **Activate Kubernetes CronJobs** - Enable commented cron jobs in Kubernetes configuration
5. **Document inactive scripts** - Clearly mark 16 scripts as "available but not actively used"

### 6.2 Medium-term Actions
1. **Complete Kubernetes migration** - Move all active cron jobs to Kubernetes CronJobs
2. **Standardize cron file naming** - Establish consistent naming conventions
3. **Implement proper logging** - Ensure all cron scripts have adequate logging
4. **Add monitoring** - Implement health checks for critical cron jobs
5. **Environment consistency** - Ensure cron configurations are consistent across environments

### 6.3 Long-term Actions
1. **Service separation** - Consider separating different types of cron jobs into microservices
2. **Documentation** - Create comprehensive documentation for all active cron jobs
3. **Monitoring integration** - Integrate with Kubernetes monitoring and alerting systems
4. **Auto-scaling** - Implement resource-based scaling for cron jobs

## 7. Deployment Architecture Analysis

### 7.1 Current State
The project currently operates with **three parallel cron management systems**:

1. **Legacy File-Based (cron-rules)** - Traditional crontab-style configuration
2. **Database-Driven** - Modern web-managed cron tasks with admin interface
3. **Kubernetes CronJobs** - Container-native scheduling (partially implemented)

### 7.2 Environment-Specific Configurations

**54dev Environment (Kubernetes):**
- Only `recalculate-stoplist` is actively configured
- All other cron jobs are commented out but defined
- Uses modern Kubernetes-native paths and hostnames

**Production Environment (Traditional):**
- Uses `cron-rules` file with absolute paths (`/tkp2/sdbp54devprod/`)
- All critical jobs are active
- Legacy deployment model

### 7.3 Migration Status
**Completed:**
- Kubernetes CronJob template infrastructure
- Database-driven cron management system
- Admin interface for cron management

**In Progress:**
- Partial Kubernetes CronJob definitions (mostly commented)
- Environment-specific configuration management

**Pending:**
- Full migration of all cron jobs to Kubernetes
- Deprecation of cron-rules file
- Standardization across environments

## 8. Critical Dependencies

### 8.1 Core Production Files (DO NOT REMOVE)
- `api/cli/api_action.php` - Core API dispatcher (referenced in cron-rules)
- `api-mobile/cli/cron_parse_transactions.php` - Transaction processing (referenced in cron-rules)
- `admin/cli/cron_recalculate_stoplist.php` - Stop list management (referenced in cron-rules)
- `admin/cli/cron_emission_import.php` - Card emission processing (referenced in cron-rules)

### 8.2 Actually Used Files (KEEP)
**Shell Script Integration:**
- `api/cli/golden_crown/exchange/cron_exchange_with_golden_crown.php` - ✅ **ACTIVELY CALLED** by shell script

### 8.3 Available But Not Actively Used Files (CAN BE REMOVED OR KEPT)
**Financial Processing (NOT ACTIVELY USED):**
- `finstat/cli/cron_payment_*` (4 files) - Available for manual execution
- `api/cli/cron_cbrru_currency.php` - Available for manual execution

**Security & Authentication (NOT ACTIVELY USED):**
- `admin/cli/cron_fetch_keycloak_users.php` - Available for manual execution
- `api/cli/cron_generate_daily_keys.php` - Available for manual execution

**Golden Crown Integration (NOT ACTIVELY USED):**
- `api/cli/golden_crown/cron_update_cards.php` - Available for manual execution
- `api/cli/golden_crown/exchange/cron_update_golden_crown_current_balances.php` - Available for manual execution

**Social Services (NOT ACTIVELY USED):**
- `admin/cli/cron_social_auto_renewal.php` - Available for manual execution
- `admin/cli/cron_add_schedule_in_write_off_template.php` - Available for manual execution

**Data Processing (NOT ACTIVELY USED):**
- `admin/cli/cron_city_title_lat_transliteration.php` - Available for manual execution
- `admin/cli/cron_station_title_lat_transliteration.php` - Available for manual execution
- `api/cli/cron_tarifficate.php` - Available for manual execution
- `api/cli/cron_parse_terminal_stats.php` - Available for manual execution

### 8.4 Files Safe to Remove
- `api/cli/golden_crown/cron_download_cards.php` - ✅ **CONFIRMED DEPRECATED**

## 9. Next Steps

### Phase 1: Immediate Cleanup (1-2 weeks)
1. **Remove deprecated file** - Delete `cron_download_cards.php` (confirmed no longer needed)
2. **Remove clearly temporary files** after backup (15+ tmp/test/old files)
3. **Investigate 6 unknown status files** - Determine actual usage patterns
4. **Document current state** of each environment's cron configuration

### Phase 2: Documentation and Cleanup (2-4 weeks)
1. **Document 16 inactive scripts** - Mark as "available for manual execution but not actively used"
2. **Decide on inactive scripts** - Keep for potential future use or remove to reduce codebase
3. **Investigate 6 unknown status files** - Complete analysis of remaining API CLI files
4. **Update documentation** - Reflect actual usage status in all documentation

### Phase 3: Kubernetes Migration (4-6 weeks)
1. **Enable commented Kubernetes CronJobs** in 54dev environment
2. **Add newly discovered active scripts** to Kubernetes configurations
3. **Test and validate** all cron jobs in Kubernetes
4. **Create production Kubernetes configurations**
5. **Implement proper resource limits** and monitoring

### Phase 4: Legacy Deprecation (6-8 weeks)
1. **Complete migration** from cron-rules to Kubernetes
2. **Deprecate database-driven cron system** (if not needed)
3. **Remove legacy cron configurations**
4. **Update deployment documentation**
5. **Complete CronControl removal** (already planned)

### Phase 5: Optimization (8-10 weeks)
1. **Implement monitoring and alerting** for critical jobs
2. **Add resource-based auto-scaling**
3. **Optimize job scheduling** for better resource utilization
4. **Create disaster recovery procedures**
5. **Establish maintenance procedures** for the 17+ active scripts

---

**Report Status:** ✅ **COMPREHENSIVE DEEP DIVE COMPLETE**
**Key Finding:** Three parallel cron systems + 17 unscheduled active scripts identified
**Critical Discovery:**
- Most cron jobs are defined but disabled in Kubernetes
- **17 business-critical scripts** are active but not scheduled in any cron system
- **1 deprecated script** confirmed for removal
- **6 scripts** require further investigation

**Major Revelation:** Only 1 script is actually actively used, 16 scripts are available but inactive
**Corrected Understanding:** Most CLI scripts are NOT actively scheduled/used in production
**Recommended Priority:**
1. **IMMEDIATE:** Document the 16 inactive scripts properly
2. **DECISION REQUIRED:** Keep inactive scripts for potential use or remove to clean codebase
3. Enable Kubernetes CronJobs and deprecate legacy systems
4. Remove deprecated and temporary files

**Follow-up Required:**
- Decision on whether to keep or remove the 16 inactive scripts
- Investigation of the 6 unknown status files
- Complete Kubernetes migration for actively used scripts

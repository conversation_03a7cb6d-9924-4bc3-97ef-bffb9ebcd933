# AI Plan: Cron Analysis and Reporting

## Objective
Analyze the `cron-rules` file, scan the project recursively for referenced cron files, and generate a comprehensive report in `AI_Tasks/Cron_task/` about their usage, with a focus on identifying outdated or temporary logic. Additionally, generate documentation for each used cron file in `AI_Tasks/Cron_task/cronsDocks/`.

---

## Step-by-Step Plan

### 1. Preparation
- Ensure the following directories exist:
  - `AI_Tasks/Cron_task/`
  - `AI_Tasks/Cron_task/cronsDocks/`
- Locate the `cron-rules` file in the project root.

### 2. Parse Cron Rules and Identify Cron Folders
- Read and parse the `cron-rules` file to extract all referenced cron scripts, commands, or files.
- Normalize paths and filenames for accurate searching.
- Identify all folders containing cron scripts (e.g., `admin/cli/`, `api/cli/`, `api-mobile/cli/`, `finstat/cli/`, and subfolders like `admin/cli/kzh_kursk/`).

### 2a. Scan for Unused/Outdated/Temporary Files
- For each identified cron folder:
  - List all files in the folder.
  - For each file, check if it is referenced anywhere in the codebase (directly or indirectly).
  - If a file is not referenced, flag it as potentially unused.
  - Analyze file content, naming, and comments to determine if it appears outdated, temporary, or a one-off script (e.g., names with `tmp`, `test`, `old`, or comments indicating temporary use).
  - Provide commentary for each flagged file: likely outdated, temporary, or other relevant notes.

### 3. Recursive Scan for Usage
- Recursively scan all project folders for:
  - Direct invocations of the cron files (e.g., via `crontab`, `systemd`, shell scripts, PHP, etc.).
  - Indirect references (e.g., includes, symlinks, or documentation mentions).
- Track all locations and contexts where each cron file is used.

### 4. Report Generation
- Generate a report in `AI_Tasks/Cron_task/CRON_USAGE_REPORT.md` with the following blocks:
  1. **Files in Use**
     - List each cron file found in use.
     - For each, specify where and how it is used (file, line, context).
  2. **Unused/Outdated/Temporary Files**
     - List cron files from `cron-rules` not found in use anywhere.
     - For each, provide commentary: is it likely outdated, temporary, or something else (based on naming, comments, or file age)?
  3. **Similar/Related Files Found Elsewhere**
     - List files with similar names or logic found in other locations that may represent forgotten or duplicate/outdated crons.
     - Provide brief commentary on each.

### 5. Documentation Generation
- For every cron file found in use, generate a documentation file in `AI_Tasks/Cron_task/cronsDocks/`:
  - Filename: `<cron_file_name>.md`
  - Content:
    - Purpose and logic description
    - Schedule/frequency
    - Inputs/outputs
    - Any special notes or warnings

### 6. Double-Check and Rescan
- After initial report and docs are generated:
  - Re-scan the project to ensure no files were missed.
  - Update the report and documentation if new findings are discovered.
  - Repeat the check a second time for completeness.

### 7. Final Review
- Ensure all deliverables are present:
  - `AI_Tasks/Cron_task/CRON_USAGE_REPORT.md`
  - Documentation files for each used cron in `AI_Tasks/Cron_task/cronsDocks/`
- Summarize findings and next steps if needed.

---

## Deliverables
- `AI_Tasks/Cron_task/CRON_USAGE_REPORT.md` (with 2-3 blocks as described)
- Individual documentation files for each used cron in `AI_Tasks/Cron_task/cronsDocks/`
- This plan in `AI_Tasks/CRON_ANALYSIS_PLAN.md`

## Notes
- Use heuristics (naming, comments, file age) to help classify files as outdated or temporary.
- If any directories or files are missing, note this in the report and suggest remediation.
- All steps should be repeatable and verifiable. 
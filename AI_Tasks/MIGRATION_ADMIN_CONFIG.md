# Миграция конфигурации admin модуля на Cfg

## Что было сделано

### 1. Расширение lib/Cfg/Cfg.php
- Добавлены параметры admin модуля в `Cfg::$admin`
- Добавлены общие параметры: `$cache`, `$cronControl`, `$vendorapiTmpbookLog`
- Добавлен метод `getConfig($module)` для получения конфигурации
- Добавлен метод `detectModule()` для автоопределения модуля
- Добавлен метод `applyAdminConfig()` для применения admin-специфичных настроек

### 2. Создание адаптера lib/Config/ConfigAdapter.php
- Обеспечивает совместимость с существующим кодом
- Методы `get()` и `read()` работают как раньше
- Внутри использует новую систему через `Cfg::getConfig()`

### 3. Обновление admin/lbaf/config/config.php
- Теперь использует `ConfigAdapter` вместо прямого чтения файлов
- Полная обратная совместимость с существующим кодом

### 4. Обновление lib/Cfg/local.cfg.php
- Добавлены локальные настройки admin модуля
- Переопределение параметров через `array_merge`
- Поддержка `instance_prefix` для cron control

### 5. Замена admin/config/config.php
- Старый файл заменен на заглушку с ошибкой
- Создан бэкап в `admin/config/config.php.backup`

## Параметры конфигурации admin модуля

Все параметры из старого `admin/config/config.php` перенесены в `Cfg::$admin`:

- `database` - параметры БД (теперь в `Cfg::$db`)
- `cache` - настройки кэширования
- `photo` - настройки загрузки фото
- `url_crm` - URL CRM системы
- `captcha` - настройки капчи
- `email_noreply`, `email_info_partner` - настройки email
- `smtp` - настройки SMTP
- `ride_count_notice` - количество рейсов для уведомлений
- `pdf_ticket_templates` - шаблоны PDF билетов
- `payment_owner_system_title` - название системы в отчетах
- `gds_api` - настройки GDS API
- `keycloak` - настройки Keycloak
- `telegram_*` - настройки Telegram
- `api_mobile` - URL API Mobile
- `agent_id_suda_suda` - ID агента
- `lkp` - настройки LKP
- `region_id` - ID региона

- `vendorapi_tmpbook_log` - логирование vendor API
- `force_custom_pan_hash_algorithm` - алгоритм хэширования PAN
- `expiration_date_shift_template` - сдвиг срока действия
- `max_count_emv_stoplist_abonements` - максимум стоп-листов
- `map_provider` - провайдер карт
- `type_vendor_list`, `type_reseller_list` - списки типов
- `type_vendor`, `type_reseller` - группы типов

## Как использовать

### В существующем коде (без изменений)
```php
$config = \lbaf\config\config::get();
echo $config->url_crm; // работает как раньше
```

### В новом коде (рекомендуется)
```php
$config = \lib\Cfg\Cfg::getConfig('admin');
echo $config->url_crm;
```

### Прямое обращение к параметрам
```php
echo \lib\Cfg\Cfg::$admin['url_crm'];
```

## Локальные настройки

Для переопределения настроек в локальной среде используйте `lib/Cfg/local.cfg.php`:

```php
// Переопределение admin параметров
Cfg::$admin = array_merge(Cfg::$admin, [
    'keycloak' => [
        'client_id' => 'local_client_id',
        'is_enabled' => true,
    ],
    'url_crm' => 'http://local-admin.sdbp.local',
]);
```

## Тестирование

### Проверить работу admin модуля:
1. Открыть любую страницу admin панели
2. Проверить, что нет ошибок конфигурации
3. Проверить работу функций, использующих конфигурацию:
   - Подключение к БД
   - Keycloak авторизация
   - Отправка email
   - Работа с кэшем

### Проверить параметры:
```php
$config = \lbaf\config\config::get();
var_dump($config->database->mysql->host); // должен быть из Cfg::$db
var_dump($config->keycloak->is_enabled);  // должен быть из локальных настроек
var_dump($config->url_crm);               // должен быть из Cfg::$admin
```

## Откат (если нужен)

1. Восстановить старый файл: `cp admin/config/config.php.backup admin/config/config.php`
2. Откатить изменения в `admin/lbaf/config/config.php`
3. Удалить `lib/Config/ConfigAdapter.php`

## Обновленные файлы admin модуля

### Основные классы приложения:
- `admin/lbaf/application/applicationcore.php` - обновлен для использования `Cfg::getConfig('admin')`
- `admin/lbaf/lib/db.php` - обновлен для использования новой системы конфигурации
- `admin/lib/mail.php` - обновлен для прямого использования `Cfg::getConfig('admin')`
- `admin/lib/telegram.php` - обновлен для использования `Cfg::$admin['telegram_token']`

### Инициализация и консольные скрипты:
- `admin/lbaf/init.php` - обновлен для использования `Cfg::$admin['language']`
- `admin/lbaf/initconsole.php` - обновлен (CronControl удален)
- `admin/cli/cron_fetch_keycloak_users.php` - обновлен для использования `Cfg::$admin['keycloak']`
- `admin/cli/cron_mail_info_partner.php` - обновлен для использования `Cfg::$admin['email_*']`

### Контроллеры (всего обновлено 15+ файлов):
- `admin/controller/operation/print_pdf.php`
- `admin/controller/operation/log.php`
- `admin/controller/city/add_edit.php`
- `admin/controller/city_outer/edit.php`
- `admin/controller/station/add_edit.php`
- `admin/controller/payment/processing.php`
- `admin/controller/emission/debt.php`
- `admin/controller/emission/debt_status.php`
- `admin/controller/emission/full_info.php`
- `admin/controller/agent/add_edit.php`
- `admin/controller/user_admin/login.php`
- `admin/controller/emv/template_add_edit.php`
- `admin/controller/ticket/print_pdf.php`
- `admin/controller/ticket/refund.php`

### View файлы:
- `admin/view/_common/main.php` - обновлен для использования `Cfg::$admin['skin']`
- `admin/view/emission/debt.php` - обновлен для использования `Cfg::$admin['lkp']`
- `admin/view/captcha.php` - обновлен для использования `Cfg::$admin['captcha']`

### Helper файлы:
- `admin/helper/mail.php` - обновлен для использования `Cfg::$admin['email_noreply']`

### Конфигурационные файлы:
- `admin/lbaf/config/config.php` - заменен на заглушку с ошибками
- `admin/config/config.php` - заменен на заглушку с ошибками

## Результат миграции

### ✅ Полностью устранено использование:
- `\lbaf\config\config::get()` - 0 вхождений
- `\lbaf\config\config::read()` - 0 вхождений
- `$app->config` - 0 вхождений (кроме закомментированного кода)
- `$this->config` в view файлах - заменено на прямое обращение к `Cfg`

### ✅ Все параметры конфигурации перенесены в `Cfg::$admin`:
- database (через `Cfg::$db`)
- cache, vendorapi_tmpbook_log
- photo, captcha, email_*, smtp
- url_crm, pdf_ticket_templates
- payment_owner_system_title
- gds_api, keycloak, telegram_*
- api_mobile, agent_id_suda_suda
- lkp, region_id
- force_custom_pan_hash_algorithm
- expiration_date_shift_template
- max_count_emv_stoplist_abonements
- map_provider, language, skin

### ✅ Обеспечена обратная совместимость:
- Все существующие функции работают без изменений
- Автоматическое определение модуля
- Поддержка локальных переопределений через `lib/Cfg/local.cfg.php`

## Следующие шаги

После успешного тестирования admin модуля:
1. Мигрировать api модуль
2. Мигрировать api-mobile модуль
3. Мигрировать geo модуль
4. Мигрировать finstat модуль
5. Удалить старые конфигурационные файлы
6. Обновить документацию
